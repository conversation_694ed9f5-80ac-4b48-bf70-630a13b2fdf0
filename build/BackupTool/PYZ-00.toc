('/Users/<USER>/coding/build/BackupTool/PYZ-00.pyz',
 [('PyQt5',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/__init__.py',
   'PYMODULE'),
  ('__future__',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_compression.py',
   'PYMODULE'),
  ('_py_abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/qt.py',
   'PYMODULE'),
  ('_strptime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_threading_local.py',
   'PYMODULE'),
  ('argparse',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/argparse.py',
   'PYMODULE'),
  ('ast',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ast.py',
   'PYMODULE'),
  ('backup_app',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/__init__.py',
   'PYMODULE'),
  ('backup_app.backup',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/backup/__init__.py',
   'PYMODULE'),
  ('backup_app.backup.handlers',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/backup/handlers.py',
   'PYMODULE'),
  ('backup_app.backup.initializer',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/backup/initializer.py',
   'PYMODULE'),
  ('backup_app.backup.worker',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/backup/worker.py',
   'PYMODULE'),
  ('backup_app.config', '-', 'PYMODULE'),
  ('backup_app.config.default_projects',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/config/default_projects.py',
   'PYMODULE'),
  ('backup_app.config.settings',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/config/settings.py',
   'PYMODULE'),
  ('backup_app.ui',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/ui/__init__.py',
   'PYMODULE'),
  ('backup_app.ui.project_manager',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/ui/project_manager.py',
   'PYMODULE'),
  ('backup_app.ui.styles',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/ui/styles.py',
   'PYMODULE'),
  ('backup_app.ui.widgets',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/ui/widgets.py',
   'PYMODULE'),
  ('backup_app.utils',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/__init__.py',
   'PYMODULE'),
  ('backup_app.utils.admin_auth',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/admin_auth.py',
   'PYMODULE'),
  ('backup_app.utils.db_manager',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/db_manager.py',
   'PYMODULE'),
  ('backup_app.utils.frame_calculator',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/frame_calculator.py',
   'PYMODULE'),
  ('backup_app.utils.logger',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/logger.py',
   'PYMODULE'),
  ('backup_app.utils.memory_manager',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/memory_manager.py',
   'PYMODULE'),
  ('backup_app.utils.path_manager',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/path_manager.py',
   'PYMODULE'),
  ('backup_app.utils.rsync_config',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/rsync_config.py',
   'PYMODULE'),
  ('base64',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/base64.py',
   'PYMODULE'),
  ('bisect',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bisect.py',
   'PYMODULE'),
  ('bz2',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bz2.py',
   'PYMODULE'),
  ('calendar',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/calendar.py',
   'PYMODULE'),
  ('concurrent',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/thread.py',
   'PYMODULE'),
  ('contextlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextvars.py',
   'PYMODULE'),
  ('copy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/copy.py',
   'PYMODULE'),
  ('csv',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/csv.py',
   'PYMODULE'),
  ('ctypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('ctypes.util',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/wintypes.py',
   'PYMODULE'),
  ('curses',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/curses/__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/curses/has_key.py',
   'PYMODULE'),
  ('dataclasses',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/dataclasses.py',
   'PYMODULE'),
  ('datetime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/datetime.py',
   'PYMODULE'),
  ('decimal',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/decimal.py',
   'PYMODULE'),
  ('dis',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/dis.py',
   'PYMODULE'),
  ('email',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/utils.py',
   'PYMODULE'),
  ('fnmatch',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/fractions.py',
   'PYMODULE'),
  ('ftplib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ftplib.py',
   'PYMODULE'),
  ('getopt',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/getopt.py',
   'PYMODULE'),
  ('getpass',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/getpass.py',
   'PYMODULE'),
  ('gettext',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/gettext.py',
   'PYMODULE'),
  ('glob',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/glob.py',
   'PYMODULE'),
  ('gzip',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/gzip.py',
   'PYMODULE'),
  ('hashlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/hashlib.py',
   'PYMODULE'),
  ('hmac',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/hmac.py',
   'PYMODULE'),
  ('http',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/__init__.py',
   'PYMODULE'),
  ('http.client',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/client.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/cookiejar.py',
   'PYMODULE'),
  ('importlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_text.py',
   'PYMODULE'),
  ('importlib.readers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.util',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/util.py',
   'PYMODULE'),
  ('inspect',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/inspect.py',
   'PYMODULE'),
  ('ipaddress',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ipaddress.py',
   'PYMODULE'),
  ('json',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/__init__.py',
   'PYMODULE'),
  ('json.decoder',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py',
   'PYMODULE'),
  ('json.encoder',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/encoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/scanner.py',
   'PYMODULE'),
  ('logging',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/handlers.py',
   'PYMODULE'),
  ('lzma',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lzma.py',
   'PYMODULE'),
  ('mimetypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/util.py',
   'PYMODULE'),
  ('netrc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/netrc.py',
   'PYMODULE'),
  ('nturl2path',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/nturl2path.py',
   'PYMODULE'),
  ('numbers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/numbers.py',
   'PYMODULE'),
  ('opcode',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/opcode.py',
   'PYMODULE'),
  ('pathlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pathlib.py',
   'PYMODULE'),
  ('pickle',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pickle.py',
   'PYMODULE'),
  ('pkgutil',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pkgutil.py',
   'PYMODULE'),
  ('platform',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/plistlib.py',
   'PYMODULE'),
  ('pprint',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pprint.py',
   'PYMODULE'),
  ('psutil',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/__init__.py',
   'PYMODULE'),
  ('psutil._common',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_common.py',
   'PYMODULE'),
  ('psutil._psosx',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_psosx.py',
   'PYMODULE'),
  ('psutil._psposix',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_psposix.py',
   'PYMODULE'),
  ('py_compile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/py_compile.py',
   'PYMODULE'),
  ('queue',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/queue.py',
   'PYMODULE'),
  ('quopri',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/quopri.py',
   'PYMODULE'),
  ('random',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/random.py',
   'PYMODULE'),
  ('runpy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/runpy.py',
   'PYMODULE'),
  ('secrets',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/secrets.py',
   'PYMODULE'),
  ('selectors',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/selectors.py',
   'PYMODULE'),
  ('shutil',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/shutil.py',
   'PYMODULE'),
  ('signal',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/signal.py',
   'PYMODULE'),
  ('smtplib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/smtplib.py',
   'PYMODULE'),
  ('socket',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/socket.py',
   'PYMODULE'),
  ('ssl',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ssl.py',
   'PYMODULE'),
  ('statistics',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/statistics.py',
   'PYMODULE'),
  ('string',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/string.py',
   'PYMODULE'),
  ('stringprep',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/subprocess.py',
   'PYMODULE'),
  ('tarfile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tarfile.py',
   'PYMODULE'),
  ('tempfile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tempfile.py',
   'PYMODULE'),
  ('textwrap',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/threading.py',
   'PYMODULE'),
  ('token',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/token.py',
   'PYMODULE'),
  ('tokenize',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tracemalloc.py',
   'PYMODULE'),
  ('typing',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/typing.py',
   'PYMODULE'),
  ('urllib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.error',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/error.py',
   'PYMODULE'),
  ('urllib.parse',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/parse.py',
   'PYMODULE'),
  ('urllib.request',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/request.py',
   'PYMODULE'),
  ('urllib.response',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/response.py',
   'PYMODULE'),
  ('uuid',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/uuid.py',
   'PYMODULE'),
  ('watchdog',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/__init__.py',
   'PYMODULE'),
  ('watchdog.events',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/events.py',
   'PYMODULE'),
  ('watchdog.observers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/__init__.py',
   'PYMODULE'),
  ('watchdog.observers.api',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/api.py',
   'PYMODULE'),
  ('watchdog.observers.fsevents',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/fsevents.py',
   'PYMODULE'),
  ('watchdog.observers.inotify',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/inotify.py',
   'PYMODULE'),
  ('watchdog.observers.inotify_buffer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/inotify_buffer.py',
   'PYMODULE'),
  ('watchdog.observers.inotify_c',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/inotify_c.py',
   'PYMODULE'),
  ('watchdog.observers.kqueue',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/kqueue.py',
   'PYMODULE'),
  ('watchdog.observers.polling',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/polling.py',
   'PYMODULE'),
  ('watchdog.observers.read_directory_changes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/read_directory_changes.py',
   'PYMODULE'),
  ('watchdog.observers.winapi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/winapi.py',
   'PYMODULE'),
  ('watchdog.tricks',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/tricks/__init__.py',
   'PYMODULE'),
  ('watchdog.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/__init__.py',
   'PYMODULE'),
  ('watchdog.utils.bricks',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/bricks.py',
   'PYMODULE'),
  ('watchdog.utils.delayed_queue',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/delayed_queue.py',
   'PYMODULE'),
  ('watchdog.utils.dirsnapshot',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/dirsnapshot.py',
   'PYMODULE'),
  ('watchdog.utils.echo',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/echo.py',
   'PYMODULE'),
  ('watchdog.utils.event_debouncer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/event_debouncer.py',
   'PYMODULE'),
  ('watchdog.utils.patterns',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/patterns.py',
   'PYMODULE'),
  ('watchdog.utils.platform',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/platform.py',
   'PYMODULE'),
  ('watchdog.utils.process_watcher',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/process_watcher.py',
   'PYMODULE'),
  ('xml',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/__init__.py',
   'PYMODULE'),
  ('xml.parsers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.sax',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xmlrpc/client.py',
   'PYMODULE'),
  ('zipfile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/_path/glob.py',
   'PYMODULE'),
  ('zipimport',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipimport.py',
   'PYMODULE')])
