(['/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/main.py'],
 ['/Users/<USER>/coding/yeson_py/backup_tool/backup'],
 ['PyQt5',
  'PyQt5.QtCore',
  'PyQt5.QtGui',
  'PyQt5.QtWidgets',
  'psutil',
  'watchdog',
  'watchdog.observers',
  'watchdog.events'],
 [('/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_pyinstaller',
   0),
  ('/Users/<USER>/coding/p312/lib/python3.12/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/Users/<USER>/coding/p312/lib/python3.12/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('config/default_projects.py',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/config/default_projects.py',
   'DATA'),
  ('ui/styles.py',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/ui/styles.py',
   'DATA')],
 '3.12.9 (main, Feb  4 2025, 14:38:38) [Clang 16.0.0 (clang-1600.0.26.6)]',
 [('pyi_rth_inspect',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/main.py',
   'PYSOURCE')],
 [('multiprocessing.spawn',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xmlrpc/client.py',
   'PYMODULE'),
  ('xmlrpc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xmlrpc/__init__.py',
   'PYMODULE'),
  ('gzip',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/gzip.py',
   'PYMODULE'),
  ('argparse',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/argparse.py',
   'PYMODULE'),
  ('textwrap',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/textwrap.py',
   'PYMODULE'),
  ('copy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/copy.py',
   'PYMODULE'),
  ('gettext',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/gettext.py',
   'PYMODULE'),
  ('_compression',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.parsers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/request.py',
   'PYMODULE'),
  ('ipaddress',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ipaddress.py',
   'PYMODULE'),
  ('fnmatch',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/fnmatch.py',
   'PYMODULE'),
  ('getpass',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/getpass.py',
   'PYMODULE'),
  ('nturl2path',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ftplib.py',
   'PYMODULE'),
  ('netrc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/netrc.py',
   'PYMODULE'),
  ('mimetypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/mimetypes.py',
   'PYMODULE'),
  ('getopt',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/getopt.py',
   'PYMODULE'),
  ('email.utils',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/utils.py',
   'PYMODULE'),
  ('email.charset',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/encoders.py',
   'PYMODULE'),
  ('quopri',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/quopri.py',
   'PYMODULE'),
  ('email.errors',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/errors.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/base64mime.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/calendar.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/cookiejar.py',
   'PYMODULE'),
  ('http',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/__init__.py',
   'PYMODULE'),
  ('urllib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/__init__.py',
   'PYMODULE'),
  ('ssl',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ssl.py',
   'PYMODULE'),
  ('urllib.response',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/response.py',
   'PYMODULE'),
  ('urllib.error',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/error.py',
   'PYMODULE'),
  ('contextlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextlib.py',
   'PYMODULE'),
  ('string',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/string.py',
   'PYMODULE'),
  ('hashlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/hashlib.py',
   'PYMODULE'),
  ('email',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/parser.py',
   'PYMODULE'),
  ('email._policybase',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_policybase.py',
   'PYMODULE'),
  ('email.feedparser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/feedparser.py',
   'PYMODULE'),
  ('email.message',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/header.py',
   'PYMODULE'),
  ('bisect',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bisect.py',
   'PYMODULE'),
  ('xml.sax',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('urllib.parse',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/parse.py',
   'PYMODULE'),
  ('http.client',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/client.py',
   'PYMODULE'),
  ('decimal',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextvars.py',
   'PYMODULE'),
  ('numbers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/numbers.py',
   'PYMODULE'),
  ('datetime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_pydatetime.py',
   'PYMODULE'),
  ('_strptime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_strptime.py',
   'PYMODULE'),
  ('base64',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/base64.py',
   'PYMODULE'),
  ('hmac',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/hmac.py',
   'PYMODULE'),
  ('struct',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/struct.py',
   'PYMODULE'),
  ('socket',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/socket.py',
   'PYMODULE'),
  ('selectors',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/selectors.py',
   'PYMODULE'),
  ('tempfile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tempfile.py',
   'PYMODULE'),
  ('shutil',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/shutil.py',
   'PYMODULE'),
  ('zipfile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/_path/glob.py',
   'PYMODULE'),
  ('pathlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pathlib.py',
   'PYMODULE'),
  ('py_compile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/machinery.py',
   'PYMODULE'),
  ('importlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('typing',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/typing.py',
   'PYMODULE'),
  ('importlib.abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('importlib._abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_text.py',
   'PYMODULE'),
  ('inspect',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/inspect.py',
   'PYMODULE'),
  ('token',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/token.py',
   'PYMODULE'),
  ('dis',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/dis.py',
   'PYMODULE'),
  ('opcode',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/opcode.py',
   'PYMODULE'),
  ('ast',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ast.py',
   'PYMODULE'),
  ('csv',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/csv.py',
   'PYMODULE'),
  ('importlib.readers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('tokenize',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tokenize.py',
   'PYMODULE'),
  ('importlib.util',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/util.py',
   'PYMODULE'),
  ('tarfile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tarfile.py',
   'PYMODULE'),
  ('lzma',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lzma.py',
   'PYMODULE'),
  ('bz2',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bz2.py',
   'PYMODULE'),
  ('logging',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/__init__.py',
   'PYMODULE'),
  ('pickle',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pprint.py',
   'PYMODULE'),
  ('dataclasses',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/heap.py',
   'PYMODULE'),
  ('ctypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py',
   'PYMODULE'),
  ('runpy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/runpy.py',
   'PYMODULE'),
  ('pkgutil',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipimport.py',
   'PYMODULE'),
  ('multiprocessing',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/__init__.py',
   'PYMODULE'),
  ('watchdog.events',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/events.py',
   'PYMODULE'),
  ('watchdog.utils.patterns',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/patterns.py',
   'PYMODULE'),
  ('watchdog.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/__init__.py',
   'PYMODULE'),
  ('watchdog.tricks',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/tricks/__init__.py',
   'PYMODULE'),
  ('watchdog.utils.process_watcher',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/process_watcher.py',
   'PYMODULE'),
  ('watchdog.utils.event_debouncer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/event_debouncer.py',
   'PYMODULE'),
  ('watchdog.utils.platform',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/platform.py',
   'PYMODULE'),
  ('watchdog.utils.echo',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/echo.py',
   'PYMODULE'),
  ('__future__',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/__future__.py',
   'PYMODULE'),
  ('watchdog',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/__init__.py',
   'PYMODULE'),
  ('PyQt5',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/__init__.py',
   'PYMODULE'),
  ('stringprep',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_py_abc.py',
   'PYMODULE'),
  ('tracemalloc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tracemalloc.py',
   'PYMODULE'),
  ('backup_app.utils.admin_auth',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/admin_auth.py',
   'PYMODULE'),
  ('backup_app.utils',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/__init__.py',
   'PYMODULE'),
  ('backup_app',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/__init__.py',
   'PYMODULE'),
  ('backup_app.utils.rsync_config',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/rsync_config.py',
   'PYMODULE'),
  ('backup_app.config.settings',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/config/settings.py',
   'PYMODULE'),
  ('backup_app.config', '-', 'PYMODULE'),
  ('backup_app.utils.memory_manager',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/memory_manager.py',
   'PYMODULE'),
  ('backup_app.utils.logger',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/logger.py',
   'PYMODULE'),
  ('logging.handlers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/handlers.py',
   'PYMODULE'),
  ('smtplib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/smtplib.py',
   'PYMODULE'),
  ('backup_app.ui.styles',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/ui/styles.py',
   'PYMODULE'),
  ('backup_app.ui',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/ui/__init__.py',
   'PYMODULE'),
  ('backup_app.ui.widgets',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/ui/widgets.py',
   'PYMODULE'),
  ('backup_app.config.default_projects',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/config/default_projects.py',
   'PYMODULE'),
  ('backup_app.ui.project_manager',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/ui/project_manager.py',
   'PYMODULE'),
  ('backup_app.utils.db_manager',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/db_manager.py',
   'PYMODULE'),
  ('backup_app.utils.frame_calculator',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/frame_calculator.py',
   'PYMODULE'),
  ('backup_app.utils.path_manager',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/utils/path_manager.py',
   'PYMODULE'),
  ('backup_app.backup.handlers',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/backup/handlers.py',
   'PYMODULE'),
  ('backup_app.backup',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/backup/__init__.py',
   'PYMODULE'),
  ('backup_app.backup.worker',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/backup/worker.py',
   'PYMODULE'),
  ('backup_app.backup.initializer',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/backup/initializer.py',
   'PYMODULE'),
  ('platform',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/plistlib.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/__init__.py',
   'PYMODULE'),
  ('uuid',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/uuid.py',
   'PYMODULE'),
  ('json',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/__init__.py',
   'PYMODULE'),
  ('json.encoder',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/encoder.py',
   'PYMODULE'),
  ('json.decoder',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/scanner.py',
   'PYMODULE'),
  ('random',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/random.py',
   'PYMODULE'),
  ('statistics',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/statistics.py',
   'PYMODULE'),
  ('fractions',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/fractions.py',
   'PYMODULE'),
  ('watchdog.observers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/__init__.py',
   'PYMODULE'),
  ('watchdog.observers.polling',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/polling.py',
   'PYMODULE'),
  ('watchdog.utils.dirsnapshot',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/dirsnapshot.py',
   'PYMODULE'),
  ('watchdog.observers.read_directory_changes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/read_directory_changes.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/wintypes.py',
   'PYMODULE'),
  ('watchdog.observers.winapi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/winapi.py',
   'PYMODULE'),
  ('watchdog.observers.kqueue',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/kqueue.py',
   'PYMODULE'),
  ('watchdog.observers.fsevents',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/fsevents.py',
   'PYMODULE'),
  ('watchdog.observers.inotify',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/inotify.py',
   'PYMODULE'),
  ('watchdog.observers.inotify_c',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/inotify_c.py',
   'PYMODULE'),
  ('ctypes.util',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/util.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('watchdog.observers.inotify_buffer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/inotify_buffer.py',
   'PYMODULE'),
  ('watchdog.utils.delayed_queue',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/delayed_queue.py',
   'PYMODULE'),
  ('watchdog.observers.api',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/observers/api.py',
   'PYMODULE'),
  ('watchdog.utils.bricks',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/watchdog/utils/bricks.py',
   'PYMODULE'),
  ('subprocess',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/subprocess.py',
   'PYMODULE'),
  ('psutil',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/__init__.py',
   'PYMODULE'),
  ('psutil._psosx',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_psosx.py',
   'PYMODULE'),
  ('psutil._psposix',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_psposix.py',
   'PYMODULE'),
  ('glob',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/glob.py',
   'PYMODULE'),
  ('psutil._common',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_common.py',
   'PYMODULE'),
  ('curses',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/curses/__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/curses/has_key.py',
   'PYMODULE'),
  ('queue',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/queue.py',
   'PYMODULE'),
  ('threading',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_threading_local.py',
   'PYMODULE'),
  ('signal',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/signal.py',
   'PYMODULE')],
 [('Python.framework/Versions/3.12/Python',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/Python',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqcocoa.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqcocoa.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqwebp.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqwebp.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqgif.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqgif.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/generic/libqtuiotouchplugin.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/generic/libqtuiotouchplugin.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqminimal.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqminimal.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqico.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqico.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqjpeg.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqjpeg.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqtiff.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqtiff.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqsvg.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqsvg.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqmacjp2.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqmacjp2.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqmacheif.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqmacheif.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/iconengines/libqsvgicon.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/iconengines/libqsvgicon.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqicns.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqicns.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platformthemes/libqxdgdesktopportal.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platformthemes/libqxdgdesktopportal.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqwebgl.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqwebgl.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqtga.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqtga.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqwbmp.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqwbmp.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqoffscreen.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqoffscreen.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/styles/libqmacstyle.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/styles/libqmacstyle.dylib',
   'BINARY'),
  ('lib-dynload/_posixshmem.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_posixshmem.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_multiprocessing.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/zlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/pyexpat.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_scproxy.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/termios.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/binascii.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_ssl.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_hashlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha3.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_blake2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_md5.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha1.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_bisect.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/unicodedata.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/math.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_contextvars.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_decimal.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_datetime.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_struct.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/array.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_socket.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/select.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_opcode.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_csv.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/grp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/resource.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_lzma.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_bz2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_pickle.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/mmap.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_ctypes.cpython-312-darwin.so',
   'EXTENSION'),
  ('PyQt5/QtGui.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/QtGui.abi3.so',
   'EXTENSION'),
  ('PyQt5/sip.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/sip.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_multibytecodec.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_jp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_kr.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_cn.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_tw.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_hk.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_heapq.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_uuid.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_json.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_statistics.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_random.cpython-312-darwin.so',
   'EXTENSION'),
  ('_watchdog_fsevents.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/_watchdog_fsevents.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/fcntl.cpython-312-darwin.so',
   'EXTENSION'),
  ('psutil/_psutil_posix.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_psutil_posix.abi3.so',
   'EXTENSION'),
  ('psutil/_psutil_osx.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_psutil_osx.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_curses.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_queue.cpython-312-darwin.so',
   'EXTENSION'),
  ('PyQt5/QtWidgets.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/QtWidgets.abi3.so',
   'EXTENSION'),
  ('PyQt5/QtCore.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/QtCore.abi3.so',
   'EXTENSION'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/QtWidgets',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/QtWidgets',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtCore.framework/Versions/5/QtCore',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtCore.framework/Versions/5/QtCore',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Versions/5/QtDBus',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtDBus.framework/Versions/5/QtDBus',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtGui.framework/Versions/5/QtGui',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtGui.framework/Versions/5/QtGui',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/QtNetwork',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/QtNetwork',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Versions/5/QtSvg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtSvg.framework/Versions/5/QtSvg',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Versions/5/QtQuick',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQuick.framework/Versions/5/QtQuick',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/QtQmlModels',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/QtQmlModels',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtQml.framework/Versions/5/QtQml',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQml.framework/Versions/5/QtQml',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/QtWebSockets',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/QtWebSockets',
   'BINARY'),
  ('libcrypto.3.dylib',
   '/usr/local/opt/openssl@3/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libssl.3.dylib', '/usr/local/opt/openssl@3/lib/libssl.3.dylib', 'BINARY'),
  ('libmpdec.4.dylib',
   '/usr/local/opt/mpdecimal/lib/libmpdec.4.dylib',
   'BINARY'),
  ('liblzma.5.dylib', '/usr/local/opt/xz/lib/liblzma.5.dylib', 'BINARY')],
 [],
 [],
 [('config/default_projects.py',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/config/default_projects.py',
   'DATA'),
  ('ui/styles.py',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/ui/styles.py',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sv.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_sv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ko.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_gl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_gl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_bg.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_de.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fa.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_fa.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_hr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_lv.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_lv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_en.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ca.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_cs.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ca.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_pl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_hr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_nn.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_zh_TW.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_sk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_de.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_tr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ja.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_hr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pt_BR.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fi.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_fi.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_it.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_da.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_zh_CN.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pt_PT.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_pt_PT.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_zh_CN.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_gl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_gl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ar.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_sk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_da.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_uk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_en.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_hu.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ja.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ar.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_sl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_tr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_nl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_he.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_he.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_sl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_sl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_en.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_hu.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_lv.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_lv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_zh_TW.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_cs.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fa.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_fa.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_pl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_nl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_da.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_es.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_gd.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_gd.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_nn.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_pt_BR.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ja.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_es.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ko.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_nn.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_bg.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_it.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fi.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_fi.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_cs.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_he.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_he.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_nl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_es.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_hu.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ko.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_bg.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_uk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ru.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_tr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_pt_BR.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_uk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_fr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_de.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ru.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_zh_CN.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ar.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_gd.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_gd.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ca.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ru.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_lt.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_lt.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_zh_TW.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_it.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_it.qm',
   'DATA'),
  ('Python', 'Python.framework/Versions/3.12/Python', 'SYMLINK'),
  ('base_library.zip',
   '/Users/<USER>/coding/build/BackupTool/base_library.zip',
   'DATA'),
  ('QtPrintSupport',
   'PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport',
   'SYMLINK'),
  ('QtWidgets',
   'PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/QtWidgets',
   'SYMLINK'),
  ('QtCore', 'PyQt5/Qt5/lib/QtCore.framework/Versions/5/QtCore', 'SYMLINK'),
  ('QtDBus', 'PyQt5/Qt5/lib/QtDBus.framework/Versions/5/QtDBus', 'SYMLINK'),
  ('QtGui', 'PyQt5/Qt5/lib/QtGui.framework/Versions/5/QtGui', 'SYMLINK'),
  ('QtNetwork',
   'PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/QtNetwork',
   'SYMLINK'),
  ('QtSvg', 'PyQt5/Qt5/lib/QtSvg.framework/Versions/5/QtSvg', 'SYMLINK'),
  ('QtQuick', 'PyQt5/Qt5/lib/QtQuick.framework/Versions/5/QtQuick', 'SYMLINK'),
  ('QtQmlModels',
   'PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/QtQmlModels',
   'SYMLINK'),
  ('QtQml', 'PyQt5/Qt5/lib/QtQml.framework/Versions/5/QtQml', 'SYMLINK'),
  ('QtWebSockets',
   'PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/QtWebSockets',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtCore.framework/QtCore',
   'Versions/Current/QtCore',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtCore.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtCore.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtCore.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtCore.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtDBus.framework/QtDBus',
   'Versions/Current/QtDBus',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtDBus.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtGui.framework/QtGui', 'Versions/Current/QtGui', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtGui.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtGui.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtGui.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtGui.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/QtNetwork',
   'Versions/Current/QtNetwork',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtNetwork.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/QtPrintSupport',
   'Versions/Current/QtPrintSupport',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtPrintSupport.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQml.framework/QtQml', 'Versions/Current/QtQml', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQml.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQml.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQml.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtQml.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/QtQmlModels',
   'Versions/Current/QtQmlModels',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQmlModels.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQuick.framework/QtQuick',
   'Versions/Current/QtQuick',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQuick.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtSvg.framework/QtSvg', 'Versions/Current/QtSvg', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtSvg.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/QtWebSockets',
   'Versions/Current/QtWebSockets',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtWebSockets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/QtWidgets',
   'Versions/Current/QtWidgets',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtWidgets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Versions/Current', '5', 'SYMLINK'),
  ('Python.framework/Python', 'Versions/Current/Python', 'SYMLINK'),
  ('Python.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python.framework/Versions/3.12/Resources/Info.plist',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/Resources/Info.plist',
   'DATA'),
  ('Python.framework/Versions/Current', '3.12', 'SYMLINK')])
