([('BackupTool',
   '/Users/<USER>/coding/build/BackupTool/BackupTool',
   'EXECUTABLE'),
  ('Python.framework/Versions/3.12/Python',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/Python',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqcocoa.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqcocoa.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqwebp.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqwebp.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqgif.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqgif.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/generic/libqtuiotouchplugin.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/generic/libqtuiotouchplugin.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqminimal.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqminimal.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqico.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqico.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqjpeg.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqjpeg.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqtiff.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqtiff.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqsvg.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqsvg.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqmacjp2.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqmacjp2.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqmacheif.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqmacheif.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/iconengines/libqsvgicon.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/iconengines/libqsvgicon.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqicns.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqicns.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platformthemes/libqxdgdesktopportal.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platformthemes/libqxdgdesktopportal.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqwebgl.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqwebgl.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqtga.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqtga.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqwbmp.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqwbmp.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqoffscreen.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqoffscreen.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/styles/libqmacstyle.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/styles/libqmacstyle.dylib',
   'BINARY'),
  ('lib-dynload/_posixshmem.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_posixshmem.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_multiprocessing.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/zlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/pyexpat.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_scproxy.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/termios.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/binascii.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_ssl.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_hashlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha3.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_blake2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_md5.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha1.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_bisect.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/unicodedata.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/math.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_contextvars.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_decimal.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_datetime.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_struct.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/array.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_socket.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/select.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_opcode.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_csv.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/grp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/resource.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_lzma.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_bz2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_pickle.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/mmap.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_ctypes.cpython-312-darwin.so',
   'EXTENSION'),
  ('PyQt5/QtGui.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/QtGui.abi3.so',
   'EXTENSION'),
  ('PyQt5/sip.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/sip.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_multibytecodec.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_jp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_kr.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_cn.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_tw.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_hk.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_heapq.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_uuid.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_json.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_statistics.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_random.cpython-312-darwin.so',
   'EXTENSION'),
  ('_watchdog_fsevents.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/_watchdog_fsevents.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/fcntl.cpython-312-darwin.so',
   'EXTENSION'),
  ('psutil/_psutil_posix.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_psutil_posix.abi3.so',
   'EXTENSION'),
  ('psutil/_psutil_osx.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_psutil_osx.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_curses.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_queue.cpython-312-darwin.so',
   'EXTENSION'),
  ('PyQt5/QtWidgets.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/QtWidgets.abi3.so',
   'EXTENSION'),
  ('PyQt5/QtCore.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/QtCore.abi3.so',
   'EXTENSION'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/QtWidgets',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/QtWidgets',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtCore.framework/Versions/5/QtCore',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtCore.framework/Versions/5/QtCore',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Versions/5/QtDBus',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtDBus.framework/Versions/5/QtDBus',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtGui.framework/Versions/5/QtGui',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtGui.framework/Versions/5/QtGui',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/QtNetwork',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/QtNetwork',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Versions/5/QtSvg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtSvg.framework/Versions/5/QtSvg',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Versions/5/QtQuick',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQuick.framework/Versions/5/QtQuick',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/QtQmlModels',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/QtQmlModels',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtQml.framework/Versions/5/QtQml',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQml.framework/Versions/5/QtQml',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/QtWebSockets',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/QtWebSockets',
   'BINARY'),
  ('libcrypto.3.dylib',
   '/usr/local/opt/openssl@3/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libssl.3.dylib', '/usr/local/opt/openssl@3/lib/libssl.3.dylib', 'BINARY'),
  ('libmpdec.4.dylib',
   '/usr/local/opt/mpdecimal/lib/libmpdec.4.dylib',
   'BINARY'),
  ('liblzma.5.dylib', '/usr/local/opt/xz/lib/liblzma.5.dylib', 'BINARY'),
  ('config/default_projects.py',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/config/default_projects.py',
   'DATA'),
  ('ui/styles.py',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/ui/styles.py',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sv.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_sv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ko.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_gl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_gl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_bg.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_de.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fa.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_fa.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_hr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_lv.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_lv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_en.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ca.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_cs.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ca.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_pl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_hr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_nn.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_zh_TW.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_sk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_de.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_tr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ja.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_hr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pt_BR.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fi.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_fi.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_it.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_da.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_zh_CN.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pt_PT.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_pt_PT.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_zh_CN.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_gl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_gl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ar.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_sk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_da.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_uk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_en.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_hu.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ja.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ar.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_sl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_tr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_nl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_he.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_he.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_sl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_sl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_en.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_hu.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_lv.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_lv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_zh_TW.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_cs.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fa.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_fa.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_pl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_nl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_da.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_es.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_gd.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_gd.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_nn.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_pt_BR.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ja.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_es.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ko.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_nn.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_bg.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_it.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fi.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_fi.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_cs.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_he.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_he.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_nl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_es.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_hu.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ko.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_bg.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_uk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ru.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_tr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_pt_BR.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_uk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_fr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_de.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ru.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_zh_CN.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ar.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_gd.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_gd.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ca.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ru.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_lt.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_lt.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_zh_TW.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_it.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_it.qm',
   'DATA'),
  ('Python', 'Python.framework/Versions/3.12/Python', 'SYMLINK'),
  ('base_library.zip',
   '/Users/<USER>/coding/build/BackupTool/base_library.zip',
   'DATA'),
  ('QtPrintSupport',
   'PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport',
   'SYMLINK'),
  ('QtWidgets',
   'PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/QtWidgets',
   'SYMLINK'),
  ('QtCore', 'PyQt5/Qt5/lib/QtCore.framework/Versions/5/QtCore', 'SYMLINK'),
  ('QtDBus', 'PyQt5/Qt5/lib/QtDBus.framework/Versions/5/QtDBus', 'SYMLINK'),
  ('QtGui', 'PyQt5/Qt5/lib/QtGui.framework/Versions/5/QtGui', 'SYMLINK'),
  ('QtNetwork',
   'PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/QtNetwork',
   'SYMLINK'),
  ('QtSvg', 'PyQt5/Qt5/lib/QtSvg.framework/Versions/5/QtSvg', 'SYMLINK'),
  ('QtQuick', 'PyQt5/Qt5/lib/QtQuick.framework/Versions/5/QtQuick', 'SYMLINK'),
  ('QtQmlModels',
   'PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/QtQmlModels',
   'SYMLINK'),
  ('QtQml', 'PyQt5/Qt5/lib/QtQml.framework/Versions/5/QtQml', 'SYMLINK'),
  ('QtWebSockets',
   'PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/QtWebSockets',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtCore.framework/QtCore',
   'Versions/Current/QtCore',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtCore.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtCore.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtCore.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtCore.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtDBus.framework/QtDBus',
   'Versions/Current/QtDBus',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtDBus.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtGui.framework/QtGui', 'Versions/Current/QtGui', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtGui.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtGui.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtGui.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtGui.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/QtNetwork',
   'Versions/Current/QtNetwork',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtNetwork.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/QtPrintSupport',
   'Versions/Current/QtPrintSupport',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtPrintSupport.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQml.framework/QtQml', 'Versions/Current/QtQml', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQml.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQml.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQml.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtQml.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/QtQmlModels',
   'Versions/Current/QtQmlModels',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQmlModels.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQuick.framework/QtQuick',
   'Versions/Current/QtQuick',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQuick.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtSvg.framework/QtSvg', 'Versions/Current/QtSvg', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtSvg.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/QtWebSockets',
   'Versions/Current/QtWebSockets',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtWebSockets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/QtWidgets',
   'Versions/Current/QtWidgets',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtWidgets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Versions/Current', '5', 'SYMLINK'),
  ('Python.framework/Python', 'Versions/Current/Python', 'SYMLINK'),
  ('Python.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python.framework/Versions/3.12/Resources/Info.plist',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/Resources/Info.plist',
   'DATA'),
  ('Python.framework/Versions/Current', '3.12', 'SYMLINK')],)
