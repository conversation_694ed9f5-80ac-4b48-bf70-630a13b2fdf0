('/Users/<USER>/coding/build/BackupTool/BackupTool',
 <PERSON>als<PERSON>,
 <PERSON>als<PERSON>,
 True,
 None,
 None,
 <PERSON>als<PERSON>,
 <PERSON>als<PERSON>,
 None,
 True,
 False,
 'x86_64',
 None,
 None,
 '/Users/<USER>/coding/build/BackupTool/BackupTool.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', '/Users/<USER>/coding/build/BackupTool/PYZ-00.pyz', 'PYZ'),
  ('lib-dynload/_struct.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_struct.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/zlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/coding/build/BackupTool/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/coding/build/BackupTool/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/coding/build/BackupTool/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/coding/build/BackupTool/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main',
   '/Users/<USER>/coding/yeson_py/backup_tool/backup/backup_app/main.py',
   'PYSOURCE')],
 [],
 False,
 False,
 1749015574,
 [('runw',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/bootloader/Darwin-64bit/runw',
   'EXECUTABLE')],
 '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/Python')
