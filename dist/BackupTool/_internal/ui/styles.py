from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QApplication, QGraphicsDropShadowEffect, QStyleFactory
from PyQt5.QtGui import QColor, QPalette, QFont

class StyleManager:
    DARK_THEME = {
        "BG": "#1E2526",
        "LIGHT_BG": "#2A3439",
        "ACCENT": "#4CAF50",  # 녹색 계열
        "TEXT": "#E8ECEF",
        "MEDIUM_GRAY": "#A9B1B3",
        "DARK_ACCENT": "#333333",  # 더 어두운 배경용
        "ERROR_RED": "#ff6b6b",  # 오류 메시지용 붉은색
        "ADD_BTN": "#2196F3"  # 추가 버튼용 파란색
    }

    LIGHT_THEME = {
        "BG": "#F5F6F5",
        "LIGHT_BG": "#FFFFFF",
        "ACCENT": "#4CAF50",  # 녹색 계열
        "TEXT": "#2A3439",
        "MEDIUM_GRAY": "#6B7280",
        "DARK_ACCENT": "#E5E7EB",
        "ERROR_RED": "#f44336",  # 오류 메시지용 붉은색
        "ADD_BTN": "#1976D2"  # 추가 버튼용 파란색
    }

    @staticmethod
    def apply_style(app, theme="dark"):
        colors = StyleManager.DARK_THEME if theme == "dark" else StyleManager.LIGHT_THEME
        
        # 기본 스타일 설정
        app.setStyle(QStyleFactory.create("Fusion"))
        
        # 기본 폰트 설정 (한 번만)
        if not hasattr(app, '_font_set'):
            font = QFont("Helvetica", 10)
            app.setFont(font)
            app._font_set = True
        
        # 팔레트 설정
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(colors["BG"]))
        palette.setColor(QPalette.WindowText, QColor(colors["TEXT"]))
        palette.setColor(QPalette.Base, QColor(colors["LIGHT_BG"]))
        palette.setColor(QPalette.AlternateBase, QColor(colors["DARK_ACCENT"]))
        palette.setColor(QPalette.ToolTipBase, QColor(colors["LIGHT_BG"]))
        palette.setColor(QPalette.ToolTipText, QColor(colors["TEXT"]))
        palette.setColor(QPalette.Text, QColor(colors["TEXT"]))
        palette.setColor(QPalette.Button, QColor(colors["DARK_ACCENT"]))
        palette.setColor(QPalette.ButtonText, QColor(colors["TEXT"]))
        palette.setColor(QPalette.BrightText, QColor(colors["ACCENT"]))
        palette.setColor(QPalette.Link, QColor(colors["ACCENT"]))
        palette.setColor(QPalette.Highlight, QColor(colors["ACCENT"]))
        palette.setColor(QPalette.HighlightedText, QColor(colors["BG"]))
        app.setPalette(palette)

        # 스타일시트 설정 (캐시 사용)
        if not hasattr(StyleManager, '_stylesheet_cache'):
            StyleManager._stylesheet_cache = {}
        
        cache_key = f"stylesheet_{theme}"
        if cache_key not in StyleManager._stylesheet_cache:
            StyleManager._stylesheet_cache[cache_key] = f"""
                QMainWindow {{
                    background: {colors["BG"]};
                }}
                
                QWidget {{
                    background: {colors["BG"]};
                    color: {colors["TEXT"]};
                }}
                
                QLabel {{
                    color: {colors["TEXT"]};
                    font-size: 11px;
                    padding: 2px;
                    background: transparent;
                }}
                
                QPushButton {{
                    background-color: {colors["ACCENT"]};
                    color: white;
                    border: none;
                    border-radius: 2px;
                    padding: 4px 8px;
                    font-size: 11px;
                    min-width: 60px;
                }}
                
                QPushButton:hover {{
                    background-color: {StyleManager.adjust_brightness(colors["ACCENT"], 20)};
                }}
                
                QPushButton:pressed {{
                    background-color: {StyleManager.adjust_brightness(colors["ACCENT"], -20)};
                }}
                
                QPushButton#add_project_btn, QPushButton#add_server_btn {{
                    background-color: {colors["ADD_BTN"]};
                    font-weight: bold;
                }}
                
                QPushButton#add_project_btn:hover, QPushButton#add_server_btn:hover {{
                    background-color: {StyleManager.adjust_brightness(colors["ADD_BTN"], 20)};
                }}
                
                QPushButton#add_project_btn:pressed, QPushButton#add_server_btn:pressed {{
                    background-color: {StyleManager.adjust_brightness(colors["ADD_BTN"], -20)};
                }}
                
                QPushButton#stopButton {{
                    background-color: {colors["ERROR_RED"]};
                    color: white;
                    padding: 4px 8px;
                    font-size: 11px;
                    min-width: 60px;
                }}
                
                QPushButton#stopButton:hover {{
                    background-color: {StyleManager.adjust_brightness(colors["ERROR_RED"], 20)};
                }}
                
                QProgressBar {{
                    border: none;
                    background-color: #444444;
                    text-align: center;
                    color: white;
                    font-size: 10px;
                    height: 20px;
                    margin: 0px;
                }}
                
                QProgressBar::chunk {{
                    background-color: {colors["ACCENT"]};
                }}
                
                QStatusBar {{
                    background: {colors["DARK_ACCENT"]};
                    color: {colors["TEXT"]};
                    font-size: 11px;
                    padding: 2px 5px;
                }}
                
                QFrame {{
                    background-color: {colors["BG"]};
                }}
                
                QFrame#CardWidget, QFrame#ShowSelectionCard, QFrame#SceneListCard, QFrame#CalculationCard {{
                    background-color: {colors["LIGHT_BG"]};
                    border: 1px solid {colors["MEDIUM_GRAY"]};
                    border-radius: 4px;
                    margin: 2px;
                }}
                
                QTextEdit {{
                    background-color: #1E1E1E;
                    color: {colors["TEXT"]};
                    border: none;
                    font-family: 'Courier New', monospace;
                    font-size: 9pt;
                    padding: 5px;
                    selection-background-color: #264F78;
                    selection-color: white;
                }}
                
                QScrollBar:vertical {{
                    border: none;
                    background: {colors["DARK_ACCENT"]};
                    width: 10px;
                    margin: 0px;
                }}
                
                QScrollBar::handle:vertical {{
                    background: #666666;
                    min-height: 20px;
                    border-radius: 5px;
                }}
                
                QScrollBar::add-line:vertical,
                QScrollBar::sub-line:vertical {{
                    border: none;
                    background: none;
                }}
                
                QScrollBar::up-arrow:vertical,
                QScrollBar::down-arrow:vertical,
                QScrollBar::add-page:vertical,
                QScrollBar::sub-page:vertical {{
                    border: none;
                    background: none;
                    color: none;
                }}
            """
        
        # 캐시된 스타일시트 적용
        app.setStyleSheet(StyleManager._stylesheet_cache[cache_key])

    @staticmethod
    def adjust_brightness(color_hex, amount):
        color = QColor(color_hex)
        r, g, b = color.red(), color.green(), color.blue()
        r = max(0, min(255, r + amount))
        g = max(0, min(255, g + amount))
        b = max(0, min(255, b + amount))
        return f"#{r:02x}{g:02x}{b:02x}"

class AnimationHelper:
    @staticmethod
    def add_drop_shadow(widget, radius=8, x_offset=1, y_offset=1, color=QColor(0, 0, 0, 60)):
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(radius)
        shadow.setXOffset(x_offset)
        shadow.setYOffset(y_offset)
        shadow.setColor(color)
        widget.setGraphicsEffect(shadow)

# Needed for StyleManager.apply_style
from PyQt5.QtWidgets import QStyleFactory