import sys
import cv2
import os
import threading
import subprocess
import re
import numpy as np
import tempfile
import shutil
import platform
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QLabel, QFileDialog,
                             QScrollBar, QComboBox, QSizePolicy, QCheckBox,
                             QSplitter, QListWidget, QListWidgetItem, QMenu, QMessageBox, QProgressDialog,
                             QProxyStyle, QStyle, QStyleOptionSlider, QSlider)
from PyQt5.QtCore import Qt, QTimer, QEvent, pyqtSlot, pyqtSignal, QPointF, QRectF, QDir, QThread, QDateTime
from PyQt5.QtGui import QWheelEvent, QPixmap, QImage, QPen, QColor, QPainter, QTransform, QFont
from PyQt5.QtWidgets import QGraphicsView, QGraphicsScene, QGraphicsLineItem
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtCore import QUrl

# 통합 오디오 지원을 위한 추가 라이브러리
try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    print("pygame not available, audio support limited")

try:
    import vlc
    VLC_AVAILABLE = True
except ImportError:
    VLC_AVAILABLE = False
    print("python-vlc not available, audio support limited")

class AudioManager:
    """통합 오디오 관리자 - 여러 백엔드를 지원하여 다양한 오디오 코덱 처리"""
    
    def __init__(self):
        self.current_backend = None
        self.media_player = None
        self.vlc_instance = None
        self.vlc_player = None
        self.pygame_initialized = False
        self.current_file = None
        self.volume = 50
        self.is_playing = False
        self.is_paused = False
        
        # 백엔드 우선순위 설정
        self.backend_priority = ['vlc', 'pygame', 'qt']
        self.available_backends = []
        
        # 사용 가능한 백엔드 확인
        if VLC_AVAILABLE:
            self.available_backends.append('vlc')
        if PYGAME_AVAILABLE:
            self.available_backends.append('pygame')
        self.available_backends.append('qt')  # QMediaPlayer는 항상 사용 가능
        
        print(f"Available audio backends: {self.available_backends}")
    
    def initialize_backend(self, backend_name):
        """백엔드 초기화"""
        try:
            if backend_name == 'vlc' and VLC_AVAILABLE:
                if not self.vlc_instance:
                    self.vlc_instance = vlc.Instance('--no-xlib --quiet')
                if not self.vlc_player:
                    self.vlc_player = self.vlc_instance.media_player_new()
                self.current_backend = 'vlc'
                self.media_player = self.vlc_player
                print("VLC audio backend initialized")
                return True
                
            elif backend_name == 'pygame' and PYGAME_AVAILABLE:
                if not self.pygame_initialized:
                    pygame.mixer.init(frequency=44100, size=-16, channels=2, buffer=4096)
                    self.pygame_initialized = True
                self.current_backend = 'pygame'
                self.media_player = None  # pygame은 별도 관리
                print("Pygame audio backend initialized")
                return True
                
            elif backend_name == 'qt':
                # QMediaPlayer는 외부에서 관리되므로 여기서는 설정만
                self.current_backend = 'qt'
                self.media_player = None
                print("Qt audio backend initialized")
                return True
                
        except Exception as e:
            print(f"Failed to initialize {backend_name} backend: {e}")
            return False
    
    def load_file(self, file_path, qt_media_player=None):
        """파일 로드 - 여러 백엔드로 시도"""
        self.current_file = file_path
        
        # 백엔드 우선순위에 따라 시도
        for backend in self.backend_priority:
            if backend in self.available_backends:
                if self._try_load_with_backend(backend, file_path, qt_media_player):
                    print(f"Successfully loaded audio with {backend} backend")
                    return True
        
        print("Failed to load audio with any backend")
        return False
    
    def _try_load_with_backend(self, backend, file_path, qt_media_player=None):
        """특정 백엔드로 파일 로드 시도"""
        try:
            if backend == 'vlc' and VLC_AVAILABLE:
                if not self.vlc_instance:
                    self.vlc_instance = vlc.Instance('--no-xlib --quiet')
                if not self.vlc_player:
                    self.vlc_player = self.vlc_instance.media_player_new()
                
                media = self.vlc_instance.media_new(file_path)
                self.vlc_player.set_media(media)
                self.vlc_player.audio_set_volume(self.volume)
                self.current_backend = 'vlc'
                self.media_player = self.vlc_player
                return True
                
            elif backend == 'pygame' and PYGAME_AVAILABLE:
                if not self.pygame_initialized:
                    pygame.mixer.init(frequency=44100, size=-16, channels=2, buffer=4096)
                    self.pygame_initialized = True
                
                # pygame은 재생 시점에 로드
                self.current_backend = 'pygame'
                self.media_player = None
                return True
                
            elif backend == 'qt' and qt_media_player:
                # QMediaPlayer는 이미 설정되어 있음
                self.current_backend = 'qt'
                self.media_player = qt_media_player
                return True
                
        except Exception as e:
            print(f"Failed to load with {backend} backend: {e}")
            return False
        
        return False
    
    def play(self, start_position_ms=0):
        """오디오 재생"""
        if not self.current_file:
            return False
        
        try:
            if self.current_backend == 'vlc' and self.vlc_player:
                self.vlc_player.set_position(start_position_ms / 1000.0)  # VLC는 초 단위
                self.vlc_player.play()
                self.is_playing = True
                self.is_paused = False
                return True
                
            elif self.current_backend == 'pygame' and PYGAME_AVAILABLE:
                try:
                    pygame.mixer.music.load(self.current_file)
                    pygame.mixer.music.play(start=start_position_ms / 1000.0)
                    pygame.mixer.music.set_volume(self.volume / 100.0)
                    self.is_playing = True
                    self.is_paused = False
                    return True
                except Exception as e:
                    print(f"Pygame play failed: {e}")
                    return False
                    
            elif self.current_backend == 'qt' and self.media_player:
                self.media_player.setPosition(start_position_ms)
                self.media_player.play()
                self.is_playing = True
                self.is_paused = False
                return True
                
        except Exception as e:
            print(f"Play failed with {self.current_backend} backend: {e}")
            return False
        
        return False
    
    def pause(self):
        """오디오 일시정지"""
        try:
            if self.current_backend == 'vlc' and self.vlc_player:
                self.vlc_player.pause()
                self.is_paused = True
                return True
                
            elif self.current_backend == 'pygame' and PYGAME_AVAILABLE:
                pygame.mixer.music.pause()
                self.is_paused = True
                return True
                
            elif self.current_backend == 'qt' and self.media_player:
                self.media_player.pause()
                self.is_paused = True
                return True
                
        except Exception as e:
            print(f"Pause failed: {e}")
            return False
        
        return False
    
    def stop(self):
        """오디오 정지"""
        try:
            if self.current_backend == 'vlc' and self.vlc_player:
                self.vlc_player.stop()
                self.is_playing = False
                self.is_paused = False
                return True
                
            elif self.current_backend == 'pygame' and PYGAME_AVAILABLE:
                pygame.mixer.music.stop()
                self.is_playing = False
                self.is_paused = False
                return True
                
            elif self.current_backend == 'qt' and self.media_player:
                self.media_player.stop()
                self.is_playing = False
                self.is_paused = False
                return True
                
        except Exception as e:
            print(f"Stop failed: {e}")
            return False
        
        return False
    
    def set_volume(self, volume):
        """볼륨 설정 (0-100)"""
        self.volume = max(0, min(100, int(volume)))
        try:
            if self.current_backend == 'vlc' and self.vlc_player:
                self.vlc_player.audio_set_volume(self.volume)
            elif self.current_backend == 'pygame' and PYGAME_AVAILABLE:
                pygame.mixer.music.set_volume(self.volume / 100.0)
            elif self.current_backend == 'qt' and self.media_player:
                self.media_player.setVolume(self.volume)
        except Exception as e:
            print(f"Volume setting failed: {e}")
    
    def set_position(self, position_ms):
        """위치 설정 (밀리초)"""
        try:
            if self.current_backend == 'vlc' and self.vlc_player:
                # VLC는 0.0-1.0 범위의 상대적 위치 사용
                duration = self.vlc_player.get_length()
                if duration > 0:
                    relative_pos = position_ms / duration
                    self.vlc_player.set_position(relative_pos)
                    
            elif self.current_backend == 'pygame' and PYGAME_AVAILABLE:
                # pygame은 위치 설정이 제한적이므로 재시작
                if self.is_playing:
                    pygame.mixer.music.stop()
                    pygame.mixer.music.play(start=position_ms / 1000.0)
                    
            elif self.current_backend == 'qt' and self.media_player:
                self.media_player.setPosition(position_ms)
                
        except Exception as e:
            print(f"Position setting failed: {e}")
    
    def get_position(self):
        """현재 위치 반환 (밀리초)"""
        try:
            if self.current_backend == 'vlc' and self.vlc_player:
                return int(self.vlc_player.get_time())
                
            elif self.current_backend == 'pygame' and PYGAME_AVAILABLE:
                # pygame은 정확한 위치 정보를 제공하지 않음
                return 0
                
            elif self.current_backend == 'qt' and self.media_player:
                return self.media_player.position()
                
        except Exception as e:
            print(f"Position getting failed: {e}")
            return 0
        
        return 0
    
    def get_duration(self):
        """전체 길이 반환 (밀리초)"""
        try:
            if self.current_backend == 'vlc' and self.vlc_player:
                return self.vlc_player.get_length()
                
            elif self.current_backend == 'pygame' and PYGAME_AVAILABLE:
                # pygame은 길이 정보를 제공하지 않음
                return 0
                
            elif self.current_backend == 'qt' and self.media_player:
                return self.media_player.duration()
                
        except Exception as e:
            print(f"Duration getting failed: {e}")
            return 0
        
        return 0
    
    def is_audio_available(self):
        """오디오 사용 가능 여부"""
        return self.current_backend is not None and self.current_file is not None
    
    def cleanup(self):
        """리소스 정리"""
        try:
            if self.current_backend == 'vlc' and self.vlc_player:
                self.vlc_player.stop()
                self.vlc_player.release()
                self.vlc_player = None
                
            elif self.current_backend == 'pygame' and PYGAME_AVAILABLE:
                pygame.mixer.music.stop()
                pygame.mixer.quit()
                self.pygame_initialized = False
                
        except Exception as e:
            print(f"Cleanup failed: {e}")

class FixedHandleStyle(QProxyStyle):
    def __init__(self, base_style=None, handle_width=80):
        super().__init__(base_style)
        self.handle_width = handle_width

    def subControlRect(self, cc, opt, sc, widget=None):
        rect = super().subControlRect(cc, opt, sc, widget)
        if cc == QStyle.CC_ScrollBar and sc == QStyle.SC_ScrollBarSlider and isinstance(opt, QStyleOptionSlider):
            if opt.orientation == Qt.Horizontal:
                rect.setWidth(self.handle_width)
        return rect

class FFmpegWorker(QThread):
    progress = pyqtSignal(int)
    finished = pyqtSignal()
    error = pyqtSignal(str)

    def __init__(self, merge_function):
        super().__init__()
        self.merge_function = merge_function

    def run(self):
        try:
            self.merge_function()
            self.finished.emit()
        except Exception as e:
            self.error.emit(str(e))

class CustomVideoWidget(QGraphicsView):
    wipePositionChanged = pyqtSignal()
    fitToWindowChanged = pyqtSignal(bool)
    def __init__(self, parent=None):
        super().__init__(parent)
        self.scene = QGraphicsScene(self)
        self.setScene(self.scene)
        self.video_item = self.scene.addPixmap(QPixmap())

        self.wipe_position_x = 0.5
        self.wipe_position_y = 0.5
        self.wipe_line_x = QGraphicsLineItem()
        self.wipe_line_y = QGraphicsLineItem()
        self.scene.addItem(self.wipe_line_x)
        self.scene.addItem(self.wipe_line_y)
        self.wipe_enabled_x = False
        self.wipe_enabled_y = False

        self.setMouseTracking(True)
        self.aspect_ratio = 16 / 9
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setRenderHint(QPainter.Antialiasing)
        self.setRenderHint(QPainter.SmoothPixmapTransform)
        self.wipeEnabled = False
        self.setAcceptDrops(True)
        self.zoom_level = 1.0
        self.base_line_width = 8
        self.initial_fit = True
        self.setDragMode(QGraphicsView.NoDrag)
        self.is_panning = False
        self.last_pan_point = QPointF()
        self.is_fit_to_window = False
        self.is_original_size = False
        self.zoom_changed = False

        self.zoom_label = QLabel(self)
        self.zoom_label.setAlignment(Qt.AlignCenter)
        self.zoom_label.setStyleSheet("background-color: rgba(255, 255, 255, 128); padding: 5px; border-radius: 3px;")
        self.zoom_label.hide()

    def resizeEvent(self, event):
        super().resizeEvent(event)
        self.update_zoom_label_position()
        if self.is_fit_to_window:
            self.fitInView(self.video_item, Qt.KeepAspectRatio)

    def update_zoom_label_position(self):
        self.zoom_label.move(10, 10)

    def wheelEvent(self, event: QWheelEvent):
        # 비디오 위젯에서는 휠 이벤트를 무시하고 부모에게 전달
        # (전역 휠 이벤트에서 처리)
        event.ignore()

    def show_original_size(self):
        if self.video_item.pixmap():
            self.is_original_size = True
            self.is_fit_to_window = False
            self.setTransform(QTransform())
            self.setRenderHint(QPainter.SmoothPixmapTransform, True)  # 안티앨리어싱 적용
            self.setRenderHint(QPainter.Antialiasing, True)  # 추가 안티앨리어싱
            self.video_item.setTransformationMode(Qt.SmoothTransformation)  # 부드러운 변환 모드
            self.centerOn(self.video_item.boundingRect().center())
            self.updateWipeLine()
            self.fitToWindowChanged.emit(False)
            self.setDragMode(QGraphicsView.ScrollHandDrag)
            self.hide_zoom_label()

    def fit_to_window(self):
        if self.video_item.pixmap():
            self.setTransform(QTransform())
            self.fitInView(self.video_item, Qt.KeepAspectRatio)
            self.setRenderHint(QPainter.SmoothPixmapTransform, True)  # 안티앨리어싱 적용
            self.setRenderHint(QPainter.Antialiasing, True)  # 추가 안티앨리어싱
            self.video_item.setTransformationMode(Qt.SmoothTransformation)  # 부드러운 변환 모드
            self.is_fit_to_window = True
            self.is_original_size = False
            self.updateWipeLine()
            self.fitToWindowChanged.emit(True)
            self.setDragMode(QGraphicsView.NoDrag)
            self.hide_zoom_label()

    def zoom_in(self):
        print(f"zoom_in called - current scale: {self.transform().m11()}")  # 디버깅 출력
        self.is_fit_to_window = False
        self.is_original_size = False
        self.setDragMode(QGraphicsView.ScrollHandDrag)
        scale_factor = 1.1
        self.scale(scale_factor, scale_factor)
        self.setRenderHint(QPainter.SmoothPixmapTransform, True)  # 안티앨리어싱 적용
        self.setRenderHint(QPainter.Antialiasing, True)  # 추가 안티앨리어싱
        self.video_item.setTransformationMode(Qt.SmoothTransformation)  # 부드러운 변환 모드
        new_scale = self.transform().m11()
        print(f"zoom_in - new scale: {new_scale}")  # 디버깅 출력
        self.updateWipeLine()
        self.show_zoom_label()

    def zoom_out(self):
        print(f"zoom_out called - current scale: {self.transform().m11()}")  # 디버깅 출력
        self.is_fit_to_window = False
        self.is_original_size = False
        scale_factor = 1 / 1.1
        self.scale(scale_factor, scale_factor)
        self.setRenderHint(QPainter.SmoothPixmapTransform, True)  # 안티앨리어싱 적용
        self.setRenderHint(QPainter.Antialiasing, True)  # 추가 안티앨리어싱
        self.video_item.setTransformationMode(Qt.SmoothTransformation)  # 부드러운 변환 모드
        new_scale = self.transform().m11()
        print(f"zoom_out - new scale: {new_scale}")  # 디버깅 출력
        if new_scale < 0.1:  # 매우 작은 최소값으로 설정
            self.fit_to_window()
        self.updateWipeLine()
        self.show_zoom_label()

    def reset_view(self):
        self.fit_to_window()
        self.setRenderHint(QPainter.SmoothPixmapTransform, True)  # 안티앨리어싱 적용
        self.setRenderHint(QPainter.Antialiasing, True)  # 추가 안티앨리어싱
        self.video_item.setTransformationMode(Qt.SmoothTransformation)  # 부드러운 변환 모드
    
    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls(): event.accept()
        else: event.ignore()

    def dropEvent(self, event):
        urls = event.mimeData().urls()
        if len(urls) == 1:
            self.parent().open_file('A', urls[0].toLocalFile())
        elif len(urls) == 2:
            self.parent().open_file('A', urls[0].toLocalFile())
            self.parent().open_file('B', urls[1].toLocalFile())

    def updateFrame(self, frame_a, frame_b):
        if frame_a is None:
            self.video_item.setPixmap(QPixmap())
            return

        height, width = frame_a.shape[:2]
        bytes_per_line = 3 * width
        
        q_img = None
        if self.wipe_enabled_x or self.wipe_enabled_y:
            if frame_b is None or frame_b.shape != frame_a.shape:
                frame_b = np.zeros_like(frame_a)
            result = frame_a.copy()
            if self.wipe_enabled_x:
                wipe_x = int(width * self.wipe_position_x)
                result[:, wipe_x:] = frame_b[:, wipe_x:]
            if self.wipe_enabled_y:
                wipe_y = int(height * self.wipe_position_y)
                result[wipe_y:, :] = frame_b[wipe_y:, :]
            q_img = QImage(result.data, width, height, bytes_per_line, QImage.Format_RGB888)
        else:
            q_img = QImage(frame_a.data, width, height, bytes_per_line, QImage.Format_RGB888)

        pixmap = QPixmap.fromImage(q_img)
        self.video_item.setPixmap(pixmap)

        scene_rect_changed = self.scene.sceneRect() != self.video_item.boundingRect()
        if self.initial_fit or scene_rect_changed:
            self.scene.setSceneRect(self.video_item.boundingRect())
            self.fit_to_window()
            self.initial_fit = False
        
        self.updateWipeLine()

    def toggleWipeEffect(self, enabled):
        self.wipeEnabled = enabled
        self.updateWipeLine()

    def updateWipeLine(self):
        if not self.video_item.pixmap() or self.video_item.pixmap().isNull():
            return
            
        current_scale = self.transform().m11()

        if self.wipe_enabled_x:
            self.wipe_line_x.show()
            pen = QPen(QColor(255, 0, 0, 80))
            pen.setWidthF(self.base_line_width / current_scale)
            self.wipe_line_x.setPen(pen)
            brect = self.video_item.boundingRect()
            scene_x = brect.x() + brect.width() * self.wipe_position_x
            self.wipe_line_x.setLine(scene_x, brect.y(), scene_x, brect.y() + brect.height())
        else:
            self.wipe_line_x.hide()

        if self.wipe_enabled_y:
            self.wipe_line_y.show()
            pen = QPen(QColor(255, 0, 0, 80))
            pen.setWidthF(self.base_line_width / current_scale)
            self.wipe_line_y.setPen(pen)
            brect = self.video_item.boundingRect()
            scene_y = brect.y() + brect.height() * self.wipe_position_y
            self.wipe_line_y.setLine(brect.x(), scene_y, brect.x() + brect.width(), scene_y)
        else:
            self.wipe_line_y.hide()

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            scene_pos = self.mapToScene(event.pos())
            is_on_wipe_x = self.wipe_enabled_x and self.wipe_line_x.shape().contains(scene_pos)
            is_on_wipe_y = self.wipe_enabled_y and self.wipe_line_y.shape().contains(scene_pos)
            
            if is_on_wipe_x or is_on_wipe_y:
                 self.setDragMode(QGraphicsView.NoDrag)
                 if is_on_wipe_x: self.setCursor(Qt.SizeHorCursor)
                 if is_on_wipe_y: self.setCursor(Qt.SizeVerCursor)
            else:
                 if not self.is_fit_to_window:
                     self.setDragMode(QGraphicsView.ScrollHandDrag)
                 super().mousePressEvent(event)
        else:
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        if self.cursor().shape() in [Qt.SizeHorCursor, Qt.SizeVerCursor]:
            scene_pos = self.mapToScene(event.pos())
            brect = self.video_item.boundingRect()
            if self.cursor().shape() == Qt.SizeHorCursor and brect.width() > 0:
                self.wipe_position_x = max(0, min(1, (scene_pos.x() - brect.x()) / brect.width()))
            elif self.cursor().shape() == Qt.SizeVerCursor and brect.height() > 0:
                self.wipe_position_y = max(0, min(1, (scene_pos.y() - brect.y()) / brect.height()))
            self.updateWipeLine()
            self.wipePositionChanged.emit()
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.setCursor(Qt.ArrowCursor)
            if not self.is_fit_to_window:
                self.setDragMode(QGraphicsView.ScrollHandDrag)
        super().mouseReleaseEvent(event)

    def show_zoom_label(self):
        self.update_zoom_label()
        self.zoom_label.show()
        QTimer.singleShot(1000, self.hide_zoom_label)

    def hide_zoom_label(self):
         self.zoom_label.hide()

    def update_zoom_label(self):
        zoom_percent = int(self.transform().m11() * 100)
        self.zoom_label.setText(f"Zoom: {zoom_percent}%")
        self.update_zoom_label_position()

class VideoPlayer(QMainWindow):
    def __init__(self, ffmpeg_path=None):
        super().__init__()
        self.setWindowTitle("Yeson Take1 & 2 비교하기")
        self.setGeometry(100, 100, 1200, 700)
        
        # 고급스러운 메인 윈도우 스타일 적용
        self.setStyleSheet("""
QMainWindow {
    background: #fafafa;
    color: #2c3e50;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 12px;
}
QWidget {
    background: transparent;
    color: #2c3e50;
    font-family: 'Segoe UI', Arial, sans-serif;
}
QPushButton {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    color: #2c3e50;
    font-weight: 500;
    padding: 8px 16px;
    min-height: 20px;
}
QPushButton:hover {
    background: #f8f9fa;
    border: 1px solid #d0d0d0;
    color: #1a252f;
}
QPushButton:pressed {
    background: #e9ecef;
    border: 1px solid #c0c0c0;
}
QPushButton:disabled {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #adb5bd;
}
QComboBox {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 6px 12px;
    color: #2c3e50;
    min-height: 20px;
}
QComboBox:hover {
    border: 1px solid #d0d0d0;
}
QComboBox::drop-down {
    border: none;
    width: 20px;
}
QComboBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #6c757d;
    margin-right: 5px;
}
QComboBox QAbstractItemView {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    selection-background-color: #e9ecef;
    color: #2c3e50;
}
QCheckBox {
    color: #2c3e50;
    spacing: 8px;
}
QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #e0e0e0;
    border-radius: 3px;
    background: #ffffff;
}
QCheckBox::indicator:checked {
    background: #495057;
    border: 2px solid #495057;
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
}
QLabel {
    color: #2c3e50;
    font-weight: 400;
}
QListWidget {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    color: #2c3e50;
    selection-background-color: #e9ecef;
    outline: none;
}
QListWidget::item {
    padding: 6px;
    border-bottom: 1px solid #f8f9fa;
}
QListWidget::item:hover {
    background: #f8f9fa;
}
QListWidget::item:selected {
    background: #e9ecef;
    color: #2c3e50;
}
QSplitter::handle {
    background: #e0e0e0;
    border-radius: 1px;
}
QSplitter::handle:horizontal {
    width: 2px;
}
QSplitter::handle:vertical {
    height: 2px;
}
QScrollBar:vertical {
    background: #f8f9fa;
    width: 12px;
    border-radius: 6px;
}
QScrollBar::handle:vertical {
    background: #adb5bd;
    border-radius: 6px;
    min-height: 20px;
}
QScrollBar::handle:vertical:hover {
    background: #6c757d;
}
QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}
QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
    background: none;
}
""")
        
        self.initUI()
        self.controls_visible = True

        self.loopEnabled = False
        self.filename_a = ""
        self.filename_b = ""
        self.cap_a = None
        self.cap_b = None
        self.frame_a = None
        self.frame_b = None
        
        self.setFocusPolicy(Qt.StrongFocus)
        self.setAcceptDrops(True)
        self.mediaPlayer = QMediaPlayer(self)
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.next_frame_playback)
        self.videoWidget.fitToWindowChanged.connect(self.on_fit_to_window_changed)
        
        # 통합 오디오 관리자 초기화
        self.audio_manager = AudioManager()
        
        self.playlist = []
        self.current_playlist_index = -1
        self.is_playlist_playing = False
        self.current_item = None
        self.use_gpu = False
        self.gpu_backend = None
        self.workComboBox.currentIndexChanged.connect(self.update_season_list)
        self.seasonComboBox.currentIndexChanged.connect(self.update_job_list)
        self.jobComboBox.currentIndexChanged.connect(self.update_playlist)

        self.installEventFilter(self)
        
        if hasattr(sys, '_MEIPASS'):
            self.ffmpeg_path = os.path.join(os.path.dirname(os.path.dirname(sys.executable)), 'Frameworks', 'ffmpeg')
        else:
            self.ffmpeg_path = "ffmpeg"

        self.show_overlay = False
        self.isFullScreen = False
        
        # 드래그 최적화를 위한 변수들
        self.last_drag_update = 0
        self.drag_update_interval = 50  # 50ms마다 업데이트 (20fps)
        self.is_dragging = False
    
    def initUI(self):
        self.videoWidget = CustomVideoWidget(self)
        self.videoWidget.wipePositionChanged.connect(self.update_frame)

        self.gpuComboBox = QComboBox()
        self.gpuComboBox.addItem("CPU")
        if cv2.cuda.getCudaEnabledDeviceCount() > 0:
            self.gpuComboBox.addItem("NVIDIA (CUDA)")
        if cv2.ocl.haveOpenCL():
            self.gpuComboBox.addItem("AMD (OpenCL)")
        self.gpuComboBox.currentIndexChanged.connect(self.change_gpu_backend)

        workLabel = QLabel("Yeson 작품")
        workLabel.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.workComboBox = QComboBox()
        self.workComboBox.addItem("작품선택")
        self.workComboBox.addItem("BB")
        self.workComboBox.addItem("KOTH")
        self.workComboBox.addItem("Big_Mouth")
        self.workComboBox.addItem("TGN")
        self.workComboBox.addItem("MatingSeason")
        self.workComboBox.addItem("BB_Animatic")
        self.workComboBox.addItem("KOTH_Animatic")
        self.workComboBox.addItem("BM_Animatic")
        self.workComboBox.addItem("TGN_Animatic")
        self.workComboBox.addItem("MS_Animatic")
        self.seasonComboBox = QComboBox()
        self.seasonComboBox.addItem("시즌선택")
        self.jobComboBox = QComboBox()
        self.jobComboBox.addItem("job선택")

        openButtonA = QPushButton('Open Video A')
        openButtonA.clicked.connect(lambda: self.open_file('A'))
        openButtonB = QPushButton('Open Video B')
        openButtonB.clicked.connect(lambda: self.open_file('B'))
        prevButton = QPushButton('Previous Frame')
        prevButton.clicked.connect(self.prev_frame)
        nextButton = QPushButton('Next Frame')
        nextButton.clicked.connect(self.next_frame)
        self.fullscreenButton = QPushButton('Full screen')
        self.fullscreenButton.clicked.connect(self.toggle_fullscreen)
        helpButton = QPushButton('Help')
        helpButton.clicked.connect(self.show_help)
        self.loopCheckBox = QCheckBox("Loop Play")
        self.loopCheckBox.setToolTip('Enable loop playback')
        self.loopCheckBox.stateChanged.connect(self.toggle_loop_play)
        self.playPauseButton = QPushButton('Play')
        self.playPauseButton.clicked.connect(self.toggle_play_pause)
        self.playPauseButton.setFixedHeight(80)
        self.playPauseButton.setStyleSheet("""
QPushButton {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    color: #2c3e50;
    font-weight: 500;
    padding: 8px 16px;
    min-height: 20px;
}
QPushButton:hover {
    background: #f8f9fa;
    border: 1px solid #d0d0d0;
    color: #1a252f;
}
QPushButton:pressed {
    background: #e9ecef;
    border: 1px solid #c0c0c0;
}
""")
        stopButton = QPushButton('Stop')
        stopButton.clicked.connect(self.stop_video)
        stopButton.setFixedHeight(80)
        stopButton.setStyleSheet("""
QPushButton {
    background: #6c757d;
    border: 1px solid #5a6268;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    font-size: 14px;
    padding: 12px 24px;
}
QPushButton:hover {
    background: #5a6268;
    border: 1px solid #495057;
}
QPushButton:pressed {
    background: #495057;
    border: 1px solid #343a40;
}
""")
        self.filenameLabel = QLabel()
        self.labelFrameCount = QLabel()
        self.labelTotalFrames = QLabel()
        font = QFont(); font.setPointSize(16)
        self.filenameLabel.setFont(font)
        self.labelFrameCount.setFont(font)
        self.labelTotalFrames.setFont(font)
        self.labelFrameCount.setFixedWidth(200)
        self.labelTotalFrames.setFixedWidth(200)
        self.filenameLabel.setStyleSheet("""
QLabel {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    color: #495057;
    font-weight: 500;
    padding: 10px;
}
""")
        self.labelFrameCount.setStyleSheet("""
QLabel {
    background: #e9ecef;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    color: #495057;
    font-weight: 500;
    padding: 10px;
}
""")
        self.labelTotalFrames.setStyleSheet("""
QLabel {
    background: #dee2e6;
    border: 1px solid #ced4da;
    border-radius: 6px;
    color: #495057;
    font-weight: 500;
    padding: 10px;
}
""")
        
        self.positionScroll = QSlider(Qt.Horizontal)
        self.positionScroll.setMinimum(0)
        self.positionScroll.setMaximum(100)
        self.positionScroll.setSingleStep(1)
        self.positionScroll.setPageStep(1)
        self.positionScroll.setStyleSheet("""
QSlider:horizontal {
    height: 20px;
    background: transparent;
}
QSlider::groove:horizontal {
    height: 6px;
    background: #e9ecef;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    margin: 7px 0;
}
QSlider::handle:horizontal {
    min-width: 80px;
    max-width: 80px;
    height: 16px;
    background: #495057;
    border: 1px solid #343a40;
    border-radius: 8px;
    margin: -5px 0;
}
QSlider::handle:horizontal:hover {
    background: #6c757d;
    border: 1px solid #495057;
}
QSlider::handle:horizontal:pressed {
    background: #343a40;
    border: 1px solid #212529;
}
QSlider::sub-page:horizontal {
    background: #495057;
    border-radius: 3px;
}
QSlider::add-page:horizontal {
    background: #e9ecef;
    border-radius: 3px;
}
""")
        self.positionScroll.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.positionScroll.setMinimumSize(100, 20)
        self.positionScroll.setToolTip('Position')
        self.positionScroll.valueChanged.connect(self.on_slider_value_changed)  # 드래그 완료 시
        self.positionScroll.sliderMoved.connect(self.on_slider_moved)  # 드래그 중 실시간 업데이트
        self.positionScroll.sliderReleased.connect(self.sync_audio_on_release)
        
        self.frameRateLabel = QLabel("FPS")
        self.frameRateComboBox = QComboBox()
        self.frameRateComboBox.setToolTip('Frame rate (frames per second)')
        self.frameRateComboBox.addItems([str(i) for i in range(1, 61)])
        self.frameRateComboBox.setCurrentText('24')
        self.wipeCheckBox = QCheckBox('Enable Wipe Effect')
        self.wipeCheckBox.setChecked(False)
        self.wipeCheckBox.stateChanged.connect(self.toggle_wipe_effect)
        self.wipeXButton = QPushButton('X-Axis Wipe'); self.wipeXButton.setCheckable(True)
        self.wipeXButton.toggled.connect(self.toggle_wipe_x)
        self.wipeYButton = QPushButton('Y-Axis Wipe'); self.wipeYButton.setCheckable(True)
        self.wipeYButton.toggled.connect(self.toggle_wipe_y)
        self.wipeXButton.setEnabled(False)
        self.wipeYButton.setEnabled(False)
        self.volumeSlider = QScrollBar(Qt.Horizontal); self.volumeSlider.setRange(0, 100)
        self.volumeSlider.setValue(50); self.volumeSlider.setFixedWidth(100)
        self.volumeSlider.valueChanged.connect(self.setVolume)
        self.volumeSlider.setStyleSheet("""
QScrollBar:horizontal {
    height: 16px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}
QScrollBar::handle:horizontal {
    background: #495057;
    border: 1px solid #343a40;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}
QScrollBar::handle:horizontal:hover {
    background: #6c757d;
    border: 1px solid #495057;
}
QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
    background: none;
    border: none;
}
QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {
    background: none;
}
""")
        volumeLabel = QLabel("Volume:")
        self.playSelectedButton = QPushButton('Play Selected')
        self.playSelectedButton.clicked.connect(self.play_selected_files)
        self.playSelectedButton.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.playSelectedPauseButton = QPushButton('Play/Pause Selected')
        self.playSelectedPauseButton.clicked.connect(self.toggle_play_pause_selected)
        self.playSelectedPauseButton.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.gpuLabel = QLabel("GPU Acceleration:")
        controlLayoutMiddle = QVBoxLayout()
        controlLayoutMiddle.addWidget(openButtonA); controlLayoutMiddle.addWidget(openButtonB)
        controlLayoutMiddle.addWidget(prevButton); controlLayoutMiddle.addWidget(nextButton)
        controlLayoutMiddle.addWidget(self.fullscreenButton); controlLayoutMiddle.addWidget(helpButton)
        controlLayoutMiddle.addWidget(self.loopCheckBox)
        controlLayoutMiddle.addWidget(volumeLabel); controlLayoutMiddle.addWidget(self.volumeSlider)
        controlLayoutMiddle.addWidget(self.frameRateLabel); controlLayoutMiddle.addWidget(self.frameRateComboBox)
        controlLayoutMiddle.addWidget(self.wipeCheckBox); controlLayoutMiddle.addWidget(self.wipeXButton)
        controlLayoutMiddle.addWidget(self.wipeYButton); controlLayoutMiddle.addWidget(self.playSelectedButton)
        controlLayoutMiddle.addWidget(self.playSelectedPauseButton); controlLayoutMiddle.setContentsMargins(0,0,0,0)
        controlLayoutMiddle.setSpacing(5); controlLayoutMiddle.addWidget(self.gpuLabel)
        controlLayoutRight = QVBoxLayout()
        controlLayoutRight.addWidget(self.positionScroll); controlLayoutRight.addWidget(self.playPauseButton)
        controlLayoutRight.addWidget(stopButton); controlLayoutRight.setContentsMargins(0,0,0,0); controlLayoutRight.setSpacing(5)
        controlLayoutMiddle.addWidget(self.gpuComboBox); controlLayoutMiddle.addWidget(workLabel)
        controlLayoutMiddle.addWidget(self.workComboBox); controlLayoutMiddle.addWidget(self.seasonComboBox)
        controlLayoutMiddle.addWidget(self.jobComboBox)
        fileInfoLayout = QHBoxLayout()
        fileInfoLayout.addWidget(self.filenameLabel); fileInfoLayout.addWidget(self.labelFrameCount)
        fileInfoLayout.addWidget(self.labelTotalFrames); fileInfoLayout.addStretch(1)
        fileInfoLayout.setContentsMargins(0,0,0,0); fileInfoLayout.setSpacing(5)
        splitter = QSplitter(Qt.Vertical); videoWidgetContainer = QWidget()
        videoWidgetLayout = QVBoxLayout(); videoWidgetLayout.addWidget(self.videoWidget)
        videoWidgetContainer.setLayout(videoWidgetLayout); videoWidgetLayout.setContentsMargins(0,0,0,0)
        videoWidgetLayout.setSpacing(0); fileInfoWidget = QWidget(); fileInfoWidget.setLayout(fileInfoLayout)
        splitter.addWidget(videoWidgetContainer); splitter.addWidget(fileInfoWidget); splitter.setSizes([700, 100])
        self.sidebar = QListWidget(); self.sidebar.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.sidebar.setSelectionMode(QListWidget.ExtendedSelection)
        self.sidebar.itemClicked.connect(self.on_sidebar_item_clicked)
        self.sidebar.installEventFilter(self)
        self.sidebar.setContextMenuPolicy(Qt.CustomContextMenu)
        self.sidebar.customContextMenuRequested.connect(self.show_sidebar_context_menu)
        # 사이드바에 드래그앤드롭 기능 추가
        self.sidebar.setAcceptDrops(True)
        selectFolderButton = QPushButton('Select Folder'); selectFolderButton.clicked.connect(self.select_folder)
        selectFolderButton.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        sidebarLayout = QVBoxLayout(); sidebarLayout.addWidget(selectFolderButton); sidebarLayout.addWidget(self.sidebar)
        sidebarLayout.setContentsMargins(0,0,0,0); sidebarLayout.setSpacing(5); sidebarContainer = QWidget()
        sidebarContainer.setLayout(sidebarLayout); sidebarContainer.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)
        middleContainer = QWidget(); middleLayout = QVBoxLayout(); middleLayout.addLayout(controlLayoutMiddle)
        middleLayout.addStretch(1); middleContainer.setLayout(middleLayout)
        mainSplitter = QSplitter(Qt.Horizontal); mainSplitter.addWidget(sidebarContainer)
        mainSplitter.addWidget(middleContainer); mainSplitter.addWidget(splitter)
        mainSplitter.setStretchFactor(0,0); mainSplitter.setStretchFactor(1,0); mainSplitter.setStretchFactor(2,1)
        mainSplitter.setSizes([200, 100, 1000]); layout = QVBoxLayout(); layout.addWidget(mainSplitter)
        layout.addLayout(controlLayoutRight); layout.setContentsMargins(0,0,0,0); layout.setSpacing(5)
        widget = QWidget(); widget.setLayout(layout); self.setCentralWidget(widget)
        self.setAcceptDrops(True)
        self.control_widgets = [
            sidebarContainer, middleContainer, workLabel, self.gpuLabel, stopButton, self.frameRateLabel, nextButton, prevButton, volumeLabel, self.gpuComboBox, self.workComboBox, self.seasonComboBox, self.jobComboBox,
            self.loopCheckBox, self.playPauseButton, self.fullscreenButton, helpButton, self.playSelectedButton,
            self.volumeSlider, self.frameRateComboBox, self.wipeCheckBox, self.sidebar, self.wipeXButton, self.wipeYButton, openButtonA, openButtonB, self.volumeSlider, selectFolderButton
        ]
        self.timeLabel = QLabel("00:00:00.000 / 00:00:00.000"); self.timeLabel.setFont(QFont("Arial", 16))
        self.timeLabel.setStyleSheet("""
QLabel {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #f39c12, stop:1 #e67e22);
    border: 1px solid #e67e22;
    border-radius: 6px;
    color: white;
    font-weight: bold;
    padding: 10px;
}
""")
        fileInfoLayout.addWidget(self.timeLabel)

        # 스크롤바 핸들(thumb) 크기를 고정 (예: 80px)
        self.positionScroll.setStyleSheet("""
QSlider:horizontal {
    height: 20px;
    background: transparent;
}
QSlider::groove:horizontal {
    height: 8px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #34495e, stop:1 #2c3e50);
    border: 1px solid #7f8c8d;
    border-radius: 4px;
    margin: 6px 0;
}
QSlider::handle:horizontal {
    min-width: 80px;
    max-width: 80px;
    height: 20px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #3498db, stop:1 #2980b9);
    border: 2px solid #2980b9;
    border-radius: 10px;
    margin: -6px 0;
}
QSlider::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #5dade2, stop:1 #3498db);
    border: 2px solid #5dade2;
}
QSlider::handle:horizontal:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #2980b9, stop:1 #21618c);
    border: 2px solid #21618c;
}
QSlider::sub-page:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #3498db, stop:1 #2980b9);
    border-radius: 4px;
}
QSlider::add-page:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #34495e, stop:1 #2c3e50);
    border-radius: 4px;
}
""")

    def dragEnterEvent(self, event):
        """메인 윈도우에 파일 드래그앤드롭 이벤트"""
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()

    def dropEvent(self, event):
        """메인 윈도우에 파일 드롭 이벤트"""
        urls = event.mimeData().urls()
        if len(urls) == 1:
            # 단일 파일: Video A에 로드
            file_path = urls[0].toLocalFile()
            if self.is_video_file(file_path):
                self.open_file('A', file_path)
            else:
                QMessageBox.warning(self, "경고", "지원되지 않는 파일 형식입니다.")
        elif len(urls) == 2:
            # 두 개 파일: Video A와 B에 각각 로드
            file_a = urls[0].toLocalFile()
            file_b = urls[1].toLocalFile()
            if self.is_video_file(file_a) and self.is_video_file(file_b):
                self.open_file('A', file_a)
                self.open_file('B', file_b)
            else:
                QMessageBox.warning(self, "경고", "지원되지 않는 파일 형식이 포함되어 있습니다.")
        else:
            # 여러 파일: 사이드바에 추가
            video_files = []
            for url in urls:
                file_path = url.toLocalFile()
                if self.is_video_file(file_path):
                    video_files.append(file_path)
            
            if video_files:
                self.add_files_to_sidebar(video_files)
            else:
                QMessageBox.warning(self, "경고", "지원되는 비디오 파일이 없습니다.")

    def is_video_file(self, file_path):
        """파일이 지원되는 비디오 형식인지 확인"""
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.ts', '.mts', '.m2ts'}
        return os.path.splitext(file_path.lower())[1] in video_extensions

    def add_files_to_sidebar(self, file_paths):
        """사이드바에 파일들을 추가"""
        for file_path in file_paths:
            if os.path.exists(file_path):
                filename = os.path.basename(file_path)
                item = QListWidgetItem(filename)
                item.setData(Qt.UserRole, file_path)
                item.setToolTip(file_path)  # toolTip도 설정하여 기존 코드와 호환
                self.sidebar.addItem(item)
        
        if file_paths:
            QMessageBox.information(self, "완료", f"{len(file_paths)}개의 파일이 사이드바에 추가되었습니다.")

    def eventFilter(self, source, event):
        if event.type() == QEvent.KeyPress:
            print(f"EventFilter - Key pressed: {event.key()} from source: {source}")  # 디버깅 출력 추가
            if source == self.sidebar:
                key = event.key()
                if key == Qt.Key_Space: self.toggle_play_pause(); return True
                elif key in (Qt.Key_Return, Qt.Key_Enter): self.play_selected_files(); return True
                elif key == Qt.Key_Backslash: self.toggle_play_pause_selected(); return True
                elif key in [Qt.Key_Plus, Qt.Key_Equal, Qt.Key_2]: 
                    print("EventFilter - Zoom in triggered"); self.videoWidget.zoom_in(); return True
                elif key in [Qt.Key_Minus, Qt.Key_1]: 
                    print("EventFilter - Zoom out triggered"); self.videoWidget.zoom_out(); return True
                elif key == Qt.Key_3: self.videoWidget.reset_view(); return True
                elif key == Qt.Key_4: self.videoWidget.show_original_size(); return True
            
            if event.key() == Qt.Key_V:
                self.controls_visible = not self.controls_visible
                for widget in self.control_widgets:
                    widget.setVisible(self.controls_visible)
                return True
        
        # 사이드바 드래그앤드롭 처리
        if source == self.sidebar:
            if event.type() == QEvent.DragEnter:
                if event.mimeData().hasUrls():
                    event.accept()
                else:
                    event.ignore()
                return True
            elif event.type() == QEvent.Drop:
                urls = event.mimeData().urls()
                video_files = []
                for url in urls:
                    file_path = url.toLocalFile()
                    if self.is_video_file(file_path):
                        video_files.append(file_path)
                
                if video_files:
                    self.add_files_to_sidebar(video_files)
                return True
        
        return super().eventFilter(source, event)
    
    def get_video_codec(self, file_path):
        """
        ffprobe를 사용하여 비디오 코덱 정보를 추출합니다.
        """
        try:
            if hasattr(sys, '_MEIPASS'):
                # 빌드된 앱의 경우 번들 리소스 경로에서 ffprobe 찾기
                ffprobe_path = os.path.join(sys._MEIPASS, 'ffprobe')
            else:
                # 개발 환경에서는 시스템 PATH의 ffprobe 사용
                ffprobe_path = "ffprobe"

            result = subprocess.run(
                [ffprobe_path, '-v', 'error', '-select_streams', 'v:0',
                '-show_entries', 'stream=codec_name', '-of',
                'default=noprint_wrappers=1:nokey=1', file_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )
            codec_name = result.stdout.strip()
            if not codec_name:
                return "코덱 정보를 찾을 수 없습니다."
            return codec_name
        except subprocess.CalledProcessError as e:
            print(f"ffprobe 실행 중 오류 발생:\n{e.stderr}")
            return f"ffprobe 실행 오류: {e.returncode}"
        except Exception as e:
            print(f"예상치 못한 오류 발생:\n{e}")
            return f"오류 발생: {str(e)}"

    def merge_videos(self):
        selected_items = self.sidebar.selectedItems()
        if not selected_items:
            return

        file_paths = [item.toolTip() for item in selected_items]
        file_paths.sort()

        output_file_path, _ = QFileDialog.getSaveFileName(self, "병합된 비디오 저장", "", "비디오 파일 (*.mov *.mp4)")

        if not output_file_path:
            return

        if not output_file_path.endswith('.mov'):
            output_file_path += '.mov'

        self.progress_dialog = QProgressDialog("비디오 병합 중...", "취소", 0, 100, self)
        self.progress_dialog.setWindowModality(Qt.WindowModal)
        self.progress_dialog.setAutoReset(False)
        self.progress_dialog.setAutoClose(False)
        self.progress_dialog.show()

        self.worker = FFmpegWorker(lambda: self._merge_videos_process(file_paths, output_file_path))
        self.worker.progress.connect(self.update_progress)
        self.worker.finished.connect(self.on_merge_finished)
        self.worker.error.connect(self.on_merge_error)
        self.worker.start()

    def _merge_videos_process(self, file_paths, output_file_path):
        temp_dir = tempfile.mkdtemp()
        temp_files = []

        try:
            total_steps = 2  # 비디오 변환 및 최종 병합의 두 단계
            current_step = 0

            # 1. 비디오 목록 파일 작성
            video_list_path = os.path.join(temp_dir, "video_list.txt")
            with open(video_list_path, "w") as f:
                for file_path in file_paths:
                    f.write(f"file '{file_path}'\n")
            temp_files.append(video_list_path)

            current_step += 1
            self.worker.progress.emit(int(current_step / total_steps * 100))

            # 2. 비디오 병합과 동시에 블랙 프레임 제거
            cmd = [
                self.ffmpeg_path,
                '-f', 'concat',
                '-safe', '0',
                '-i', video_list_path,
                '-vf', "blackdetect=d=0.1:pix_th=0.1,blackframe=0:32",
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '23',
                '-c:a', 'aac',
                '-b:a', '192k',
                '-pix_fmt', 'yuv420p',
                '-movflags', 'faststart',
                '-f', 'mov',
                output_file_path
            ]
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)

            while True:
                output = process.stderr.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    progress = int((current_step + 0.5) / total_steps * 100)
                    self.worker.progress.emit(progress)

            self.worker.progress.emit(100)  # 완료

        except subprocess.CalledProcessError as e:
            error_msg = f"FFmpeg 처리 중 오류 발생:\nCommand: {e.cmd}\nReturn code: {e.returncode}\nOutput: {e.stdout}\nError: {e.stderr}"
            raise Exception(error_msg)
        except Exception as e:
            print(f"예상치 못한 오류 발생: {str(e)}")
            raise
        finally:
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)

    def update_progress(self, value):
        self.progress_dialog.setValue(value)

    def on_merge_finished(self):
        self.progress_dialog.close()
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setText("애니매틱 하나로 합치기가 완료되었습니다.")
        msg_box.setWindowTitle("완료")
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()

    def on_merge_error(self, error_message):
        self.progress_dialog.close()
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setText(f"오류가 발생했습니다: {error_message}")
        msg_box.setWindowTitle("오류")
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()

    #리스트 우클릭 메뉴 설정
    def show_sidebar_context_menu(self, pos):
        index = self.sidebar.indexAt(pos)
        if index.isValid():
            menu = QMenu(self.sidebar)
            open_video_a_action = menu.addAction("비디오 A 열기")
            open_video_a_action.triggered.connect(lambda: self.open_video_a(index))
            open_video_b_action = menu.addAction("비디오 B 열기")
            open_video_b_action.triggered.connect(lambda: self.open_video_b(index))

            # 구분선 추가
            menu.addSeparator()

            merge_videos_action = menu.addAction("애니매틱 하나로 합치기")
            merge_videos_action.triggered.connect(self.merge_videos)

            # 구분선 추가
            menu.addSeparator()

            copy_file_action = menu.addAction("파일 복사")
            copy_file_action.triggered.connect(self.copy_selected_files)  # copy_selected_files 메서드 호출

            # 구분선 추가
            menu.addSeparator()

            video_info_action = menu.addAction("미디어 정보")
            video_info_action.triggered.connect(lambda: self.show_video_info(index))

            menu.exec_(self.sidebar.viewport().mapToGlobal(pos))

    def copy_selected_files(self):
        selected_items = self.sidebar.selectedItems()
        if not selected_items:
            return

        file_paths = [item.toolTip() for item in selected_items]
        target_path = QFileDialog.getExistingDirectory(self, "Select Folder to Copy")
        if target_path:
            self.copy_files_to_target(file_paths, target_path)

    def copy_files_to_target(self, file_paths, target_path):
        current_platform = platform.system()
        if current_platform == 'Darwin':  # macOS
            self.copy_files_mac(file_paths, target_path)
        elif current_platform == 'Windows':  # Windows
            self.copy_files_windows(file_paths, target_path)
        else:  # Linux
            self.copy_files_linux(file_paths, target_path)

    def copy_files_mac(self, file_paths, target_path):
        for file_path in file_paths:
            try:
                shutil.copy2(file_path, target_path)  # shutil.copy2는 파일의 메타데이터를 유지합니다.
                print(f"File copied to {target_path}")
            except Exception as e:
                print(f"Error copying file: {str(e)}")

    def copy_files_windows(self, file_paths, target_path):
        for file_path in file_paths:
            cmd = f"robocopy /copyall /e /dcopy:DAT /B /r:3 /w:60 /is /nfl /ndl /np /MT:32 \"{file_path}\" \"{target_path}\""
            try:
                subprocess.run(cmd, shell=True, check=True)
                print(f"File copied to {target_path}")
            except subprocess.CalledProcessError as e:
                print(f"Error copying file: {str(e)}")

    def copy_files_linux(self, file_paths, target_path):
        for file_path in file_paths:
            try:
                shutil.copy2(file_path, target_path)  # shutil.copy2는 파일의 메타데이터를 유지합니다.
                print(f"File copied to {target_path}")
            except Exception as e:
                print(f"Error copying file: {str(e)}")

    def open_video_a(self, index):
        file_path = self.sidebar.item(index.row()).toolTip()
        self.open_file('A', file_path)

    def open_video_b(self, index):
        file_path = self.sidebar.item(index.row()).toolTip()
        self.open_file('B', file_path)

    def update_season_list(self):
        selected_work = self.workComboBox.currentText()
        self.seasonComboBox.clear()
        self.seasonComboBox.addItem("시즌선택")

        try:
            if selected_work == "BB":
                path = "/usadata3/Bento_Project"
                season_list = [file for file in os.listdir(path) if "BB_Season" in file]
            elif selected_work == "KOTH":
                path = "/usadata2/Disney/KOTH"
                season_list = [file for file in os.listdir(path) if "KOTH_Season" in file]
            elif selected_work == "Big_Mouth":
                path = "/usadata2/Titmouse/Big_Mouth"
                season_list = [file for file in os.listdir(path) if "BM_Season" in file]
            elif selected_work == "TGN":
                path = "/usadata3/Bento_Project2/Great_North"
                season_list = [file for file in os.listdir(path) if "GN_Season" in file]
            elif selected_work == "MatingSeason":
                path = "/usadata2/Titmouse/MatingSeason"
                season_list = [file for file in os.listdir(path) if "MS_Season" in file]
            elif selected_work == "BB_Animatic":
                path = "/Users/<USER>/Desktop/usadata4/BentoBox_Data/BentoBox_data/BB_New/"
                season_list = [file for file in os.listdir(path) if "season" in file]
            elif selected_work == "KOTH_Animatic":
                path = "/Volumes/bgfinal/colordata/KOTH"
                season_list = [file for file in os.listdir(path) if "KOTH_SEASON" in file]
            elif selected_work == "BM_Animatic":
                path = "/Volumes/bgfinal/colordata/BIG_MOUTH"
                season_list = [file for file in os.listdir(path) if "BM_SEASON" in file]
            elif selected_work == "TGN_Animatic":
                path = "/Users/<USER>/Desktop/usadata4/BentoBox_Data/BentoBox_data/"
                season_list = [file for file in os.listdir(path) if "TGN_Season" in file]
            elif selected_work == "MS_Animatic":
                path = "/Volumes/bgfinal/colordata/MatingSeason"
                season_list = [file for file in os.listdir(path) if "MS" in file and os.path.isdir(os.path.join(path, file))]
            else:
                season_list = []

            season_list.sort()  # Sort the list
            for season in season_list:
                self.seasonComboBox.addItem(season)

        except FileNotFoundError:
            # Handle the case where the directory does not exist
            print(f"Directory not found: {path}")
            # Optionally, you can display a message to the user or log the error

    def update_job_list(self):
        selected_season = self.seasonComboBox.currentText()
        if selected_season != "시즌선택":
            self.jobComboBox.clear()
            self.jobComboBox.addItem("job선택")
            selected_work = self.workComboBox.currentText()

            try:
                if selected_work == "BB":
                    path = os.path.join("/usadata3/Bento_Project", selected_season)
                elif selected_work == "KOTH":
                    path = os.path.join("/usadata2/Disney/KOTH", selected_season)
                elif selected_work == "Big_Mouth":
                    path = os.path.join("/usadata2/Titmouse/Big_Mouth", selected_season)
                elif selected_work == "TGN":
                    path = os.path.join("/usadata3/Bento_Project2/Great_North", selected_season)
                elif selected_work == "MatingSeason":
                    path = os.path.join("/usadata2/Titmouse/MatingSeason", selected_season)
                elif selected_work == "BB_Animatic":
                    path = os.path.join("/Users/<USER>/Desktop/usadata4/BentoBox_Data/BentoBox_data/BB_New/", selected_season)
                elif selected_work == "KOTH_Animatic":
                    path = os.path.join("/Volumes/bgfinal/colordata/KOTH/", selected_season)
                elif selected_work == "BM_Animatic":
                    path = os.path.join("/Volumes/bgfinal/colordata/BIG_MOUTH/", selected_season)
                elif selected_work == "TGN_Animatic":
                    path = os.path.join("/Users/<USER>/Desktop/usadata4/BentoBox_Data/BentoBox_data/", selected_season)
                elif selected_work == "MS_Animatic":
                    path = os.path.join("/Volumes/bgfinal/colordata/MatingSeason", selected_season)
                    job_list = [
                        file for file in os.listdir(path)
                        if os.path.isdir(os.path.join(path, file))
                        and not file.startswith('.')
                        and 'ANIMATIC' in file.upper()
                    ]
                    job_list.sort()  # Sort the list
                    for job in job_list:
                        self.jobComboBox.addItem(job)
                    return  # MS_Animatic 처리 후 바로 반환
                else:
                    path = None

                if path and selected_work != "MS_Animatic":  # MS_Animatic이 아닌 경우에만 처리
                    job_list = [
                        file for file in os.listdir(path)
                        if os.path.isdir(os.path.join(path, file))
                        and not file.startswith('.')
                        and file not in ["@eaDir", "EXPORT", "Yeson_BB_Model", "Yeson_GN_Model", "KOTH_library", "TEST_IB_4A", "KOTH_Main_Models", "Yeson_BM_Model", "Yeson_KOTH_Model", "A_Season13_IB_Model"]
                    ]
                    job_list.sort()  # Sort the list
                    for job in job_list:
                        self.jobComboBox.addItem(job)

            except FileNotFoundError:
                # Handle the case where the directory does not exist
                print(f"Directory not found: {path}")
                # Optionally, you can display a message to the user or log the error

    def update_playlist(self):
        selected_job = self.jobComboBox.currentText()
        if selected_job != "job선택":
            self.sidebar.clear()
            selected_work = self.workComboBox.currentText()
            selected_season = self.seasonComboBox.currentText()

            try:
                if selected_work == "BB":
                    base_path = os.path.join("/usadata3/Bento_Project", selected_season, selected_job)
                elif selected_work == "KOTH":
                    base_path = os.path.join("/usadata2/Disney/KOTH", selected_season, selected_job)
                elif selected_work == "Big_Mouth":
                    base_path = os.path.join("/usadata2/Titmouse/Big_Mouth", selected_season, selected_job)
                elif selected_work == "TGN":
                    base_path = os.path.join("/usadata3/Bento_Project2/Great_North", selected_season, selected_job)
                elif selected_work == "MatingSeason":
                    base_path = os.path.join("/usadata2/Titmouse/MatingSeason", selected_season, selected_job)
                elif selected_work == "BB_Animatic":
                    base_path = os.path.join("/Users/<USER>/Desktop/usadata4/BentoBox_Data/BentoBox_data/BB_New/", selected_season, selected_job)
                elif selected_work == "KOTH_Animatic":
                    base_path = os.path.join("/Volumes/bgfinal/colordata/KOTH/", selected_season, selected_job)
                elif selected_work == "BM_Animatic":
                    base_path = os.path.join("/Volumes/bgfinal/colordata/BIG_MOUTH/", selected_season, selected_job)
                elif selected_work == "TGN_Animatic":
                    base_path = os.path.join("/Users/<USER>/Desktop/usadata4/BentoBox_Data/BentoBox_data/", selected_season, selected_job)
                elif selected_work == "MS_Animatic":
                    base_path = os.path.join("/Volumes/bgfinal/colordata/MatingSeason", selected_season, selected_job)
                else:
                    base_path = None

                if base_path and os.path.isdir(base_path):
                    def update_playlist_thread():
                        all_mov_files = []

                        # MS_Animatic 전용 처리
                        if selected_work == "MS_Animatic":
                            mov_files = [f for f in os.listdir(base_path) if f.endswith(('.mov', '.mp4')) and not f.startswith('.')]
                            for mov_file in mov_files:
                                full_path = os.path.join(base_path, mov_file)
                                all_mov_files.append((mov_file, full_path))

                        # 다른 작품들 처리
                        else:
                            for folder in os.listdir(base_path):
                                frames_path = None
                                if selected_work in ["BB", "KOTH", "Big_Mouth", "TGN", "MatingSeason"]:
                                    frames_path = os.path.join(base_path, folder, "frames")
                                elif selected_work == "BB_Animatic":
                                    frames_path = os.path.join(base_path, folder, "Quicktime")
                                elif selected_work == "TGN_Animatic":
                                    frames_path = os.path.join(base_path, folder, "ANIMATIC")

                                if frames_path and os.path.isdir(frames_path):
                                    mov_files = [f for f in os.listdir(frames_path) if f.endswith(('.mov', '.mp4')) and not f.startswith('.')]
                                    for mov_file in mov_files:
                                        full_path = os.path.join(frames_path, mov_file)
                                        all_mov_files.append((mov_file, full_path))

                            # 부모 폴더 검색 (MS_Animatic 제외)
                            parent_folder_path = os.path.join(base_path)
                            if os.path.isdir(parent_folder_path):
                                parent_mov_files = [f for f in os.listdir(parent_folder_path) if f.endswith(('.mov', '.mp4')) and not f.startswith('.')]
                                for parent_mov_file in parent_mov_files:
                                    full_path = os.path.join(parent_folder_path, parent_mov_file)
                                    all_mov_files.append((parent_mov_file, full_path))

                            # 특수 케이스 처리
                            if selected_work == "BM_Animatic":
                                frames_path = os.path.join(base_path, "ANIMATIC")
                                if os.path.isdir(frames_path):
                                    mov_files = [f for f in os.listdir(frames_path) if f.endswith(('.mov', '.mp4'))]
                                    for mov_file in mov_files:
                                        full_path = os.path.join(frames_path, mov_file)
                                        all_mov_files.append((mov_file, full_path))

                            if selected_work == "KOTH_Animatic":
                                frames_path = os.path.join(base_path, "POST-LOCK ANIMATIC")
                                if os.path.isdir(frames_path):
                                    mov_files = [f for f in os.listdir(frames_path) if f.endswith(('.mov', '.mp4'))]
                                    for mov_file in mov_files:
                                        full_path = os.path.join(frames_path, mov_file)
                                        all_mov_files.append((mov_file, full_path))

                        def sort_key(item, case_sensitive=False):
                            filename = item[0]
                            is_hd = '_HD' in filename
                            parts = filename.split('_')
                            if len(parts) >= 4:
                                series_ep = parts[0] + '_' + parts[1]  # BB_802
                                scene = parts[2]  # A001, B001 등
                                scene_letter = scene[0]  # A, B 등
                                scene_number = int(scene[1:]) if scene[1:].isdigit() else 0  # 001, 002 등을 정수로
                                take = parts[3]  # TK1 등
                                return (is_hd, series_ep, scene_letter, scene_number, take)
                            else:
                                return (is_hd, filename if case_sensitive else filename.lower())

                        # 파일을 HD와 non-HD로 분리
                        hd_files = [f for f in all_mov_files if '_HD' in f[0]]
                        non_hd_files = [f for f in all_mov_files if '_HD' not in f[0]]
                        import locale
                        try:
                            locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        except locale.Error:
                            locale.setlocale(locale.LC_ALL, '') # fallback to system default
                        hd_files.sort(key=lambda x: locale.strxfrm(x[0]))
                        non_hd_files.sort(key=lambda x: locale.strxfrm(x[0]))

                        # 정렬된 non-HD 파일을 먼저 추가하고, 그 다음 HD 파일 추가
                        all_mov_files = non_hd_files + hd_files

                        for mov_file, full_path in all_mov_files:
                            item = QListWidgetItem(mov_file)  # Display filename only
                            item.setToolTip(full_path)  # Full path as tooltip
                            self.sidebar.addItem(item)

                        # Close previous video files
                        if self.cap_a:
                            self.cap_a.release()
                            self.cap_a = None
                        if self.cap_b:
                            self.cap_b.release()
                            self.cap_b = None

                        # Initialize video file info
                        self.filename_a = ""
                        self.filename_b = ""
                        self.frame_a = None
                        self.frame_b = None

                        # Automatically open the first video file
                        if all_mov_files:
                            self.playlist = [full_path for _, full_path in all_mov_files]
                            self.open_file('A', self.playlist[0])
                    thread = threading.Thread(target=update_playlist_thread)
                    thread.start()
                else:
                    print(f"Directory not found or invalid: {base_path}")
            except Exception as e:
                print(f"An error occurred: {e}")

    def select_folder(self):
        folder_path = QFileDialog.getExistingDirectory(self, "Select Folder")
        if folder_path:
            self.load_videos_from_folder(folder_path)

    def load_videos_from_folder(self, folder_path):
        self.playlist.clear()
        self.sidebar.clear()
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
        
        files = sorted(os.listdir(folder_path))
        for file in files:
            if any(file.lower().endswith(ext) for ext in video_extensions):
                full_path = os.path.join(folder_path, file)
                self.playlist.append(full_path)
                item = QListWidgetItem(file)
                item.setToolTip(full_path)
                self.sidebar.addItem(item)
        if self.playlist:
            self.current_playlist_index = 0
            self.open_file('A', self.playlist[0])
            self.sidebar.selectAll()

    def open_file(self, video_type, file_path=None):
        if file_path is None:
            file_path, _ = QFileDialog.getOpenFileName(self, f"Open Video {video_type}")

        if file_path:
            # 플레이리스트 재생 중이 아닐 때만 stop_video 호출
            if not self.is_playlist_playing:
                self.stop_video()
            else:
                # 플레이리스트 재생 중에는 타이머만 정지
                self.timer.stop()
                self.mediaPlayer.stop()
                
            print(f"Attempting to open video file: {file_path}")
            
            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened():
                print(f"Failed to open video file: {file_path}")
                return

            if video_type == 'A':
                if self.cap_a: self.cap_a.release()
                self.filename_a = file_path
                self.cap_a = cap
                
                # 통합 오디오 관리자로 오디오 로드
                audio_loaded = self.audio_manager.load_file(file_path, self.mediaPlayer)
                if audio_loaded:
                    print(f"Audio loaded successfully with {self.audio_manager.current_backend} backend")
                    # QMediaPlayer도 함께 설정 (호환성을 위해)
                    self.mediaPlayer.setMedia(QMediaContent(QUrl.fromLocalFile(file_path)))
                else:
                    print("Audio loading failed, using video-only mode")
                    # 오디오 로드 실패 시 QMediaPlayer만 설정
                    self.mediaPlayer.setMedia(QMediaContent(QUrl.fromLocalFile(file_path)))
                
                total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
                self.positionScroll.setMaximum(max(1, total_frames - 1))
                # 스크롤바 핸들 크기를 고정 (페이지 스텝 설정)
                # self.positionScroll.setPageStep(100)  # 고정된 페이지 스텝 값

            elif video_type == 'B':
                if self.cap_b: self.cap_b.release()
                self.filename_b = file_path
                self.cap_b = cap

            if self.filename_a and self.filename_b:
                self.filenameLabel.setText(f"A: {os.path.basename(self.filename_a)}, B: {os.path.basename(self.filename_b)}")
            elif self.filename_a:
                self.filenameLabel.setText(f"A: {os.path.basename(self.filename_a)}")

            self.videoWidget.initial_fit = True
            self.detect_fps()
            QTimer.singleShot(50, self.display_first_frame)
    
    # 누락되었던 메서드 추가
    def change_gpu_backend(self, index):
        backend = self.gpuComboBox.currentText()
        if backend == "CPU":
            self.use_gpu = False
            self.gpu_backend = None
        elif backend == "NVIDIA (CUDA)":
            self.use_gpu = True
            self.gpu_backend = 'cuda'
            if cv2.cuda.getCudaEnabledDeviceCount() > 0:
                cv2.cuda.setDevice(0)
        elif backend == "AMD (OpenCL)":
            self.use_gpu = True
            self.gpu_backend = 'opencl'
            if cv2.ocl.haveOpenCL():
                cv2.ocl.setUseOpenCL(True)

        if self.cap_a:
            current_pos = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES))
            self.seek_to_frame(current_pos)

    def process_frame(self, frame):
        if self.use_gpu:
            if self.gpu_backend == 'cuda':
                gpu_frame = cv2.cuda_GpuMat()
                gpu_frame.upload(frame)
                gpu_frame = cv2.cuda.cvtColor(gpu_frame, cv2.COLOR_BGR2RGB)
                return gpu_frame.download()
            elif self.gpu_backend == 'opencl':
                gpu_frame = cv2.UMat(frame)
                return cv2.cvtColor(gpu_frame, cv2.COLOR_BGR2RGB).get()
        return cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

    def detect_fps(self):
        if self.cap_a:
            fps = self.cap_a.get(cv2.CAP_PROP_FPS)
            if fps > 0:
                self.frameRateComboBox.setCurrentText(str(round(fps)))

    def display_first_frame(self):
        if self.cap_a:
            self.seek_to_frame(1)
            if not self.is_playlist_playing:
                self.videoWidget.reset_view()

    def update_frame(self):
        print(f"update_frame 호출됨 - frame_a 크기: {self.frame_a.shape if self.frame_a is not None else 'None'}")
        
        # 오른쪽 끝 드래그 감지 및 특별 처리
        if self.cap_a and self.positionScroll.isSliderDown():
            total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
            current_scroll_value = self.positionScroll.value()
            
            # 오른쪽 끝에 정확히 있을 때만 Total frame과 일치하도록 표시
            if current_scroll_value == self.positionScroll.maximum():
                print(f"오른쪽 끝 드래그 감지: Total={total_frames}, Current={total_frames}로 강제 설정")
                self.labelFrameCount.setText(f'Current: {total_frames}')
                self.labelTotalFrames.setText(f'Total: {total_frames}')
                
                # 시간 정보도 업데이트
                fps = self.get_frame_rate()
                if fps > 0:
                    current_time = (total_frames - 1) / fps if total_frames > 1 else 0
                    total_time = (total_frames - 1) / fps if total_frames > 1 else 0
                    self.timeLabel.setText(f"{self.format_time(current_time)} / {self.format_time(total_time)}")
        
        frame_a_to_show = self.frame_a
        if frame_a_to_show is not None and self.show_overlay:
            frame_a_to_show = self.frame_a.copy()
            self.overlay_text(frame_a_to_show)
        
        frame_b_to_show = self.frame_b if (self.videoWidget.wipe_enabled_x or self.videoWidget.wipe_enabled_y) else None
        
        self.videoWidget.updateFrame(frame_a_to_show, frame_b_to_show)
        self.update_position_info()

    def overlay_text(self, frame):
        height, width = frame.shape[:2]
        x, y = 10, height - 20
        text = f"File: {os.path.basename(self.filename_a)}, Frame: {int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES))}/{int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))}"
        font, scale, thickness = cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2
        (tw, th), _ = cv2.getTextSize(text, font, scale, thickness)
        cv2.rectangle(frame, (x - 5, y - th - 5), (x + tw + 5, y + 5), (255,255,255), -1)
        cv2.putText(frame, text, (x, y), font, scale, (0,0,0), thickness)

    def change_position(self, value):
        """기존 메서드 - 호환성을 위해 유지"""
        self.on_slider_value_changed(value)

    def on_slider_moved(self, value):
        """슬라이더 드래그 중 실시간 업데이트"""
        if not self.cap_a:
            return

        # 드래그 중 업데이트 빈도 제어
        current_time = QDateTime.currentMSecsSinceEpoch()
        if current_time - self.last_drag_update < self.drag_update_interval:
            return
        
        self.last_drag_update = current_time
        self.is_dragging = True

        total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames <= 1:
            return

        # 스크롤바 값이 최대값에 가까우면 마지막 프레임으로 설정
        if value >= self.positionScroll.maximum() - 1:
            target_frame_idx = total_frames - 1
        else:
            target_frame_idx = round((value / self.positionScroll.maximum()) * (total_frames - 1))
            target_frame_idx = max(0, min(target_frame_idx, total_frames - 1))

        # 드래그 중일 때는 빠른 프리뷰를 위해 간소화된 처리
        self._quick_preview_seek(target_frame_idx, total_frames)

    def on_slider_value_changed(self, value):
        """슬라이더 값 변경 시 (드래그 완료 후)"""
        if not self.cap_a:
            return

        # 드래그 상태 초기화
        self.is_dragging = False

        total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames <= 1:
            return

        # 스크롤바 값이 최대값에 가까우면 마지막 프레임으로 설정
        if value >= self.positionScroll.maximum() - 1:
            target_frame_idx = total_frames - 1
        else:
            target_frame_idx = round((value / self.positionScroll.maximum()) * (total_frames - 1))
            target_frame_idx = max(0, min(target_frame_idx, total_frames - 1))

        # 드래그가 끝났을 때는 정확한 프레임으로 이동
        self._precise_seek(target_frame_idx, total_frames)

    def _quick_preview_seek(self, target_frame_idx, total_frames):
        """드래그 중 빠른 프리뷰를 위한 간소화된 프레임 이동"""
        try:
            # 현재 위치와 목표 위치의 차이가 크면 스킵
            current_pos = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES)) - 1
            if current_pos < 0:
                current_pos = 0
            
            # 너무 가까운 프레임은 스킵 (성능 최적화) - 더 작은 값으로 조정
            if abs(target_frame_idx - current_pos) < 2:
                return
            
            # 빠른 직접 이동 시도
            self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, target_frame_idx)
            ret_a, frame_a = self.cap_a.read()
            
            if ret_a and frame_a is not None:
                # 드래그 중일 때는 프레임 처리를 간소화
                if self.is_dragging:
                    # 드래그 중일 때는 원본 프레임을 그대로 사용 (처리 시간 단축)
                    self.frame_a = frame_a
                else:
                    self.frame_a = self.process_frame(frame_a)
                
                # B 동기화 (간소화)
                if self.cap_b and self.videoWidget.wipeEnabled:
                    self.cap_b.set(cv2.CAP_PROP_POS_FRAMES, target_frame_idx)
                    ret_b, frame_b = self.cap_b.read()
                    if ret_b and frame_b is not None:
                        if self.is_dragging:
                            self.frame_b = frame_b
                        else:
                            self.frame_b = self.process_frame(frame_b)
                    else:
                        self.frame_b = None
                
                # 빠른 UI 업데이트 (시간 정보만 업데이트)
                self._quick_ui_update(target_frame_idx, total_frames)
                
        except Exception as e:
            # 오류 발생 시 무시하고 계속 진행
            pass

    def _precise_seek(self, target_frame_idx, total_frames):
        """정확한 프레임 이동 (드래그 완료 후)"""
        print(f"정확한 이동: 목표프레임={target_frame_idx}, 전체프레임={total_frames}")

        # 마지막 프레임인 경우 특별 처리
        if target_frame_idx == total_frames - 1:
            self._seek_to_last_frame(total_frames)
        # 마지막 프레임 근처에서는 더 보수적으로 접근
        elif target_frame_idx >= total_frames - 3:
            self._seek_conservative(target_frame_idx, total_frames)
        else:
            # 일반적인 경우 직접 프레임 이동 시도
            if self._seek_direct(target_frame_idx, total_frames):
                pass
            else:
                self._seek_sequential(target_frame_idx, total_frames)

    def _quick_ui_update(self, frame_idx, total_frames):
        """드래그 중 빠른 UI 업데이트"""
        # 비디오 프레임만 업데이트
        self.videoWidget.updateFrame(self.frame_a, self.frame_b)
        
        # 드래그 중일 때는 최소한의 UI 업데이트만
        if self.is_dragging:
            # 현재 프레임 번호만 업데이트 (간소화)
            self.labelFrameCount.setText(f'Current: {frame_idx + 1}')
        else:
            # 현재 프레임 번호 업데이트
            self.labelFrameCount.setText(f'Current: {frame_idx + 1}')
            
            # 시간 정보 업데이트 (간소화)
            fps = self.get_frame_rate()
            if fps > 0:
                current_time = frame_idx / fps
                total_time = (total_frames - 1) / fps if total_frames > 1 else 0
                self.timeLabel.setText(f"{self.format_time(current_time)} / {self.format_time(total_time)}")

    def _seek_to_last_frame(self, total_frames):
        """마지막 프레임으로 정확하게 이동하는 전용 메서드"""
        try:
            print(f"마지막 프레임 이동: {total_frames - 1}")
            
            # 방법 1: 실제 마지막 프레임 찾기 (보고된 프레임 수보다 작을 수 있음)
            actual_last_frame = self._find_actual_last_frame(total_frames)
            print(f"실제 마지막 프레임: {actual_last_frame}")
            
            if actual_last_frame >= 0:
                # 실제 마지막 프레임으로 이동
                self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, actual_last_frame)
                ret_a, frame_a = self.cap_a.read()
                
                if ret_a and frame_a is not None:
                    self.frame_a = self.process_frame(frame_a)
                    
                    # B 동기화
                    if self.cap_b and self.videoWidget.wipeEnabled:
                        self.cap_b.set(cv2.CAP_PROP_POS_FRAMES, actual_last_frame)
                        ret_b, frame_b = self.cap_b.read()
                        if ret_b and frame_b is not None:
                            self.frame_b = self.process_frame(frame_b)
                    
                    # UI 업데이트 - OpenCV가 보고하는 총 프레임 수를 사용하여 일치시킴
                    self._update_ui_for_last_frame(total_frames)
                    return True
                else:
                    print("마지막 프레임 읽기 실패")
                    return False
            else:
                print("실제 마지막 프레임을 찾을 수 없음")
                return False
                
        except Exception as e:
            print(f"마지막 프레임 이동 실패: {e}")
            return False

    def _find_actual_last_frame(self, reported_total):
        """실제 마지막 프레임을 찾습니다"""
        try:
            # 빠른 검색: 끝에서부터 역순으로 확인
            test_frames = [reported_total - 1, reported_total - 2, reported_total - 3, 
                          reported_total - 5, reported_total - 10, reported_total - 20]
            
            for test_frame in test_frames:
                if test_frame < 0:
                    continue
                    
                self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, test_frame)
                ret, frame = self.cap_a.read()
                if ret and frame is not None:
                    print(f"실제 마지막 프레임 발견: {test_frame}")
                    return test_frame
            
            # 빠른 검색 실패 시 순차적 검색 (더 느리지만 확실함)
            print("빠른 검색 실패, 순차적 검색 시작")
            self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, 0)
            
            last_valid_frame = -1
            for i in range(min(reported_total, 10000)):  # 최대 10000프레임까지만 검색
                ret, frame = self.cap_a.read()
                if ret and frame is not None:
                    last_valid_frame = i
                else:
                    break
            
            print(f"순차적 검색 결과: {last_valid_frame}")
            return last_valid_frame
            
        except Exception as e:
            print(f"실제 마지막 프레임 검색 실패: {e}")
            return -1

    def _update_ui_for_last_frame(self, actual_total_frames):
        """마지막 프레임에 대한 UI 업데이트"""
        self.videoWidget.updateFrame(self.frame_a, self.frame_b)
        
        # 오른쪽 끝 드래그 시 Current frame을 Total frame과 정확히 일치시킴
        # OpenCV가 보고하는 총 프레임 수를 사용
        reported_total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
        self.labelFrameCount.setText(f'Current: {reported_total_frames}')
        
        # 스크롤바를 최대값으로 설정
        self.positionScroll.blockSignals(True)
        self.positionScroll.setValue(self.positionScroll.maximum())
        self.positionScroll.blockSignals(False)
        
        # 전체 프레임 정보 업데이트 - OpenCV가 보고하는 총 프레임 수 사용
        total_frames_text = f'Total: {reported_total_frames}'
        if self.videoWidget.wipeEnabled and self.cap_b:
            total_frames_b = int(self.cap_b.get(cv2.CAP_PROP_FRAME_COUNT))
            total_frames_text = f'Total A: {reported_total_frames}, B: {total_frames_b}'
        self.labelTotalFrames.setText(total_frames_text)
        
        # 시간 정보 업데이트
        fps = self.get_frame_rate()
        if fps > 0:
            current_time = (reported_total_frames - 1) / fps if reported_total_frames > 1 else 0
            total_time = (reported_total_frames - 1) / fps if reported_total_frames > 1 else 0
            self.timeLabel.setText(f"{self.format_time(current_time)} / {self.format_time(total_time)}")
        
        self.update_frame()

    def _seek_conservative(self, target_frame_idx, total_frames):
        """보수적인 프레임 이동 방법 - 마지막 프레임 근처에서 사용"""
        try:
            current_pos = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES))
            if target_frame_idx > current_pos:
                for i in range(current_pos, target_frame_idx + 1):
                    ret_a, frame_a = self.cap_a.read()
                    if ret_a and frame_a is not None:
                        if i == target_frame_idx:
                            self.frame_a = self.process_frame(frame_a)
                            self._update_ui_after_seek(target_frame_idx, total_frames)
                            return True
                    else:
                        break
            elif target_frame_idx < current_pos:
                start_frame = max(0, target_frame_idx - 10)
                self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
                for i in range(start_frame, target_frame_idx + 1):
                    ret_a, frame_a = self.cap_a.read()
                    if ret_a and frame_a is not None:
                        if i == target_frame_idx:
                            self.frame_a = self.process_frame(frame_a)
                            self._update_ui_after_seek(target_frame_idx, total_frames)
                            return True
                    else:
                        break
            return False
        except Exception as e:
            return False

    def _seek_direct(self, target_frame_idx, total_frames):
        """직접 프레임 이동을 시도합니다. 빠른 반응을 위해 사용됩니다."""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 마지막 프레임 근처에서는 보수적으로 접근
                if target_frame_idx >= total_frames - 5:
                    # 마지막 5프레임 근처에서는 이전 프레임부터 시작
                    start_frame = max(0, target_frame_idx - 5)
                    self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
                    
                    # 목표 프레임까지 순차적으로 읽기
                    for i in range(start_frame, target_frame_idx + 1):
                        ret_a, frame_a = self.cap_a.read()
                        if ret_a and frame_a is not None:
                            if i == target_frame_idx:
                                self.frame_a = self.process_frame(frame_a)
                                
                                # B 동기화
                                if self.cap_b and self.videoWidget.wipeEnabled:
                                    self.cap_b.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
                                    for j in range(start_frame, target_frame_idx + 1):
                                        ret_b, frame_b = self.cap_b.read()
                                        if ret_b and frame_b is not None:
                                            if j == target_frame_idx:
                                                self.frame_b = self.process_frame(frame_b)
                                                break
                                        else:
                                            break
                                
                                # UI 업데이트
                                self._update_ui_after_seek(target_frame_idx, total_frames)
                                return True
                        else:
                            break
                    return False
                else:
                    # 일반적인 경우 직접 프레임 이동
                    self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, target_frame_idx)
                    
                    # 프레임 읽기
                    ret_a, frame_a = self.cap_a.read()
                    if ret_a and frame_a is not None:
                        self.frame_a = self.process_frame(frame_a)
                        
                        # B 동기화
                        if self.cap_b and self.videoWidget.wipeEnabled:
                            self.cap_b.set(cv2.CAP_PROP_POS_FRAMES, target_frame_idx)
                            ret_b, frame_b = self.cap_b.read()
                            if ret_b and frame_b is not None:
                                self.frame_b = self.process_frame(frame_b)
                        
                        # UI 업데이트
                        self._update_ui_after_seek(target_frame_idx, total_frames)
                        return True
                    else:
                        # 프레임 읽기 실패 시 다음 프레임 시도
                        if attempt < max_retries - 1:
                            target_frame_idx += 1
                            continue
                        else:
                            print("직접 프레임 읽기 실패")
                            return False
                        
            except Exception as e:
                if attempt == max_retries - 1:
                    print(f"직접 프레임 이동 실패: {e}")
                    return False
                continue
        
        return False

    def _seek_sequential(self, target_frame_idx, total_frames):
        """순차적 프레임 읽기를 사용하여 목표 프레임으로 이동합니다."""
        print("순차적 프레임 읽기 사용")
        self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, 0)
        
        found = False
        for i in range(target_frame_idx + 1):
            ret_a, frame_a = self.cap_a.read()
            if ret_a and frame_a is not None:
                if i == target_frame_idx:
                    print(f"순차적 읽기로 프레임 {i} 찾음")
                    self.frame_a = self.process_frame(frame_a)
                    found = True
                    break
            else:
                print(f"순차적 읽기 실패 at {i}")
                break

        if found:
            # B 동기화
            if self.cap_b and self.videoWidget.wipeEnabled:
                self.cap_b.set(cv2.CAP_PROP_POS_FRAMES, 0)
                for i in range(target_frame_idx + 1):
                    ret_b, frame_b = self.cap_b.read()
                    if ret_b and frame_b is not None:
                        if i == target_frame_idx:
                            self.frame_b = self.process_frame(frame_b)
                            break
                    else:
                        break
            
            self._update_ui_after_seek(target_frame_idx, total_frames)
        else:
            print(f"프레임을 찾지 못했습니다: {target_frame_idx}")

    def _update_ui_after_seek(self, frame_idx, total_frames):
        """프레임 이동 후 UI를 업데이트합니다."""
        self.videoWidget.updateFrame(self.frame_a, self.frame_b)
        
        # 실제 프레임 위치 확인
        actual_frame_pos = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES))
        
        # 프레임 정보 업데이트 - 실제 프레임 위치 사용
        current_frame_num = actual_frame_pos
        self.labelFrameCount.setText(f'Current: {current_frame_num}')
        
        # 드래그 중이 아닐 때만 전체 프레임 정보 업데이트
        if not self.positionScroll.isSliderDown():
            total_frames_text = f'Total: {total_frames}'
            if self.videoWidget.wipeEnabled and self.cap_b:
                total_frames_b = int(self.cap_b.get(cv2.CAP_PROP_FRAME_COUNT))
                total_frames_text = f'Total A: {total_frames}, B: {total_frames_b}'
            self.labelTotalFrames.setText(total_frames_text)
        
        # 시간 정보 업데이트
        fps = self.get_frame_rate()
        if fps > 0:
            current_time = (actual_frame_pos - 1) / fps if actual_frame_pos > 0 else 0
            total_time = (total_frames - 1) / fps if total_frames > 1 else 0
            self.timeLabel.setText(f"{self.format_time(current_time)} / {self.format_time(total_time)}")
        
        # 드래그 중이 아닐 때만 추가 업데이트
        if not self.positionScroll.isSliderDown():
            self.update_frame()

    def sync_audio_on_release(self):
        if self.cap_a:
            # 드래그가 끝났을 때 정확한 위치로 조정
            total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
            current_scroll_value = self.positionScroll.value()
            
            # 오른쪽 끝에 가까우면 마지막 프레임으로 설정
            if current_scroll_value >= self.positionScroll.maximum() - 1:
                print("드래그 완료: 오른쪽 끝 - 마지막 프레임으로 설정")
                self._seek_to_last_frame(total_frames)
            else:
                target_frame_idx = round((current_scroll_value / self.positionScroll.maximum()) * (total_frames - 1))
                target_frame_idx = max(0, min(target_frame_idx, total_frames - 1))
                
                # 현재 실제 프레임 위치 확인
                actual_frame_idx = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES)) - 1
                if actual_frame_idx < 0:
                    actual_frame_idx = 0
                
                # 오차가 크면 순차적 읽기로 정확한 위치로 조정
                if abs(actual_frame_idx - target_frame_idx) > 5:
                    print(f"드래그 완료: 정확한 위치로 조정 {actual_frame_idx} -> {target_frame_idx}")
                    self._seek_sequential(target_frame_idx, total_frames)
            
            # 오디오 동기화 - 통합 오디오 관리자 사용
            fps = self.get_frame_rate()
            if fps > 0:
                current_frame_idx = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES)) - 1
                if current_frame_idx < 0:
                    current_frame_idx = 0
                target_time_ms = int(current_frame_idx / fps * 1000)
                
                # 통합 오디오 관리자로 위치 설정
                if self.audio_manager.is_audio_available():
                    self.audio_manager.set_position(target_time_ms)
                else:
                    # 폴백: QMediaPlayer 사용
                    self.mediaPlayer.setPosition(target_time_ms)
    
    def update_position_info(self):
        if self.cap_a and self.cap_a.isOpened():
            # 스크롤바 드래그 중일 때는 스크롤바 업데이트를 건너뜀
            if self.positionScroll.isSliderDown():
                return
                
            total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
            # 다음 읽을 프레임 인덱스(0-based)를 현재 프레임 번호(1-based)로 변환
            current_frame_num = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES))
            
            # 오른쪽 끝에 정확히 있을 때만 Total frame과 일치하도록 표시
            current_scroll_value = self.positionScroll.value()
            if current_scroll_value == self.positionScroll.maximum():
                print(f"update_position_info: 정확한 오른쪽 끝 감지, Current={total_frames}로 강제 설정")
                self.labelFrameCount.setText(f'Current: {total_frames}')
            else:
                self.labelFrameCount.setText(f'Current: {current_frame_num}')

            if total_frames > 1:
                scroll_val = round(((current_frame_num - 1) / (total_frames - 1)) * self.positionScroll.maximum())
                if scroll_val != self.positionScroll.value():
                    self.positionScroll.blockSignals(True)
                    self.positionScroll.setValue(scroll_val)
                    self.positionScroll.blockSignals(False)
            
            total_frames_text = f'Total: {total_frames}'
            if self.videoWidget.wipeEnabled and self.cap_b:
                total_frames_b = int(self.cap_b.get(cv2.CAP_PROP_FRAME_COUNT))
                total_frames_text = f'Total A: {total_frames}, B: {total_frames_b}'
            self.labelTotalFrames.setText(total_frames_text)

            fps = self.get_frame_rate()
            if fps > 0:
                # 오른쪽 끝에 정확히 있을 때만 Total frame 기준으로 시간 계산
                if current_scroll_value == self.positionScroll.maximum():
                    current_time = (total_frames - 1) / fps if total_frames > 1 else 0
                else:
                    current_time = (current_frame_num - 1) / fps if current_frame_num > 0 else 0
                total_time = (total_frames - 1) / fps if total_frames > 1 else 0
                self.timeLabel.setText(f"{self.format_time(current_time)} / {self.format_time(total_time)}")

    def format_time(self, seconds):
        if seconds < 0: seconds = 0
        m, s = divmod(seconds, 60)
        h, m = divmod(m, 60)
        return f"{int(h):02d}:{int(m):02d}:{s:06.3f}"

    def get_frame_rate(self):
        try:
            return float(self.frameRateComboBox.currentText().strip())
        except (ValueError, TypeError):
            if self.cap_a:
                fps = self.cap_a.get(cv2.CAP_PROP_FPS)
                return fps if fps > 0 else 24.0
            return 24.0
    
    def on_sidebar_item_clicked(self, item):
        if self.is_playlist_playing: return
        self.open_file('A', item.toolTip())

    def play_selected_files(self):
        selected_items = self.sidebar.selectedItems()
        if selected_items:
            selected_items.sort(key=lambda x: self.sidebar.row(x))
            self.playlist = [item.toolTip() for item in selected_items]
            self.current_playlist_index = 0
            self.is_playlist_playing = True
            print(f"Play Selected: {len(selected_items)} files selected, starting playback")
            self.play_current_playlist_item()
        else:
            print("No files selected")
            # 사용자에게 선택된 파일이 없다는 것을 알림
            QMessageBox.information(self, "Play Selected", "Please select one or more files from the sidebar first.")

    def play_current_playlist_item(self):
        if 0 <= self.current_playlist_index < len(self.playlist):
            current_file = self.playlist[self.current_playlist_index]
            print(f"Playing playlist item {self.current_playlist_index + 1}/{len(self.playlist)}: {os.path.basename(current_file)}")
            print(f"is_playlist_playing before: {self.is_playlist_playing}")
            
            # 플레이리스트 재생 상태 확실히 설정
            self.is_playlist_playing = True
            
            # 사이드바에서 현재 파일 강조
            for i in range(self.sidebar.count()):
                item = self.sidebar.item(i)
                is_current = item.toolTip() == current_file
                font = item.font()
                font.setBold(is_current)
                item.setFont(font)
                item.setForeground(QColor("red") if is_current else QColor("black"))
            
            # 파일 열기 및 재생
            try:
                self.open_file('A', current_file)
                # 첫 번째 프레임으로 이동
                self.seek_to_frame(1)
                self.play_video()
                self.playSelectedPauseButton.setText('Selected Pause')
                self.playSelectedPauseButton.setStyleSheet("background-color: yellow;")
                print(f"Successfully started playing: {os.path.basename(current_file)}")
                print(f"is_playlist_playing after: {self.is_playlist_playing}")
            except Exception as e:
                print(f"Error playing file {current_file}: {e}")
                QMessageBox.warning(self, "Play Error", f"Failed to play file: {os.path.basename(current_file)}\nError: {str(e)}")
                # 오류 발생 시 다음 파일로 이동
                if self.current_playlist_index < len(self.playlist) - 1:
                    self.current_playlist_index += 1
                    self.play_current_playlist_item()
        else:
            print(f"Invalid playlist index: {self.current_playlist_index}, playlist length: {len(self.playlist)}")
            self.is_playlist_playing = False
            self.reset_sidebar_colors()

    def reset_sidebar_colors(self):
        for i in range(self.sidebar.count()):
            item = self.sidebar.item(i)
            font = item.font(); font.setBold(False)
            item.setFont(font); item.setForeground(QColor("black"))

    def play_next_in_playlist(self):
        if self.current_playlist_index < len(self.playlist) - 1:
            self.current_playlist_index += 1
            print(f"Moving to next item in playlist: {self.current_playlist_index + 1}/{len(self.playlist)}")
            self.play_current_playlist_item()
        else:
            print("Reached end of playlist, stopping playback")
            self.is_playlist_playing = False
            self.stop_video()
            self.reset_sidebar_colors()
            QMessageBox.information(self, "Playlist Complete", "All selected files have been played.")

    def next_frame_playback(self):
        if not self.cap_a or not self.timer.isActive(): 
            return
            
        ret_a, frame_a = self.cap_a.read()
        if ret_a:
            self.frame_a = self.process_frame(frame_a)
            if self.cap_b and self.videoWidget.wipeEnabled:
                ret_b, frame_b = self.cap_b.read()
                self.frame_b = self.process_frame(frame_b) if ret_b else None
            self.update_frame()
        else:
            print("Video end detected in next_frame_playback")
            self.handle_video_end()

    def next_frame(self):
        if not self.cap_a: 
            return
            
        total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames <= 1:
            return
            
        # 실제 현재 프레임 위치 가져오기 (CAP_PROP_POS_FRAMES는 다음 읽을 프레임)
        current_frame_idx = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES)) - 1
        if current_frame_idx < 0:
            current_frame_idx = 0
        
        print(f"next_frame: 실제 현재 프레임 = {current_frame_idx}")
        
        # 다음 프레임으로 이동 - 범위 제한 강화
        next_frame_idx = min(current_frame_idx + 1, total_frames - 1)
        next_frame_idx = max(0, next_frame_idx)  # 음수 방지
        
        # 추가 안전장치: 음수나 범위를 벗어나는 경우 처리
        if next_frame_idx < 0 or next_frame_idx >= total_frames:
            print(f"Next frame: 잘못된 프레임 인덱스 {next_frame_idx}, 현재 위치 유지")
            return
        
        # 현재 위치와 동일하면 이동하지 않음
        if next_frame_idx == current_frame_idx:
            print(f"Next frame: 이미 마지막 프레임입니다 ({current_frame_idx})")
            return
        
        print(f"Next frame: {current_frame_idx} -> {next_frame_idx}")
        
        # 효율적인 프레임 이동 - 직접 프레임 설정 시도
        if self._seek_direct_fast(next_frame_idx, total_frames):
            print(f"Next frame {next_frame_idx} 직접 이동 성공")
        else:
            # 실패 시 순차적 읽기로 폴백
            print(f"Next frame {next_frame_idx} 직접 이동 실패, 순차적 읽기로 폴백")
            self._seek_sequential_fast(next_frame_idx, total_frames)

    def prev_frame(self):
        if not self.cap_a: 
            return
            
        total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames <= 1:
            return
            
        # 실제 현재 프레임 위치 가져오기 (CAP_PROP_POS_FRAMES는 다음 읽을 프레임)
        current_frame_idx = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES)) - 1
        if current_frame_idx < 0:
            current_frame_idx = 0
        
        print(f"prev_frame: 실제 현재 프레임 = {current_frame_idx}")
        
        # 이전 프레임으로 이동 - 범위 제한 강화
        prev_frame_idx = max(current_frame_idx - 1, 0)  # 음수 방지
        prev_frame_idx = min(prev_frame_idx, total_frames - 1)  # 최대값 제한
        
        # 추가 안전장치: 음수나 범위를 벗어나는 경우 처리
        if prev_frame_idx < 0 or prev_frame_idx >= total_frames:
            print(f"Prev frame: 잘못된 프레임 인덱스 {prev_frame_idx}, 현재 위치 유지")
            return
        
        # 현재 위치와 동일하면 이동하지 않음
        if prev_frame_idx == current_frame_idx:
            print(f"Prev frame: 이미 첫 번째 프레임입니다 ({current_frame_idx})")
            return
        
        print(f"Prev frame: {current_frame_idx} -> {prev_frame_idx}")
        
        # 최적화된 이전 프레임 이동
        if self._seek_prev_frame_optimized(prev_frame_idx, total_frames):
            print(f"Prev frame {prev_frame_idx} 최적화 이동 성공")
        else:
            # 실패 시 기존 방법으로 폴백
            print(f"Prev frame {prev_frame_idx} 최적화 실패, 기존 방법으로 폴백")
            if self._seek_direct_fast(prev_frame_idx, total_frames):
                print(f"Prev frame {prev_frame_idx} 직접 이동 성공")
            else:
                self._seek_sequential_fast(prev_frame_idx, total_frames)

    def _seek_prev_frame_optimized(self, target_frame_idx, total_frames):
        """이전 프레임 이동 최적화 - 직접 프레임 설정 우선"""
        try:
            # 이전 프레임은 항상 직접 프레임 설정이 가장 빠름
            self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, target_frame_idx)
            ret_a, frame_a = self.cap_a.read()
            if ret_a and frame_a is not None:
                self.frame_a = self.process_frame(frame_a)
                self._update_ui_fast(target_frame_idx, total_frames)
                return True
            return False
            
        except Exception as e:
            print(f"이전 프레임 최적화 이동 실패: {e}")
            return False

    def _seek_direct_fast(self, target_frame_idx, total_frames):
        """빠른 직접 프레임 이동 - 단축키용"""
        try:
            # 현재 위치 정확히 계산 (CAP_PROP_POS_FRAMES는 다음 읽을 프레임)
            current_pos = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES)) - 1
            if current_pos < 0:
                current_pos = 0
            
            print(f"_seek_direct_fast: 현재위치={current_pos}, 목표위치={target_frame_idx}")
                
            if abs(target_frame_idx - current_pos) <= 5:
                # 가까운 경우 현재 위치에서 순차적 읽기
                if target_frame_idx > current_pos:
                    # 앞으로 이동 - 현재 위치에서 시작하여 목표까지 읽기
                    frames_to_read = target_frame_idx - current_pos
                    for _ in range(frames_to_read + 1):
                        ret_a, frame_a = self.cap_a.read()
                        if ret_a and frame_a is not None:
                            current_pos += 1
                            if current_pos == target_frame_idx:
                                self.frame_a = self.process_frame(frame_a)
                                self._update_ui_fast(target_frame_idx, total_frames)
                                return True
                        else:
                            break
                else:
                    # 뒤로 이동하는 경우 - 최적화된 방법
                    frames_to_go_back = current_pos - target_frame_idx
                    if frames_to_go_back <= 10:
                        # 가까운 경우 현재 위치에서 뒤로 읽기 (캐시 활용)
                        # 현재 위치에서 목표 위치까지 다시 읽기
                        self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, target_frame_idx)
                        ret_a, frame_a = self.cap_a.read()
                        if ret_a and frame_a is not None:
                            self.frame_a = self.process_frame(frame_a)
                            self._update_ui_fast(target_frame_idx, total_frames)
                            return True
                    else:
                        # 먼 경우 직접 프레임 설정
                        self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, target_frame_idx)
                        ret_a, frame_a = self.cap_a.read()
                        if ret_a and frame_a is not None:
                            self.frame_a = self.process_frame(frame_a)
                            self._update_ui_fast(target_frame_idx, total_frames)
                            return True
            else:
                # 먼 경우 직접 프레임 설정
                self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, target_frame_idx)
                ret_a, frame_a = self.cap_a.read()
                if ret_a and frame_a is not None:
                    self.frame_a = self.process_frame(frame_a)
                    self._update_ui_fast(target_frame_idx, total_frames)
                    return True
                    
            return False
            
        except Exception as e:
            print(f"빠른 직접 프레임 이동 실패: {e}")
            return False

    def _seek_sequential_fast(self, target_frame_idx, total_frames):
        """빠른 순차적 프레임 읽기 - 단축키용"""
        try:
            # 현재 위치에서 시작하여 목표 프레임까지 읽기
            current_pos = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES)) - 1
            if current_pos < 0:
                current_pos = 0
            
            print(f"_seek_sequential_fast: 현재위치={current_pos}, 목표위치={target_frame_idx}")
                
            if target_frame_idx >= current_pos:
                # 앞으로 이동하는 경우
                frames_to_read = target_frame_idx - current_pos
                for _ in range(frames_to_read + 1):
                    ret_a, frame_a = self.cap_a.read()
                    if ret_a and frame_a is not None:
                        if current_pos == target_frame_idx:
                            self.frame_a = self.process_frame(frame_a)
                            self._update_ui_fast(target_frame_idx, total_frames)
                            return True
                        current_pos += 1
                    else:
                        break
            else:
                # 뒤로 이동하는 경우 - 최적화된 방법
                frames_to_go_back = current_pos - target_frame_idx
                if frames_to_go_back <= 10:
                    # 가까운 경우 직접 프레임 설정이 더 빠름
                    self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, target_frame_idx)
                    ret_a, frame_a = self.cap_a.read()
                    if ret_a and frame_a is not None:
                        self.frame_a = self.process_frame(frame_a)
                        self._update_ui_fast(target_frame_idx, total_frames)
                        return True
                else:
                    # 먼 경우에도 직접 프레임 설정이 더 효율적
                    self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, target_frame_idx)
                    ret_a, frame_a = self.cap_a.read()
                    if ret_a and frame_a is not None:
                        self.frame_a = self.process_frame(frame_a)
                        self._update_ui_fast(target_frame_idx, total_frames)
                        return True
                        
            return False
            
        except Exception as e:
            print(f"빠른 순차적 프레임 읽기 실패: {e}")
            return False

    def _update_ui_fast(self, frame_idx, total_frames):
        """빠른 UI 업데이트 - 단축키용"""
        # 프레임 범위 체크 강화
        frame_idx = max(0, min(frame_idx, total_frames - 1))
        
        self.videoWidget.updateFrame(self.frame_a, self.frame_b)
        
        # 프레임 라벨 직접 업데이트 (중복 방지)
        current_frame_num = frame_idx + 1
        self.labelFrameCount.setText(f'Current: {current_frame_num}')
        
        # 스크롤바 업데이트
        if total_frames > 1:
            scroll_val = round((frame_idx / (total_frames - 1)) * self.positionScroll.maximum())
            scroll_val = max(0, min(scroll_val, self.positionScroll.maximum()))
            self.positionScroll.blockSignals(True)
            self.positionScroll.setValue(scroll_val)
            self.positionScroll.blockSignals(False)
        
        # 시간 정보 업데이트
        fps = self.get_frame_rate()
        if fps > 0:
            current_time = frame_idx / fps
            total_time = (total_frames - 1) / fps if total_frames > 1 else 0
            self.timeLabel.setText(f"{self.format_time(current_time)} / {self.format_time(total_time)}")

    def keyPressEvent(self, event):
        key = event.key()
        print(f"Key pressed: {key} (Qt.Key_1 = {Qt.Key_1})")  # 디버깅 출력 추가
        if key == Qt.Key_B: self.show_overlay = not self.show_overlay; self.update_frame()
        elif key in [Qt.Key_Comma, Qt.Key_Left]: self.prev_frame()
        elif key in [Qt.Key_Period, Qt.Key_Right]: self.next_frame()
        elif key == Qt.Key_Space: self.toggle_play_pause()
        elif key == Qt.Key_F: self.toggle_fullscreen()
        elif key in [Qt.Key_Plus, Qt.Key_Equal, Qt.Key_2]: 
            print("Zoom in triggered"); self.videoWidget.zoom_in()
        elif key in [Qt.Key_Minus, Qt.Key_1]: 
            print("Zoom out triggered"); self.videoWidget.zoom_out()
        elif key == Qt.Key_3: self.videoWidget.reset_view()
        elif key == Qt.Key_4: self.videoWidget.show_original_size()
        else: super().keyPressEvent(event)

    def wheelEvent(self, event):
        """전역 휠 이벤트 처리 - 사이드바를 제외한 모든 영역에서 프레임 이동"""
        # 사이드바 영역에서는 휠 이벤트를 무시
        if self.sidebar.underMouse():
            event.ignore()
            return
            
        # 비디오가 로드되지 않은 경우 무시
        if not self.cap_a:
            event.ignore()
            return
            
        delta = event.angleDelta().y()
        frame_step = 1  # 3에서 1로 변경
        
        # CAP_PROP_POS_FRAMES는 다음 읽을 프레임의 0-based 인덱스
        current_frame_idx = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES)) - 1
        if current_frame_idx < 0:
            current_frame_idx = 0
        
        if delta < 0:  # 휠 다운 (이전)
            target_frame_idx = max(0, current_frame_idx - frame_step)
        else:  # 휠 업 (다음)
            total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
            target_frame_idx = min(total_frames - 1, current_frame_idx + frame_step)
        
        # 프레임 이동
        self.seek_to_frame(target_frame_idx + 1)  # seek_to_frame은 1-based
        event.accept()

    def handle_video_end(self):
        print(f"handle_video_end called - is_playlist_playing: {self.is_playlist_playing}")
        self.timer.stop()
        
        if self.is_playlist_playing: 
            print("Current video ended, moving to next in playlist")
            self.play_next_in_playlist()
        elif self.loopEnabled:
            print("Loop enabled, restarting current video")
            self.seek_to_frame(1)
            self.play_video()
        else:
            print("Video ended, stopping playback")
            self.stop_video()
            self.playPauseButton.setText('Play')
            self.playPauseButton.setStyleSheet("")

    def toggle_fullscreen(self):
        if self.isFullScreen:
            self.showNormal(); self.isFullScreen = False
            self.fullscreenButton.setText('Full screen')
        else:
            self.showFullScreen(); self.isFullScreen = True
            self.fullscreenButton.setText('Exit Full screen')

    @pyqtSlot(int)
    def toggle_loop_play(self, state):
        self.loopEnabled = state == Qt.Checked

    def play_video(self):
        if not self.timer.isActive() and self.cap_a:
            current_frame = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES))
            total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
            if current_frame >= total_frames: 
                self.seek_to_frame(1)
            fps = self.get_frame_rate()
            if fps > 0: 
                self.timer.start(int(1000 / fps))
            
            # 통합 오디오 관리자로 오디오 재생
            if self.audio_manager.is_audio_available():
                # 현재 프레임 위치를 밀리초로 변환
                current_frame_ms = int((current_frame - 1) / fps * 1000) if fps > 0 else 0
                audio_success = self.audio_manager.play(current_frame_ms)
                if audio_success:
                    print(f"Audio playing with {self.audio_manager.current_backend} backend")
                else:
                    print("Audio play failed, falling back to QMediaPlayer")
                    # 폴백: QMediaPlayer 사용
                    self.mediaPlayer.setPosition(int(self.cap_a.get(cv2.CAP_PROP_POS_MSEC)))
                    self.mediaPlayer.play()
            else:
                # 오디오 관리자에 파일이 없으면 QMediaPlayer 사용
                self.mediaPlayer.setPosition(int(self.cap_a.get(cv2.CAP_PROP_POS_MSEC)))
                self.mediaPlayer.play()
                
            self.playPauseButton.setText('Pause')
            self.playPauseButton.setStyleSheet("background-color: yellow;")
            print(f"play_video called - is_playlist_playing: {self.is_playlist_playing}")

    def pause_video(self):
        if self.timer.isActive():
            self.timer.stop()
            
            # 통합 오디오 관리자로 오디오 일시정지
            if self.audio_manager.is_audio_available():
                audio_success = self.audio_manager.pause()
                if not audio_success:
                    # 폴백: QMediaPlayer 사용
                    self.mediaPlayer.pause()
            else:
                self.mediaPlayer.pause()
                
            self.playPauseButton.setText('Play')
            self.playPauseButton.setStyleSheet("")
            print(f"pause_video called - is_playlist_playing: {self.is_playlist_playing}")

    def stop_video(self):
        self.timer.stop()
        
        # 통합 오디오 관리자로 오디오 정지
        if self.audio_manager.is_audio_available():
            self.audio_manager.stop()
        else:
            self.mediaPlayer.stop()
            
        if self.cap_a: self.seek_to_frame(1)
        self.playPauseButton.setText('Play'); self.playPauseButton.setStyleSheet("")
        if self.is_playlist_playing:
            self.is_playlist_playing = False; self.reset_sidebar_colors()
        self.playSelectedPauseButton.setText('Selected Play'); self.playSelectedPauseButton.setStyleSheet("")

    def setVolume(self, volume):
        # 통합 오디오 관리자로 볼륨 설정
        self.audio_manager.set_volume(volume)
        # 호환성을 위해 QMediaPlayer에도 설정
        self.mediaPlayer.setVolume(volume)

    def on_fit_to_window_changed(self, is_fit):
        self.is_fit_to_window = is_fit

    def toggle_play_pause(self):
        if self.timer.isActive(): 
            self.pause_video()
        elif self.cap_a:
            # 플레이리스트 재생 중이 아닐 때만 일반 재생 모드로 설정
            if not self.is_playlist_playing:
                self.is_playlist_playing = False
            self.play_video()

    def toggle_play_pause_selected(self):
        if self.is_playlist_playing:
            if self.timer.isActive():
                self.pause_video(); self.playSelectedPauseButton.setText('Selected Play'); self.playSelectedPauseButton.setStyleSheet("")
            else:
                self.play_video(); self.playSelectedPauseButton.setText('Selected Pause'); self.playSelectedPauseButton.setStyleSheet("background-color: yellow;")
        else:
            self.play_selected_files()

    def toggle_wipe_effect(self, state):
        enabled = state == Qt.Checked
        self.videoWidget.toggleWipeEffect(enabled)
        self.wipeXButton.setEnabled(enabled)
        self.wipeYButton.setEnabled(enabled)
        if not enabled:
            self.wipeXButton.setChecked(False); self.wipeYButton.setChecked(False)
        self.update_frame()

    def toggle_wipe_x(self, checked):
        self.videoWidget.wipe_enabled_x = checked; self.update_frame()

    def toggle_wipe_y(self, checked):
        self.videoWidget.wipe_enabled_y = checked; self.update_frame()
        
    def show_video_info(self, index):
        file_path = self.sidebar.item(index.row()).toolTip()
        if not file_path: return
        msg_box = QMessageBox(self); msg_box.setWindowTitle("미디어 정보")
        try:
            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened(): raise Exception("비디오 파일을 열 수 없습니다.")
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)); height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS); total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration_sec = (total_frames - 1) / fps if fps > 0 and total_frames > 1 else 0
            duration_str = self.format_time(duration_sec)
            codec = self.get_video_codec(file_path)
            msg = (f"**해상도:** {width} x {height}\n"
                   f"**코덱:** {codec}\n"
                   f"**길이:** {duration_str}\n"
                   f"**프레임:** {total_frames}\n"
                   f"**프레임 속도 (FPS):** {fps:.2f}\n"
                   f"**파일 경로:** {file_path}")
            cap.release()
        except Exception as e:
            msg = f"미디어 정보를 가져오는 데 실패했습니다: {e}"
        msg_box.setText(msg)
        msg_box.exec_()

    def show_help(self):
        """단축키 도움말을 표시합니다"""
        help_text = """
<h2>🎬 Yeson Video Player - 단축키 도움말</h2>

<h3>📁 파일 관리</h3>
• <b>드래그앤드롭</b>: 파일을 윈도우나 사이드바에 드래그하여 로드<br>
• <b>단일 파일</b>: Video A에 자동 로드<br>
• <b>두 개 파일</b>: Video A와 B에 각각 로드<br>
• <b>여러 파일</b>: 사이드바에 추가

<h3>▶️ 재생 제어</h3>
• <b>Space</b>: 재생/일시정지<br>
• <b>Enter</b>: 선택된 파일들 순차 재생<br>
• <b>\\</b>: 선택된 파일들 재생/일시정지<br>
• <b>← →</b>: 이전/다음 프레임<br>
• <b>마우스 휠</b>: 3프레임씩 이동

<h3>🔍 줌 및 뷰</h3>
• <b>+ / 2</b>: 줌 인<br>
• <b>- / 1</b>: 줌 아웃<br>
• <b>3</b>: 화면에 맞춤<br>
• <b>4</b>: 원본 크기<br>
• <b>마우스 드래그</b>: 줌 상태에서 화면 이동

<h3>🎛️ 기타</h3>
• <b>V</b>: 컨트롤 패널 표시/숨김<br>
• <b>F11</b>: 전체화면 전환<br>
• <b>우클릭</b>: 사이드바에서 컨텍스트 메뉴

<h3>💡 유용한 팁</h3>
• 슬라이더를 드래그하여 빠른 위치 이동<br>
• 사이드바에서 파일 선택 후 Enter로 순차 재생<br>
• 와이프 효과로 두 비디오 비교 가능<br>
• GPU 가속으로 성능 향상<br>
• <b>통합 오디오 지원</b>: VLC, Pygame, Qt 백엔드로 다양한 오디오 코덱 지원
        """
        
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("단축키 도움말")
        msg_box.setText(help_text)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setStandardButtons(QMessageBox.Ok)
        
        # 메시지 박스 크기 조정
        msg_box.setMinimumWidth(500)
        msg_box.setMinimumHeight(400)
        
        msg_box.exec_()

    def seek_to_frame(self, frame_number, update_audio=True):
        if not self.cap_a:
            return
            
        target_index = max(0, frame_number - 1)
        
        # 비표준 해상도 파일을 위한 재시도 로직
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 프레임 위치 설정
                self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, target_index)
                
                # 실제로 설정된 프레임 위치 확인
                actual_pos = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES))
                
                # 프레임 읽기
                ret_a, frame_a = self.cap_a.read()
                
                if ret_a and frame_a is not None:
                    self.frame_a = self.process_frame(frame_a)
                    
                    # 비디오 B도 동기화
                    if self.cap_b and self.videoWidget.wipeEnabled:
                        self.cap_b.set(cv2.CAP_PROP_POS_FRAMES, target_index)
                        ret_b, frame_b = self.cap_b.read()
                        self.frame_b = self.process_frame(frame_b) if ret_b else None
                    
                    # 오디오 동기화 - 통합 오디오 관리자 사용
                    if update_audio:
                        fps = self.get_frame_rate()
                        if fps > 0:
                            target_time_ms = int(actual_pos / fps * 1000)
                            
                            # 통합 오디오 관리자로 위치 설정
                            if self.audio_manager.is_audio_available():
                                self.audio_manager.set_position(target_time_ms)
                            else:
                                # 폴백: QMediaPlayer 사용
                                self.mediaPlayer.setPosition(target_time_ms)
                    
                    self.update_frame()
                    break
                    
                else:
                    # 프레임 읽기 실패 시 다음 프레임 시도
                    if attempt < max_retries - 1:
                        target_index += 1
                        continue
                        
            except Exception as e:
                print(f"Seek attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    print(f"Failed to seek to frame {frame_number} after {max_retries} attempts")
                    break

    def closeEvent(self, event):
        """앱 종료 시 리소스 정리"""
        try:
            # 오디오 관리자 리소스 정리
            if hasattr(self, 'audio_manager'):
                self.audio_manager.cleanup()
            
            # 비디오 캡처 객체 정리
            if self.cap_a:
                self.cap_a.release()
            if self.cap_b:
                self.cap_b.release()
            
            # 타이머 정지
            if self.timer.isActive():
                self.timer.stop()
            
            # 미디어 플레이어 정지
            if self.mediaPlayer:
                self.mediaPlayer.stop()
                
        except Exception as e:
            print(f"Error during cleanup: {e}")
        
        # 기본 종료 이벤트 처리
        super().closeEvent(event)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    player = VideoPlayer()

    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        print(f"Opening file: {file_path}")
        if os.path.isfile(file_path):
            player.open_file('A', file_path)
        else:
            print(f"File does not exist: {file_path}")

    player.show()
    sys.exit(app.exec_())