# -*- mode: python ; coding: utf-8 -*-
import os
import glob
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

vlc_app_path = '/Applications/VLC.app'
vlc_macos_path = '/Applications/VLC.app/Contents/MacOS'
vlc_lib_path = '/Applications/VLC.app/Contents/MacOS/lib'
vlc_plugins_path = '/Applications/VLC.app/Contents/MacOS/plugins'

vlc_binaries = []

# VLC 메인 바이너리
if os.path.exists(os.path.join(vlc_macos_path, 'VLC')):
    vlc_binaries.append((os.path.join(vlc_macos_path, 'VLC'), 'vlc'))

# VLC 라이브러리들
if os.path.exists(vlc_lib_path):
    for lib_file in glob.glob(os.path.join(vlc_lib_path, '*.dylib')):
        vlc_binaries.append((lib_file, 'vlc'))

# VLC 플러그인 전체 포함 (폴더 구조 유지)
if os.path.exists(vlc_plugins_path):
    for root, dirs, files in os.walk(vlc_plugins_path):
        for file in files:
            if file.endswith('.dylib') or file.endswith('.so') or file.endswith('.bundle'):
                full_path = os.path.join(root, file)
                rel_path = os.path.relpath(root, vlc_plugins_path)
                # plugins 루트면 plugins에, 하위면 plugins/하위폴더에 복사
                if rel_path == '.':
                    target_dir = os.path.join('vlc', 'plugins')
                else:
                    target_dir = os.path.join('vlc', 'plugins', rel_path)
                vlc_binaries.append((full_path, target_dir))

# 시스템 오디오 라이브러리들
system_audio_libs = [
    '/usr/lib/libSystem.B.dylib',
    '/System/Library/Frameworks/AudioToolbox.framework/AudioToolbox',
    '/System/Library/Frameworks/AudioUnit.framework/AudioUnit',
    '/System/Library/Frameworks/CoreAudio.framework/CoreAudio',
    '/System/Library/Frameworks/CoreMedia.framework/CoreMedia',
    '/System/Library/Frameworks/AVFoundation.framework/AVFoundation',
]
for lib in system_audio_libs:
    if os.path.exists(lib):
        vlc_binaries.append((lib, '.'))

# Homebrew 오디오 라이브러리들 (Intel + ARM)
homebrew_audio_libs = [
    '/usr/local/lib/libmodplug.dylib',
    '/usr/local/lib/libmad.dylib',
    '/usr/local/lib/libmp3lame.dylib',
    '/usr/local/lib/libfaad.dylib',
    '/usr/local/lib/libfaac.dylib',
    '/usr/local/lib/libvorbis.dylib',
    '/usr/local/lib/libogg.dylib',
    '/usr/local/lib/libflac.dylib',
    '/usr/local/lib/libavcodec.dylib',
    '/usr/local/lib/libavformat.dylib',
    '/usr/local/lib/libavutil.dylib',
    '/usr/local/lib/libswresample.dylib',
    '/opt/homebrew/lib/libmodplug.dylib',
    '/opt/homebrew/lib/libmad.dylib',
    '/opt/homebrew/lib/libmp3lame.dylib',
    '/opt/homebrew/lib/libfaad.dylib',
    '/opt/homebrew/lib/libfaac.dylib',
    '/opt/homebrew/lib/libvorbis.dylib',
    '/opt/homebrew/lib/libogg.dylib',
    '/opt/homebrew/lib/libflac.dylib',
    '/opt/homebrew/lib/libavcodec.dylib',
    '/opt/homebrew/lib/libavformat.dylib',
    '/opt/homebrew/lib/libavutil.dylib',
    '/opt/homebrew/lib/libswresample.dylib',
]
for lib in homebrew_audio_libs:
    if os.path.exists(lib):
        vlc_binaries.append((lib, '.'))

pygame_data = collect_data_files('pygame')

hidden_imports = [
    'vlc',
    'pygame',
    'pygame.mixer',
    'pygame.mixer.music',
    'cv2',
    'numpy',
    'PyQt5.QtMultimedia',
    'PyQt5.QtMultimediaWidgets',
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'ctypes',
    'ctypes.util',
] + collect_submodules('vlc') + collect_submodules('pygame')

a = Analysis(
    ['YesonCheck_final_20250613.py'],
    pathex=[],
    binaries=vlc_binaries,
    datas=pygame_data,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='YesonVideoPlayer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['../icon.ico'],
)

app = BUNDLE(
    exe,
    name='YesonVideoPlayer.app',
    icon='../icon.ico',
    bundle_identifier='com.yeson.videoplayer',
    info_plist={
        'NSHighResolutionCapable': True,
        'NSRequiresAquaSystemAppearance': False,
        'CFBundleDisplayName': 'Yeson Video Player',
        'CFBundleName': 'YesonVideoPlayer',
        'CFBundleVersion': '1.0.0',
        'CFBundleShortVersionString': '1.0.0',
        'NSMicrophoneUsageDescription': 'This app needs microphone access for audio playback',
        'NSCameraUsageDescription': 'This app needs camera access for video playback',
        'NSAppleEventsUsageDescription': 'This app needs to control system audio',
        'LSMinimumSystemVersion': '10.13.0',
        'NSPrincipalClass': 'NSApplication',
    },
)
