(['/Users/<USER>/coding/yesoncheck/YesonCheck_final_mkvaudio_20250613.py'],
 ['/Users/<USER>/coding/yesoncheck'],
 ['vlc',
  'pygame',
  'pygame.mixer',
  'pygame.mixer.music',
  'cv2',
  'numpy',
  'PyQt5.QtMultimedia',
  'PyQt5.QtMultimediaWidgets',
  'PyQt5.QtCore',
  'PyQt5.QtGui',
  'PyQt5.QtWidgets',
  'ctypes',
  'ctypes.util',
  'ctypes.cdll',
  'ctypes.CDLL',
  'vlc',
  'pygame',
  'pygame.__pyinstaller',
  'pygame.__pyinstaller.hook-pygame',
  'pygame._camera',
  'pygame._camera_opencv',
  'pygame._camera_vidcapture',
  'pygame._freetype',
  'pygame._sdl2',
  'pygame._sdl2.audio',
  'pygame._sdl2.controller',
  'pygame._sdl2.mixer',
  'pygame._sdl2.sdl2',
  'pygame._sdl2.touch',
  'pygame._sdl2.video',
  'pygame._sprite',
  'pygame.base',
  'pygame.bufferproxy',
  'pygame.camera',
  'pygame.color',
  'pygame.colordict',
  'pygame.constants',
  'pygame.cursors',
  'pygame.display',
  'pygame.draw',
  'pygame.draw_py',
  'pygame.event',
  'pygame.examples',
  'pygame.examples.aacircle',
  'pygame.examples.aliens',
  'pygame.examples.arraydemo',
  'pygame.examples.audiocapture',
  'pygame.examples.blend_fill',
  'pygame.examples.blit_blends',
  'pygame.examples.camera',
  'pygame.examples.chimp',
  'pygame.examples.cursors',
  'pygame.examples.dropevent',
  'pygame.examples.eventlist',
  'pygame.examples.font_viewer',
  'pygame.examples.fonty',
  'pygame.examples.freetype_misc',
  'pygame.examples.glcube',
  'pygame.examples.go_over_there',
  'pygame.examples.grid',
  'pygame.examples.headless_no_windows_needed',
  'pygame.examples.joystick',
  'pygame.examples.liquid',
  'pygame.examples.mask',
  'pygame.examples.midi',
  'pygame.examples.moveit',
  'pygame.examples.music_drop_fade',
  'pygame.examples.pixelarray',
  'pygame.examples.playmus',
  'pygame.examples.resizing_new',
  'pygame.examples.scaletest',
  'pygame.examples.scrap_clipboard',
  'pygame.examples.scroll',
  'pygame.examples.setmodescale',
  'pygame.examples.sound',
  'pygame.examples.sound_array_demos',
  'pygame.examples.sprite_texture',
  'pygame.examples.stars',
  'pygame.examples.testsprite',
  'pygame.examples.textinput',
  'pygame.examples.vgrade',
  'pygame.examples.video',
  'pygame.fastevent',
  'pygame.font',
  'pygame.freetype',
  'pygame.ftfont',
  'pygame.gfxdraw',
  'pygame.image',
  'pygame.imageext',
  'pygame.joystick',
  'pygame.key',
  'pygame.locals',
  'pygame.macosx',
  'pygame.mask',
  'pygame.math',
  'pygame.midi',
  'pygame.mixer',
  'pygame.mixer_music',
  'pygame.mouse',
  'pygame.newbuffer',
  'pygame.pixelarray',
  'pygame.pixelcopy',
  'pygame.pkgdata',
  'pygame.pypm',
  'pygame.rect',
  'pygame.rwobject',
  'pygame.scrap',
  'pygame.sdlmain_osx',
  'pygame.sndarray',
  'pygame.sprite',
  'pygame.surface',
  'pygame.surfarray',
  'pygame.surflock',
  'pygame.sysfont',
  'pygame.tests',
  'pygame.tests.__main__',
  'pygame.tests.base_test',
  'pygame.tests.blit_test',
  'pygame.tests.bufferproxy_test',
  'pygame.tests.camera_test',
  'pygame.tests.color_test',
  'pygame.tests.constants_test',
  'pygame.tests.controller_test',
  'pygame.tests.cursors_test',
  'pygame.tests.display_test',
  'pygame.tests.docs_test',
  'pygame.tests.draw_test',
  'pygame.tests.event_test',
  'pygame.tests.font_test',
  'pygame.tests.freetype_tags',
  'pygame.tests.freetype_test',
  'pygame.tests.ftfont_tags',
  'pygame.tests.ftfont_test',
  'pygame.tests.gfxdraw_test',
  'pygame.tests.image__save_gl_surface_test',
  'pygame.tests.image_tags',
  'pygame.tests.image_test',
  'pygame.tests.imageext_tags',
  'pygame.tests.imageext_test',
  'pygame.tests.joystick_test',
  'pygame.tests.key_test',
  'pygame.tests.locals_test',
  'pygame.tests.mask_test',
  'pygame.tests.math_test',
  'pygame.tests.midi_test',
  'pygame.tests.mixer_music_tags',
  'pygame.tests.mixer_music_test',
  'pygame.tests.mixer_tags',
  'pygame.tests.mixer_test',
  'pygame.tests.mouse_test',
  'pygame.tests.pixelarray_test',
  'pygame.tests.pixelcopy_test',
  'pygame.tests.rect_test',
  'pygame.tests.run_tests__tests',
  'pygame.tests.run_tests__tests.all_ok',
  'pygame.tests.run_tests__tests.all_ok.fake_2_test',
  'pygame.tests.run_tests__tests.all_ok.fake_3_test',
  'pygame.tests.run_tests__tests.all_ok.fake_4_test',
  'pygame.tests.run_tests__tests.all_ok.fake_5_test',
  'pygame.tests.run_tests__tests.all_ok.fake_6_test',
  'pygame.tests.run_tests__tests.all_ok.no_assertions__ret_code_of_1__test',
  'pygame.tests.run_tests__tests.all_ok.zero_tests_test',
  'pygame.tests.run_tests__tests.everything',
  'pygame.tests.run_tests__tests.everything.fake_2_test',
  'pygame.tests.run_tests__tests.everything.incomplete_todo_test',
  'pygame.tests.run_tests__tests.everything.magic_tag_test',
  'pygame.tests.run_tests__tests.everything.sleep_test',
  'pygame.tests.run_tests__tests.exclude',
  'pygame.tests.run_tests__tests.exclude.fake_2_test',
  'pygame.tests.run_tests__tests.exclude.invisible_tag_test',
  'pygame.tests.run_tests__tests.exclude.magic_tag_test',
  'pygame.tests.run_tests__tests.failures1',
  'pygame.tests.run_tests__tests.failures1.fake_2_test',
  'pygame.tests.run_tests__tests.failures1.fake_3_test',
  'pygame.tests.run_tests__tests.failures1.fake_4_test',
  'pygame.tests.run_tests__tests.incomplete',
  'pygame.tests.run_tests__tests.incomplete.fake_2_test',
  'pygame.tests.run_tests__tests.incomplete.fake_3_test',
  'pygame.tests.run_tests__tests.incomplete_todo',
  'pygame.tests.run_tests__tests.incomplete_todo.fake_2_test',
  'pygame.tests.run_tests__tests.incomplete_todo.fake_3_test',
  'pygame.tests.run_tests__tests.infinite_loop',
  'pygame.tests.run_tests__tests.infinite_loop.fake_1_test',
  'pygame.tests.run_tests__tests.infinite_loop.fake_2_test',
  'pygame.tests.run_tests__tests.print_stderr',
  'pygame.tests.run_tests__tests.print_stderr.fake_2_test',
  'pygame.tests.run_tests__tests.print_stderr.fake_3_test',
  'pygame.tests.run_tests__tests.print_stderr.fake_4_test',
  'pygame.tests.run_tests__tests.print_stdout',
  'pygame.tests.run_tests__tests.print_stdout.fake_2_test',
  'pygame.tests.run_tests__tests.print_stdout.fake_3_test',
  'pygame.tests.run_tests__tests.print_stdout.fake_4_test',
  'pygame.tests.run_tests__tests.run_tests__test',
  'pygame.tests.run_tests__tests.timeout',
  'pygame.tests.run_tests__tests.timeout.fake_2_test',
  'pygame.tests.run_tests__tests.timeout.sleep_test',
  'pygame.tests.rwobject_test',
  'pygame.tests.scrap_tags',
  'pygame.tests.scrap_test',
  'pygame.tests.sndarray_tags',
  'pygame.tests.sndarray_test',
  'pygame.tests.sprite_test',
  'pygame.tests.surface_test',
  'pygame.tests.surfarray_tags',
  'pygame.tests.surfarray_test',
  'pygame.tests.surflock_test',
  'pygame.tests.sysfont_test',
  'pygame.tests.test_utils',
  'pygame.tests.test_utils.arrinter',
  'pygame.tests.test_utils.async_sub',
  'pygame.tests.test_utils.buftools',
  'pygame.tests.test_utils.endian',
  'pygame.tests.test_utils.png',
  'pygame.tests.test_utils.run_tests',
  'pygame.tests.test_utils.test_machinery',
  'pygame.tests.test_utils.test_runner',
  'pygame.tests.threads_test',
  'pygame.tests.time_test',
  'pygame.tests.touch_test',
  'pygame.tests.transform_test',
  'pygame.tests.version_test',
  'pygame.tests.video_test',
  'pygame.threads',
  'pygame.time',
  'pygame.transform',
  'pygame.version'],
 [('/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_pyinstaller',
   0),
  ('/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/__pyinstaller',
   0),
  ('/Users/<USER>/coding/p312/lib/python3.12/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/Users/<USER>/coding/p312/lib/python3.12/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 ['vlc_runtime_hook.py'],
 False,
 {},
 0,
 [('VLC', '/Applications/VLC.app/Contents/MacOS/VLC', 'BINARY'),
  ('libavcodec.dylib', '/usr/local/lib/libavcodec.dylib', 'BINARY'),
  ('libavdevice.dylib', '/usr/local/lib/libavdevice.dylib', 'BINARY'),
  ('libavfilter.dylib', '/usr/local/lib/libavfilter.dylib', 'BINARY'),
  ('libavformat.dylib', '/usr/local/lib/libavformat.dylib', 'BINARY'),
  ('libavutil.dylib', '/usr/local/lib/libavutil.dylib', 'BINARY'),
  ('libflac.dylib', '/usr/local/lib/libflac.dylib', 'BINARY'),
  ('libmp3lame.dylib', '/usr/local/lib/libmp3lame.dylib', 'BINARY'),
  ('libogg.dylib', '/usr/local/lib/libogg.dylib', 'BINARY'),
  ('libopus.dylib', '/usr/local/lib/libopus.dylib', 'BINARY'),
  ('libpostproc.dylib', '/usr/local/lib/libpostproc.dylib', 'BINARY'),
  ('libspeex.dylib', '/usr/local/lib/libspeex.dylib', 'BINARY'),
  ('libswresample.dylib', '/usr/local/lib/libswresample.dylib', 'BINARY'),
  ('libswscale.dylib', '/usr/local/lib/libswscale.dylib', 'BINARY'),
  ('libtheora.dylib', '/usr/local/lib/libtheora.dylib', 'BINARY'),
  ('libvorbis.dylib', '/usr/local/lib/libvorbis.dylib', 'BINARY'),
  ('libvpx.dylib', '/usr/local/lib/libvpx.dylib', 'BINARY'),
  ('libx264.dylib', '/usr/local/lib/libx264.dylib', 'BINARY'),
  ('libx265.dylib', '/usr/local/lib/libx265.dylib', 'BINARY'),
  ('vlc/lib/libvlc.5.dylib',
   '/Applications/VLC.app/Contents/MacOS/lib/libvlc.5.dylib',
   'BINARY'),
  ('vlc/lib/libvlc.dylib',
   '/Applications/VLC.app/Contents/MacOS/lib/libvlc.dylib',
   'BINARY'),
  ('vlc/lib/libvlccore.9.dylib',
   '/Applications/VLC.app/Contents/MacOS/lib/libvlccore.9.dylib',
   'BINARY'),
  ('vlc/lib/libvlccore.dylib',
   '/Applications/VLC.app/Contents/MacOS/lib/libvlccore.dylib',
   'BINARY'),
  ('vlc/plugins/liba52_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liba52_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_concat_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_concat_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_imem_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_imem_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_mms_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_mms_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_dummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_dummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_file_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_file_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_http_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_http_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_livehttp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_livehttp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_rist_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_rist_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_shout_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_shout_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_srt_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_srt_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_udp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_udp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_realrtsp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_realrtsp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_srt_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_srt_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libadaptive_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libadaptive_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaddonsfsstorage_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaddonsfsstorage_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaddonsvorepository_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaddonsvorepository_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libadf_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libadf_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libadjust_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libadjust_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libadpcm_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libadpcm_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libadummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libadummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaes3_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaes3_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libafile_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libafile_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaiff_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaiff_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libalphamask_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libalphamask_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libamem_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libamem_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libanaglyph_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libanaglyph_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libantiflicker_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libantiflicker_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaom_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaom_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaraw_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaraw_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libarchive_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libarchive_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaribsub_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaribsub_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libasf_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libasf_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libattachment_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libattachment_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libau_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libau_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaudio_format_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaudio_format_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaudiobargraph_a_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaudiobargraph_a_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaudiobargraph_v_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaudiobargraph_v_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaudioscrobbler_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaudioscrobbler_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaudiotoolboxmidi_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaudiotoolboxmidi_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libauhal_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libauhal_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libavaudiocapture_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libavaudiocapture_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libavcapture_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libavcapture_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libavcodec_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libavcodec_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libavi_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libavi_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libball_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libball_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libblend_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libblend_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libblendbench_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libblendbench_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libbluescreen_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libbluescreen_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libbonjour_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libbonjour_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcache_block_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcache_block_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcache_read_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcache_read_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcaf_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcaf_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcanvas_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcanvas_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcaopengllayer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcaopengllayer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcc_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcc_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcdda_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcdda_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcdg_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcdg_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libchain_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libchain_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libchorus_flanger_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libchorus_flanger_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libci_filters_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libci_filters_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libclone_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libclone_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcolorthres_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcolorthres_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcompressor_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcompressor_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libconsole_logger_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libconsole_logger_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcroppadd_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcroppadd_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcvdsub_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcvdsub_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcvpx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcvpx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdav1d_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdav1d_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdca_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdca_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdcp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdcp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libddummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libddummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdecomp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdecomp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdeinterlace_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdeinterlace_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdemux_cdg_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdemux_cdg_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdemux_chromecast_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdemux_chromecast_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdemux_stl_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdemux_stl_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdemuxdump_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdemuxdump_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdiracsys_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdiracsys_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdirectory_demux_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdirectory_demux_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdolby_surround_decoder_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdolby_surround_decoder_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdvbsub_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdvbsub_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdvdnav_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdvdnav_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdvdread_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdvdread_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdynamicoverlay_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdynamicoverlay_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libedgedetection_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libedgedetection_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libedummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libedummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libequalizer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libequalizer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liberase_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liberase_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libes_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libes_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libexport_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libexport_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libextract_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libextract_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfaad_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfaad_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfile_keystore_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfile_keystore_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfile_logger_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfile_logger_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfilesystem_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfilesystem_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfingerprinter_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfingerprinter_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libflac_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libflac_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libflacsys_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libflacsys_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libflaschen_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libflaschen_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfloat_mixer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfloat_mixer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfolder_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfolder_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfps_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfps_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfreetype_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfreetype_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfreeze_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfreeze_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libftp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libftp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libg711_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libg711_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgain_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgain_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgaussianblur_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgaussianblur_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgestures_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgestures_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libglconv_cvpx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libglconv_cvpx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgme_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgme_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgnutls_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgnutls_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgoom_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgoom_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgradfun_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgradfun_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgradient_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgradient_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgrain_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgrain_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgrey_yuv_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgrey_yuv_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libh26x_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libh26x_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libhds_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libhds_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libheadphone_channel_mixer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libheadphone_channel_mixer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libhotkeys_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libhotkeys_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libhqdn3d_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libhqdn3d_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libhttp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libhttp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libhttps_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libhttps_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_10_p010_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_10_p010_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_nv12_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_nv12_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_rgb_mmx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_rgb_mmx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_rgb_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_rgb_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_rgb_sse2_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_rgb_sse2_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_yuy2_mmx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_yuy2_mmx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_yuy2_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_yuy2_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_yuy2_sse2_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_yuy2_sse2_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi422_i420_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi422_i420_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi422_yuy2_mmx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi422_yuy2_mmx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi422_yuy2_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi422_yuy2_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi422_yuy2_sse2_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi422_yuy2_sse2_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libidummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libidummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libimage_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libimage_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libimem_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libimem_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libinflate_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libinflate_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libinteger_mixer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libinteger_mixer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libinvert_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libinvert_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libjpeg_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libjpeg_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libkaraoke_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libkaraoke_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libkate_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libkate_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libkeychain_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libkeychain_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblibass_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblibass_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblibbluray_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblibbluray_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblibmpeg2_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblibmpeg2_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblive555_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblive555_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblogger_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblogger_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblogo_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblogo_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblpcm_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblpcm_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblua_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblua_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmacosx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmacosx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmad_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmad_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmagnify_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmagnify_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmarq_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmarq_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmediadirs_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmediadirs_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmemory_keystore_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmemory_keystore_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmirror_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmirror_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmjpeg_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmjpeg_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmkv_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmkv_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmod_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmod_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmono_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmono_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmosaic_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmosaic_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmotion_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmotion_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmotionblur_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmotionblur_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmotiondetect_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmotiondetect_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmp4_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmp4_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmpc_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmpc_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmpg123_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmpg123_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmpgv_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmpgv_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_asf_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_asf_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_avi_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_avi_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_dummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_dummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_mp4_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_mp4_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_mpjpeg_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_mpjpeg_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_ogg_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_ogg_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_ps_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_ps_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_ts_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_ts_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_wav_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_wav_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libncurses_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libncurses_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnetsync_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnetsync_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnfs_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnfs_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnormvol_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnormvol_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnoseek_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnoseek_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnsc_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnsc_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnsspeechsynthesizer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnsspeechsynthesizer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnsv_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnsv_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnuv_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnuv_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libogg_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libogg_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liboggspots_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liboggspots_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liboldmovie_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liboldmovie_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liboldrc_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liboldrc_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libopus_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libopus_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libosx_notifications_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libosx_notifications_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_a52_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_a52_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_av1_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_av1_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_copy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_copy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_dirac_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_dirac_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_dts_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_dts_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_flac_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_flac_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_h264_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_h264_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_hevc_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_hevc_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_mlp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_mlp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_mpeg4audio_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_mpeg4audio_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_mpeg4video_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_mpeg4video_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_mpegaudio_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_mpegaudio_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_mpegvideo_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_mpegvideo_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_vc1_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_vc1_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libparam_eq_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libparam_eq_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libplaylist_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libplaylist_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpng_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpng_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpodcast_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpodcast_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libposterize_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libposterize_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpostproc_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpostproc_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libprefetch_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libprefetch_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libps_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libps_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpsychedelic_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpsychedelic_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpuzzle_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpuzzle_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpva_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpva_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librawaud_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librawaud_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librawdv_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librawdv_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librawvid_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librawvid_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librawvideo_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librawvideo_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libreal_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libreal_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librecord_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librecord_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libremap_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libremap_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libremoteosd_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libremoteosd_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libripple_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libripple_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librist_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librist_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librotate_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librotate_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librss_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librss_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librtp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librtp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librtpvideo_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librtpvideo_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librv32_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librv32_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsamplerate_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsamplerate_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsap_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsap_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsatip_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsatip_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libscale_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libscale_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libscaletempo_pitch_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libscaletempo_pitch_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libscaletempo_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libscaletempo_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libscene_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libscene_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libschroedinger_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libschroedinger_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libscreen_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libscreen_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libscte18_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libscte18_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libscte27_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libscte27_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsdp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsdp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsecuretransport_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsecuretransport_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsepia_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsepia_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsftp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsftp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsharpen_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsharpen_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libshm_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libshm_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsid_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsid_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsimple_channel_mixer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsimple_channel_mixer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libskiptags_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libskiptags_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsmf_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsmf_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libspatialaudio_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libspatialaudio_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libspatializer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libspatializer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libspdif_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libspdif_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libspeex_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libspeex_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libspeex_resampler_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libspeex_resampler_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libspudec_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libspudec_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstats_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstats_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstereo_widen_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstereo_widen_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstl_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstl_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_autodel_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_autodel_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_bridge_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_bridge_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_chromaprint_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_chromaprint_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_chromecast_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_chromecast_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_cycle_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_cycle_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_delay_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_delay_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_description_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_description_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_display_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_display_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_dummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_dummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_duplicate_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_duplicate_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_es_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_es_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_gather_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_gather_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_mosaic_bridge_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_mosaic_bridge_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_record_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_record_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_rtp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_rtp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_setid_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_setid_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_smem_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_smem_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_standard_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_standard_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_stats_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_stats_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_transcode_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_transcode_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsubsdec_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsubsdec_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsubsdelay_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsubsdelay_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsubstx3g_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsubstx3g_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsubsusf_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsubsusf_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsubtitle_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsubtitle_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsvcdsub_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsvcdsub_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libswscale_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libswscale_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsyslog_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsyslog_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libt140_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libt140_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtaglib_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtaglib_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtcp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtcp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtdummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtdummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtelx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtelx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtextst_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtextst_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtheora_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtheora_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtimecode_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtimecode_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtospdif_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtospdif_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtransform_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtransform_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtrivial_channel_mixer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtrivial_channel_mixer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libts_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libts_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtta_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtta_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libttml_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libttml_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtwolame_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtwolame_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libty_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libty_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libudp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libudp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libugly_resampler_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libugly_resampler_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libuleaddvaudio_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libuleaddvaudio_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libupnp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libupnp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvc1_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvc1_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvcd_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvcd_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvdr_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvdr_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvdummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvdummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvhs_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvhs_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvideotoolbox_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvideotoolbox_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvisual_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvisual_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvmem_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvmem_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvobsub_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvobsub_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvoc_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvoc_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvod_rtsp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvod_rtsp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvorbis_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvorbis_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvout_macosx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvout_macosx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvpx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvpx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libwall_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libwall_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libwav_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libwav_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libwave_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libwave_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libwebvtt_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libwebvtt_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libx26410b_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libx26410b_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libx264_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libx264_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libx265_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libx265_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libxa_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libxa_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libxml_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libxml_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libyuv_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libyuv_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libyuvp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libyuvp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libyuy2_i420_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libyuy2_i420_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libyuy2_i422_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libyuy2_i422_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libzvbi_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libzvbi_plugin.dylib',
   'BINARY')],
 [('pygame/.dylibs/libFLAC.8.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libFLAC.8.dylib',
   'DATA'),
  ('pygame/.dylibs/libSDL2-2.0.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libSDL2-2.0.0.dylib',
   'DATA'),
  ('pygame/.dylibs/libSDL2_image-2.0.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libSDL2_image-2.0.0.dylib',
   'DATA'),
  ('pygame/.dylibs/libSDL2_mixer-2.0.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libSDL2_mixer-2.0.0.dylib',
   'DATA'),
  ('pygame/.dylibs/libSDL2_ttf-2.0.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libSDL2_ttf-2.0.0.dylib',
   'DATA'),
  ('pygame/.dylibs/libbrotlicommon.1.0.9.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libbrotlicommon.1.0.9.dylib',
   'DATA'),
  ('pygame/.dylibs/libbrotlidec.1.0.9.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libbrotlidec.1.0.9.dylib',
   'DATA'),
  ('pygame/.dylibs/libfluidsynth.3.1.1.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libfluidsynth.3.1.1.dylib',
   'DATA'),
  ('pygame/.dylibs/libfreetype.6.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libfreetype.6.dylib',
   'DATA'),
  ('pygame/.dylibs/libglib-2.0.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libglib-2.0.0.dylib',
   'DATA'),
  ('pygame/.dylibs/libgthread-2.0.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libgthread-2.0.0.dylib',
   'DATA'),
  ('pygame/.dylibs/libintl.8.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libintl.8.dylib',
   'DATA'),
  ('pygame/.dylibs/libjpeg.62.3.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libjpeg.62.3.0.dylib',
   'DATA'),
  ('pygame/.dylibs/libmpg123.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libmpg123.0.dylib',
   'DATA'),
  ('pygame/.dylibs/libogg.0.8.5.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libogg.0.8.5.dylib',
   'DATA'),
  ('pygame/.dylibs/libopus.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libopus.0.dylib',
   'DATA'),
  ('pygame/.dylibs/libopusfile.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libopusfile.0.dylib',
   'DATA'),
  ('pygame/.dylibs/libpng16.16.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libpng16.16.dylib',
   'DATA'),
  ('pygame/.dylibs/libportmidi.2.0.3.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libportmidi.2.0.3.dylib',
   'DATA'),
  ('pygame/.dylibs/libsharpyuv.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libsharpyuv.0.dylib',
   'DATA'),
  ('pygame/.dylibs/libsndfile.1.0.34.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libsndfile.1.0.34.dylib',
   'DATA'),
  ('pygame/.dylibs/libtiff.5.8.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libtiff.5.8.0.dylib',
   'DATA'),
  ('pygame/.dylibs/libvorbis.0.4.9.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libvorbis.0.4.9.dylib',
   'DATA'),
  ('pygame/.dylibs/libvorbisenc.2.0.12.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libvorbisenc.2.0.12.dylib',
   'DATA'),
  ('pygame/.dylibs/libvorbisfile.3.3.8.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libvorbisfile.3.3.8.dylib',
   'DATA'),
  ('pygame/.dylibs/libwebp.7.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libwebp.7.dylib',
   'DATA'),
  ('pygame/.dylibs/libz.1.2.11.zlib-ng.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libz.1.2.11.zlib-ng.dylib',
   'DATA'),
  ('pygame/__init__.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/__init__.pyi',
   'DATA'),
  ('pygame/_common.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_common.pyi',
   'DATA'),
  ('pygame/_sdl2/__init__.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/__init__.pyi',
   'DATA'),
  ('pygame/_sdl2/audio.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/audio.pyi',
   'DATA'),
  ('pygame/_sdl2/controller.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/controller.pyi',
   'DATA'),
  ('pygame/_sdl2/sdl2.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/sdl2.pyi',
   'DATA'),
  ('pygame/_sdl2/touch.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/touch.pyi',
   'DATA'),
  ('pygame/_sdl2/video.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/video.pyi',
   'DATA'),
  ('pygame/base.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/base.pyi',
   'DATA'),
  ('pygame/bufferproxy.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/bufferproxy.pyi',
   'DATA'),
  ('pygame/camera.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/camera.pyi',
   'DATA'),
  ('pygame/color.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/color.pyi',
   'DATA'),
  ('pygame/constants.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/constants.pyi',
   'DATA'),
  ('pygame/cursors.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/cursors.pyi',
   'DATA'),
  ('pygame/display.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/display.pyi',
   'DATA'),
  ('pygame/docs/generated/LGPL.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/LGPL.txt',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput1.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput1.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput11.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput11.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput2.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput2.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput21.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput21.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput3.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput3.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput31.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput31.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput4.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput4.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput41.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput41.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput5.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput5.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput51.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput51.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputAlpha1.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputAlpha1.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputAlpha11.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputAlpha11.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputAlpha2.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputAlpha2.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputAlpha21.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputAlpha21.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputAlpha3.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputAlpha3.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputAlpha31.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputAlpha31.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess1.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess1.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess11.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess11.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess2.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess2.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess21.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess21.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess3.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess3.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess31.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess31.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess4.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess4.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess41.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess41.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess5.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess5.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess51.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess51.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess6.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess6.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess61.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess61.gif',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-INPUT-resultscreen.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-INPUT-resultscreen.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-INPUT-resultscreen1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-INPUT-resultscreen1.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-INPUT-sourcecode.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-INPUT-sourcecode.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-INPUT-sourcecode1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-INPUT-sourcecode1.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-PROCESS-resultscreen.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-PROCESS-resultscreen.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-PROCESS-resultscreen1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-PROCESS-resultscreen1.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-PROCESS-sourcecode.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-PROCESS-sourcecode.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-PROCESS-sourcecode1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-PROCESS-sourcecode1.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-ouput-result-screen.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-ouput-result-screen.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-ouput-result-screen1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-ouput-result-screen1.png',
   'DATA'),
  ('pygame/docs/generated/_images/Basic-ouput-sourcecode.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Basic-ouput-sourcecode.png',
   'DATA'),
  ('pygame/docs/generated/_images/Basic-ouput-sourcecode1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Basic-ouput-sourcecode1.png',
   'DATA'),
  ('pygame/docs/generated/_images/angle_to.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/angle_to.png',
   'DATA'),
  ('pygame/docs/generated/_images/camera_average.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_average.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_background.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_background.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_green.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_green.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_hsv.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_hsv.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_mask.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_mask.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_rgb.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_rgb.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_thresh.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_thresh.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_thresholded.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_thresholded.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_yuv.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_yuv.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/chimpshot.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/chimpshot.gif',
   'DATA'),
  ('pygame/docs/generated/_images/draw_module_example.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/draw_module_example.png',
   'DATA'),
  ('pygame/docs/generated/_images/intro_ball.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/intro_ball.gif',
   'DATA'),
  ('pygame/docs/generated/_images/intro_blade.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/intro_blade.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/intro_freedom.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/intro_freedom.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/introduction-Battleship.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/introduction-Battleship.png',
   'DATA'),
  ('pygame/docs/generated/_images/introduction-Battleship1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/introduction-Battleship1.png',
   'DATA'),
  ('pygame/docs/generated/_images/introduction-PuyoPuyo.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/introduction-PuyoPuyo.png',
   'DATA'),
  ('pygame/docs/generated/_images/introduction-PuyoPuyo1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/introduction-PuyoPuyo1.png',
   'DATA'),
  ('pygame/docs/generated/_images/introduction-TPS.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/introduction-TPS.png',
   'DATA'),
  ('pygame/docs/generated/_images/introduction-TPS1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/introduction-TPS1.png',
   'DATA'),
  ('pygame/docs/generated/_images/joystick_calls.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/joystick_calls.png',
   'DATA'),
  ('pygame/docs/generated/_images/pygame_lofi.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/pygame_lofi.png',
   'DATA'),
  ('pygame/docs/generated/_images/pygame_logo.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/pygame_logo.png',
   'DATA'),
  ('pygame/docs/generated/_images/pygame_powered.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/pygame_powered.png',
   'DATA'),
  ('pygame/docs/generated/_images/pygame_powered_lowres.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/pygame_powered_lowres.png',
   'DATA'),
  ('pygame/docs/generated/_images/pygame_tiny.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/pygame_tiny.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_allblack.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_allblack.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_flipped.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_flipped.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_redimg.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_redimg.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_rgbarray.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_rgbarray.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_scaledown.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_scaledown.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_scaleup.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_scaleup.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_soften.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_soften.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_striped.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_striped.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_xfade.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_xfade.png',
   'DATA'),
  ('pygame/docs/generated/_images/tom_basic.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/tom_basic.png',
   'DATA'),
  ('pygame/docs/generated/_images/tom_event-flowchart.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/tom_event-flowchart.png',
   'DATA'),
  ('pygame/docs/generated/_images/tom_formulae.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/tom_formulae.png',
   'DATA'),
  ('pygame/docs/generated/_images/tom_radians.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/tom_radians.png',
   'DATA'),
  ('pygame/docs/generated/_sources/c_api.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/c_api.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/filepaths.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/filepaths.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/index.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/index.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/logos.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/logos.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/bufferproxy.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/bufferproxy.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/camera.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/camera.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/cdrom.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/cdrom.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/color.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/color.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/color_list.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/color_list.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/cursors.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/cursors.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/display.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/display.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/draw.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/draw.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/event.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/event.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/examples.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/examples.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/fastevent.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/fastevent.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/font.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/font.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/freetype.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/freetype.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/gfxdraw.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/gfxdraw.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/image.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/image.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/joystick.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/joystick.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/key.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/key.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/locals.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/locals.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/mask.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/mask.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/math.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/math.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/midi.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/midi.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/mixer.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/mixer.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/mouse.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/mouse.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/music.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/music.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/overlay.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/overlay.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/pixelarray.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/pixelarray.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/pixelcopy.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/pixelcopy.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/pygame.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/pygame.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/rect.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/rect.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/scrap.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/scrap.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/sdl2_controller.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/sdl2_controller.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/sdl2_video.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/sdl2_video.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/sndarray.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/sndarray.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/sprite.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/sprite.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/surface.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/surface.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/surfarray.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/surfarray.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/tests.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/tests.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/time.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/time.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/touch.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/touch.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/transform.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/transform.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_static/_sphinx_javascript_frameworks_compat.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/_sphinx_javascript_frameworks_compat.js',
   'DATA'),
  ('pygame/docs/generated/_static/basic.css',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/basic.css',
   'DATA'),
  ('pygame/docs/generated/_static/doctools.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/doctools.js',
   'DATA'),
  ('pygame/docs/generated/_static/documentation_options.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/documentation_options.js',
   'DATA'),
  ('pygame/docs/generated/_static/file.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/file.png',
   'DATA'),
  ('pygame/docs/generated/_static/jquery-3.6.0.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/jquery-3.6.0.js',
   'DATA'),
  ('pygame/docs/generated/_static/jquery.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/jquery.js',
   'DATA'),
  ('pygame/docs/generated/_static/language_data.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/language_data.js',
   'DATA'),
  ('pygame/docs/generated/_static/legacy_logos.zip',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/legacy_logos.zip',
   'DATA'),
  ('pygame/docs/generated/_static/minus.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/minus.png',
   'DATA'),
  ('pygame/docs/generated/_static/plus.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/plus.png',
   'DATA'),
  ('pygame/docs/generated/_static/pygame.css',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame.css',
   'DATA'),
  ('pygame/docs/generated/_static/pygame.ico',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame.ico',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_lofi.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_lofi.png',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_lofi.svg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_lofi.svg',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_logo.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_logo.png',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_logo.svg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_logo.svg',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_powered.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_powered.png',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_powered.svg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_powered.svg',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_powered_lowres.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_powered_lowres.png',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_tiny.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_tiny.png',
   'DATA'),
  ('pygame/docs/generated/_static/pygments.css',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygments.css',
   'DATA'),
  ('pygame/docs/generated/_static/reset.css',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/reset.css',
   'DATA'),
  ('pygame/docs/generated/_static/searchtools.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/searchtools.js',
   'DATA'),
  ('pygame/docs/generated/_static/sphinx_highlight.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/sphinx_highlight.js',
   'DATA'),
  ('pygame/docs/generated/_static/tooltip.css',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/tooltip.css',
   'DATA'),
  ('pygame/docs/generated/_static/underscore-1.13.1.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/underscore-1.13.1.js',
   'DATA'),
  ('pygame/docs/generated/_static/underscore.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/underscore.js',
   'DATA'),
  ('pygame/docs/generated/c_api.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api.html',
   'DATA'),
  ('pygame/docs/generated/c_api/base.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/base.html',
   'DATA'),
  ('pygame/docs/generated/c_api/bufferproxy.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/bufferproxy.html',
   'DATA'),
  ('pygame/docs/generated/c_api/color.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/color.html',
   'DATA'),
  ('pygame/docs/generated/c_api/display.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/display.html',
   'DATA'),
  ('pygame/docs/generated/c_api/event.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/event.html',
   'DATA'),
  ('pygame/docs/generated/c_api/freetype.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/freetype.html',
   'DATA'),
  ('pygame/docs/generated/c_api/mixer.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/mixer.html',
   'DATA'),
  ('pygame/docs/generated/c_api/rect.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/rect.html',
   'DATA'),
  ('pygame/docs/generated/c_api/rwobject.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/rwobject.html',
   'DATA'),
  ('pygame/docs/generated/c_api/slots.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/slots.html',
   'DATA'),
  ('pygame/docs/generated/c_api/surface.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/surface.html',
   'DATA'),
  ('pygame/docs/generated/c_api/surflock.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/surflock.html',
   'DATA'),
  ('pygame/docs/generated/c_api/version.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/version.html',
   'DATA'),
  ('pygame/docs/generated/filepaths.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/filepaths.html',
   'DATA'),
  ('pygame/docs/generated/genindex.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/genindex.html',
   'DATA'),
  ('pygame/docs/generated/index.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/index.html',
   'DATA'),
  ('pygame/docs/generated/logos.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/logos.html',
   'DATA'),
  ('pygame/docs/generated/py-modindex.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/py-modindex.html',
   'DATA'),
  ('pygame/docs/generated/ref/bufferproxy.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/bufferproxy.html',
   'DATA'),
  ('pygame/docs/generated/ref/camera.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/camera.html',
   'DATA'),
  ('pygame/docs/generated/ref/cdrom.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/cdrom.html',
   'DATA'),
  ('pygame/docs/generated/ref/color.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/color.html',
   'DATA'),
  ('pygame/docs/generated/ref/color_list.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/color_list.html',
   'DATA'),
  ('pygame/docs/generated/ref/cursors.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/cursors.html',
   'DATA'),
  ('pygame/docs/generated/ref/display.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/display.html',
   'DATA'),
  ('pygame/docs/generated/ref/draw.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/draw.html',
   'DATA'),
  ('pygame/docs/generated/ref/event.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/event.html',
   'DATA'),
  ('pygame/docs/generated/ref/examples.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/examples.html',
   'DATA'),
  ('pygame/docs/generated/ref/fastevent.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/fastevent.html',
   'DATA'),
  ('pygame/docs/generated/ref/font.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/font.html',
   'DATA'),
  ('pygame/docs/generated/ref/freetype.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/freetype.html',
   'DATA'),
  ('pygame/docs/generated/ref/gfxdraw.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/gfxdraw.html',
   'DATA'),
  ('pygame/docs/generated/ref/image.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/image.html',
   'DATA'),
  ('pygame/docs/generated/ref/joystick.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/joystick.html',
   'DATA'),
  ('pygame/docs/generated/ref/key.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/key.html',
   'DATA'),
  ('pygame/docs/generated/ref/locals.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/locals.html',
   'DATA'),
  ('pygame/docs/generated/ref/mask.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/mask.html',
   'DATA'),
  ('pygame/docs/generated/ref/math.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/math.html',
   'DATA'),
  ('pygame/docs/generated/ref/midi.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/midi.html',
   'DATA'),
  ('pygame/docs/generated/ref/mixer.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/mixer.html',
   'DATA'),
  ('pygame/docs/generated/ref/mouse.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/mouse.html',
   'DATA'),
  ('pygame/docs/generated/ref/music.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/music.html',
   'DATA'),
  ('pygame/docs/generated/ref/overlay.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/overlay.html',
   'DATA'),
  ('pygame/docs/generated/ref/pixelarray.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/pixelarray.html',
   'DATA'),
  ('pygame/docs/generated/ref/pixelcopy.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/pixelcopy.html',
   'DATA'),
  ('pygame/docs/generated/ref/pygame.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/pygame.html',
   'DATA'),
  ('pygame/docs/generated/ref/rect.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/rect.html',
   'DATA'),
  ('pygame/docs/generated/ref/scrap.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/scrap.html',
   'DATA'),
  ('pygame/docs/generated/ref/sdl2_controller.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/sdl2_controller.html',
   'DATA'),
  ('pygame/docs/generated/ref/sdl2_video.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/sdl2_video.html',
   'DATA'),
  ('pygame/docs/generated/ref/sndarray.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/sndarray.html',
   'DATA'),
  ('pygame/docs/generated/ref/sprite.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/sprite.html',
   'DATA'),
  ('pygame/docs/generated/ref/surface.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/surface.html',
   'DATA'),
  ('pygame/docs/generated/ref/surfarray.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/surfarray.html',
   'DATA'),
  ('pygame/docs/generated/ref/tests.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/tests.html',
   'DATA'),
  ('pygame/docs/generated/ref/time.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/time.html',
   'DATA'),
  ('pygame/docs/generated/ref/touch.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/touch.html',
   'DATA'),
  ('pygame/docs/generated/ref/transform.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/transform.html',
   'DATA'),
  ('pygame/docs/generated/search.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/search.html',
   'DATA'),
  ('pygame/docs/generated/searchindex.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/searchindex.js',
   'DATA'),
  ('pygame/docs/generated/tut/CameraIntro.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/CameraIntro.html',
   'DATA'),
  ('pygame/docs/generated/tut/ChimpLineByLine.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/ChimpLineByLine.html',
   'DATA'),
  ('pygame/docs/generated/tut/DisplayModes.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/DisplayModes.html',
   'DATA'),
  ('pygame/docs/generated/tut/ImportInit.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/ImportInit.html',
   'DATA'),
  ('pygame/docs/generated/tut/MakeGames.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/MakeGames.html',
   'DATA'),
  ('pygame/docs/generated/tut/MoveIt.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/MoveIt.html',
   'DATA'),
  ('pygame/docs/generated/tut/PygameIntro.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/PygameIntro.html',
   'DATA'),
  ('pygame/docs/generated/tut/SpriteIntro.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/SpriteIntro.html',
   'DATA'),
  ('pygame/docs/generated/tut/SurfarrayIntro.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/SurfarrayIntro.html',
   'DATA'),
  ('pygame/docs/generated/tut/chimp.py.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/chimp.py.html',
   'DATA'),
  ('pygame/docs/generated/tut/newbieguide.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/newbieguide.html',
   'DATA'),
  ('pygame/docs/generated/tut/tom_games2.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/tom_games2.html',
   'DATA'),
  ('pygame/docs/generated/tut/tom_games3.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/tom_games3.html',
   'DATA'),
  ('pygame/docs/generated/tut/tom_games4.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/tom_games4.html',
   'DATA'),
  ('pygame/docs/generated/tut/tom_games5.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/tom_games5.html',
   'DATA'),
  ('pygame/docs/generated/tut/tom_games6.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/tom_games6.html',
   'DATA'),
  ('pygame/draw.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/draw.pyi',
   'DATA'),
  ('pygame/event.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/event.pyi',
   'DATA'),
  ('pygame/examples/README.rst',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/README.rst',
   'DATA'),
  ('pygame/examples/data/BGR.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/BGR.png',
   'DATA'),
  ('pygame/examples/data/alien1.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/alien1.gif',
   'DATA'),
  ('pygame/examples/data/alien1.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/alien1.jpg',
   'DATA'),
  ('pygame/examples/data/alien1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/alien1.png',
   'DATA'),
  ('pygame/examples/data/alien2.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/alien2.gif',
   'DATA'),
  ('pygame/examples/data/alien2.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/alien2.png',
   'DATA'),
  ('pygame/examples/data/alien3.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/alien3.gif',
   'DATA'),
  ('pygame/examples/data/alien3.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/alien3.png',
   'DATA'),
  ('pygame/examples/data/arraydemo.bmp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/arraydemo.bmp',
   'DATA'),
  ('pygame/examples/data/asprite.bmp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/asprite.bmp',
   'DATA'),
  ('pygame/examples/data/background.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/background.gif',
   'DATA'),
  ('pygame/examples/data/black.ppm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/black.ppm',
   'DATA'),
  ('pygame/examples/data/blue.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/blue.gif',
   'DATA'),
  ('pygame/examples/data/blue.mpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/blue.mpg',
   'DATA'),
  ('pygame/examples/data/bomb.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/bomb.gif',
   'DATA'),
  ('pygame/examples/data/boom.wav',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/boom.wav',
   'DATA'),
  ('pygame/examples/data/brick.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/brick.png',
   'DATA'),
  ('pygame/examples/data/car_door.wav',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/car_door.wav',
   'DATA'),
  ('pygame/examples/data/chimp.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/chimp.png',
   'DATA'),
  ('pygame/examples/data/city.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/city.png',
   'DATA'),
  ('pygame/examples/data/crimson.pnm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/crimson.pnm',
   'DATA'),
  ('pygame/examples/data/cursor.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/cursor.png',
   'DATA'),
  ('pygame/examples/data/danger.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/danger.gif',
   'DATA'),
  ('pygame/examples/data/explosion1.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/explosion1.gif',
   'DATA'),
  ('pygame/examples/data/fist.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/fist.png',
   'DATA'),
  ('pygame/examples/data/green.pcx',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/green.pcx',
   'DATA'),
  ('pygame/examples/data/grey.pgm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/grey.pgm',
   'DATA'),
  ('pygame/examples/data/house_lo.mp3',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/house_lo.mp3',
   'DATA'),
  ('pygame/examples/data/house_lo.ogg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/house_lo.ogg',
   'DATA'),
  ('pygame/examples/data/house_lo.wav',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/house_lo.wav',
   'DATA'),
  ('pygame/examples/data/laplacian.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/laplacian.png',
   'DATA'),
  ('pygame/examples/data/liquid.bmp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/liquid.bmp',
   'DATA'),
  ('pygame/examples/data/midikeys.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/midikeys.png',
   'DATA'),
  ('pygame/examples/data/player1.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/player1.gif',
   'DATA'),
  ('pygame/examples/data/punch.wav',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/punch.wav',
   'DATA'),
  ('pygame/examples/data/purple.xpm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/purple.xpm',
   'DATA'),
  ('pygame/examples/data/red.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/red.jpg',
   'DATA'),
  ('pygame/examples/data/sans.ttf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/sans.ttf',
   'DATA'),
  ('pygame/examples/data/scarlet.webp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/scarlet.webp',
   'DATA'),
  ('pygame/examples/data/secosmic_lo.wav',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/secosmic_lo.wav',
   'DATA'),
  ('pygame/examples/data/shot.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/shot.gif',
   'DATA'),
  ('pygame/examples/data/static.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/static.png',
   'DATA'),
  ('pygame/examples/data/teal.svg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/teal.svg',
   'DATA'),
  ('pygame/examples/data/turquoise.tif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/turquoise.tif',
   'DATA'),
  ('pygame/examples/data/whiff.wav',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/whiff.wav',
   'DATA'),
  ('pygame/examples/data/yellow.tga',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/yellow.tga',
   'DATA'),
  ('pygame/fastevent.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/fastevent.pyi',
   'DATA'),
  ('pygame/font.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/font.pyi',
   'DATA'),
  ('pygame/freesansbold.ttf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/freesansbold.ttf',
   'DATA'),
  ('pygame/freetype.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/freetype.pyi',
   'DATA'),
  ('pygame/gfxdraw.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/gfxdraw.pyi',
   'DATA'),
  ('pygame/image.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/image.pyi',
   'DATA'),
  ('pygame/joystick.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/joystick.pyi',
   'DATA'),
  ('pygame/key.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/key.pyi',
   'DATA'),
  ('pygame/locals.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/locals.pyi',
   'DATA'),
  ('pygame/mask.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/mask.pyi',
   'DATA'),
  ('pygame/math.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/math.pyi',
   'DATA'),
  ('pygame/midi.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/midi.pyi',
   'DATA'),
  ('pygame/mixer.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/mixer.pyi',
   'DATA'),
  ('pygame/mixer_music.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/mixer_music.pyi',
   'DATA'),
  ('pygame/mouse.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/mouse.pyi',
   'DATA'),
  ('pygame/pixelarray.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pixelarray.pyi',
   'DATA'),
  ('pygame/pixelcopy.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pixelcopy.pyi',
   'DATA'),
  ('pygame/py.typed',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/py.typed',
   'DATA'),
  ('pygame/pygame.ico',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pygame.ico',
   'DATA'),
  ('pygame/pygame_icon.bmp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pygame_icon.bmp',
   'DATA'),
  ('pygame/pygame_icon.icns',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pygame_icon.icns',
   'DATA'),
  ('pygame/pygame_icon_mac.bmp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pygame_icon_mac.bmp',
   'DATA'),
  ('pygame/rect.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/rect.pyi',
   'DATA'),
  ('pygame/rwobject.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/rwobject.pyi',
   'DATA'),
  ('pygame/scrap.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/scrap.pyi',
   'DATA'),
  ('pygame/sndarray.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/sndarray.pyi',
   'DATA'),
  ('pygame/sprite.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/sprite.pyi',
   'DATA'),
  ('pygame/surface.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/surface.pyi',
   'DATA'),
  ('pygame/surfarray.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/surfarray.pyi',
   'DATA'),
  ('pygame/surflock.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/surflock.pyi',
   'DATA'),
  ('pygame/tests/fixtures/fonts/A_PyGameMono-8.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/A_PyGameMono-8.png',
   'DATA'),
  ('pygame/tests/fixtures/fonts/PlayfairDisplaySemibold.ttf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/PlayfairDisplaySemibold.ttf',
   'DATA'),
  ('pygame/tests/fixtures/fonts/PyGameMono-18-100dpi.bdf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/PyGameMono-18-100dpi.bdf',
   'DATA'),
  ('pygame/tests/fixtures/fonts/PyGameMono-18-75dpi.bdf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/PyGameMono-18-75dpi.bdf',
   'DATA'),
  ('pygame/tests/fixtures/fonts/PyGameMono-8.bdf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/PyGameMono-8.bdf',
   'DATA'),
  ('pygame/tests/fixtures/fonts/PyGameMono.otf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/PyGameMono.otf',
   'DATA'),
  ('pygame/tests/fixtures/fonts/test_fixed.otf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/test_fixed.otf',
   'DATA'),
  ('pygame/tests/fixtures/fonts/test_sans.ttf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/test_sans.ttf',
   'DATA'),
  ('pygame/tests/fixtures/fonts/u13079_PyGameMono-8.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/u13079_PyGameMono-8.png',
   'DATA'),
  ('pygame/tests/fixtures/xbm_cursors/white_sizing.xbm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/xbm_cursors/white_sizing.xbm',
   'DATA'),
  ('pygame/tests/fixtures/xbm_cursors/white_sizing_mask.xbm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/xbm_cursors/white_sizing_mask.xbm',
   'DATA'),
  ('pygame/time.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/time.pyi',
   'DATA'),
  ('pygame/transform.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/transform.pyi',
   'DATA'),
  ('pygame/version.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/version.pyi',
   'DATA'),
  ('vlc/share/hrtfs/dodeca_and_7channel_3DSL_HRTF.sofa',
   '/Applications/VLC.app/Contents/MacOS/share/hrtfs/dodeca_and_7channel_3DSL_HRTF.sofa',
   'DATA'),
  ('vlc/share/locale/ach/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ach/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/af/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/af/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/am/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/am/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/am_ET/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/am_ET/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/an/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/an/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ar/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ar/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/as_IN/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/as_IN/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ast/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ast/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/be/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/be/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/bg/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/bg/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/bn/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/bn/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/bn_IN/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/bn_IN/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/br/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/br/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/brx/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/brx/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/bs/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/bs/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ca/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ca/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ca@valencia/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ca@valencia/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/cgg/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/cgg/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/co/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/co/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/cs/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/cs/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/cy/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/cy/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/da/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/da/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/de/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/de/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/el/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/el/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/en_GB/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/en_GB/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/eo/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/eo/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/es/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/es/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/es_MX/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/es_MX/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/et/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/et/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/eu/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/eu/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/fa/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/fa/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ff/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ff/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/fi/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/fi/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/fr/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/fr/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/fur/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/fur/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/fy/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/fy/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ga/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ga/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/gd/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/gd/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/gl/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/gl/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/gu/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/gu/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/he/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/he/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/hi/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/hi/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/hr/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/hr/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/hu/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/hu/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/hy/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/hy/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/id/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/id/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ie/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ie/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/is/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/is/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/it/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/it/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ja/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ja/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ka/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ka/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/kab/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/kab/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/kk/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/kk/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/km/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/km/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/kn/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/kn/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ko/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ko/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ks_IN/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ks_IN/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ku_IQ/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ku_IQ/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ky/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ky/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/lg/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/lg/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/lo/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/lo/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/lt/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/lt/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/lv/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/lv/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/mai/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/mai/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/mk/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/mk/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ml/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ml/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/mn/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/mn/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/mr/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/mr/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ms/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ms/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/my/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/my/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/nb/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/nb/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ne/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ne/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/nl/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/nl/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/nn/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/nn/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/oc/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/oc/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/or_IN/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/or_IN/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/pa/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/pa/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/pl/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/pl/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ps/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ps/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/pt_BR/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/pt_BR/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/pt_PT/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/pt_PT/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ro/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ro/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ru/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ru/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/si/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/si/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/sk/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/sk/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/sl/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/sl/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/sm/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/sm/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/sq/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/sq/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/sr/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/sr/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/sv/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/sv/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/sw/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/sw/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ta/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ta/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/te/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/te/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/th/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/th/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/tr/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/tr/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/tt/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/tt/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ug/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ug/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/uk/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/uk/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/uz/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/uz/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/vi/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/vi/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/wa/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/wa/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/zh_CN/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/zh_CN/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/zh_TW/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/zh_TW/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/zu/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/zu/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/lua/extensions/VLSub.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/extensions/VLSub.luac',
   'DATA'),
  ('vlc/share/lua/http/css/main.css',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/main.css',
   'DATA'),
  ('vlc/share/lua/http/css/mobile.css',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/mobile.css',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_diagonals-thick_18_b81900_40x40.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_diagonals-thick_18_b81900_40x40.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_diagonals-thick_20_666666_40x40.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_diagonals-thick_20_666666_40x40.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_flat_10_000000_40x100.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_flat_10_000000_40x100.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_glass_100_f6f6f6_1x400.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_glass_100_f6f6f6_1x400.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_glass_100_fdf5ce_1x400.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_glass_100_fdf5ce_1x400.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_glass_65_ffffff_1x400.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_glass_65_ffffff_1x400.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_gloss-wave_35_f6a828_500x100.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_gloss-wave_35_f6a828_500x100.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_highlight-soft_100_eeeeee_1x100.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_highlight-soft_100_eeeeee_1x100.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_highlight-soft_75_ffe45c_1x100.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_highlight-soft_75_ffe45c_1x100.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-icons_222222_256x240.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-icons_222222_256x240.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-icons_228ef1_256x240.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-icons_228ef1_256x240.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-icons_ef8c08_256x240.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-icons_ef8c08_256x240.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-icons_ffd27a_256x240.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-icons_ffd27a_256x240.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-icons_ffffff_256x240.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-icons_ffffff_256x240.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/jquery-ui-1.8.13.custom.css',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/jquery-ui-1.8.13.custom.css',
   'DATA'),
  ('vlc/share/lua/http/custom.lua',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/custom.lua',
   'DATA'),
  ('vlc/share/lua/http/dialogs/batch_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/batch_window.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/browse_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/browse_window.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/create_stream.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/create_stream.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/equalizer_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/equalizer_window.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/error_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/error_window.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/mosaic_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/mosaic_window.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/offset_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/offset_window.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/stream_config_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/stream_config_window.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/stream_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/stream_window.html',
   'DATA'),
  ('vlc/share/lua/http/favicon.ico',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/favicon.ico',
   'DATA'),
  ('vlc/share/lua/http/images/Audio-48.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/Audio-48.png',
   'DATA'),
  ('vlc/share/lua/http/images/Back-48.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/Back-48.png',
   'DATA'),
  ('vlc/share/lua/http/images/Folder-48.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/Folder-48.png',
   'DATA'),
  ('vlc/share/lua/http/images/Other-48.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/Other-48.png',
   'DATA'),
  ('vlc/share/lua/http/images/Video-48.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/Video-48.png',
   'DATA'),
  ('vlc/share/lua/http/images/buttons.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/buttons.png',
   'DATA'),
  ('vlc/share/lua/http/images/speaker-32.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/speaker-32.png',
   'DATA'),
  ('vlc/share/lua/http/images/vlc-48.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/vlc-48.png',
   'DATA'),
  ('vlc/share/lua/http/images/vlc16x16.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/vlc16x16.png',
   'DATA'),
  ('vlc/share/lua/http/index.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/index.html',
   'DATA'),
  ('vlc/share/lua/http/js/common.js',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/js/common.js',
   'DATA'),
  ('vlc/share/lua/http/js/controllers.js',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/js/controllers.js',
   'DATA'),
  ('vlc/share/lua/http/js/jquery.jstree.js',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/js/jquery.jstree.js',
   'DATA'),
  ('vlc/share/lua/http/js/ui.js',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/js/ui.js',
   'DATA'),
  ('vlc/share/lua/http/mobile.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/mobile.html',
   'DATA'),
  ('vlc/share/lua/http/mobile_browse.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/mobile_browse.html',
   'DATA'),
  ('vlc/share/lua/http/mobile_equalizer.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/mobile_equalizer.html',
   'DATA'),
  ('vlc/share/lua/http/mobile_view.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/mobile_view.html',
   'DATA'),
  ('vlc/share/lua/http/requests/README.txt',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/README.txt',
   'DATA'),
  ('vlc/share/lua/http/requests/browse.json',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/browse.json',
   'DATA'),
  ('vlc/share/lua/http/requests/browse.xml',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/browse.xml',
   'DATA'),
  ('vlc/share/lua/http/requests/playlist.json',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/playlist.json',
   'DATA'),
  ('vlc/share/lua/http/requests/playlist.xml',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/playlist.xml',
   'DATA'),
  ('vlc/share/lua/http/requests/playlist_jstree.xml',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/playlist_jstree.xml',
   'DATA'),
  ('vlc/share/lua/http/requests/status.json',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/status.json',
   'DATA'),
  ('vlc/share/lua/http/requests/status.xml',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/status.xml',
   'DATA'),
  ('vlc/share/lua/http/requests/vlm.xml',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/vlm.xml',
   'DATA'),
  ('vlc/share/lua/http/requests/vlm_cmd.xml',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/vlm_cmd.xml',
   'DATA'),
  ('vlc/share/lua/http/view.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/view.html',
   'DATA'),
  ('vlc/share/lua/http/vlm.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/vlm.html',
   'DATA'),
  ('vlc/share/lua/http/vlm_export.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/vlm_export.html',
   'DATA'),
  ('vlc/share/lua/intf/cli.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/cli.luac',
   'DATA'),
  ('vlc/share/lua/intf/dummy.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/dummy.luac',
   'DATA'),
  ('vlc/share/lua/intf/dumpmeta.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/dumpmeta.luac',
   'DATA'),
  ('vlc/share/lua/intf/http.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/http.luac',
   'DATA'),
  ('vlc/share/lua/intf/luac.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/luac.luac',
   'DATA'),
  ('vlc/share/lua/intf/modules/host.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/modules/host.luac',
   'DATA'),
  ('vlc/share/lua/intf/modules/httprequests.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/modules/httprequests.luac',
   'DATA'),
  ('vlc/share/lua/intf/telnet.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/telnet.luac',
   'DATA'),
  ('vlc/share/lua/meta/art/00_musicbrainz.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/meta/art/00_musicbrainz.luac',
   'DATA'),
  ('vlc/share/lua/meta/art/01_googleimage.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/meta/art/01_googleimage.luac',
   'DATA'),
  ('vlc/share/lua/meta/art/02_frenchtv.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/meta/art/02_frenchtv.luac',
   'DATA'),
  ('vlc/share/lua/meta/art/03_lastfm.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/meta/art/03_lastfm.luac',
   'DATA'),
  ('vlc/share/lua/meta/reader/filename.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/meta/reader/filename.luac',
   'DATA'),
  ('vlc/share/lua/modules/common.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/modules/common.luac',
   'DATA'),
  ('vlc/share/lua/modules/dkjson.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/modules/dkjson.luac',
   'DATA'),
  ('vlc/share/lua/modules/sandbox.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/modules/sandbox.luac',
   'DATA'),
  ('vlc/share/lua/modules/simplexml.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/modules/simplexml.luac',
   'DATA'),
  ('vlc/share/lua/playlist/anevia_streams.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/anevia_streams.luac',
   'DATA'),
  ('vlc/share/lua/playlist/anevia_xml.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/anevia_xml.luac',
   'DATA'),
  ('vlc/share/lua/playlist/appletrailers.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/appletrailers.luac',
   'DATA'),
  ('vlc/share/lua/playlist/bbc_co_uk.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/bbc_co_uk.luac',
   'DATA'),
  ('vlc/share/lua/playlist/cue.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/cue.luac',
   'DATA'),
  ('vlc/share/lua/playlist/dailymotion.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/dailymotion.luac',
   'DATA'),
  ('vlc/share/lua/playlist/jamendo.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/jamendo.luac',
   'DATA'),
  ('vlc/share/lua/playlist/koreus.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/koreus.luac',
   'DATA'),
  ('vlc/share/lua/playlist/liveleak.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/liveleak.luac',
   'DATA'),
  ('vlc/share/lua/playlist/newgrounds.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/newgrounds.luac',
   'DATA'),
  ('vlc/share/lua/playlist/rockbox_fm_presets.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/rockbox_fm_presets.luac',
   'DATA'),
  ('vlc/share/lua/playlist/soundcloud.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/soundcloud.luac',
   'DATA'),
  ('vlc/share/lua/playlist/twitch.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/twitch.luac',
   'DATA'),
  ('vlc/share/lua/playlist/vimeo.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/vimeo.luac',
   'DATA'),
  ('vlc/share/lua/playlist/vocaroo.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/vocaroo.luac',
   'DATA'),
  ('vlc/share/lua/playlist/youtube.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/youtube.luac',
   'DATA'),
  ('vlc/share/lua/sd/icecast.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/sd/icecast.luac',
   'DATA'),
  ('vlc/share/lua/sd/jamendo.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/sd/jamendo.luac',
   'DATA')],
 '3.12.9 (main, Feb  4 2025, 14:38:38) [Clang 16.0.0 (clang-1600.0.26.6)]',
 [('vlc_runtime_hook',
   '/Users/<USER>/coding/yesoncheck/vlc_runtime_hook.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('YesonCheck_final_mkvaudio_20250613',
   '/Users/<USER>/coding/yesoncheck/YesonCheck_final_mkvaudio_20250613.py',
   'PYSOURCE')],
 [('_pyi_rth_utils.qt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/qt.py',
   'PYMODULE'),
  ('importlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('typing',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/typing.py',
   'PYMODULE'),
  ('importlib.abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('importlib._abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_text.py',
   'PYMODULE'),
  ('email.message',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/quoprimime.py',
   'PYMODULE'),
  ('string',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/string.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/__init__.py',
   'PYMODULE'),
  ('email.iterators',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/generator.py',
   'PYMODULE'),
  ('copy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/copy.py',
   'PYMODULE'),
  ('random',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/random.py',
   'PYMODULE'),
  ('statistics',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/statistics.py',
   'PYMODULE'),
  ('decimal',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextvars.py',
   'PYMODULE'),
  ('fractions',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/fractions.py',
   'PYMODULE'),
  ('numbers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/numbers.py',
   'PYMODULE'),
  ('hashlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/hashlib.py',
   'PYMODULE'),
  ('logging',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/__init__.py',
   'PYMODULE'),
  ('pickle',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pprint.py',
   'PYMODULE'),
  ('dataclasses',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_compat_pickle.py',
   'PYMODULE'),
  ('struct',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/struct.py',
   'PYMODULE'),
  ('bisect',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bisect.py',
   'PYMODULE'),
  ('_strptime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_strptime.py',
   'PYMODULE'),
  ('datetime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_pydatetime.py',
   'PYMODULE'),
  ('calendar',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/calendar.py',
   'PYMODULE'),
  ('argparse',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/argparse.py',
   'PYMODULE'),
  ('gettext',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/gettext.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_encoded_words.py',
   'PYMODULE'),
  ('base64',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/base64.py',
   'PYMODULE'),
  ('getopt',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/getopt.py',
   'PYMODULE'),
  ('email.charset',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_policybase.py',
   'PYMODULE'),
  ('email.header',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/header.py',
   'PYMODULE'),
  ('email.errors',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/errors.py',
   'PYMODULE'),
  ('email.utils',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/parse.py',
   'PYMODULE'),
  ('ipaddress',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ipaddress.py',
   'PYMODULE'),
  ('socket',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/socket.py',
   'PYMODULE'),
  ('selectors',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/selectors.py',
   'PYMODULE'),
  ('quopri',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/quopri.py',
   'PYMODULE'),
  ('inspect',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/inspect.py',
   'PYMODULE'),
  ('token',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/token.py',
   'PYMODULE'),
  ('dis',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/dis.py',
   'PYMODULE'),
  ('opcode',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/opcode.py',
   'PYMODULE'),
  ('ast',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ast.py',
   'PYMODULE'),
  ('contextlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextlib.py',
   'PYMODULE'),
  ('textwrap',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/textwrap.py',
   'PYMODULE'),
  ('zipfile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/_path/glob.py',
   'PYMODULE'),
  ('py_compile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/py_compile.py',
   'PYMODULE'),
  ('lzma',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lzma.py',
   'PYMODULE'),
  ('_compression',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_compression.py',
   'PYMODULE'),
  ('bz2',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bz2.py',
   'PYMODULE'),
  ('importlib.util',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/util.py',
   'PYMODULE'),
  ('pathlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pathlib.py',
   'PYMODULE'),
  ('fnmatch',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/fnmatch.py',
   'PYMODULE'),
  ('email',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/parser.py',
   'PYMODULE'),
  ('email.feedparser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/feedparser.py',
   'PYMODULE'),
  ('csv',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/csv.py',
   'PYMODULE'),
  ('importlib.readers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('tokenize',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tokenize.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_bootstrap.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/__init__.py',
   'PYMODULE'),
  ('pkg_resources',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pkg_resources/__init__.py',
   'PYMODULE'),
  ('packaging.tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('packaging.metadata',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/metadata.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/licenses/_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/licenses/__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('sysconfig',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/sysconfig.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_aix_support',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_aix_support.py',
   'PYMODULE'),
  ('_osx_support',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_osx_support.py',
   'PYMODULE'),
  ('typing_extensions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/unix.py',
   'PYMODULE'),
  ('configparser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/configparser.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/windows.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/msvc.py',
   'PYMODULE'),
  ('more_itertools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/more_itertools/__init__.py',
   'PYMODULE'),
  ('more_itertools.recipes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/more_itertools/recipes.py',
   'PYMODULE'),
  ('more_itertools.more',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/more_itertools/more.py',
   'PYMODULE'),
  ('queue',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/queue.py',
   'PYMODULE'),
  ('json',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/__init__.py',
   'PYMODULE'),
  ('json.encoder',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/encoder.py',
   'PYMODULE'),
  ('json.decoder',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/scanner.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/command/build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/archive_util.py',
   'PYMODULE'),
  ('tarfile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tarfile.py',
   'PYMODULE'),
  ('gzip',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/gzip.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/file_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/mock.py',
   'PYMODULE'),
  ('unittest',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/signals.py',
   'PYMODULE'),
  ('signal',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/signal.py',
   'PYMODULE'),
  ('unittest.main',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/main.py',
   'PYMODULE'),
  ('unittest.runner',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/runner.py',
   'PYMODULE'),
  ('unittest.loader',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/loader.py',
   'PYMODULE'),
  ('unittest.suite',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/suite.py',
   'PYMODULE'),
  ('unittest.case',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/case.py',
   'PYMODULE'),
  ('unittest._log',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/_log.py',
   'PYMODULE'),
  ('difflib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/difflib.py',
   'PYMODULE'),
  ('unittest.result',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/result.py',
   'PYMODULE'),
  ('unittest.util',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/util.py',
   'PYMODULE'),
  ('asyncio',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/selector_events.py',
   'PYMODULE'),
  ('ssl',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ssl.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/constants.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/spawn.py',
   'PYMODULE'),
  ('runpy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/runpy.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/secrets.py',
   'PYMODULE'),
  ('hmac',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/hmac.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xmlrpc/client.py',
   'PYMODULE'),
  ('xmlrpc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.parsers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/request.py',
   'PYMODULE'),
  ('getpass',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/getpass.py',
   'PYMODULE'),
  ('nturl2path',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ftplib.py',
   'PYMODULE'),
  ('netrc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/netrc.py',
   'PYMODULE'),
  ('mimetypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/cookiejar.py',
   'PYMODULE'),
  ('http',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/__init__.py',
   'PYMODULE'),
  ('urllib.response',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/response.py',
   'PYMODULE'),
  ('urllib.error',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/error.py',
   'PYMODULE'),
  ('xml.sax',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('http.client',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/client.py',
   'PYMODULE'),
  ('multiprocessing',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/util.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/jaraco/functools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/compat/py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/extension.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/_modified.py',
   'PYMODULE'),
  ('site',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site.py',
   'PYMODULE'),
  ('sitecustomize',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/sitecustomize.py',
   'PYMODULE'),
  ('rlcompleter',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/webbrowser.py',
   'PYMODULE'),
  ('shlex',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/shlex.py',
   'PYMODULE'),
  ('http.server',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/server.py',
   'PYMODULE'),
  ('socketserver',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/socketserver.py',
   'PYMODULE'),
  ('html',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/html/entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pydoc_data/topics.py',
   'PYMODULE'),
  ('pydoc_data',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pydoc_data/__init__.py',
   'PYMODULE'),
  ('tty',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tty.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/_log.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_importlib.py',
   'PYMODULE'),
  ('importlib_metadata',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/_text.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/compat/py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/compat/py39.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/compat/__init__.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('zipp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/zipp/__init__.py',
   'PYMODULE'),
  ('zipp._functools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/zipp/_functools.py',
   'PYMODULE'),
  ('zipp.glob',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/zipp/glob.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/zipp/compat/py310.py',
   'PYMODULE'),
  ('zipp.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/zipp/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/extension.py',
   'PYMODULE'),
  ('setuptools._path',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel._setuptools_logging',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/_requirestxt.py',
   'PYMODULE'),
  ('setuptools.glob',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/command/build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/compat/py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/compat/py311.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.bdist_wheel',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/log.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/expand.py',
   'PYMODULE'),
  ('setuptools.errors',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/errors.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/formats.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/tomli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/tomli/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/tomli/_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/tomli/_re.py',
   'PYMODULE'),
  ('tomllib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tomllib/__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tomllib/_parser.py',
   'PYMODULE'),
  ('tomllib._types',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tomllib/_types.py',
   'PYMODULE'),
  ('tomllib._re',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tomllib/_re.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_static.py',
   'PYMODULE'),
  ('glob',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/glob.py',
   'PYMODULE'),
  ('setuptools._shutil',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/windows_support.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/wintypes.py',
   'PYMODULE'),
  ('setuptools.command',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/_distutils_hack/override.py',
   'PYMODULE'),
  ('_distutils_hack',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/_distutils_hack/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/api.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py',
   'PYMODULE'),
  ('backports',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('importlib_resources',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/__init__.py',
   'PYMODULE'),
  ('importlib_resources.abc',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/abc.py',
   'PYMODULE'),
  ('importlib_resources._functional',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/_functional.py',
   'PYMODULE'),
  ('importlib_resources._common',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/_common.py',
   'PYMODULE'),
  ('importlib_resources.future.adapters',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/future/adapters.py',
   'PYMODULE'),
  ('importlib_resources.future',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/future/__init__.py',
   'PYMODULE'),
  ('importlib_resources.readers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/readers.py',
   'PYMODULE'),
  ('importlib_resources.compat.py39',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/compat/py39.py',
   'PYMODULE'),
  ('importlib_resources.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/compat/__init__.py',
   'PYMODULE'),
  ('importlib_resources._itertools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/_itertools.py',
   'PYMODULE'),
  ('importlib_resources._adapters',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/_adapters.py',
   'PYMODULE'),
  ('packaging.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/version.py',
   'PYMODULE'),
  ('packaging.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging.markers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('zipimport',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipimport.py',
   'PYMODULE'),
  ('plistlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/plistlib.py',
   'PYMODULE'),
  ('pkgutil',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pkgutil.py',
   'PYMODULE'),
  ('__future__',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/__future__.py',
   'PYMODULE'),
  ('pygame.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/version.py',
   'PYMODULE'),
  ('pygame.threads',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/threads/__init__.py',
   'PYMODULE'),
  ('pygame.tests.video_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/video_test.py',
   'PYMODULE'),
  ('pygame.tests.version_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/version_test.py',
   'PYMODULE'),
  ('pygame.tests.transform_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/transform_test.py',
   'PYMODULE'),
  ('pygame.tests.touch_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/touch_test.py',
   'PYMODULE'),
  ('pygame.tests.time_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/time_test.py',
   'PYMODULE'),
  ('pygame.tests.threads_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/threads_test.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.test_runner',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/test_runner.py',
   'PYMODULE'),
  ('optparse',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/optparse.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.test_machinery',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/test_machinery.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.run_tests',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/run_tests.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/png.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.endian',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/endian.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.buftools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/buftools.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.async_sub',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/async_sub.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.arrinter',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/arrinter.py',
   'PYMODULE'),
  ('pygame.tests.test_utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/__init__.py',
   'PYMODULE'),
  ('pygame.tests.sysfont_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/sysfont_test.py',
   'PYMODULE'),
  ('pygame.tests.surflock_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/surflock_test.py',
   'PYMODULE'),
  ('pygame.tests.surfarray_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/surfarray_test.py',
   'PYMODULE'),
  ('pygame.tests.surfarray_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/surfarray_tags.py',
   'PYMODULE'),
  ('pygame.tests.surface_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/surface_test.py',
   'PYMODULE'),
  ('pygame.tests.sprite_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/sprite_test.py',
   'PYMODULE'),
  ('pygame.tests.sndarray_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/sndarray_test.py',
   'PYMODULE'),
  ('pygame.tests.sndarray_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/sndarray_tags.py',
   'PYMODULE'),
  ('pygame.tests.scrap_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/scrap_test.py',
   'PYMODULE'),
  ('pygame.tests.scrap_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/scrap_tags.py',
   'PYMODULE'),
  ('pygame.tests.rwobject_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/rwobject_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.timeout.sleep_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/timeout/sleep_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.timeout.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/timeout/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.timeout',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/timeout/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.run_tests__test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/run_tests__test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stdout.fake_4_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stdout/fake_4_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stdout.fake_3_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stdout/fake_3_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stdout.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stdout/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stdout',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stdout/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stderr.fake_4_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stderr/fake_4_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stderr.fake_3_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stderr/fake_3_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stderr.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stderr/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stderr',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stderr/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.infinite_loop.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/infinite_loop/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.infinite_loop.fake_1_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/infinite_loop/fake_1_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.infinite_loop',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/infinite_loop/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.incomplete_todo.fake_3_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/incomplete_todo/fake_3_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.incomplete_todo.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/incomplete_todo/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.incomplete_todo',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/incomplete_todo/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.incomplete.fake_3_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/incomplete/fake_3_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.incomplete.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/incomplete/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.incomplete',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/incomplete/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.failures1.fake_4_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/failures1/fake_4_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.failures1.fake_3_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/failures1/fake_3_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.failures1.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/failures1/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.failures1',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/failures1/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.exclude.magic_tag_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/exclude/magic_tag_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.exclude.invisible_tag_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/exclude/invisible_tag_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.exclude.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/exclude/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.exclude',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/exclude/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.everything.sleep_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/everything/sleep_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.everything.magic_tag_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/everything/magic_tag_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.everything.incomplete_todo_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/everything/incomplete_todo_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.everything.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/everything/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.everything',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/everything/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok.zero_tests_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/zero_tests_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok.no_assertions__ret_code_of_1__test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/no_assertions__ret_code_of_1__test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok.fake_6_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/fake_6_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok.fake_5_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/fake_5_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok.fake_4_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/fake_4_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok.fake_3_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/fake_3_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/__init__.py',
   'PYMODULE'),
  ('pygame.tests.rect_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/rect_test.py',
   'PYMODULE'),
  ('pygame.tests.pixelcopy_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/pixelcopy_test.py',
   'PYMODULE'),
  ('pygame.tests.pixelarray_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/pixelarray_test.py',
   'PYMODULE'),
  ('pygame.tests.mouse_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/mouse_test.py',
   'PYMODULE'),
  ('pygame.tests.mixer_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/mixer_test.py',
   'PYMODULE'),
  ('pygame.tests.mixer_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/mixer_tags.py',
   'PYMODULE'),
  ('pygame.tests.mixer_music_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/mixer_music_test.py',
   'PYMODULE'),
  ('pygame.tests.mixer_music_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/mixer_music_tags.py',
   'PYMODULE'),
  ('pygame.tests.midi_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/midi_test.py',
   'PYMODULE'),
  ('pygame.tests.math_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/math_test.py',
   'PYMODULE'),
  ('pygame.tests.mask_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/mask_test.py',
   'PYMODULE'),
  ('pygame.tests.locals_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/locals_test.py',
   'PYMODULE'),
  ('pygame.tests.key_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/key_test.py',
   'PYMODULE'),
  ('pygame.tests.joystick_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/joystick_test.py',
   'PYMODULE'),
  ('pygame.tests.imageext_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/imageext_test.py',
   'PYMODULE'),
  ('pygame.tests.imageext_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/imageext_tags.py',
   'PYMODULE'),
  ('pygame.tests.image_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/image_test.py',
   'PYMODULE'),
  ('pygame.tests.image_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/image_tags.py',
   'PYMODULE'),
  ('pygame.tests.image__save_gl_surface_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/image__save_gl_surface_test.py',
   'PYMODULE'),
  ('pygame.tests.gfxdraw_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/gfxdraw_test.py',
   'PYMODULE'),
  ('pygame.tests.ftfont_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/ftfont_test.py',
   'PYMODULE'),
  ('pygame.tests.ftfont_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/ftfont_tags.py',
   'PYMODULE'),
  ('pygame.tests.freetype_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/freetype_test.py',
   'PYMODULE'),
  ('pygame.tests.freetype_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/freetype_tags.py',
   'PYMODULE'),
  ('pygame.tests.font_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/font_test.py',
   'PYMODULE'),
  ('pygame.tests.event_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/event_test.py',
   'PYMODULE'),
  ('pygame.tests.draw_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/draw_test.py',
   'PYMODULE'),
  ('pygame.tests.docs_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/docs_test.py',
   'PYMODULE'),
  ('pygame.docs.__main__',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/__main__.py',
   'PYMODULE'),
  ('pygame.docs', '-', 'PYMODULE'),
  ('pygame.tests.display_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/display_test.py',
   'PYMODULE'),
  ('pygame.tests.cursors_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/cursors_test.py',
   'PYMODULE'),
  ('pygame.tests.controller_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/controller_test.py',
   'PYMODULE'),
  ('pygame.tests.constants_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/constants_test.py',
   'PYMODULE'),
  ('pygame.tests.color_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/color_test.py',
   'PYMODULE'),
  ('pygame.tests.camera_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/camera_test.py',
   'PYMODULE'),
  ('pygame.tests.bufferproxy_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/bufferproxy_test.py',
   'PYMODULE'),
  ('pygame.tests.blit_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/blit_test.py',
   'PYMODULE'),
  ('pygame.tests.base_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/base_test.py',
   'PYMODULE'),
  ('pygame.tests.__main__',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/__main__.py',
   'PYMODULE'),
  ('pygame.tests',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/__init__.py',
   'PYMODULE'),
  ('test.__main__',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/__main__.py',
   'PYMODULE'),
  ('test',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/__init__.py',
   'PYMODULE'),
  ('test.support',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/support/__init__.py',
   'PYMODULE'),
  ('test.support.threading_helper',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/support/threading_helper.py',
   'PYMODULE'),
  ('test.support.import_helper',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/support/import_helper.py',
   'PYMODULE'),
  ('test.support.script_helper',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/support/script_helper.py',
   'PYMODULE'),
  ('tracemalloc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tracemalloc.py',
   'PYMODULE'),
  ('test.support.os_helper',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/support/os_helper.py',
   'PYMODULE'),
  ('test.libregrtest.main',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/main.py',
   'PYMODULE'),
  ('test.libregrtest',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/__init__.py',
   'PYMODULE'),
  ('test.libregrtest.run_workers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/run_workers.py',
   'PYMODULE'),
  ('test.libregrtest.worker',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/worker.py',
   'PYMODULE'),
  ('trace',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/trace.py',
   'PYMODULE'),
  ('test.libregrtest.utils',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/utils.py',
   'PYMODULE'),
  ('test.libregrtest.tsan',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/tsan.py',
   'PYMODULE'),
  ('test.libregrtest.single',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/single.py',
   'PYMODULE'),
  ('doctest',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/doctest.py',
   'PYMODULE'),
  ('pdb',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pdb.py',
   'PYMODULE'),
  ('code',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/code.py',
   'PYMODULE'),
  ('codeop',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/codeop.py',
   'PYMODULE'),
  ('bdb',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bdb.py',
   'PYMODULE'),
  ('cmd',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/cmd.py',
   'PYMODULE'),
  ('test.libregrtest.refleak',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/refleak.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/etree/ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/etree/cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/etree/ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/etree/ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/etree/__init__.py',
   'PYMODULE'),
  ('test.libregrtest.testresult',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/testresult.py',
   'PYMODULE'),
  ('test.libregrtest.save_env',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/save_env.py',
   'PYMODULE'),
  ('test.libregrtest.filter',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/filter.py',
   'PYMODULE'),
  ('test.libregrtest.setup',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/setup.py',
   'PYMODULE'),
  ('test.libregrtest.runtests',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/runtests.py',
   'PYMODULE'),
  ('test.libregrtest.results',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/results.py',
   'PYMODULE'),
  ('test.libregrtest.result',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/result.py',
   'PYMODULE'),
  ('test.libregrtest.pgo',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/pgo.py',
   'PYMODULE'),
  ('test.libregrtest.logger',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/logger.py',
   'PYMODULE'),
  ('test.libregrtest.win_utils',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/win_utils.py',
   'PYMODULE'),
  ('test.libregrtest.findtests',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/findtests.py',
   'PYMODULE'),
  ('test.libregrtest.cmdline',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/cmdline.py',
   'PYMODULE'),
  ('pygame.sysfont',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/sysfont.py',
   'PYMODULE'),
  ('pygame.surfarray',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/surfarray.py',
   'PYMODULE'),
  ('pygame.sprite',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/sprite.py',
   'PYMODULE'),
  ('pygame.sndarray',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/sndarray.py',
   'PYMODULE'),
  ('pygame.pkgdata',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pkgdata.py',
   'PYMODULE'),
  ('pygame.midi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/midi.py',
   'PYMODULE'),
  ('pygame.macosx',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/macosx.py',
   'PYMODULE'),
  ('pygame.locals',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/locals.py',
   'PYMODULE'),
  ('pygame.ftfont',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/ftfont.py',
   'PYMODULE'),
  ('pygame.freetype',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/freetype.py',
   'PYMODULE'),
  ('pygame.fastevent',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/fastevent.py',
   'PYMODULE'),
  ('pygame.examples.video',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/video.py',
   'PYMODULE'),
  ('pygame.examples.vgrade',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/vgrade.py',
   'PYMODULE'),
  ('numpy.random',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/random/__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/testing/__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/testing/overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/_iotools.py',
   'PYMODULE'),
  ('numpy.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/compat/__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/compat/py3k.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_utils/_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_utils/__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_utils/_convertions.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/arrayprint.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/stride_tricks.py',
   'PYMODULE'),
  ('numpy._globals',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_globals.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_exceptions.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_ufunc_config.py',
   'PYMODULE'),
  ('numpy.exceptions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/exceptions.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/shape_base.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/multiarray.py',
   'PYMODULE'),
  ('numpy.core',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/__init__.py',
   'PYMODULE'),
  ('numpy.core._internal',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_internal.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/einsumfunc.py',
   'PYMODULE'),
  ('numpy.core._machar',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_machar.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/function_base.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/memmap.py',
   'PYMODULE'),
  ('numpy.core.records',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/records.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/defchararray.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/getlimits.py',
   'PYMODULE'),
  ('numpy.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/version.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/ma/mrecords.py',
   'PYMODULE'),
  ('numpy.ma',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/ma/__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/ma/extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/index_tricks.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/matrixlib/__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/matrixlib/defmatrix.py',
   'PYMODULE'),
  ('numpy.linalg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/linalg/__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/linalg/linalg.py',
   'PYMODULE'),
  ('numpy._typing',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_nested_sequence.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/histograms.py',
   'PYMODULE'),
  ('numpy.ma.core',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/ma/core.py',
   'PYMODULE'),
  ('numpy.lib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/npyio.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/type_check.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/mixins.py',
   'PYMODULE'),
  ('numpy.core.umath',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/overrides.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/testing/_private/extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/testing/_private/utils.py',
   'PYMODULE'),
  ('psutil',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/__init__.py',
   'PYMODULE'),
  ('psutil._psosx',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_psosx.py',
   'PYMODULE'),
  ('psutil._psposix',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_psposix.py',
   'PYMODULE'),
  ('psutil._common',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_common.py',
   'PYMODULE'),
  ('curses',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/curses/__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/curses/has_key.py',
   'PYMODULE'),
  ('numpy.testing._private',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/testing/_private/__init__.py',
   'PYMODULE'),
  ('numpy.array_api',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_add_docstring.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/linalg.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_constants.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/random/_pickle.py',
   'PYMODULE'),
  ('pygame.examples.textinput',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/textinput.py',
   'PYMODULE'),
  ('pygame.examples.testsprite',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/testsprite.py',
   'PYMODULE'),
  ('pygame.examples.stars',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/stars.py',
   'PYMODULE'),
  ('pygame.examples.sprite_texture',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/sprite_texture.py',
   'PYMODULE'),
  ('pygame.examples.sound_array_demos',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/sound_array_demos.py',
   'PYMODULE'),
  ('pygame.examples.sound',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/sound.py',
   'PYMODULE'),
  ('pygame.examples.setmodescale',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/setmodescale.py',
   'PYMODULE'),
  ('pygame.examples.scroll',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/scroll.py',
   'PYMODULE'),
  ('pygame.examples.scrap_clipboard',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/scrap_clipboard.py',
   'PYMODULE'),
  ('pygame.examples.scaletest',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/scaletest.py',
   'PYMODULE'),
  ('pygame.examples.resizing_new',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/resizing_new.py',
   'PYMODULE'),
  ('pygame.examples.playmus',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/playmus.py',
   'PYMODULE'),
  ('pygame.examples.pixelarray',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/pixelarray.py',
   'PYMODULE'),
  ('pygame.examples.music_drop_fade',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/music_drop_fade.py',
   'PYMODULE'),
  ('pygame.examples.moveit',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/moveit.py',
   'PYMODULE'),
  ('pygame.examples.midi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/midi.py',
   'PYMODULE'),
  ('pygame.examples.mask',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/mask.py',
   'PYMODULE'),
  ('pygame.examples.liquid',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/liquid.py',
   'PYMODULE'),
  ('pygame.examples.joystick',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/joystick.py',
   'PYMODULE'),
  ('pygame.examples.headless_no_windows_needed',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/headless_no_windows_needed.py',
   'PYMODULE'),
  ('pygame.examples.grid',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/grid.py',
   'PYMODULE'),
  ('pygame.examples.go_over_there',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/go_over_there.py',
   'PYMODULE'),
  ('pygame.examples.glcube',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/glcube.py',
   'PYMODULE'),
  ('pygame.examples.freetype_misc',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/freetype_misc.py',
   'PYMODULE'),
  ('pygame.examples.fonty',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/fonty.py',
   'PYMODULE'),
  ('pygame.examples.font_viewer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/font_viewer.py',
   'PYMODULE'),
  ('pygame.examples.eventlist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/eventlist.py',
   'PYMODULE'),
  ('pygame.examples.dropevent',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/dropevent.py',
   'PYMODULE'),
  ('pygame.examples.cursors',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/cursors.py',
   'PYMODULE'),
  ('pygame.examples.chimp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/chimp.py',
   'PYMODULE'),
  ('pygame.examples.camera',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/camera.py',
   'PYMODULE'),
  ('pygame.examples.blit_blends',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/blit_blends.py',
   'PYMODULE'),
  ('pygame.examples.blend_fill',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/blend_fill.py',
   'PYMODULE'),
  ('pygame.examples.audiocapture',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/audiocapture.py',
   'PYMODULE'),
  ('pygame.examples.arraydemo',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/arraydemo.py',
   'PYMODULE'),
  ('pygame.examples.aliens',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/aliens.py',
   'PYMODULE'),
  ('pygame.examples.aacircle',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/aacircle.py',
   'PYMODULE'),
  ('pygame.examples',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/__init__.py',
   'PYMODULE'),
  ('pygame.draw_py',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/draw_py.py',
   'PYMODULE'),
  ('pygame.cursors',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/cursors.py',
   'PYMODULE'),
  ('pygame.colordict',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/colordict.py',
   'PYMODULE'),
  ('pygame.camera',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/camera.py',
   'PYMODULE'),
  ('pygame._sdl2',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/__init__.py',
   'PYMODULE'),
  ('pygame._camera_vidcapture',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_camera_vidcapture.py',
   'PYMODULE'),
  ('pygame._camera_opencv',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_camera_opencv.py',
   'PYMODULE'),
  ('pygame.__pyinstaller.hook-pygame',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/__pyinstaller/hook-pygame.py',
   'PYMODULE'),
  ('PyInstaller.utils.hooks',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/utils/hooks/__init__.py',
   'PYMODULE'),
  ('PyInstaller.utils.hooks.conda',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/utils/hooks/conda.py',
   'PYMODULE'),
  ('PyInstaller.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/utils/__init__.py',
   'PYMODULE'),
  ('PyInstaller.utils.misc',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/utils/misc.py',
   'PYMODULE'),
  ('PyInstaller.utils.win32.versioninfo',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/utils/win32/versioninfo.py',
   'PYMODULE'),
  ('PyInstaller.utils.win32',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/utils/win32/__init__.py',
   'PYMODULE'),
  ('PyInstaller.config',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/config.py',
   'PYMODULE'),
  ('PyInstaller.isolated',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/isolated/__init__.py',
   'PYMODULE'),
  ('PyInstaller.isolated._parent',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/isolated/_parent.py',
   'PYMODULE'),
  ('PyInstaller.depend.imphookapi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/depend/imphookapi.py',
   'PYMODULE'),
  ('PyInstaller.depend',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/depend/__init__.py',
   'PYMODULE'),
  ('PyInstaller.lib.modulegraph.modulegraph',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/lib/modulegraph/modulegraph.py',
   'PYMODULE'),
  ('PyInstaller.lib.modulegraph.util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/lib/modulegraph/util.py',
   'PYMODULE'),
  ('PyInstaller.lib.modulegraph',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/lib/modulegraph/__init__.py',
   'PYMODULE'),
  ('PyInstaller.lib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/lib/__init__.py',
   'PYMODULE'),
  ('altgraph',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/altgraph/__init__.py',
   'PYMODULE'),
  ('altgraph.ObjectGraph',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/altgraph/ObjectGraph.py',
   'PYMODULE'),
  ('altgraph.GraphUtil',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/altgraph/GraphUtil.py',
   'PYMODULE'),
  ('altgraph.Graph',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/altgraph/Graph.py',
   'PYMODULE'),
  ('PyInstaller.building.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/building/utils.py',
   'PYMODULE'),
  ('PyInstaller.building',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/building/__init__.py',
   'PYMODULE'),
  ('PyInstaller.utils.osx',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/utils/osx.py',
   'PYMODULE'),
  ('macholib.util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/macholib/util.py',
   'PYMODULE'),
  ('macholib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/macholib/__init__.py',
   'PYMODULE'),
  ('macholib.MachO',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/macholib/MachO.py',
   'PYMODULE'),
  ('macholib.ptypes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/macholib/ptypes.py',
   'PYMODULE'),
  ('macholib.mach_o',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/macholib/mach_o.py',
   'PYMODULE'),
  ('PyInstaller.exceptions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/exceptions.py',
   'PYMODULE'),
  ('PyInstaller.log',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/log.py',
   'PYMODULE'),
  ('PyInstaller.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/compat.py',
   'PYMODULE'),
  ('PyInstaller._shared_with_waf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/_shared_with_waf.py',
   'PYMODULE'),
  ('PyInstaller',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/__init__.py',
   'PYMODULE'),
  ('pygame.__pyinstaller',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/__pyinstaller/__init__.py',
   'PYMODULE'),
  ('ctypes.util',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/util.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('ctypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/_endian.py',
   'PYMODULE'),
  ('PyQt5',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/__init__.py',
   'PYMODULE'),
  ('_py_abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_py_abc.py',
   'PYMODULE'),
  ('stringprep',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/stringprep.py',
   'PYMODULE'),
  ('vlc',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/vlc.py',
   'PYMODULE'),
  ('distro',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/distro/__init__.py',
   'PYMODULE'),
  ('distro.distro',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/distro/distro.py',
   'PYMODULE'),
  ('pygame',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/__init__.py',
   'PYMODULE'),
  ('platform',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/platform.py',
   'PYMODULE'),
  ('shutil',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/shutil.py',
   'PYMODULE'),
  ('tempfile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tempfile.py',
   'PYMODULE'),
  ('numpy',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/ctypeslib.py',
   'PYMODULE'),
  ('numpy.polynomial',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/fft/__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/fft/helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/fft/_pocketfft.py',
   'PYMODULE'),
  ('numpy.dtypes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/dtypes.py',
   'PYMODULE'),
  ('numpy.__config__',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/__config__.py',
   'PYMODULE'),
  ('yaml',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/__init__.py',
   'PYMODULE'),
  ('yaml.cyaml',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/cyaml.py',
   'PYMODULE'),
  ('yaml.resolver',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/resolver.py',
   'PYMODULE'),
  ('yaml.representer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/representer.py',
   'PYMODULE'),
  ('yaml.serializer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/serializer.py',
   'PYMODULE'),
  ('yaml.constructor',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/constructor.py',
   'PYMODULE'),
  ('yaml.dumper',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/emitter.py',
   'PYMODULE'),
  ('yaml.loader',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/loader.py',
   'PYMODULE'),
  ('yaml.composer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/composer.py',
   'PYMODULE'),
  ('yaml.parser',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/parser.py',
   'PYMODULE'),
  ('yaml.scanner',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/scanner.py',
   'PYMODULE'),
  ('yaml.reader',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/reader.py',
   'PYMODULE'),
  ('yaml.nodes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/nodes.py',
   'PYMODULE'),
  ('yaml.events',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/events.py',
   'PYMODULE'),
  ('yaml.tokens',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/tokens.py',
   'PYMODULE'),
  ('yaml.error',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/error.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_distributor_init.py',
   'PYMODULE'),
  ('subprocess',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/subprocess.py',
   'PYMODULE'),
  ('threading',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_threading_local.py',
   'PYMODULE')],
 [('pygame/.dylibs/libFLAC.8.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libFLAC.8.dylib',
   'BINARY'),
  ('pygame/.dylibs/libSDL2-2.0.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libSDL2-2.0.0.dylib',
   'BINARY'),
  ('pygame/.dylibs/libSDL2_image-2.0.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libSDL2_image-2.0.0.dylib',
   'BINARY'),
  ('pygame/.dylibs/libSDL2_mixer-2.0.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libSDL2_mixer-2.0.0.dylib',
   'BINARY'),
  ('pygame/.dylibs/libSDL2_ttf-2.0.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libSDL2_ttf-2.0.0.dylib',
   'BINARY'),
  ('pygame/.dylibs/libbrotlicommon.1.0.9.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libbrotlicommon.1.0.9.dylib',
   'BINARY'),
  ('pygame/.dylibs/libbrotlidec.1.0.9.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libbrotlidec.1.0.9.dylib',
   'BINARY'),
  ('pygame/.dylibs/libfluidsynth.3.1.1.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libfluidsynth.3.1.1.dylib',
   'BINARY'),
  ('pygame/.dylibs/libfreetype.6.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libfreetype.6.dylib',
   'BINARY'),
  ('pygame/.dylibs/libglib-2.0.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libglib-2.0.0.dylib',
   'BINARY'),
  ('pygame/.dylibs/libgthread-2.0.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libgthread-2.0.0.dylib',
   'BINARY'),
  ('pygame/.dylibs/libintl.8.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libintl.8.dylib',
   'BINARY'),
  ('pygame/.dylibs/libjpeg.62.3.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libjpeg.62.3.0.dylib',
   'BINARY'),
  ('pygame/.dylibs/libmpg123.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libmpg123.0.dylib',
   'BINARY'),
  ('pygame/.dylibs/libogg.0.8.5.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libogg.0.8.5.dylib',
   'BINARY'),
  ('pygame/.dylibs/libopus.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libopus.0.dylib',
   'BINARY'),
  ('pygame/.dylibs/libopusfile.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libopusfile.0.dylib',
   'BINARY'),
  ('pygame/.dylibs/libpng16.16.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libpng16.16.dylib',
   'BINARY'),
  ('pygame/.dylibs/libportmidi.2.0.3.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libportmidi.2.0.3.dylib',
   'BINARY'),
  ('pygame/.dylibs/libsharpyuv.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libsharpyuv.0.dylib',
   'BINARY'),
  ('pygame/.dylibs/libsndfile.1.0.34.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libsndfile.1.0.34.dylib',
   'BINARY'),
  ('pygame/.dylibs/libtiff.5.8.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libtiff.5.8.0.dylib',
   'BINARY'),
  ('pygame/.dylibs/libvorbis.0.4.9.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libvorbis.0.4.9.dylib',
   'BINARY'),
  ('pygame/.dylibs/libvorbisenc.2.0.12.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libvorbisenc.2.0.12.dylib',
   'BINARY'),
  ('pygame/.dylibs/libvorbisfile.3.3.8.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libvorbisfile.3.3.8.dylib',
   'BINARY'),
  ('pygame/.dylibs/libwebp.7.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libwebp.7.dylib',
   'BINARY'),
  ('pygame/.dylibs/libz.1.2.11.zlib-ng.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/.dylibs/libz.1.2.11.zlib-ng.dylib',
   'BINARY'),
  ('VLC', '/Applications/VLC.app/Contents/MacOS/VLC', 'BINARY'),
  ('libavcodec.dylib', '/usr/local/lib/libavcodec.dylib', 'BINARY'),
  ('libavdevice.dylib', '/usr/local/lib/libavdevice.dylib', 'BINARY'),
  ('libavfilter.dylib', '/usr/local/lib/libavfilter.dylib', 'BINARY'),
  ('libavformat.dylib', '/usr/local/lib/libavformat.dylib', 'BINARY'),
  ('libavutil.dylib', '/usr/local/lib/libavutil.dylib', 'BINARY'),
  ('libflac.dylib', '/usr/local/lib/libflac.dylib', 'BINARY'),
  ('libmp3lame.dylib', '/usr/local/lib/libmp3lame.dylib', 'BINARY'),
  ('libogg.dylib', '/usr/local/lib/libogg.dylib', 'BINARY'),
  ('libopus.dylib', '/usr/local/lib/libopus.dylib', 'BINARY'),
  ('libpostproc.dylib', '/usr/local/lib/libpostproc.dylib', 'BINARY'),
  ('libspeex.dylib', '/usr/local/lib/libspeex.dylib', 'BINARY'),
  ('libswresample.dylib', '/usr/local/lib/libswresample.dylib', 'BINARY'),
  ('libswscale.dylib', '/usr/local/lib/libswscale.dylib', 'BINARY'),
  ('libtheora.dylib', '/usr/local/lib/libtheora.dylib', 'BINARY'),
  ('libvorbis.dylib', '/usr/local/lib/libvorbis.dylib', 'BINARY'),
  ('libvpx.dylib', '/usr/local/lib/libvpx.dylib', 'BINARY'),
  ('libx264.dylib', '/usr/local/lib/libx264.dylib', 'BINARY'),
  ('libx265.dylib', '/usr/local/lib/libx265.dylib', 'BINARY'),
  ('vlc/lib/libvlc.5.dylib',
   '/Applications/VLC.app/Contents/MacOS/lib/libvlc.5.dylib',
   'BINARY'),
  ('vlc/lib/libvlccore.9.dylib',
   '/Applications/VLC.app/Contents/MacOS/lib/libvlccore.9.dylib',
   'BINARY'),
  ('vlc/plugins/liba52_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liba52_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_concat_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_concat_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_imem_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_imem_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_mms_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_mms_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_dummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_dummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_file_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_file_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_http_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_http_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_livehttp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_livehttp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_rist_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_rist_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_shout_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_shout_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_srt_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_srt_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_output_udp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_output_udp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_realrtsp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_realrtsp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaccess_srt_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaccess_srt_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libadaptive_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libadaptive_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaddonsfsstorage_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaddonsfsstorage_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaddonsvorepository_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaddonsvorepository_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libadf_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libadf_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libadjust_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libadjust_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libadpcm_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libadpcm_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libadummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libadummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaes3_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaes3_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libafile_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libafile_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaiff_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaiff_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libalphamask_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libalphamask_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libamem_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libamem_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libanaglyph_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libanaglyph_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libantiflicker_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libantiflicker_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaom_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaom_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaraw_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaraw_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libarchive_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libarchive_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaribsub_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaribsub_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libasf_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libasf_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libattachment_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libattachment_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libau_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libau_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaudio_format_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaudio_format_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaudiobargraph_a_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaudiobargraph_a_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaudiobargraph_v_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaudiobargraph_v_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaudioscrobbler_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaudioscrobbler_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libaudiotoolboxmidi_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libaudiotoolboxmidi_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libauhal_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libauhal_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libavaudiocapture_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libavaudiocapture_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libavcapture_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libavcapture_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libavcodec_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libavcodec_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libavi_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libavi_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libball_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libball_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libblend_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libblend_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libblendbench_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libblendbench_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libbluescreen_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libbluescreen_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libbonjour_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libbonjour_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcache_block_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcache_block_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcache_read_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcache_read_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcaf_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcaf_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcanvas_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcanvas_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcaopengllayer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcaopengllayer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcc_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcc_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcdda_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcdda_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcdg_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcdg_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libchain_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libchain_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libchorus_flanger_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libchorus_flanger_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libci_filters_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libci_filters_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libclone_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libclone_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcolorthres_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcolorthres_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcompressor_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcompressor_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libconsole_logger_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libconsole_logger_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcroppadd_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcroppadd_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcvdsub_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcvdsub_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libcvpx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libcvpx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdav1d_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdav1d_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdca_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdca_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdcp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdcp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libddummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libddummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdecomp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdecomp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdeinterlace_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdeinterlace_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdemux_cdg_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdemux_cdg_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdemux_chromecast_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdemux_chromecast_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdemux_stl_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdemux_stl_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdemuxdump_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdemuxdump_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdiracsys_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdiracsys_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdirectory_demux_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdirectory_demux_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdolby_surround_decoder_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdolby_surround_decoder_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdvbsub_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdvbsub_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdvdnav_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdvdnav_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdvdread_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdvdread_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libdynamicoverlay_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libdynamicoverlay_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libedgedetection_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libedgedetection_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libedummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libedummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libequalizer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libequalizer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liberase_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liberase_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libes_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libes_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libexport_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libexport_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libextract_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libextract_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfaad_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfaad_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfile_keystore_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfile_keystore_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfile_logger_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfile_logger_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfilesystem_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfilesystem_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfingerprinter_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfingerprinter_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libflac_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libflac_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libflacsys_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libflacsys_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libflaschen_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libflaschen_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfloat_mixer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfloat_mixer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfolder_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfolder_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfps_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfps_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfreetype_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfreetype_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libfreeze_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libfreeze_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libftp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libftp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libg711_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libg711_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgain_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgain_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgaussianblur_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgaussianblur_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgestures_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgestures_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libglconv_cvpx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libglconv_cvpx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgme_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgme_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgnutls_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgnutls_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgoom_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgoom_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgradfun_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgradfun_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgradient_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgradient_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgrain_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgrain_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libgrey_yuv_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libgrey_yuv_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libh26x_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libh26x_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libhds_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libhds_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libheadphone_channel_mixer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libheadphone_channel_mixer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libhotkeys_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libhotkeys_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libhqdn3d_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libhqdn3d_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libhttp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libhttp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libhttps_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libhttps_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_10_p010_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_10_p010_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_nv12_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_nv12_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_rgb_mmx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_rgb_mmx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_rgb_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_rgb_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_rgb_sse2_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_rgb_sse2_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_yuy2_mmx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_yuy2_mmx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_yuy2_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_yuy2_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi420_yuy2_sse2_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi420_yuy2_sse2_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi422_i420_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi422_i420_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi422_yuy2_mmx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi422_yuy2_mmx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi422_yuy2_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi422_yuy2_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libi422_yuy2_sse2_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libi422_yuy2_sse2_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libidummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libidummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libimage_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libimage_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libimem_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libimem_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libinflate_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libinflate_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libinteger_mixer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libinteger_mixer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libinvert_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libinvert_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libjpeg_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libjpeg_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libkaraoke_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libkaraoke_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libkate_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libkate_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libkeychain_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libkeychain_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblibass_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblibass_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblibbluray_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblibbluray_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblibmpeg2_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblibmpeg2_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblive555_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblive555_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblogger_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblogger_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblogo_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblogo_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblpcm_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblpcm_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liblua_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liblua_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmacosx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmacosx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmad_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmad_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmagnify_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmagnify_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmarq_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmarq_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmediadirs_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmediadirs_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmemory_keystore_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmemory_keystore_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmirror_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmirror_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmjpeg_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmjpeg_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmkv_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmkv_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmod_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmod_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmono_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmono_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmosaic_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmosaic_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmotion_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmotion_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmotionblur_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmotionblur_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmotiondetect_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmotiondetect_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmp4_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmp4_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmpc_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmpc_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmpg123_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmpg123_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmpgv_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmpgv_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_asf_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_asf_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_avi_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_avi_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_dummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_dummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_mp4_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_mp4_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_mpjpeg_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_mpjpeg_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_ogg_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_ogg_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_ps_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_ps_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_ts_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_ts_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libmux_wav_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libmux_wav_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libncurses_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libncurses_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnetsync_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnetsync_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnfs_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnfs_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnormvol_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnormvol_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnoseek_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnoseek_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnsc_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnsc_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnsspeechsynthesizer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnsspeechsynthesizer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnsv_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnsv_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libnuv_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libnuv_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libogg_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libogg_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liboggspots_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liboggspots_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liboldmovie_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liboldmovie_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/liboldrc_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/liboldrc_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libopus_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libopus_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libosx_notifications_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libosx_notifications_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_a52_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_a52_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_av1_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_av1_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_copy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_copy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_dirac_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_dirac_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_dts_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_dts_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_flac_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_flac_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_h264_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_h264_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_hevc_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_hevc_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_mlp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_mlp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_mpeg4audio_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_mpeg4audio_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_mpeg4video_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_mpeg4video_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_mpegaudio_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_mpegaudio_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_mpegvideo_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_mpegvideo_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpacketizer_vc1_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpacketizer_vc1_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libparam_eq_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libparam_eq_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libplaylist_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libplaylist_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpng_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpng_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpodcast_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpodcast_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libposterize_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libposterize_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpostproc_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpostproc_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libprefetch_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libprefetch_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libps_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libps_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpsychedelic_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpsychedelic_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpuzzle_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpuzzle_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libpva_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libpva_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librawaud_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librawaud_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librawdv_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librawdv_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librawvid_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librawvid_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librawvideo_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librawvideo_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libreal_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libreal_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librecord_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librecord_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libremap_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libremap_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libremoteosd_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libremoteosd_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libripple_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libripple_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librist_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librist_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librotate_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librotate_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librss_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librss_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librtp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librtp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librtpvideo_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librtpvideo_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/librv32_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/librv32_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsamplerate_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsamplerate_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsap_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsap_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsatip_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsatip_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libscale_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libscale_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libscaletempo_pitch_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libscaletempo_pitch_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libscaletempo_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libscaletempo_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libscene_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libscene_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libschroedinger_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libschroedinger_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libscreen_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libscreen_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libscte18_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libscte18_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libscte27_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libscte27_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsdp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsdp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsecuretransport_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsecuretransport_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsepia_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsepia_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsftp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsftp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsharpen_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsharpen_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libshm_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libshm_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsid_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsid_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsimple_channel_mixer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsimple_channel_mixer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libskiptags_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libskiptags_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsmf_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsmf_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libspatialaudio_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libspatialaudio_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libspatializer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libspatializer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libspdif_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libspdif_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libspeex_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libspeex_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libspeex_resampler_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libspeex_resampler_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libspudec_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libspudec_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstats_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstats_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstereo_widen_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstereo_widen_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstl_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstl_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_autodel_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_autodel_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_bridge_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_bridge_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_chromaprint_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_chromaprint_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_chromecast_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_chromecast_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_cycle_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_cycle_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_delay_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_delay_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_description_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_description_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_display_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_display_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_dummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_dummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_duplicate_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_duplicate_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_es_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_es_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_gather_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_gather_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_mosaic_bridge_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_mosaic_bridge_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_record_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_record_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_rtp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_rtp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_setid_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_setid_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_smem_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_smem_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_standard_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_standard_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_stats_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_stats_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libstream_out_transcode_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libstream_out_transcode_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsubsdec_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsubsdec_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsubsdelay_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsubsdelay_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsubstx3g_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsubstx3g_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsubsusf_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsubsusf_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsubtitle_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsubtitle_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsvcdsub_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsvcdsub_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libswscale_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libswscale_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libsyslog_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libsyslog_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libt140_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libt140_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtaglib_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtaglib_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtcp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtcp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtdummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtdummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtelx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtelx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtextst_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtextst_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtheora_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtheora_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtimecode_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtimecode_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtospdif_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtospdif_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtransform_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtransform_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtrivial_channel_mixer_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtrivial_channel_mixer_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libts_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libts_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtta_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtta_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libttml_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libttml_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libtwolame_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libtwolame_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libty_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libty_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libudp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libudp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libugly_resampler_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libugly_resampler_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libuleaddvaudio_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libuleaddvaudio_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libupnp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libupnp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvc1_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvc1_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvcd_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvcd_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvdr_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvdr_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvdummy_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvdummy_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvhs_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvhs_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvideotoolbox_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvideotoolbox_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvisual_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvisual_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvmem_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvmem_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvobsub_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvobsub_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvoc_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvoc_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvod_rtsp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvod_rtsp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvorbis_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvorbis_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvout_macosx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvout_macosx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libvpx_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libvpx_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libwall_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libwall_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libwav_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libwav_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libwave_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libwave_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libwebvtt_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libwebvtt_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libx26410b_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libx26410b_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libx264_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libx264_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libx265_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libx265_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libxa_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libxa_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libxml_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libxml_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libyuv_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libyuv_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libyuvp_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libyuvp_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libyuy2_i420_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libyuy2_i420_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libyuy2_i422_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libyuy2_i422_plugin.dylib',
   'BINARY'),
  ('vlc/plugins/libzvbi_plugin.dylib',
   '/Applications/VLC.app/Contents/MacOS/plugins/libzvbi_plugin.dylib',
   'BINARY'),
  ('Python.framework/Versions/3.12/Python',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/Python',
   'BINARY'),
  ('PyQt5/Qt5/plugins/bearer/libqgenericbearer.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/bearer/libqgenericbearer.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/mediaservice/libqtmedia_audioengine.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/mediaservice/libqtmedia_audioengine.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/mediaservice/libqavfcamera.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/mediaservice/libqavfcamera.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/audio/libqtaudio_coreaudio.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/audio/libqtaudio_coreaudio.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/playlistformats/libqtmultimedia_m3u.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/playlistformats/libqtmultimedia_m3u.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/mediaservice/libqavfmediaplayer.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/mediaservice/libqavfmediaplayer.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqwbmp.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqwbmp.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqcocoa.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqcocoa.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqmacheif.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqmacheif.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqminimal.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqminimal.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqico.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqico.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqsvg.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqsvg.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platformthemes/libqxdgdesktopportal.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platformthemes/libqxdgdesktopportal.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/generic/libqtuiotouchplugin.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/generic/libqtuiotouchplugin.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqtga.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqtga.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqtiff.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqtiff.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqoffscreen.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqoffscreen.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqmacjp2.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqmacjp2.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqjpeg.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqjpeg.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqicns.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqicns.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqwebp.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqwebp.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqgif.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqgif.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqwebgl.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqwebgl.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/iconengines/libqsvgicon.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/iconengines/libqsvgicon.dylib',
   'BINARY'),
  ('PyQt5/Qt5/plugins/styles/libqmacstyle.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/plugins/styles/libqmacstyle.dylib',
   'BINARY'),
  ('numpy/.dylibs/libopenblas64_.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/.dylibs/libopenblas64_.0.dylib',
   'BINARY'),
  ('numpy/.dylibs/libgcc_s.1.1.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/.dylibs/libgcc_s.1.1.dylib',
   'BINARY'),
  ('numpy/.dylibs/libquadmath.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/.dylibs/libquadmath.0.dylib',
   'BINARY'),
  ('numpy/.dylibs/libgfortran.5.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/.dylibs/libgfortran.5.dylib',
   'BINARY'),
  ('lib-dynload/_statistics.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_statistics.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_contextvars.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_decimal.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_pickle.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_struct.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_hashlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha3.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_blake2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_md5.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha1.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_sha2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_random.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_bisect.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/math.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_datetime.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/unicodedata.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/array.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/select.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_socket.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/binascii.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_opcode.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_lzma.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_bz2.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/zlib.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/grp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_csv.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/resource.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/syslog.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_queue.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_json.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_ssl.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_asyncio.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/mmap.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_posixshmem.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_posixsubprocess.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_multiprocessing.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/pyexpat.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_scproxy.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/termios.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/readline.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/transform.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/transform.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/time.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/time.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/fcntl.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_testcapi.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_testcapi.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_testinternalcapi.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_testinternalcapi.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_elementtree.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/surflock.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/surflock.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/surface.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/surface.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/sdlmain_osx.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/sdlmain_osx.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/scrap.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/scrap.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/rwobject.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/rwobject.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/rect.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/rect.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/pypm.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pypm.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/pixelcopy.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pixelcopy.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/pixelarray.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pixelarray.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/newbuffer.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/newbuffer.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/mouse.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/mouse.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/mixer_music.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/mixer_music.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/math.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/math.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/mask.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/mask.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/key.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/key.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/joystick.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/joystick.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/imageext.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/imageext.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/image.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/image.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/gfxdraw.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/gfxdraw.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/font.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/font.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/core/_multiarray_umath.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_multiarray_umath.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_ctypes.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/linalg/_umath_linalg.cpython-312-darwin.so',
   'EXTENSION'),
  ('psutil/_psutil_posix.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_psutil_posix.abi3.so',
   'EXTENSION'),
  ('psutil/_psutil_osx.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_psutil_osx.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_curses.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/random/mtrand.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/random/_sfc64.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/random/_philox.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/random/_pcg64.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/random/_mt19937.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/random/bit_generator.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/random/_generator.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/random/_bounded_integers.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/random/_common.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/event.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/event.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/draw.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/draw.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/display.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/display.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/constants.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/constants.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/color.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/color.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/bufferproxy.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/bufferproxy.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/base.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/base.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/_sprite.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sprite.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/_sdl2/video.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/video.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/_sdl2/touch.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/touch.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/_sdl2/sdl2.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/sdl2.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/_sdl2/mixer.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/mixer.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/_sdl2/controller.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/controller.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/_sdl2/audio.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/audio.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/_freetype.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_freetype.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/_camera.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_camera.cpython-312-darwin.so',
   'EXTENSION'),
  ('PyQt5/QtMultimediaWidgets.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/QtMultimediaWidgets.abi3.so',
   'EXTENSION'),
  ('PyQt5/QtNetwork.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/QtNetwork.abi3.so',
   'EXTENSION'),
  ('PyQt5/QtOpenGL.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/QtOpenGL.abi3.so',
   'EXTENSION'),
  ('PyQt5/sip.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/sip.cpython-312-darwin.so',
   'EXTENSION'),
  ('pygame/mixer.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/mixer.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_multibytecodec.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_jp.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_kr.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_iso2022.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_cn.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_tw.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_codecs_hk.cpython-312-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-312-darwin.so',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lib-dynload/_heapq.cpython-312-darwin.so',
   'EXTENSION'),
  ('PyQt5/QtMultimedia.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/QtMultimedia.abi3.so',
   'EXTENSION'),
  ('PyQt5/QtGui.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/QtGui.abi3.so',
   'EXTENSION'),
  ('PyQt5/QtCore.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/QtCore.abi3.so',
   'EXTENSION'),
  ('PyQt5/QtWidgets.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/QtWidgets.abi3.so',
   'EXTENSION'),
  ('numpy/core/_multiarray_tests.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_multiarray_tests.cpython-312-darwin.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_internal.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/fft/_pocketfft_internal.cpython-312-darwin.so',
   'EXTENSION'),
  ('yaml/_yaml.cpython-312-darwin.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/_yaml.cpython-312-darwin.so',
   'EXTENSION'),
  ('cv2/cv2.abi3.so',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/cv2.abi3.so',
   'EXTENSION'),
  ('libvlccore.dylib',
   '/Applications/VLC.app/Contents/MacOS/lib/libvlccore.dylib',
   'BINARY'),
  ('Breakpad.framework/Versions/A/Breakpad',
   '/Applications/VLC.app/Contents/Frameworks/Breakpad.framework/Versions/A/Breakpad',
   'BINARY'),
  ('libvlc.dylib',
   '/Applications/VLC.app/Contents/MacOS/lib/libvlc.dylib',
   'BINARY'),
  ('libjxl_threads.0.11.dylib',
   '/usr/local/opt/jpeg-xl/lib/libjxl_threads.0.11.dylib',
   'BINARY'),
  ('libaom.3.dylib', '/usr/local/opt/aom/lib/libaom.3.dylib', 'BINARY'),
  ('libvpx.9.dylib', '/usr/local/opt/libvpx/lib/libvpx.9.dylib', 'BINARY'),
  ('libjxl.0.11.dylib',
   '/usr/local/opt/jpeg-xl/lib/libjxl.0.11.dylib',
   'BINARY'),
  ('libavutil.59.dylib',
   '/usr/local/Cellar/ffmpeg/7.1.1_1/lib/libavutil.59.dylib',
   'BINARY'),
  ('libSvtAv1Enc.3.dylib',
   '/usr/local/opt/svt-av1/lib/libSvtAv1Enc.3.dylib',
   'BINARY'),
  ('libswresample.5.dylib',
   '/usr/local/Cellar/ffmpeg/7.1.1_1/lib/libswresample.5.dylib',
   'BINARY'),
  ('libx265.215.dylib', '/usr/local/opt/x265/lib/libx265.215.dylib', 'BINARY'),
  ('libopenjp2.7.dylib',
   '/usr/local/opt/openjpeg/lib/libopenjp2.7.dylib',
   'BINARY'),
  ('libwebpmux.3.dylib',
   '/usr/local/opt/webp/lib/libwebpmux.3.dylib',
   'BINARY'),
  ('libswscale.8.dylib',
   '/usr/local/Cellar/ffmpeg/7.1.1_1/lib/libswscale.8.dylib',
   'BINARY'),
  ('libavfilter.10.dylib',
   '/usr/local/Cellar/ffmpeg/7.1.1_1/lib/libavfilter.10.dylib',
   'BINARY'),
  ('libavformat.61.dylib',
   '/usr/local/Cellar/ffmpeg/7.1.1_1/lib/libavformat.61.dylib',
   'BINARY'),
  ('libharfbuzz.0.dylib',
   '/usr/local/opt/harfbuzz/lib/libharfbuzz.0.dylib',
   'BINARY'),
  ('libssh.4.dylib', '/usr/local/opt/libssh/lib/libssh.4.dylib', 'BINARY'),
  ('libxcb-xfixes.0.dylib',
   '/usr/local/opt/libxcb/lib/libxcb-xfixes.0.dylib',
   'BINARY'),
  ('libsamplerate.0.dylib',
   '/usr/local/opt/libsamplerate/lib/libsamplerate.0.dylib',
   'BINARY'),
  ('libavcodec.61.dylib',
   '/usr/local/Cellar/ffmpeg/7.1.1_1/lib/libavcodec.61.dylib',
   'BINARY'),
  ('libzimg.2.dylib', '/usr/local/opt/zimg/lib/libzimg.2.dylib', 'BINARY'),
  ('libsrt.1.5.dylib', '/usr/local/opt/srt/lib/libsrt.1.5.dylib', 'BINARY'),
  ('librubberband.3.dylib',
   '/usr/local/opt/rubberband/lib/librubberband.3.dylib',
   'BINARY'),
  ('libxcb-shm.0.dylib',
   '/usr/local/opt/libxcb/lib/libxcb-shm.0.dylib',
   'BINARY'),
  ('libass.9.dylib', '/usr/local/opt/libass/lib/libass.9.dylib', 'BINARY'),
  ('libpostproc.58.dylib',
   '/usr/local/Cellar/ffmpeg/7.1.1_1/lib/libpostproc.58.dylib',
   'BINARY'),
  ('libxcb-shape.0.dylib',
   '/usr/local/opt/libxcb/lib/libxcb-shape.0.dylib',
   'BINARY'),
  ('libvidstab.1.2.dylib',
   '/usr/local/opt/libvidstab/lib/libvidstab.1.2.dylib',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtCore.framework/Versions/5/QtCore',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtCore.framework/Versions/5/QtCore',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/QtNetwork',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/QtNetwork',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtMultimedia.framework/Versions/5/QtMultimedia',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtMultimedia.framework/Versions/5/QtMultimedia',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtGui.framework/Versions/5/QtGui',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtGui.framework/Versions/5/QtGui',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtMultimediaWidgets.framework/Versions/5/QtMultimediaWidgets',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtMultimediaWidgets.framework/Versions/5/QtMultimediaWidgets',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/QtWidgets',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/QtWidgets',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Versions/5/QtDBus',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtDBus.framework/Versions/5/QtDBus',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Versions/5/QtSvg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtSvg.framework/Versions/5/QtSvg',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/QtWebSockets',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/QtWebSockets',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/QtQmlModels',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/QtQmlModels',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtQml.framework/Versions/5/QtQml',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQml.framework/Versions/5/QtQml',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Versions/5/QtQuick',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQuick.framework/Versions/5/QtQuick',
   'BINARY'),
  ('libmpdec.4.dylib',
   '/usr/local/opt/mpdecimal/lib/libmpdec.4.dylib',
   'BINARY'),
  ('PyQt5/Qt5/lib/QtOpenGL.framework/Versions/5/QtOpenGL',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtOpenGL.framework/Versions/5/QtOpenGL',
   'BINARY'),
  ('cv2/.dylibs/libavcodec.61.3.100.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libavcodec.61.3.100.dylib',
   'BINARY'),
  ('cv2/.dylibs/libswscale.8.1.100.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libswscale.8.1.100.dylib',
   'BINARY'),
  ('cv2/.dylibs/libavutil.59.8.100.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libavutil.59.8.100.dylib',
   'BINARY'),
  ('cv2/.dylibs/libavformat.61.1.100.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libavformat.61.1.100.dylib',
   'BINARY'),
  ('cv2/.dylibs/libarchive.13.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libarchive.13.dylib',
   'BINARY'),
  ('cv2/.dylibs/libtesseract.5.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libtesseract.5.dylib',
   'BINARY'),
  ('libjxl_cms.0.11.dylib',
   '/usr/local/Cellar/jpeg-xl/0.11.1/lib/libjxl_cms.0.11.dylib',
   'BINARY'),
  ('libgraphite2.3.dylib',
   '/usr/local/opt/graphite2/lib/libgraphite2.3.dylib',
   'BINARY'),
  ('libzstd.1.dylib', '/usr/local/opt/zstd/lib/libzstd.1.dylib', 'BINARY'),
  ('liblz4.1.dylib', '/usr/local/opt/lz4/lib/liblz4.1.dylib', 'BINARY'),
  ('libXau.6.dylib', '/usr/local/opt/libxau/lib/libXau.6.dylib', 'BINARY'),
  ('libmbedcrypto.16.dylib',
   '/usr/local/opt/mbedtls/lib/libmbedcrypto.16.dylib',
   'BINARY'),
  ('libhogweed.6.dylib',
   '/usr/local/opt/nettle/lib/libhogweed.6.dylib',
   'BINARY'),
  ('libnettle.8.dylib',
   '/usr/local/opt/nettle/lib/libnettle.8.dylib',
   'BINARY'),
  ('libfribidi.0.dylib',
   '/usr/local/opt/fribidi/lib/libfribidi.0.dylib',
   'BINARY'),
  ('libunibreak.6.dylib',
   '/usr/local/opt/libunibreak/lib/libunibreak.6.dylib',
   'BINARY'),
  ('cv2/.dylibs/librav1e.0.7.1.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/librav1e.0.7.1.dylib',
   'BINARY'),
  ('cv2/.dylibs/libogg.0.8.5.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libogg.0.8.5.dylib',
   'BINARY'),
  ('cv2/.dylibs/libsoxr.0.1.2.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libsoxr.0.1.2.dylib',
   'BINARY'),
  ('cv2/.dylibs/libopus.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libopus.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libswresample.5.1.100.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libswresample.5.1.100.dylib',
   'BINARY'),
  ('cv2/.dylibs/libopencore-amrwb.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libopencore-amrwb.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libvpx.8.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libvpx.8.dylib',
   'BINARY'),
  ('cv2/.dylibs/libjxl_threads.0.10.2.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libjxl_threads.0.10.2.dylib',
   'BINARY'),
  ('cv2/.dylibs/libx265.209.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libx265.209.dylib',
   'BINARY'),
  ('cv2/.dylibs/libtheoradec.1.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libtheoradec.1.dylib',
   'BINARY'),
  ('cv2/.dylibs/libaom.3.9.1.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libaom.3.9.1.dylib',
   'BINARY'),
  ('cv2/.dylibs/libtheoraenc.1.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libtheoraenc.1.dylib',
   'BINARY'),
  ('cv2/.dylibs/libjxl.0.10.2.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libjxl.0.10.2.dylib',
   'BINARY'),
  ('cv2/.dylibs/libvorbisenc.2.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libvorbisenc.2.dylib',
   'BINARY'),
  ('cv2/.dylibs/libvmaf.3.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libvmaf.3.dylib',
   'BINARY'),
  ('cv2/.dylibs/libX11.6.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libX11.6.dylib',
   'BINARY'),
  ('cv2/.dylibs/liblzma.5.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/liblzma.5.dylib',
   'BINARY'),
  ('cv2/.dylibs/libvorbis.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libvorbis.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libaribb24.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libaribb24.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libwebp.7.1.9.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libwebp.7.1.9.dylib',
   'BINARY'),
  ('cv2/.dylibs/libopencore-amrnb.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libopencore-amrnb.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libx264.164.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libx264.164.dylib',
   'BINARY'),
  ('cv2/.dylibs/libsnappy.1.2.1.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libsnappy.1.2.1.dylib',
   'BINARY'),
  ('cv2/.dylibs/libspeex.1.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libspeex.1.dylib',
   'BINARY'),
  ('cv2/.dylibs/libSvtAv1Enc.2.1.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libSvtAv1Enc.2.1.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libwebpmux.3.1.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libwebpmux.3.1.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libopenjp2.2.5.2.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libopenjp2.2.5.2.dylib',
   'BINARY'),
  ('cv2/.dylibs/libdav1d.7.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libdav1d.7.dylib',
   'BINARY'),
  ('cv2/.dylibs/libmp3lame.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libmp3lame.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libzmq.5.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libzmq.5.dylib',
   'BINARY'),
  ('cv2/.dylibs/libsrt.1.5.3.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libsrt.1.5.3.dylib',
   'BINARY'),
  ('cv2/.dylibs/libgnutls.30.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libgnutls.30.dylib',
   'BINARY'),
  ('cv2/.dylibs/librist.4.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/librist.4.dylib',
   'BINARY'),
  ('cv2/.dylibs/libssh.4.9.6.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libssh.4.9.6.dylib',
   'BINARY'),
  ('cv2/.dylibs/libbluray.2.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libbluray.2.dylib',
   'BINARY'),
  ('cv2/.dylibs/libb2.1.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libb2.1.dylib',
   'BINARY'),
  ('cv2/.dylibs/liblz4.1.9.4.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/liblz4.1.9.4.dylib',
   'BINARY'),
  ('cv2/.dylibs/libzstd.1.5.6.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libzstd.1.5.6.dylib',
   'BINARY'),
  ('cv2/.dylibs/libleptonica.6.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libleptonica.6.dylib',
   'BINARY'),
  ('libpcre2-8.0.dylib',
   '/usr/local/opt/pcre2/lib/libpcre2-8.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libbrotlicommon.1.1.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libbrotlicommon.1.1.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libhwy.1.2.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libhwy.1.2.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libbrotlidec.1.1.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libbrotlidec.1.1.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libbrotlienc.1.1.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libbrotlienc.1.1.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libjxl_cms.0.10.2.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libjxl_cms.0.10.2.dylib',
   'BINARY'),
  ('cv2/.dylibs/libxcb.1.1.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libxcb.1.1.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libpng16.16.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libpng16.16.dylib',
   'BINARY'),
  ('cv2/.dylibs/libsharpyuv.0.1.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libsharpyuv.0.1.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libsodium.26.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libsodium.26.dylib',
   'BINARY'),
  ('cv2/.dylibs/libssl.3.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libssl.3.dylib',
   'BINARY'),
  ('cv2/.dylibs/libcrypto.3.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libcrypto.3.dylib',
   'BINARY'),
  ('cv2/.dylibs/libnettle.8.8.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libnettle.8.8.dylib',
   'BINARY'),
  ('cv2/.dylibs/libtasn1.6.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libtasn1.6.dylib',
   'BINARY'),
  ('cv2/.dylibs/libintl.8.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libintl.8.dylib',
   'BINARY'),
  ('cv2/.dylibs/libidn2.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libidn2.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libp11-kit.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libp11-kit.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libhogweed.6.8.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libhogweed.6.8.dylib',
   'BINARY'),
  ('cv2/.dylibs/libunistring.5.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libunistring.5.dylib',
   'BINARY'),
  ('cv2/.dylibs/libgmp.10.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libgmp.10.dylib',
   'BINARY'),
  ('cv2/.dylibs/libcjson.1.7.18.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libcjson.1.7.18.dylib',
   'BINARY'),
  ('cv2/.dylibs/libmbedcrypto.3.6.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libmbedcrypto.3.6.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libfontconfig.1.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libfontconfig.1.dylib',
   'BINARY'),
  ('cv2/.dylibs/libfreetype.6.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libfreetype.6.dylib',
   'BINARY'),
  ('cv2/.dylibs/libtiff.6.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libtiff.6.dylib',
   'BINARY'),
  ('cv2/.dylibs/libgif.7.2.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libgif.7.2.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libjpeg.8.3.2.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libjpeg.8.3.2.dylib',
   'BINARY'),
  ('cv2/.dylibs/liblcms2.2.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/liblcms2.2.dylib',
   'BINARY'),
  ('cv2/.dylibs/libXau.6.0.0.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libXau.6.0.0.dylib',
   'BINARY'),
  ('cv2/.dylibs/libXdmcp.6.dylib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/.dylibs/libXdmcp.6.dylib',
   'BINARY')],
 [],
 [],
 [('pygame/__init__.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/__init__.pyi',
   'DATA'),
  ('pygame/_common.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_common.pyi',
   'DATA'),
  ('pygame/_sdl2/__init__.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/__init__.pyi',
   'DATA'),
  ('pygame/_sdl2/audio.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/audio.pyi',
   'DATA'),
  ('pygame/_sdl2/controller.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/controller.pyi',
   'DATA'),
  ('pygame/_sdl2/sdl2.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/sdl2.pyi',
   'DATA'),
  ('pygame/_sdl2/touch.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/touch.pyi',
   'DATA'),
  ('pygame/_sdl2/video.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/video.pyi',
   'DATA'),
  ('pygame/base.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/base.pyi',
   'DATA'),
  ('pygame/bufferproxy.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/bufferproxy.pyi',
   'DATA'),
  ('pygame/camera.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/camera.pyi',
   'DATA'),
  ('pygame/color.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/color.pyi',
   'DATA'),
  ('pygame/constants.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/constants.pyi',
   'DATA'),
  ('pygame/cursors.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/cursors.pyi',
   'DATA'),
  ('pygame/display.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/display.pyi',
   'DATA'),
  ('pygame/docs/generated/LGPL.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/LGPL.txt',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput1.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput1.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput11.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput11.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput2.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput2.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput21.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput21.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput3.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput3.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput31.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput31.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput4.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput4.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput41.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput41.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput5.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput5.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedInputOutput51.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedInputOutput51.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputAlpha1.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputAlpha1.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputAlpha11.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputAlpha11.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputAlpha2.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputAlpha2.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputAlpha21.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputAlpha21.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputAlpha3.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputAlpha3.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputAlpha31.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputAlpha31.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess1.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess1.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess11.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess11.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess2.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess2.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess21.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess21.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess3.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess3.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess31.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess31.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess4.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess4.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess41.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess41.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess5.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess5.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess51.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess51.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess6.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess6.gif',
   'DATA'),
  ('pygame/docs/generated/_images/AdvancedOutputProcess61.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/AdvancedOutputProcess61.gif',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-INPUT-resultscreen.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-INPUT-resultscreen.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-INPUT-resultscreen1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-INPUT-resultscreen1.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-INPUT-sourcecode.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-INPUT-sourcecode.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-INPUT-sourcecode1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-INPUT-sourcecode1.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-PROCESS-resultscreen.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-PROCESS-resultscreen.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-PROCESS-resultscreen1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-PROCESS-resultscreen1.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-PROCESS-sourcecode.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-PROCESS-sourcecode.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-PROCESS-sourcecode1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-PROCESS-sourcecode1.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-ouput-result-screen.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-ouput-result-screen.png',
   'DATA'),
  ('pygame/docs/generated/_images/Bagic-ouput-result-screen1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Bagic-ouput-result-screen1.png',
   'DATA'),
  ('pygame/docs/generated/_images/Basic-ouput-sourcecode.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Basic-ouput-sourcecode.png',
   'DATA'),
  ('pygame/docs/generated/_images/Basic-ouput-sourcecode1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/Basic-ouput-sourcecode1.png',
   'DATA'),
  ('pygame/docs/generated/_images/angle_to.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/angle_to.png',
   'DATA'),
  ('pygame/docs/generated/_images/camera_average.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_average.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_background.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_background.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_green.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_green.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_hsv.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_hsv.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_mask.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_mask.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_rgb.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_rgb.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_thresh.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_thresh.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_thresholded.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_thresholded.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/camera_yuv.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/camera_yuv.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/chimpshot.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/chimpshot.gif',
   'DATA'),
  ('pygame/docs/generated/_images/draw_module_example.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/draw_module_example.png',
   'DATA'),
  ('pygame/docs/generated/_images/intro_ball.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/intro_ball.gif',
   'DATA'),
  ('pygame/docs/generated/_images/intro_blade.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/intro_blade.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/intro_freedom.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/intro_freedom.jpg',
   'DATA'),
  ('pygame/docs/generated/_images/introduction-Battleship.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/introduction-Battleship.png',
   'DATA'),
  ('pygame/docs/generated/_images/introduction-Battleship1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/introduction-Battleship1.png',
   'DATA'),
  ('pygame/docs/generated/_images/introduction-PuyoPuyo.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/introduction-PuyoPuyo.png',
   'DATA'),
  ('pygame/docs/generated/_images/introduction-PuyoPuyo1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/introduction-PuyoPuyo1.png',
   'DATA'),
  ('pygame/docs/generated/_images/introduction-TPS.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/introduction-TPS.png',
   'DATA'),
  ('pygame/docs/generated/_images/introduction-TPS1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/introduction-TPS1.png',
   'DATA'),
  ('pygame/docs/generated/_images/joystick_calls.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/joystick_calls.png',
   'DATA'),
  ('pygame/docs/generated/_images/pygame_lofi.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/pygame_lofi.png',
   'DATA'),
  ('pygame/docs/generated/_images/pygame_logo.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/pygame_logo.png',
   'DATA'),
  ('pygame/docs/generated/_images/pygame_powered.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/pygame_powered.png',
   'DATA'),
  ('pygame/docs/generated/_images/pygame_powered_lowres.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/pygame_powered_lowres.png',
   'DATA'),
  ('pygame/docs/generated/_images/pygame_tiny.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/pygame_tiny.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_allblack.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_allblack.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_flipped.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_flipped.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_redimg.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_redimg.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_rgbarray.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_rgbarray.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_scaledown.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_scaledown.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_scaleup.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_scaleup.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_soften.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_soften.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_striped.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_striped.png',
   'DATA'),
  ('pygame/docs/generated/_images/surfarray_xfade.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/surfarray_xfade.png',
   'DATA'),
  ('pygame/docs/generated/_images/tom_basic.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/tom_basic.png',
   'DATA'),
  ('pygame/docs/generated/_images/tom_event-flowchart.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/tom_event-flowchart.png',
   'DATA'),
  ('pygame/docs/generated/_images/tom_formulae.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/tom_formulae.png',
   'DATA'),
  ('pygame/docs/generated/_images/tom_radians.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_images/tom_radians.png',
   'DATA'),
  ('pygame/docs/generated/_sources/c_api.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/c_api.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/filepaths.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/filepaths.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/index.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/index.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/logos.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/logos.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/bufferproxy.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/bufferproxy.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/camera.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/camera.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/cdrom.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/cdrom.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/color.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/color.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/color_list.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/color_list.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/cursors.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/cursors.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/display.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/display.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/draw.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/draw.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/event.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/event.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/examples.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/examples.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/fastevent.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/fastevent.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/font.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/font.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/freetype.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/freetype.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/gfxdraw.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/gfxdraw.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/image.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/image.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/joystick.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/joystick.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/key.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/key.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/locals.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/locals.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/mask.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/mask.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/math.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/math.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/midi.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/midi.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/mixer.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/mixer.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/mouse.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/mouse.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/music.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/music.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/overlay.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/overlay.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/pixelarray.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/pixelarray.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/pixelcopy.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/pixelcopy.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/pygame.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/pygame.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/rect.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/rect.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/scrap.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/scrap.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/sdl2_controller.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/sdl2_controller.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/sdl2_video.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/sdl2_video.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/sndarray.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/sndarray.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/sprite.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/sprite.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/surface.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/surface.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/surfarray.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/surfarray.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/tests.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/tests.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/time.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/time.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/touch.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/touch.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_sources/ref/transform.rst.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_sources/ref/transform.rst.txt',
   'DATA'),
  ('pygame/docs/generated/_static/_sphinx_javascript_frameworks_compat.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/_sphinx_javascript_frameworks_compat.js',
   'DATA'),
  ('pygame/docs/generated/_static/basic.css',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/basic.css',
   'DATA'),
  ('pygame/docs/generated/_static/doctools.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/doctools.js',
   'DATA'),
  ('pygame/docs/generated/_static/documentation_options.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/documentation_options.js',
   'DATA'),
  ('pygame/docs/generated/_static/file.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/file.png',
   'DATA'),
  ('pygame/docs/generated/_static/jquery-3.6.0.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/jquery-3.6.0.js',
   'DATA'),
  ('pygame/docs/generated/_static/jquery.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/jquery.js',
   'DATA'),
  ('pygame/docs/generated/_static/language_data.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/language_data.js',
   'DATA'),
  ('pygame/docs/generated/_static/legacy_logos.zip',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/legacy_logos.zip',
   'DATA'),
  ('pygame/docs/generated/_static/minus.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/minus.png',
   'DATA'),
  ('pygame/docs/generated/_static/plus.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/plus.png',
   'DATA'),
  ('pygame/docs/generated/_static/pygame.css',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame.css',
   'DATA'),
  ('pygame/docs/generated/_static/pygame.ico',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame.ico',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_lofi.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_lofi.png',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_lofi.svg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_lofi.svg',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_logo.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_logo.png',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_logo.svg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_logo.svg',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_powered.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_powered.png',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_powered.svg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_powered.svg',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_powered_lowres.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_powered_lowres.png',
   'DATA'),
  ('pygame/docs/generated/_static/pygame_tiny.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygame_tiny.png',
   'DATA'),
  ('pygame/docs/generated/_static/pygments.css',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/pygments.css',
   'DATA'),
  ('pygame/docs/generated/_static/reset.css',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/reset.css',
   'DATA'),
  ('pygame/docs/generated/_static/searchtools.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/searchtools.js',
   'DATA'),
  ('pygame/docs/generated/_static/sphinx_highlight.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/sphinx_highlight.js',
   'DATA'),
  ('pygame/docs/generated/_static/tooltip.css',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/tooltip.css',
   'DATA'),
  ('pygame/docs/generated/_static/underscore-1.13.1.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/underscore-1.13.1.js',
   'DATA'),
  ('pygame/docs/generated/_static/underscore.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/_static/underscore.js',
   'DATA'),
  ('pygame/docs/generated/c_api.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api.html',
   'DATA'),
  ('pygame/docs/generated/c_api/base.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/base.html',
   'DATA'),
  ('pygame/docs/generated/c_api/bufferproxy.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/bufferproxy.html',
   'DATA'),
  ('pygame/docs/generated/c_api/color.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/color.html',
   'DATA'),
  ('pygame/docs/generated/c_api/display.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/display.html',
   'DATA'),
  ('pygame/docs/generated/c_api/event.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/event.html',
   'DATA'),
  ('pygame/docs/generated/c_api/freetype.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/freetype.html',
   'DATA'),
  ('pygame/docs/generated/c_api/mixer.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/mixer.html',
   'DATA'),
  ('pygame/docs/generated/c_api/rect.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/rect.html',
   'DATA'),
  ('pygame/docs/generated/c_api/rwobject.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/rwobject.html',
   'DATA'),
  ('pygame/docs/generated/c_api/slots.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/slots.html',
   'DATA'),
  ('pygame/docs/generated/c_api/surface.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/surface.html',
   'DATA'),
  ('pygame/docs/generated/c_api/surflock.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/surflock.html',
   'DATA'),
  ('pygame/docs/generated/c_api/version.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/c_api/version.html',
   'DATA'),
  ('pygame/docs/generated/filepaths.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/filepaths.html',
   'DATA'),
  ('pygame/docs/generated/genindex.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/genindex.html',
   'DATA'),
  ('pygame/docs/generated/index.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/index.html',
   'DATA'),
  ('pygame/docs/generated/logos.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/logos.html',
   'DATA'),
  ('pygame/docs/generated/py-modindex.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/py-modindex.html',
   'DATA'),
  ('pygame/docs/generated/ref/bufferproxy.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/bufferproxy.html',
   'DATA'),
  ('pygame/docs/generated/ref/camera.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/camera.html',
   'DATA'),
  ('pygame/docs/generated/ref/cdrom.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/cdrom.html',
   'DATA'),
  ('pygame/docs/generated/ref/color.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/color.html',
   'DATA'),
  ('pygame/docs/generated/ref/color_list.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/color_list.html',
   'DATA'),
  ('pygame/docs/generated/ref/cursors.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/cursors.html',
   'DATA'),
  ('pygame/docs/generated/ref/display.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/display.html',
   'DATA'),
  ('pygame/docs/generated/ref/draw.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/draw.html',
   'DATA'),
  ('pygame/docs/generated/ref/event.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/event.html',
   'DATA'),
  ('pygame/docs/generated/ref/examples.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/examples.html',
   'DATA'),
  ('pygame/docs/generated/ref/fastevent.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/fastevent.html',
   'DATA'),
  ('pygame/docs/generated/ref/font.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/font.html',
   'DATA'),
  ('pygame/docs/generated/ref/freetype.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/freetype.html',
   'DATA'),
  ('pygame/docs/generated/ref/gfxdraw.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/gfxdraw.html',
   'DATA'),
  ('pygame/docs/generated/ref/image.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/image.html',
   'DATA'),
  ('pygame/docs/generated/ref/joystick.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/joystick.html',
   'DATA'),
  ('pygame/docs/generated/ref/key.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/key.html',
   'DATA'),
  ('pygame/docs/generated/ref/locals.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/locals.html',
   'DATA'),
  ('pygame/docs/generated/ref/mask.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/mask.html',
   'DATA'),
  ('pygame/docs/generated/ref/math.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/math.html',
   'DATA'),
  ('pygame/docs/generated/ref/midi.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/midi.html',
   'DATA'),
  ('pygame/docs/generated/ref/mixer.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/mixer.html',
   'DATA'),
  ('pygame/docs/generated/ref/mouse.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/mouse.html',
   'DATA'),
  ('pygame/docs/generated/ref/music.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/music.html',
   'DATA'),
  ('pygame/docs/generated/ref/overlay.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/overlay.html',
   'DATA'),
  ('pygame/docs/generated/ref/pixelarray.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/pixelarray.html',
   'DATA'),
  ('pygame/docs/generated/ref/pixelcopy.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/pixelcopy.html',
   'DATA'),
  ('pygame/docs/generated/ref/pygame.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/pygame.html',
   'DATA'),
  ('pygame/docs/generated/ref/rect.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/rect.html',
   'DATA'),
  ('pygame/docs/generated/ref/scrap.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/scrap.html',
   'DATA'),
  ('pygame/docs/generated/ref/sdl2_controller.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/sdl2_controller.html',
   'DATA'),
  ('pygame/docs/generated/ref/sdl2_video.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/sdl2_video.html',
   'DATA'),
  ('pygame/docs/generated/ref/sndarray.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/sndarray.html',
   'DATA'),
  ('pygame/docs/generated/ref/sprite.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/sprite.html',
   'DATA'),
  ('pygame/docs/generated/ref/surface.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/surface.html',
   'DATA'),
  ('pygame/docs/generated/ref/surfarray.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/surfarray.html',
   'DATA'),
  ('pygame/docs/generated/ref/tests.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/tests.html',
   'DATA'),
  ('pygame/docs/generated/ref/time.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/time.html',
   'DATA'),
  ('pygame/docs/generated/ref/touch.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/touch.html',
   'DATA'),
  ('pygame/docs/generated/ref/transform.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/ref/transform.html',
   'DATA'),
  ('pygame/docs/generated/search.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/search.html',
   'DATA'),
  ('pygame/docs/generated/searchindex.js',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/searchindex.js',
   'DATA'),
  ('pygame/docs/generated/tut/CameraIntro.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/CameraIntro.html',
   'DATA'),
  ('pygame/docs/generated/tut/ChimpLineByLine.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/ChimpLineByLine.html',
   'DATA'),
  ('pygame/docs/generated/tut/DisplayModes.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/DisplayModes.html',
   'DATA'),
  ('pygame/docs/generated/tut/ImportInit.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/ImportInit.html',
   'DATA'),
  ('pygame/docs/generated/tut/MakeGames.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/MakeGames.html',
   'DATA'),
  ('pygame/docs/generated/tut/MoveIt.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/MoveIt.html',
   'DATA'),
  ('pygame/docs/generated/tut/PygameIntro.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/PygameIntro.html',
   'DATA'),
  ('pygame/docs/generated/tut/SpriteIntro.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/SpriteIntro.html',
   'DATA'),
  ('pygame/docs/generated/tut/SurfarrayIntro.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/SurfarrayIntro.html',
   'DATA'),
  ('pygame/docs/generated/tut/chimp.py.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/chimp.py.html',
   'DATA'),
  ('pygame/docs/generated/tut/newbieguide.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/newbieguide.html',
   'DATA'),
  ('pygame/docs/generated/tut/tom_games2.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/tom_games2.html',
   'DATA'),
  ('pygame/docs/generated/tut/tom_games3.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/tom_games3.html',
   'DATA'),
  ('pygame/docs/generated/tut/tom_games4.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/tom_games4.html',
   'DATA'),
  ('pygame/docs/generated/tut/tom_games5.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/tom_games5.html',
   'DATA'),
  ('pygame/docs/generated/tut/tom_games6.html',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/generated/tut/tom_games6.html',
   'DATA'),
  ('pygame/draw.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/draw.pyi',
   'DATA'),
  ('pygame/event.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/event.pyi',
   'DATA'),
  ('pygame/examples/README.rst',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/README.rst',
   'DATA'),
  ('pygame/examples/data/BGR.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/BGR.png',
   'DATA'),
  ('pygame/examples/data/alien1.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/alien1.gif',
   'DATA'),
  ('pygame/examples/data/alien1.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/alien1.jpg',
   'DATA'),
  ('pygame/examples/data/alien1.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/alien1.png',
   'DATA'),
  ('pygame/examples/data/alien2.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/alien2.gif',
   'DATA'),
  ('pygame/examples/data/alien2.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/alien2.png',
   'DATA'),
  ('pygame/examples/data/alien3.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/alien3.gif',
   'DATA'),
  ('pygame/examples/data/alien3.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/alien3.png',
   'DATA'),
  ('pygame/examples/data/arraydemo.bmp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/arraydemo.bmp',
   'DATA'),
  ('pygame/examples/data/asprite.bmp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/asprite.bmp',
   'DATA'),
  ('pygame/examples/data/background.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/background.gif',
   'DATA'),
  ('pygame/examples/data/black.ppm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/black.ppm',
   'DATA'),
  ('pygame/examples/data/blue.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/blue.gif',
   'DATA'),
  ('pygame/examples/data/blue.mpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/blue.mpg',
   'DATA'),
  ('pygame/examples/data/bomb.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/bomb.gif',
   'DATA'),
  ('pygame/examples/data/boom.wav',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/boom.wav',
   'DATA'),
  ('pygame/examples/data/brick.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/brick.png',
   'DATA'),
  ('pygame/examples/data/car_door.wav',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/car_door.wav',
   'DATA'),
  ('pygame/examples/data/chimp.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/chimp.png',
   'DATA'),
  ('pygame/examples/data/city.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/city.png',
   'DATA'),
  ('pygame/examples/data/crimson.pnm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/crimson.pnm',
   'DATA'),
  ('pygame/examples/data/cursor.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/cursor.png',
   'DATA'),
  ('pygame/examples/data/danger.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/danger.gif',
   'DATA'),
  ('pygame/examples/data/explosion1.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/explosion1.gif',
   'DATA'),
  ('pygame/examples/data/fist.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/fist.png',
   'DATA'),
  ('pygame/examples/data/green.pcx',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/green.pcx',
   'DATA'),
  ('pygame/examples/data/grey.pgm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/grey.pgm',
   'DATA'),
  ('pygame/examples/data/house_lo.mp3',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/house_lo.mp3',
   'DATA'),
  ('pygame/examples/data/house_lo.ogg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/house_lo.ogg',
   'DATA'),
  ('pygame/examples/data/house_lo.wav',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/house_lo.wav',
   'DATA'),
  ('pygame/examples/data/laplacian.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/laplacian.png',
   'DATA'),
  ('pygame/examples/data/liquid.bmp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/liquid.bmp',
   'DATA'),
  ('pygame/examples/data/midikeys.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/midikeys.png',
   'DATA'),
  ('pygame/examples/data/player1.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/player1.gif',
   'DATA'),
  ('pygame/examples/data/punch.wav',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/punch.wav',
   'DATA'),
  ('pygame/examples/data/purple.xpm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/purple.xpm',
   'DATA'),
  ('pygame/examples/data/red.jpg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/red.jpg',
   'DATA'),
  ('pygame/examples/data/sans.ttf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/sans.ttf',
   'DATA'),
  ('pygame/examples/data/scarlet.webp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/scarlet.webp',
   'DATA'),
  ('pygame/examples/data/secosmic_lo.wav',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/secosmic_lo.wav',
   'DATA'),
  ('pygame/examples/data/shot.gif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/shot.gif',
   'DATA'),
  ('pygame/examples/data/static.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/static.png',
   'DATA'),
  ('pygame/examples/data/teal.svg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/teal.svg',
   'DATA'),
  ('pygame/examples/data/turquoise.tif',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/turquoise.tif',
   'DATA'),
  ('pygame/examples/data/whiff.wav',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/whiff.wav',
   'DATA'),
  ('pygame/examples/data/yellow.tga',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/data/yellow.tga',
   'DATA'),
  ('pygame/fastevent.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/fastevent.pyi',
   'DATA'),
  ('pygame/font.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/font.pyi',
   'DATA'),
  ('pygame/freesansbold.ttf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/freesansbold.ttf',
   'DATA'),
  ('pygame/freetype.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/freetype.pyi',
   'DATA'),
  ('pygame/gfxdraw.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/gfxdraw.pyi',
   'DATA'),
  ('pygame/image.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/image.pyi',
   'DATA'),
  ('pygame/joystick.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/joystick.pyi',
   'DATA'),
  ('pygame/key.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/key.pyi',
   'DATA'),
  ('pygame/locals.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/locals.pyi',
   'DATA'),
  ('pygame/mask.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/mask.pyi',
   'DATA'),
  ('pygame/math.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/math.pyi',
   'DATA'),
  ('pygame/midi.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/midi.pyi',
   'DATA'),
  ('pygame/mixer.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/mixer.pyi',
   'DATA'),
  ('pygame/mixer_music.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/mixer_music.pyi',
   'DATA'),
  ('pygame/mouse.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/mouse.pyi',
   'DATA'),
  ('pygame/pixelarray.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pixelarray.pyi',
   'DATA'),
  ('pygame/pixelcopy.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pixelcopy.pyi',
   'DATA'),
  ('pygame/py.typed',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/py.typed',
   'DATA'),
  ('pygame/pygame.ico',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pygame.ico',
   'DATA'),
  ('pygame/pygame_icon.bmp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pygame_icon.bmp',
   'DATA'),
  ('pygame/pygame_icon.icns',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pygame_icon.icns',
   'DATA'),
  ('pygame/pygame_icon_mac.bmp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pygame_icon_mac.bmp',
   'DATA'),
  ('pygame/rect.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/rect.pyi',
   'DATA'),
  ('pygame/rwobject.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/rwobject.pyi',
   'DATA'),
  ('pygame/scrap.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/scrap.pyi',
   'DATA'),
  ('pygame/sndarray.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/sndarray.pyi',
   'DATA'),
  ('pygame/sprite.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/sprite.pyi',
   'DATA'),
  ('pygame/surface.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/surface.pyi',
   'DATA'),
  ('pygame/surfarray.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/surfarray.pyi',
   'DATA'),
  ('pygame/surflock.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/surflock.pyi',
   'DATA'),
  ('pygame/tests/fixtures/fonts/A_PyGameMono-8.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/A_PyGameMono-8.png',
   'DATA'),
  ('pygame/tests/fixtures/fonts/PlayfairDisplaySemibold.ttf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/PlayfairDisplaySemibold.ttf',
   'DATA'),
  ('pygame/tests/fixtures/fonts/PyGameMono-18-100dpi.bdf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/PyGameMono-18-100dpi.bdf',
   'DATA'),
  ('pygame/tests/fixtures/fonts/PyGameMono-18-75dpi.bdf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/PyGameMono-18-75dpi.bdf',
   'DATA'),
  ('pygame/tests/fixtures/fonts/PyGameMono-8.bdf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/PyGameMono-8.bdf',
   'DATA'),
  ('pygame/tests/fixtures/fonts/PyGameMono.otf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/PyGameMono.otf',
   'DATA'),
  ('pygame/tests/fixtures/fonts/test_fixed.otf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/test_fixed.otf',
   'DATA'),
  ('pygame/tests/fixtures/fonts/test_sans.ttf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/test_sans.ttf',
   'DATA'),
  ('pygame/tests/fixtures/fonts/u13079_PyGameMono-8.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/fonts/u13079_PyGameMono-8.png',
   'DATA'),
  ('pygame/tests/fixtures/xbm_cursors/white_sizing.xbm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/xbm_cursors/white_sizing.xbm',
   'DATA'),
  ('pygame/tests/fixtures/xbm_cursors/white_sizing_mask.xbm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/fixtures/xbm_cursors/white_sizing_mask.xbm',
   'DATA'),
  ('pygame/time.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/time.pyi',
   'DATA'),
  ('pygame/transform.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/transform.pyi',
   'DATA'),
  ('pygame/version.pyi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/version.pyi',
   'DATA'),
  ('vlc/share/hrtfs/dodeca_and_7channel_3DSL_HRTF.sofa',
   '/Applications/VLC.app/Contents/MacOS/share/hrtfs/dodeca_and_7channel_3DSL_HRTF.sofa',
   'DATA'),
  ('vlc/share/locale/ach/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ach/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/af/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/af/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/am/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/am/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/am_ET/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/am_ET/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/an/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/an/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ar/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ar/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/as_IN/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/as_IN/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ast/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ast/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/be/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/be/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/bg/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/bg/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/bn/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/bn/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/bn_IN/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/bn_IN/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/br/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/br/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/brx/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/brx/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/bs/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/bs/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ca/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ca/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ca@valencia/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ca@valencia/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/cgg/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/cgg/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/co/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/co/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/cs/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/cs/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/cy/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/cy/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/da/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/da/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/de/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/de/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/el/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/el/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/en_GB/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/en_GB/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/eo/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/eo/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/es/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/es/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/es_MX/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/es_MX/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/et/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/et/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/eu/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/eu/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/fa/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/fa/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ff/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ff/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/fi/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/fi/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/fr/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/fr/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/fur/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/fur/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/fy/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/fy/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ga/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ga/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/gd/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/gd/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/gl/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/gl/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/gu/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/gu/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/he/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/he/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/hi/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/hi/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/hr/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/hr/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/hu/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/hu/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/hy/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/hy/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/id/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/id/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ie/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ie/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/is/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/is/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/it/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/it/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ja/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ja/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ka/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ka/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/kab/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/kab/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/kk/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/kk/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/km/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/km/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/kn/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/kn/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ko/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ko/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ks_IN/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ks_IN/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ku_IQ/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ku_IQ/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ky/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ky/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/lg/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/lg/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/lo/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/lo/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/lt/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/lt/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/lv/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/lv/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/mai/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/mai/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/mk/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/mk/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ml/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ml/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/mn/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/mn/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/mr/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/mr/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ms/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ms/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/my/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/my/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/nb/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/nb/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ne/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ne/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/nl/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/nl/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/nn/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/nn/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/oc/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/oc/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/or_IN/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/or_IN/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/pa/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/pa/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/pl/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/pl/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ps/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ps/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/pt_BR/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/pt_BR/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/pt_PT/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/pt_PT/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ro/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ro/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ru/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ru/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/si/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/si/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/sk/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/sk/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/sl/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/sl/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/sm/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/sm/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/sq/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/sq/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/sr/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/sr/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/sv/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/sv/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/sw/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/sw/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ta/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ta/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/te/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/te/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/th/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/th/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/tr/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/tr/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/tt/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/tt/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/ug/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/ug/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/uk/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/uk/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/uz/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/uz/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/vi/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/vi/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/wa/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/wa/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/zh_CN/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/zh_CN/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/zh_TW/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/zh_TW/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/locale/zu/LC_MESSAGES/vlc.mo',
   '/Applications/VLC.app/Contents/MacOS/share/locale/zu/LC_MESSAGES/vlc.mo',
   'DATA'),
  ('vlc/share/lua/extensions/VLSub.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/extensions/VLSub.luac',
   'DATA'),
  ('vlc/share/lua/http/css/main.css',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/main.css',
   'DATA'),
  ('vlc/share/lua/http/css/mobile.css',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/mobile.css',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_diagonals-thick_18_b81900_40x40.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_diagonals-thick_18_b81900_40x40.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_diagonals-thick_20_666666_40x40.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_diagonals-thick_20_666666_40x40.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_flat_10_000000_40x100.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_flat_10_000000_40x100.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_glass_100_f6f6f6_1x400.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_glass_100_f6f6f6_1x400.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_glass_100_fdf5ce_1x400.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_glass_100_fdf5ce_1x400.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_glass_65_ffffff_1x400.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_glass_65_ffffff_1x400.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_gloss-wave_35_f6a828_500x100.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_gloss-wave_35_f6a828_500x100.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_highlight-soft_100_eeeeee_1x100.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_highlight-soft_100_eeeeee_1x100.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-bg_highlight-soft_75_ffe45c_1x100.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-bg_highlight-soft_75_ffe45c_1x100.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-icons_222222_256x240.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-icons_222222_256x240.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-icons_228ef1_256x240.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-icons_228ef1_256x240.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-icons_ef8c08_256x240.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-icons_ef8c08_256x240.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-icons_ffd27a_256x240.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-icons_ffd27a_256x240.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/images/ui-icons_ffffff_256x240.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/images/ui-icons_ffffff_256x240.png',
   'DATA'),
  ('vlc/share/lua/http/css/ui-lightness/jquery-ui-1.8.13.custom.css',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/css/ui-lightness/jquery-ui-1.8.13.custom.css',
   'DATA'),
  ('vlc/share/lua/http/custom.lua',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/custom.lua',
   'DATA'),
  ('vlc/share/lua/http/dialogs/batch_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/batch_window.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/browse_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/browse_window.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/create_stream.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/create_stream.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/equalizer_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/equalizer_window.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/error_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/error_window.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/mosaic_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/mosaic_window.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/offset_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/offset_window.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/stream_config_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/stream_config_window.html',
   'DATA'),
  ('vlc/share/lua/http/dialogs/stream_window.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/dialogs/stream_window.html',
   'DATA'),
  ('vlc/share/lua/http/favicon.ico',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/favicon.ico',
   'DATA'),
  ('vlc/share/lua/http/images/Audio-48.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/Audio-48.png',
   'DATA'),
  ('vlc/share/lua/http/images/Back-48.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/Back-48.png',
   'DATA'),
  ('vlc/share/lua/http/images/Folder-48.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/Folder-48.png',
   'DATA'),
  ('vlc/share/lua/http/images/Other-48.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/Other-48.png',
   'DATA'),
  ('vlc/share/lua/http/images/Video-48.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/Video-48.png',
   'DATA'),
  ('vlc/share/lua/http/images/buttons.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/buttons.png',
   'DATA'),
  ('vlc/share/lua/http/images/speaker-32.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/speaker-32.png',
   'DATA'),
  ('vlc/share/lua/http/images/vlc-48.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/vlc-48.png',
   'DATA'),
  ('vlc/share/lua/http/images/vlc16x16.png',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/images/vlc16x16.png',
   'DATA'),
  ('vlc/share/lua/http/index.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/index.html',
   'DATA'),
  ('vlc/share/lua/http/js/common.js',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/js/common.js',
   'DATA'),
  ('vlc/share/lua/http/js/controllers.js',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/js/controllers.js',
   'DATA'),
  ('vlc/share/lua/http/js/jquery.jstree.js',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/js/jquery.jstree.js',
   'DATA'),
  ('vlc/share/lua/http/js/ui.js',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/js/ui.js',
   'DATA'),
  ('vlc/share/lua/http/mobile.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/mobile.html',
   'DATA'),
  ('vlc/share/lua/http/mobile_browse.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/mobile_browse.html',
   'DATA'),
  ('vlc/share/lua/http/mobile_equalizer.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/mobile_equalizer.html',
   'DATA'),
  ('vlc/share/lua/http/mobile_view.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/mobile_view.html',
   'DATA'),
  ('vlc/share/lua/http/requests/README.txt',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/README.txt',
   'DATA'),
  ('vlc/share/lua/http/requests/browse.json',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/browse.json',
   'DATA'),
  ('vlc/share/lua/http/requests/browse.xml',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/browse.xml',
   'DATA'),
  ('vlc/share/lua/http/requests/playlist.json',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/playlist.json',
   'DATA'),
  ('vlc/share/lua/http/requests/playlist.xml',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/playlist.xml',
   'DATA'),
  ('vlc/share/lua/http/requests/playlist_jstree.xml',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/playlist_jstree.xml',
   'DATA'),
  ('vlc/share/lua/http/requests/status.json',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/status.json',
   'DATA'),
  ('vlc/share/lua/http/requests/status.xml',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/status.xml',
   'DATA'),
  ('vlc/share/lua/http/requests/vlm.xml',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/vlm.xml',
   'DATA'),
  ('vlc/share/lua/http/requests/vlm_cmd.xml',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/requests/vlm_cmd.xml',
   'DATA'),
  ('vlc/share/lua/http/view.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/view.html',
   'DATA'),
  ('vlc/share/lua/http/vlm.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/vlm.html',
   'DATA'),
  ('vlc/share/lua/http/vlm_export.html',
   '/Applications/VLC.app/Contents/MacOS/share/lua/http/vlm_export.html',
   'DATA'),
  ('vlc/share/lua/intf/cli.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/cli.luac',
   'DATA'),
  ('vlc/share/lua/intf/dummy.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/dummy.luac',
   'DATA'),
  ('vlc/share/lua/intf/dumpmeta.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/dumpmeta.luac',
   'DATA'),
  ('vlc/share/lua/intf/http.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/http.luac',
   'DATA'),
  ('vlc/share/lua/intf/luac.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/luac.luac',
   'DATA'),
  ('vlc/share/lua/intf/modules/host.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/modules/host.luac',
   'DATA'),
  ('vlc/share/lua/intf/modules/httprequests.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/modules/httprequests.luac',
   'DATA'),
  ('vlc/share/lua/intf/telnet.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/intf/telnet.luac',
   'DATA'),
  ('vlc/share/lua/meta/art/00_musicbrainz.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/meta/art/00_musicbrainz.luac',
   'DATA'),
  ('vlc/share/lua/meta/art/01_googleimage.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/meta/art/01_googleimage.luac',
   'DATA'),
  ('vlc/share/lua/meta/art/02_frenchtv.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/meta/art/02_frenchtv.luac',
   'DATA'),
  ('vlc/share/lua/meta/art/03_lastfm.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/meta/art/03_lastfm.luac',
   'DATA'),
  ('vlc/share/lua/meta/reader/filename.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/meta/reader/filename.luac',
   'DATA'),
  ('vlc/share/lua/modules/common.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/modules/common.luac',
   'DATA'),
  ('vlc/share/lua/modules/dkjson.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/modules/dkjson.luac',
   'DATA'),
  ('vlc/share/lua/modules/sandbox.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/modules/sandbox.luac',
   'DATA'),
  ('vlc/share/lua/modules/simplexml.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/modules/simplexml.luac',
   'DATA'),
  ('vlc/share/lua/playlist/anevia_streams.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/anevia_streams.luac',
   'DATA'),
  ('vlc/share/lua/playlist/anevia_xml.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/anevia_xml.luac',
   'DATA'),
  ('vlc/share/lua/playlist/appletrailers.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/appletrailers.luac',
   'DATA'),
  ('vlc/share/lua/playlist/bbc_co_uk.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/bbc_co_uk.luac',
   'DATA'),
  ('vlc/share/lua/playlist/cue.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/cue.luac',
   'DATA'),
  ('vlc/share/lua/playlist/dailymotion.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/dailymotion.luac',
   'DATA'),
  ('vlc/share/lua/playlist/jamendo.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/jamendo.luac',
   'DATA'),
  ('vlc/share/lua/playlist/koreus.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/koreus.luac',
   'DATA'),
  ('vlc/share/lua/playlist/liveleak.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/liveleak.luac',
   'DATA'),
  ('vlc/share/lua/playlist/newgrounds.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/newgrounds.luac',
   'DATA'),
  ('vlc/share/lua/playlist/rockbox_fm_presets.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/rockbox_fm_presets.luac',
   'DATA'),
  ('vlc/share/lua/playlist/soundcloud.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/soundcloud.luac',
   'DATA'),
  ('vlc/share/lua/playlist/twitch.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/twitch.luac',
   'DATA'),
  ('vlc/share/lua/playlist/vimeo.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/vimeo.luac',
   'DATA'),
  ('vlc/share/lua/playlist/vocaroo.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/vocaroo.luac',
   'DATA'),
  ('vlc/share/lua/playlist/youtube.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/playlist/youtube.luac',
   'DATA'),
  ('vlc/share/lua/sd/icecast.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/sd/icecast.luac',
   'DATA'),
  ('vlc/share/lua/sd/jamendo.luac',
   '/Applications/VLC.app/Contents/MacOS/share/lua/sd/jamendo.luac',
   'DATA'),
  ('setuptools/_vendor/jaraco/text/Lorem ipsum.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/Lorem '
   'ipsum.txt',
   'DATA'),
  ('importlib_metadata-7.2.1.dist-info/LICENSE',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata-7.2.1.dist-info/LICENSE',
   'DATA'),
  ('importlib_metadata-7.2.1.dist-info/METADATA',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata-7.2.1.dist-info/METADATA',
   'DATA'),
  ('importlib_metadata-7.2.1.dist-info/RECORD',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata-7.2.1.dist-info/RECORD',
   'DATA'),
  ('importlib_metadata-7.2.1.dist-info/INSTALLER',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata-7.2.1.dist-info/INSTALLER',
   'DATA'),
  ('importlib_metadata-7.2.1.dist-info/top_level.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata-7.2.1.dist-info/top_level.txt',
   'DATA'),
  ('importlib_metadata-7.2.1.dist-info/WHEEL',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata-7.2.1.dist-info/WHEEL',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_en.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_ar.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_de.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_cs.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_nl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_fa.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_fa.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_fr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_sk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_hr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_ja.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_pt_BR.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_ru.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_hu.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_ko.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_zh_CN.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_uk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_fi.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_fi.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_ca.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_es.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_pl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_tr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_da.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_nn.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_it.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_zh_TW.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtmultimedia_bg.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtmultimedia_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_cs.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_en.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_es.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_cs.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sv.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_sv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ja.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_he.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_he.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_en.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ko.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ca.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_bg.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_lv.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_lv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_it.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_bg.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_cs.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_sk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pt_BR.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_sk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_hr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fa.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_fa.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_zh_TW.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ar.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_he.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_he.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_es.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pt_PT.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_pt_PT.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_uk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_nn.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_nl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_nn.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_tr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ru.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_it.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_lt.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_lt.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_pt_BR.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_pl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_pl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_de.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_lv.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_lv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_zh_TW.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ko.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ca.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fi.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_fi.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_bg.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_sl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ar.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ja.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_sl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_sl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_tr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_hu.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_uk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fa.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_fa.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ja.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_es.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_it.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fi.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_fi.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_gd.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_gd.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_de.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_da.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_gl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_gl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ca.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ar.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_hu.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_da.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_gd.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_gd.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ko.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_nn.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ru.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_gl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_gl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_da.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_hr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_zh_TW.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_zh_CN.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_fr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_hr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_nl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_uk.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_zh_CN.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ru.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_nl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pl.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_en.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_de.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_zh_CN.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_pt_BR.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_hu.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_tr.qm',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_tr.qm',
   'DATA'),
  ('cv2/load_config_py3.py',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/load_config_py3.py',
   'DATA'),
  ('cv2/config.py',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/config.py',
   'DATA'),
  ('cv2/config-3.py',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/config-3.py',
   'DATA'),
  ('Python', 'Python.framework/Versions/3.12/Python', 'SYMLINK'),
  ('altgraph-0.17.4.dist-info/INSTALLER',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/altgraph-0.17.4.dist-info/INSTALLER',
   'DATA'),
  ('setuptools/_vendor/wheel-0.43.0.dist-info/WHEEL',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/WHEEL',
   'DATA'),
  ('altgraph-0.17.4.dist-info/METADATA',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/altgraph-0.17.4.dist-info/METADATA',
   'DATA'),
  ('altgraph-0.17.4.dist-info/LICENSE',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/altgraph-0.17.4.dist-info/LICENSE',
   'DATA'),
  ('altgraph-0.17.4.dist-info/RECORD',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/altgraph-0.17.4.dist-info/RECORD',
   'DATA'),
  ('altgraph-0.17.4.dist-info/WHEEL',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/altgraph-0.17.4.dist-info/WHEEL',
   'DATA'),
  ('altgraph-0.17.4.dist-info/top_level.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/altgraph-0.17.4.dist-info/top_level.txt',
   'DATA'),
  ('setuptools/_vendor/wheel-0.43.0.dist-info/entry_points.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/entry_points.txt',
   'DATA'),
  ('altgraph-0.17.4.dist-info/zip-safe',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/altgraph-0.17.4.dist-info/zip-safe',
   'DATA'),
  ('setuptools/_vendor/wheel-0.43.0.dist-info/REQUESTED',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/REQUESTED',
   'DATA'),
  ('setuptools/_vendor/wheel-0.43.0.dist-info/METADATA',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/METADATA',
   'DATA'),
  ('setuptools/_vendor/wheel-0.43.0.dist-info/RECORD',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/RECORD',
   'DATA'),
  ('setuptools/_vendor/wheel-0.43.0.dist-info/INSTALLER',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/INSTALLER',
   'DATA'),
  ('setuptools/_vendor/wheel-0.43.0.dist-info/LICENSE.txt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/LICENSE.txt',
   'DATA'),
  ('cv2/__init__.py',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/__init__.py',
   'DATA'),
  ('cv2/version.py',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/version.py',
   'DATA'),
  ('cv2/utils/__init__.py',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/utils/__init__.py',
   'DATA'),
  ('cv2/typing/__init__.py',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/typing/__init__.py',
   'DATA'),
  ('cv2/misc/version.py',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/misc/version.py',
   'DATA'),
  ('cv2/misc/__init__.py',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/misc/__init__.py',
   'DATA'),
  ('cv2/mat_wrapper/__init__.py',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/mat_wrapper/__init__.py',
   'DATA'),
  ('cv2/gapi/__init__.py',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/gapi/__init__.py',
   'DATA'),
  ('cv2/data/__init__.py',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/cv2/data/__init__.py',
   'DATA'),
  ('base_library.zip',
   '/Users/<USER>/coding/yesoncheck/build/YesonVideoPlayer_improved/base_library.zip',
   'DATA'),
  ('vlc/lib/libvlc.dylib', 'libvlc.5.dylib', 'SYMLINK'),
  ('vlc/lib/libvlccore.dylib', 'libvlccore.9.dylib', 'SYMLINK'),
  ('libogg.0.8.5.dylib', 'pygame/.dylibs/libogg.0.8.5.dylib', 'SYMLINK'),
  ('libz.1.2.11.zlib-ng.dylib',
   'pygame/.dylibs/libz.1.2.11.zlib-ng.dylib',
   'SYMLINK'),
  ('libjpeg.62.3.0.dylib', 'pygame/.dylibs/libjpeg.62.3.0.dylib', 'SYMLINK'),
  ('libpng16.16.dylib', 'pygame/.dylibs/libpng16.16.dylib', 'SYMLINK'),
  ('libsharpyuv.0.dylib', 'pygame/.dylibs/libsharpyuv.0.dylib', 'SYMLINK'),
  ('libSDL2-2.0.0.dylib', 'pygame/.dylibs/libSDL2-2.0.0.dylib', 'SYMLINK'),
  ('libwebp.7.dylib', 'pygame/.dylibs/libwebp.7.dylib', 'SYMLINK'),
  ('libtiff.5.8.0.dylib', 'pygame/.dylibs/libtiff.5.8.0.dylib', 'SYMLINK'),
  ('libmpg123.0.dylib', 'pygame/.dylibs/libmpg123.0.dylib', 'SYMLINK'),
  ('libfluidsynth.3.1.1.dylib',
   'pygame/.dylibs/libfluidsynth.3.1.1.dylib',
   'SYMLINK'),
  ('libopusfile.0.dylib', 'pygame/.dylibs/libopusfile.0.dylib', 'SYMLINK'),
  ('libFLAC.8.dylib', 'pygame/.dylibs/libFLAC.8.dylib', 'SYMLINK'),
  ('libopus.0.dylib', 'pygame/.dylibs/libopus.0.dylib', 'SYMLINK'),
  ('libvorbisfile.3.3.8.dylib',
   'pygame/.dylibs/libvorbisfile.3.3.8.dylib',
   'SYMLINK'),
  ('libbrotlicommon.1.0.9.dylib',
   'pygame/.dylibs/libbrotlicommon.1.0.9.dylib',
   'SYMLINK'),
  ('libintl.8.dylib', 'pygame/.dylibs/libintl.8.dylib', 'SYMLINK'),
  ('libglib-2.0.0.dylib', 'pygame/.dylibs/libglib-2.0.0.dylib', 'SYMLINK'),
  ('libgthread-2.0.0.dylib',
   'pygame/.dylibs/libgthread-2.0.0.dylib',
   'SYMLINK'),
  ('libsndfile.1.0.34.dylib',
   'pygame/.dylibs/libsndfile.1.0.34.dylib',
   'SYMLINK'),
  ('libbrotlidec.1.0.9.dylib',
   'pygame/.dylibs/libbrotlidec.1.0.9.dylib',
   'SYMLINK'),
  ('libvorbisenc.2.0.12.dylib',
   'pygame/.dylibs/libvorbisenc.2.0.12.dylib',
   'SYMLINK'),
  ('libvorbis.0.4.9.dylib', 'pygame/.dylibs/libvorbis.0.4.9.dylib', 'SYMLINK'),
  ('Breakpad', 'Breakpad.framework/Versions/A/Breakpad', 'SYMLINK'),
  ('libvorbisenc.2.dylib', 'cv2/.dylibs/libvorbisenc.2.dylib', 'SYMLINK'),
  ('libvorbis.0.dylib', 'cv2/.dylibs/libvorbis.0.dylib', 'SYMLINK'),
  ('libogg.0.dylib', 'libogg.0.8.5.dylib', 'SYMLINK'),
  ('libopencore-amrnb.0.dylib',
   'cv2/.dylibs/libopencore-amrnb.0.dylib',
   'SYMLINK'),
  ('libaribb24.0.dylib', 'cv2/.dylibs/libaribb24.0.dylib', 'SYMLINK'),
  ('liblzma.5.dylib', 'cv2/.dylibs/liblzma.5.dylib', 'SYMLINK'),
  ('librav1e.0.7.dylib', 'librav1e.0.7.1.dylib', 'SYMLINK'),
  ('libdav1d.7.dylib', 'cv2/.dylibs/libdav1d.7.dylib', 'SYMLINK'),
  ('libspeex.1.dylib', 'cv2/.dylibs/libspeex.1.dylib', 'SYMLINK'),
  ('libsoxr.0.dylib', 'libsoxr.0.1.2.dylib', 'SYMLINK'),
  ('libvmaf.3.dylib', 'cv2/.dylibs/libvmaf.3.dylib', 'SYMLINK'),
  ('libopencore-amrwb.0.dylib',
   'cv2/.dylibs/libopencore-amrwb.0.dylib',
   'SYMLINK'),
  ('libX11.6.dylib', 'cv2/.dylibs/libX11.6.dylib', 'SYMLINK'),
  ('libsnappy.1.dylib', 'libsnappy.1.2.1.dylib', 'SYMLINK'),
  ('libtheoraenc.1.dylib', 'cv2/.dylibs/libtheoraenc.1.dylib', 'SYMLINK'),
  ('libx264.164.dylib', 'cv2/.dylibs/libx264.164.dylib', 'SYMLINK'),
  ('libmp3lame.0.dylib', 'cv2/.dylibs/libmp3lame.0.dylib', 'SYMLINK'),
  ('libtheoradec.1.dylib', 'cv2/.dylibs/libtheoradec.1.dylib', 'SYMLINK'),
  ('libbluray.2.dylib', 'cv2/.dylibs/libbluray.2.dylib', 'SYMLINK'),
  ('libarchive.13.dylib', 'cv2/.dylibs/libarchive.13.dylib', 'SYMLINK'),
  ('librist.4.dylib', 'cv2/.dylibs/librist.4.dylib', 'SYMLINK'),
  ('libxcb.1.dylib', 'libxcb.1.1.0.dylib', 'SYMLINK'),
  ('libfreetype.6.dylib', 'pygame/.dylibs/libfreetype.6.dylib', 'SYMLINK'),
  ('libgnutls.30.dylib', 'cv2/.dylibs/libgnutls.30.dylib', 'SYMLINK'),
  ('libfontconfig.1.dylib', 'cv2/.dylibs/libfontconfig.1.dylib', 'SYMLINK'),
  ('libzmq.5.dylib', 'cv2/.dylibs/libzmq.5.dylib', 'SYMLINK'),
  ('libtesseract.5.dylib', 'cv2/.dylibs/libtesseract.5.dylib', 'SYMLINK'),
  ('QtCore', 'PyQt5/Qt5/lib/QtCore.framework/Versions/5/QtCore', 'SYMLINK'),
  ('QtNetwork',
   'PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/QtNetwork',
   'SYMLINK'),
  ('QtMultimedia',
   'PyQt5/Qt5/lib/QtMultimedia.framework/Versions/5/QtMultimedia',
   'SYMLINK'),
  ('QtGui', 'PyQt5/Qt5/lib/QtGui.framework/Versions/5/QtGui', 'SYMLINK'),
  ('QtMultimediaWidgets',
   'PyQt5/Qt5/lib/QtMultimediaWidgets.framework/Versions/5/QtMultimediaWidgets',
   'SYMLINK'),
  ('QtWidgets',
   'PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/QtWidgets',
   'SYMLINK'),
  ('QtDBus', 'PyQt5/Qt5/lib/QtDBus.framework/Versions/5/QtDBus', 'SYMLINK'),
  ('QtPrintSupport',
   'PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/QtPrintSupport',
   'SYMLINK'),
  ('QtSvg', 'PyQt5/Qt5/lib/QtSvg.framework/Versions/5/QtSvg', 'SYMLINK'),
  ('QtWebSockets',
   'PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/QtWebSockets',
   'SYMLINK'),
  ('QtQmlModels',
   'PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/QtQmlModels',
   'SYMLINK'),
  ('QtQml', 'PyQt5/Qt5/lib/QtQml.framework/Versions/5/QtQml', 'SYMLINK'),
  ('QtQuick', 'PyQt5/Qt5/lib/QtQuick.framework/Versions/5/QtQuick', 'SYMLINK'),
  ('libgcc_s.1.1.dylib', 'numpy/.dylibs/libgcc_s.1.1.dylib', 'SYMLINK'),
  ('libgfortran.5.dylib', 'numpy/.dylibs/libgfortran.5.dylib', 'SYMLINK'),
  ('libquadmath.0.dylib', 'numpy/.dylibs/libquadmath.0.dylib', 'SYMLINK'),
  ('libcrypto.3.dylib', 'cv2/.dylibs/libcrypto.3.dylib', 'SYMLINK'),
  ('libssl.3.dylib', 'cv2/.dylibs/libssl.3.dylib', 'SYMLINK'),
  ('libportmidi.2.0.3.dylib',
   'pygame/.dylibs/libportmidi.2.0.3.dylib',
   'SYMLINK'),
  ('libSDL2_mixer-2.0.0.dylib',
   'pygame/.dylibs/libSDL2_mixer-2.0.0.dylib',
   'SYMLINK'),
  ('libSDL2_image-2.0.0.dylib',
   'pygame/.dylibs/libSDL2_image-2.0.0.dylib',
   'SYMLINK'),
  ('libSDL2_ttf-2.0.0.dylib',
   'pygame/.dylibs/libSDL2_ttf-2.0.0.dylib',
   'SYMLINK'),
  ('libopenblas64_.0.dylib', 'numpy/.dylibs/libopenblas64_.0.dylib', 'SYMLINK'),
  ('QtOpenGL',
   'PyQt5/Qt5/lib/QtOpenGL.framework/Versions/5/QtOpenGL',
   'SYMLINK'),
  ('libavcodec.61.3.100.dylib',
   'cv2/.dylibs/libavcodec.61.3.100.dylib',
   'SYMLINK'),
  ('libswscale.8.1.100.dylib',
   'cv2/.dylibs/libswscale.8.1.100.dylib',
   'SYMLINK'),
  ('libavutil.59.8.100.dylib',
   'cv2/.dylibs/libavutil.59.8.100.dylib',
   'SYMLINK'),
  ('libavformat.61.1.100.dylib',
   'cv2/.dylibs/libavformat.61.1.100.dylib',
   'SYMLINK'),
  ('libbrotlidec.1.dylib', 'libbrotlidec.1.1.0.dylib', 'SYMLINK'),
  ('libhwy.1.dylib', 'libhwy.1.2.0.dylib', 'SYMLINK'),
  ('libbrotlienc.1.dylib', 'libbrotlienc.1.1.0.dylib', 'SYMLINK'),
  ('libbrotlicommon.1.dylib', 'libbrotlicommon.1.1.0.dylib', 'SYMLINK'),
  ('libb2.1.dylib', 'cv2/.dylibs/libb2.1.dylib', 'SYMLINK'),
  ('libXdmcp.6.dylib', 'cv2/.dylibs/libXdmcp.6.dylib', 'SYMLINK'),
  ('libcjson.1.dylib', 'libcjson.1.7.18.dylib', 'SYMLINK'),
  ('libtasn1.6.dylib', 'cv2/.dylibs/libtasn1.6.dylib', 'SYMLINK'),
  ('libunistring.5.dylib', 'cv2/.dylibs/libunistring.5.dylib', 'SYMLINK'),
  ('libgmp.10.dylib', 'cv2/.dylibs/libgmp.10.dylib', 'SYMLINK'),
  ('libidn2.0.dylib', 'cv2/.dylibs/libidn2.0.dylib', 'SYMLINK'),
  ('libp11-kit.0.dylib', 'cv2/.dylibs/libp11-kit.0.dylib', 'SYMLINK'),
  ('libsodium.26.dylib', 'cv2/.dylibs/libsodium.26.dylib', 'SYMLINK'),
  ('libleptonica.6.dylib', 'cv2/.dylibs/libleptonica.6.dylib', 'SYMLINK'),
  ('librav1e.0.7.1.dylib', 'cv2/.dylibs/librav1e.0.7.1.dylib', 'SYMLINK'),
  ('libsoxr.0.1.2.dylib', 'cv2/.dylibs/libsoxr.0.1.2.dylib', 'SYMLINK'),
  ('libswresample.5.1.100.dylib',
   'cv2/.dylibs/libswresample.5.1.100.dylib',
   'SYMLINK'),
  ('libvpx.8.dylib', 'cv2/.dylibs/libvpx.8.dylib', 'SYMLINK'),
  ('libjxl_threads.0.10.2.dylib',
   'cv2/.dylibs/libjxl_threads.0.10.2.dylib',
   'SYMLINK'),
  ('libx265.209.dylib', 'cv2/.dylibs/libx265.209.dylib', 'SYMLINK'),
  ('libaom.3.9.1.dylib', 'cv2/.dylibs/libaom.3.9.1.dylib', 'SYMLINK'),
  ('libjxl.0.10.2.dylib', 'cv2/.dylibs/libjxl.0.10.2.dylib', 'SYMLINK'),
  ('libwebp.7.1.9.dylib', 'cv2/.dylibs/libwebp.7.1.9.dylib', 'SYMLINK'),
  ('libsnappy.1.2.1.dylib', 'cv2/.dylibs/libsnappy.1.2.1.dylib', 'SYMLINK'),
  ('libSvtAv1Enc.2.1.0.dylib',
   'cv2/.dylibs/libSvtAv1Enc.2.1.0.dylib',
   'SYMLINK'),
  ('libwebpmux.3.1.0.dylib', 'cv2/.dylibs/libwebpmux.3.1.0.dylib', 'SYMLINK'),
  ('libopenjp2.2.5.2.dylib', 'cv2/.dylibs/libopenjp2.2.5.2.dylib', 'SYMLINK'),
  ('libsrt.1.5.3.dylib', 'cv2/.dylibs/libsrt.1.5.3.dylib', 'SYMLINK'),
  ('libssh.4.9.6.dylib', 'cv2/.dylibs/libssh.4.9.6.dylib', 'SYMLINK'),
  ('liblz4.1.9.4.dylib', 'cv2/.dylibs/liblz4.1.9.4.dylib', 'SYMLINK'),
  ('libzstd.1.5.6.dylib', 'cv2/.dylibs/libzstd.1.5.6.dylib', 'SYMLINK'),
  ('liblcms2.2.dylib', 'cv2/.dylibs/liblcms2.2.dylib', 'SYMLINK'),
  ('libgif.dylib', 'libgif.7.2.0.dylib', 'SYMLINK'),
  ('libtiff.6.dylib', 'cv2/.dylibs/libtiff.6.dylib', 'SYMLINK'),
  ('libjpeg.8.dylib', 'libjpeg.8.3.2.dylib', 'SYMLINK'),
  ('libbrotlicommon.1.1.0.dylib',
   'cv2/.dylibs/libbrotlicommon.1.1.0.dylib',
   'SYMLINK'),
  ('libhwy.1.2.0.dylib', 'cv2/.dylibs/libhwy.1.2.0.dylib', 'SYMLINK'),
  ('libbrotlidec.1.1.0.dylib',
   'cv2/.dylibs/libbrotlidec.1.1.0.dylib',
   'SYMLINK'),
  ('libbrotlienc.1.1.0.dylib',
   'cv2/.dylibs/libbrotlienc.1.1.0.dylib',
   'SYMLINK'),
  ('libjxl_cms.0.10.2.dylib', 'cv2/.dylibs/libjxl_cms.0.10.2.dylib', 'SYMLINK'),
  ('libxcb.1.1.0.dylib', 'cv2/.dylibs/libxcb.1.1.0.dylib', 'SYMLINK'),
  ('libsharpyuv.0.1.0.dylib', 'cv2/.dylibs/libsharpyuv.0.1.0.dylib', 'SYMLINK'),
  ('libnettle.8.8.dylib', 'cv2/.dylibs/libnettle.8.8.dylib', 'SYMLINK'),
  ('libhogweed.6.8.dylib', 'cv2/.dylibs/libhogweed.6.8.dylib', 'SYMLINK'),
  ('libcjson.1.7.18.dylib', 'cv2/.dylibs/libcjson.1.7.18.dylib', 'SYMLINK'),
  ('libmbedcrypto.3.6.0.dylib',
   'cv2/.dylibs/libmbedcrypto.3.6.0.dylib',
   'SYMLINK'),
  ('libgif.7.2.0.dylib', 'cv2/.dylibs/libgif.7.2.0.dylib', 'SYMLINK'),
  ('libjpeg.8.3.2.dylib', 'cv2/.dylibs/libjpeg.8.3.2.dylib', 'SYMLINK'),
  ('libXau.6.0.0.dylib', 'cv2/.dylibs/libXau.6.0.0.dylib', 'SYMLINK'),
  ('Breakpad.framework/Breakpad', 'Versions/Current/Breakpad', 'SYMLINK'),
  ('Breakpad.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Breakpad.framework/Versions/A/Resources/Info.plist',
   '/Applications/VLC.app/Contents/Frameworks/Breakpad.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('Breakpad.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtCore.framework/QtCore',
   'Versions/Current/QtCore',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtCore.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtCore.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtCore.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtCore.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtDBus.framework/QtDBus',
   'Versions/Current/QtDBus',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtDBus.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtDBus.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtGui.framework/QtGui', 'Versions/Current/QtGui', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtGui.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtGui.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtGui.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtGui.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtMultimedia.framework/QtMultimedia',
   'Versions/Current/QtMultimedia',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtMultimedia.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtMultimedia.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtMultimedia.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtMultimedia.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtMultimediaWidgets.framework/QtMultimediaWidgets',
   'Versions/Current/QtMultimediaWidgets',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtMultimediaWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtMultimediaWidgets.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtMultimediaWidgets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtMultimediaWidgets.framework/Versions/Current',
   '5',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/QtNetwork',
   'Versions/Current/QtNetwork',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtNetwork.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtNetwork.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtOpenGL.framework/QtOpenGL',
   'Versions/Current/QtOpenGL',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtOpenGL.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtOpenGL.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtOpenGL.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtOpenGL.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/QtPrintSupport',
   'Versions/Current/QtPrintSupport',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtPrintSupport.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtPrintSupport.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQml.framework/QtQml', 'Versions/Current/QtQml', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQml.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQml.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQml.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtQml.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/QtQmlModels',
   'Versions/Current/QtQmlModels',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQmlModels.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtQmlModels.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQuick.framework/QtQuick',
   'Versions/Current/QtQuick',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtQuick.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtQuick.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtSvg.framework/QtSvg', 'Versions/Current/QtSvg', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtSvg.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtSvg.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/QtWebSockets',
   'Versions/Current/QtWebSockets',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtWebSockets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtWebSockets.framework/Versions/Current', '5', 'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/QtWidgets',
   'Versions/Current/QtWidgets',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Versions/5/Resources/Info.plist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/Qt5/lib/QtWidgets.framework/Resources/Info.plist',
   'DATA'),
  ('PyQt5/Qt5/lib/QtWidgets.framework/Versions/Current', '5', 'SYMLINK'),
  ('Python.framework/Python', 'Versions/Current/Python', 'SYMLINK'),
  ('Python.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python.framework/Versions/3.12/Resources/Info.plist',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/Resources/Info.plist',
   'DATA'),
  ('Python.framework/Versions/Current', '3.12', 'SYMLINK')])
