
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named winreg - imported by importlib._bootstrap_external (conditional), platform (delayed, optional), mimetypes (optional), urllib.request (delayed, conditional, optional), pygame.sysfont (conditional), setuptools._vendor.platformdirs.windows (delayed, optional), vlc (delayed, conditional, optional), test.libregrtest.win_utils (top-level), setuptools._distutils._msvccompiler (top-level), setuptools.msvc (conditional)
missing module named nt - imported by os (delayed, conditional, optional), ntpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), ctypes (delayed, conditional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named pyimod02_importers - imported by /Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py (delayed), /Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py (delayed)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by setuptools.glob (conditional), setuptools.compat.py311 (conditional), pkg_resources (conditional)
missing module named jnius - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named android - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named msvcrt - imported by subprocess (optional), multiprocessing.spawn (delayed, conditional), multiprocessing.popen_spawn_win32 (top-level), asyncio.windows_events (top-level), asyncio.windows_utils (top-level), getpass (optional), vlc (conditional, optional), PyInstaller.isolated._parent (conditional), pygame.tests.test_utils.async_sub (conditional), test.support.os_helper (delayed, conditional, optional), test.support (delayed, conditional, optional), test.libregrtest.runtests (delayed, conditional), test.libregrtest.run_workers (conditional)
missing module named _winapi - imported by encodings (delayed, conditional, optional), ntpath (optional), shutil (conditional), subprocess (conditional), multiprocessing.connection (optional), multiprocessing.spawn (delayed, conditional), multiprocessing.reduction (conditional), multiprocessing.shared_memory (conditional), multiprocessing.heap (conditional), multiprocessing.popen_spawn_win32 (top-level), asyncio.windows_events (top-level), asyncio.windows_utils (top-level), mimetypes (optional), test.support (delayed, conditional), test.libregrtest.win_utils (top-level)
missing module named _overlapped - imported by asyncio.windows_events (top-level), test.libregrtest.win_utils (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named jaraco.text.yield_lines - imported by setuptools._vendor.jaraco.text (top-level), setuptools._entry_points (top-level), setuptools.command._requirestxt (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named StringIO - imported by pygame.tests.test_utils.test_machinery (optional), pygame.tests.test_utils.png (optional)
missing module named 'test.test_utils' - imported by pygame.tests.test_utils.run_tests (delayed, conditional), pygame.tests.__main__ (conditional), pygame.tests (conditional)
missing module named win32api - imported by pygame.tests.test_utils.async_sub (conditional, optional)
missing module named win32pipe - imported by pygame.tests.test_utils.async_sub (conditional, optional)
missing module named win32file - imported by pygame.tests.test_utils.async_sub (conditional, optional)
missing module named numpy.rint - imported by numpy (top-level), pygame.tests.surfarray_test (top-level)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level), pygame.tests.surfarray_test (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level), pygame.examples.glcube (optional), pygame.tests.sndarray_test (top-level), pygame.tests.surfarray_test (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level), pygame.tests.surfarray_test (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level), pygame.surfarray (top-level), pygame.examples.glcube (optional), pygame.tests.mixer_test (delayed, optional), pygame.tests.surfarray_test (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level), pygame.tests.mixer_test (delayed, optional), pygame.tests.sndarray_test (top-level), pygame.tests.surfarray_test (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level), pygame.tests.mixer_test (delayed, optional), pygame.tests.pixelcopy_test (delayed, optional), pygame.tests.sndarray_test (top-level), pygame.tests.surfarray_test (top-level)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), pygame.surfarray (top-level), pygame.examples.glcube (optional), pygame.tests.mixer_test (delayed, optional), pygame.tests.pixelcopy_test (delayed, optional), pygame.tests.sndarray_test (top-level)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level), pygame.examples.sound_array_demos (top-level), pygame.tests.mixer_test (delayed, optional), pygame.tests.sndarray_test (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level), pygame.tests.mixer_test (delayed, optional), pygame.tests.sndarray_test (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level), pygame.examples.arraydemo (delayed), pygame.examples.sound_array_demos (top-level), pygame.tests.mixer_test (delayed, optional), pygame.tests.pixelcopy_test (delayed, optional)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), pygame.tests.pixelcopy_test (delayed, optional)
missing module named pygame.BufferError - imported by pygame (optional), pygame.tests.bufferproxy_test (optional)
missing module named tkinter - imported by test.support (delayed, conditional, optional)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), pygame.surfarray (top-level)
missing module named pygame.register_quit - imported by pygame (top-level), pygame.fastevent (top-level)
missing module named pygame.error - imported by pygame (top-level), pygame.fastevent (top-level), pygame.camera (top-level)
missing module named pygame._sdl2.messagebox - imported by pygame._sdl2 (top-level), pygame.examples.video (top-level)
missing module named pygame._sdl2.get_drivers - imported by pygame._sdl2 (top-level), pygame.examples.video (top-level)
missing module named pygame._sdl2.Renderer - imported by pygame._sdl2 (top-level), pygame.examples.sprite_texture (top-level), pygame.examples.video (top-level)
missing module named pygame._sdl2.Image - imported by pygame._sdl2 (top-level), pygame.examples.sprite_texture (top-level), pygame.examples.video (top-level)
missing module named pygame._sdl2.Texture - imported by pygame._sdl2 (top-level), pygame.examples.sprite_texture (top-level), pygame.examples.video (top-level)
missing module named pygame._sdl2.Window - imported by pygame._sdl2 (top-level), pygame.examples.sprite_texture (top-level), pygame.examples.video (top-level)
missing module named pickle5 - imported by numpy.compat.py3k (optional)
missing module named _dummy_thread - imported by numpy.core.arrayprint (optional)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed), pygame.examples.glcube (optional)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.max - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.prod - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.recarray - imported by numpy (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named threadpoolctl - imported by numpy.lib.utils (delayed, optional)
missing module named numpy.lib.imag - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.real - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.iscomplexobj - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level), numpy.testing.overrides (top-level)
missing module named psutil._psutil_windows - imported by psutil (conditional)
missing module named win32pdh - imported by numpy.testing._private.utils (delayed, conditional)
missing module named numpy.isinf - imported by numpy (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.isnan - imported by numpy (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.isfinite - imported by numpy (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named 'OpenGL.GLU' - imported by pygame.examples.glcube (optional)
missing module named 'OpenGL.GL' - imported by pygame.examples.glcube (optional)
missing module named pygame.K_9 - imported by pygame (top-level), pygame.examples.blend_fill (top-level)
missing module named pygame.K_8 - imported by pygame (top-level), pygame.examples.blend_fill (top-level)
missing module named pygame.K_7 - imported by pygame (top-level), pygame.examples.blend_fill (top-level)
missing module named pygame.K_6 - imported by pygame (top-level), pygame.examples.blend_fill (top-level)
missing module named pygame.K_5 - imported by pygame (top-level), pygame.examples.blend_fill (top-level)
missing module named pygame.K_4 - imported by pygame (top-level), pygame.examples.blend_fill (top-level)
missing module named pygame.K_3 - imported by pygame (top-level), pygame.examples.blend_fill (top-level)
missing module named pygame.K_2 - imported by pygame (top-level), pygame.examples.blend_fill (top-level)
missing module named pygame.K_1 - imported by pygame (top-level), pygame.examples.blend_fill (top-level)
missing module named pygame._sdl2.AUDIO_ALLOW_FORMAT_CHANGE - imported by pygame._sdl2 (top-level), pygame.examples.audiocapture (top-level)
missing module named pygame._sdl2.AUDIO_F32 - imported by pygame._sdl2 (top-level), pygame.examples.audiocapture (top-level)
missing module named pygame._sdl2.AudioDevice - imported by pygame._sdl2 (top-level), pygame.examples.audiocapture (top-level)
missing module named pygame._sdl2.get_audio_device_names - imported by pygame._sdl2 (top-level), pygame.examples.audiocapture (top-level)
missing module named numpy.uint - imported by numpy (delayed), pygame.examples.arraydemo (delayed)
missing module named VideoCapture - imported by pygame._camera_vidcapture (delayed, optional)
missing module named vidcap - imported by pygame._camera_vidcapture (delayed, optional)
missing module named pefile - imported by PyInstaller.utils.win32.versioninfo (top-level)
missing module named 'macholib.compat' - imported by macholib.MachO (optional)
missing module named 'win32ctypes.pywin32' - imported by PyInstaller.compat (conditional, optional)
missing module named win32ctypes - imported by PyInstaller.compat (conditional, optional)
missing module named pygame.__file__ - imported by pygame (top-level), pygame.__pyinstaller.hook-pygame (top-level)
missing module named OpenGL - imported by pygame (delayed)
missing module named 'pygame.overlay' - imported by pygame (optional)
missing module named 'pygame.cdrom' - imported by pygame (conditional, optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named _wmi - imported by platform (optional)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
