('/Users/<USER>/coding/yesoncheck/build/YesonVideoPlayer_improved/PYZ-00.pyz',
 [('PyInstaller',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/__init__.py',
   'PYMODULE'),
  ('PyInstaller._shared_with_waf',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/_shared_with_waf.py',
   'PYMODULE'),
  ('PyInstaller.building',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/building/__init__.py',
   'PYMODULE'),
  ('PyInstaller.building.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/building/utils.py',
   'PYMODULE'),
  ('PyInstaller.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/compat.py',
   'PYMODULE'),
  ('PyInstaller.config',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/config.py',
   'PYMODULE'),
  ('PyInstaller.depend',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/depend/__init__.py',
   'PYMODULE'),
  ('PyInstaller.depend.imphookapi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/depend/imphookapi.py',
   'PYMODULE'),
  ('PyInstaller.exceptions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/exceptions.py',
   'PYMODULE'),
  ('PyInstaller.isolated',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/isolated/__init__.py',
   'PYMODULE'),
  ('PyInstaller.isolated._parent',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/isolated/_parent.py',
   'PYMODULE'),
  ('PyInstaller.lib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/lib/__init__.py',
   'PYMODULE'),
  ('PyInstaller.lib.modulegraph',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/lib/modulegraph/__init__.py',
   'PYMODULE'),
  ('PyInstaller.lib.modulegraph.modulegraph',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/lib/modulegraph/modulegraph.py',
   'PYMODULE'),
  ('PyInstaller.lib.modulegraph.util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/lib/modulegraph/util.py',
   'PYMODULE'),
  ('PyInstaller.log',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/log.py',
   'PYMODULE'),
  ('PyInstaller.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/utils/__init__.py',
   'PYMODULE'),
  ('PyInstaller.utils.hooks',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/utils/hooks/__init__.py',
   'PYMODULE'),
  ('PyInstaller.utils.hooks.conda',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/utils/hooks/conda.py',
   'PYMODULE'),
  ('PyInstaller.utils.misc',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/utils/misc.py',
   'PYMODULE'),
  ('PyInstaller.utils.osx',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/utils/osx.py',
   'PYMODULE'),
  ('PyInstaller.utils.win32',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/utils/win32/__init__.py',
   'PYMODULE'),
  ('PyInstaller.utils.win32.versioninfo',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/utils/win32/versioninfo.py',
   'PYMODULE'),
  ('PyQt5',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyQt5/__init__.py',
   'PYMODULE'),
  ('__future__',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/__future__.py',
   'PYMODULE'),
  ('_aix_support',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/_distutils_hack/__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/_distutils_hack/override.py',
   'PYMODULE'),
  ('_osx_support',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/qt.py',
   'PYMODULE'),
  ('_sitebuiltins',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_strptime.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_threading_local',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/_threading_local.py',
   'PYMODULE'),
  ('altgraph',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/altgraph/__init__.py',
   'PYMODULE'),
  ('altgraph.Graph',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/altgraph/Graph.py',
   'PYMODULE'),
  ('altgraph.GraphUtil',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/altgraph/GraphUtil.py',
   'PYMODULE'),
  ('altgraph.ObjectGraph',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/altgraph/ObjectGraph.py',
   'PYMODULE'),
  ('argparse',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/argparse.py',
   'PYMODULE'),
  ('ast',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ast.py',
   'PYMODULE'),
  ('asyncio',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.log',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/windows_utils.py',
   'PYMODULE'),
  ('backports',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('base64',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/base64.py',
   'PYMODULE'),
  ('bdb',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bdb.py',
   'PYMODULE'),
  ('bisect',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bisect.py',
   'PYMODULE'),
  ('bz2',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/bz2.py',
   'PYMODULE'),
  ('calendar',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/calendar.py',
   'PYMODULE'),
  ('cmd',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/cmd.py',
   'PYMODULE'),
  ('code',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/code.py',
   'PYMODULE'),
  ('codeop',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/codeop.py',
   'PYMODULE'),
  ('concurrent',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/concurrent/futures/thread.py',
   'PYMODULE'),
  ('configparser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/configparser.py',
   'PYMODULE'),
  ('contextlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextvars.py',
   'PYMODULE'),
  ('copy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/copy.py',
   'PYMODULE'),
  ('csv',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/csv.py',
   'PYMODULE'),
  ('ctypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('ctypes.util',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ctypes/wintypes.py',
   'PYMODULE'),
  ('curses',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/curses/__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/curses/has_key.py',
   'PYMODULE'),
  ('dataclasses',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/dataclasses.py',
   'PYMODULE'),
  ('datetime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/datetime.py',
   'PYMODULE'),
  ('decimal',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/decimal.py',
   'PYMODULE'),
  ('difflib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/difflib.py',
   'PYMODULE'),
  ('dis',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/dis.py',
   'PYMODULE'),
  ('distro',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/distro/__init__.py',
   'PYMODULE'),
  ('distro.distro',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/distro/distro.py',
   'PYMODULE'),
  ('doctest',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/doctest.py',
   'PYMODULE'),
  ('email',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/email/utils.py',
   'PYMODULE'),
  ('fnmatch',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/fractions.py',
   'PYMODULE'),
  ('ftplib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ftplib.py',
   'PYMODULE'),
  ('getopt',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/getopt.py',
   'PYMODULE'),
  ('getpass',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/getpass.py',
   'PYMODULE'),
  ('gettext',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/gettext.py',
   'PYMODULE'),
  ('glob',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/glob.py',
   'PYMODULE'),
  ('gzip',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/gzip.py',
   'PYMODULE'),
  ('hashlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/hashlib.py',
   'PYMODULE'),
  ('hmac',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/hmac.py',
   'PYMODULE'),
  ('html',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/html/entities.py',
   'PYMODULE'),
  ('http',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/__init__.py',
   'PYMODULE'),
  ('http.client',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/client.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/cookiejar.py',
   'PYMODULE'),
  ('http.server',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/server.py',
   'PYMODULE'),
  ('importlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/metadata/_text.py',
   'PYMODULE'),
  ('importlib.readers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.util',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/importlib/util.py',
   'PYMODULE'),
  ('importlib_metadata',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/compat/__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/compat/py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_metadata/compat/py39.py',
   'PYMODULE'),
  ('importlib_resources',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/__init__.py',
   'PYMODULE'),
  ('importlib_resources._adapters',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/_adapters.py',
   'PYMODULE'),
  ('importlib_resources._common',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/_common.py',
   'PYMODULE'),
  ('importlib_resources._functional',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/_functional.py',
   'PYMODULE'),
  ('importlib_resources._itertools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/_itertools.py',
   'PYMODULE'),
  ('importlib_resources.abc',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/abc.py',
   'PYMODULE'),
  ('importlib_resources.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/compat/__init__.py',
   'PYMODULE'),
  ('importlib_resources.compat.py39',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/compat/py39.py',
   'PYMODULE'),
  ('importlib_resources.future',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/future/__init__.py',
   'PYMODULE'),
  ('importlib_resources.future.adapters',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/future/adapters.py',
   'PYMODULE'),
  ('importlib_resources.readers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/importlib_resources/readers.py',
   'PYMODULE'),
  ('inspect',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/inspect.py',
   'PYMODULE'),
  ('ipaddress',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/__init__.py',
   'PYMODULE'),
  ('json.decoder',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py',
   'PYMODULE'),
  ('json.encoder',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/encoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/scanner.py',
   'PYMODULE'),
  ('logging',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/__init__.py',
   'PYMODULE'),
  ('lzma',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/lzma.py',
   'PYMODULE'),
  ('macholib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/macholib/__init__.py',
   'PYMODULE'),
  ('macholib.MachO',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/macholib/MachO.py',
   'PYMODULE'),
  ('macholib.mach_o',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/macholib/mach_o.py',
   'PYMODULE'),
  ('macholib.ptypes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/macholib/ptypes.py',
   'PYMODULE'),
  ('macholib.util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/macholib/util.py',
   'PYMODULE'),
  ('mimetypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/mimetypes.py',
   'PYMODULE'),
  ('more_itertools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/more_itertools/__init__.py',
   'PYMODULE'),
  ('more_itertools.more',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/more_itertools/more.py',
   'PYMODULE'),
  ('more_itertools.recipes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/more_itertools/recipes.py',
   'PYMODULE'),
  ('multiprocessing',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/multiprocessing/util.py',
   'PYMODULE'),
  ('netrc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/netrc.py',
   'PYMODULE'),
  ('nturl2path',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/nturl2path.py',
   'PYMODULE'),
  ('numbers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/numbers.py',
   'PYMODULE'),
  ('numpy',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_typing/_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_utils/__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_utils/_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/_utils/_inspect.py',
   'PYMODULE'),
  ('numpy.array_api',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/array_api/linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/compat/__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/compat/py3k.py',
   'PYMODULE'),
  ('numpy.core',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/core/umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/exceptions.py',
   'PYMODULE'),
  ('numpy.fft',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/fft/__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/fft/_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/fft/helper.py',
   'PYMODULE'),
  ('numpy.lib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/polynomial.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/lib/utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/linalg/__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/linalg/linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/ma/__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/ma/core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/ma/extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/ma/mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/matrixlib/__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/matrixlib/defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/polynomial/polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/random/__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/random/_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/testing/__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/testing/_private/__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/testing/_private/extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/testing/_private/utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/testing/overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/typing/__init__.py',
   'PYMODULE'),
  ('numpy.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/numpy/version.py',
   'PYMODULE'),
  ('opcode',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/opcode.py',
   'PYMODULE'),
  ('optparse',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/optparse.py',
   'PYMODULE'),
  ('packaging',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/licenses/__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/licenses/_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('packaging.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/packaging/version.py',
   'PYMODULE'),
  ('pathlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pathlib.py',
   'PYMODULE'),
  ('pdb',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pdb.py',
   'PYMODULE'),
  ('pickle',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pickle.py',
   'PYMODULE'),
  ('pkg_resources',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pkg_resources/__init__.py',
   'PYMODULE'),
  ('pkgutil',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pkgutil.py',
   'PYMODULE'),
  ('platform',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/plistlib.py',
   'PYMODULE'),
  ('pprint',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pprint.py',
   'PYMODULE'),
  ('psutil',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/__init__.py',
   'PYMODULE'),
  ('psutil._common',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_common.py',
   'PYMODULE'),
  ('psutil._psosx',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_psosx.py',
   'PYMODULE'),
  ('psutil._psposix',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/psutil/_psposix.py',
   'PYMODULE'),
  ('py_compile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/py_compile.py',
   'PYMODULE'),
  ('pydoc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pydoc_data/__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/pydoc_data/topics.py',
   'PYMODULE'),
  ('pygame',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/__init__.py',
   'PYMODULE'),
  ('pygame.__pyinstaller',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/__pyinstaller/__init__.py',
   'PYMODULE'),
  ('pygame.__pyinstaller.hook-pygame',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/__pyinstaller/hook-pygame.py',
   'PYMODULE'),
  ('pygame._camera_opencv',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_camera_opencv.py',
   'PYMODULE'),
  ('pygame._camera_vidcapture',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_camera_vidcapture.py',
   'PYMODULE'),
  ('pygame._sdl2',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/_sdl2/__init__.py',
   'PYMODULE'),
  ('pygame.camera',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/camera.py',
   'PYMODULE'),
  ('pygame.colordict',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/colordict.py',
   'PYMODULE'),
  ('pygame.cursors',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/cursors.py',
   'PYMODULE'),
  ('pygame.docs', '-', 'PYMODULE'),
  ('pygame.docs.__main__',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/docs/__main__.py',
   'PYMODULE'),
  ('pygame.draw_py',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/draw_py.py',
   'PYMODULE'),
  ('pygame.examples',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/__init__.py',
   'PYMODULE'),
  ('pygame.examples.aacircle',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/aacircle.py',
   'PYMODULE'),
  ('pygame.examples.aliens',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/aliens.py',
   'PYMODULE'),
  ('pygame.examples.arraydemo',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/arraydemo.py',
   'PYMODULE'),
  ('pygame.examples.audiocapture',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/audiocapture.py',
   'PYMODULE'),
  ('pygame.examples.blend_fill',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/blend_fill.py',
   'PYMODULE'),
  ('pygame.examples.blit_blends',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/blit_blends.py',
   'PYMODULE'),
  ('pygame.examples.camera',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/camera.py',
   'PYMODULE'),
  ('pygame.examples.chimp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/chimp.py',
   'PYMODULE'),
  ('pygame.examples.cursors',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/cursors.py',
   'PYMODULE'),
  ('pygame.examples.dropevent',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/dropevent.py',
   'PYMODULE'),
  ('pygame.examples.eventlist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/eventlist.py',
   'PYMODULE'),
  ('pygame.examples.font_viewer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/font_viewer.py',
   'PYMODULE'),
  ('pygame.examples.fonty',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/fonty.py',
   'PYMODULE'),
  ('pygame.examples.freetype_misc',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/freetype_misc.py',
   'PYMODULE'),
  ('pygame.examples.glcube',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/glcube.py',
   'PYMODULE'),
  ('pygame.examples.go_over_there',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/go_over_there.py',
   'PYMODULE'),
  ('pygame.examples.grid',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/grid.py',
   'PYMODULE'),
  ('pygame.examples.headless_no_windows_needed',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/headless_no_windows_needed.py',
   'PYMODULE'),
  ('pygame.examples.joystick',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/joystick.py',
   'PYMODULE'),
  ('pygame.examples.liquid',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/liquid.py',
   'PYMODULE'),
  ('pygame.examples.mask',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/mask.py',
   'PYMODULE'),
  ('pygame.examples.midi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/midi.py',
   'PYMODULE'),
  ('pygame.examples.moveit',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/moveit.py',
   'PYMODULE'),
  ('pygame.examples.music_drop_fade',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/music_drop_fade.py',
   'PYMODULE'),
  ('pygame.examples.pixelarray',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/pixelarray.py',
   'PYMODULE'),
  ('pygame.examples.playmus',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/playmus.py',
   'PYMODULE'),
  ('pygame.examples.resizing_new',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/resizing_new.py',
   'PYMODULE'),
  ('pygame.examples.scaletest',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/scaletest.py',
   'PYMODULE'),
  ('pygame.examples.scrap_clipboard',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/scrap_clipboard.py',
   'PYMODULE'),
  ('pygame.examples.scroll',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/scroll.py',
   'PYMODULE'),
  ('pygame.examples.setmodescale',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/setmodescale.py',
   'PYMODULE'),
  ('pygame.examples.sound',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/sound.py',
   'PYMODULE'),
  ('pygame.examples.sound_array_demos',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/sound_array_demos.py',
   'PYMODULE'),
  ('pygame.examples.sprite_texture',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/sprite_texture.py',
   'PYMODULE'),
  ('pygame.examples.stars',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/stars.py',
   'PYMODULE'),
  ('pygame.examples.testsprite',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/testsprite.py',
   'PYMODULE'),
  ('pygame.examples.textinput',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/textinput.py',
   'PYMODULE'),
  ('pygame.examples.vgrade',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/vgrade.py',
   'PYMODULE'),
  ('pygame.examples.video',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/examples/video.py',
   'PYMODULE'),
  ('pygame.fastevent',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/fastevent.py',
   'PYMODULE'),
  ('pygame.freetype',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/freetype.py',
   'PYMODULE'),
  ('pygame.ftfont',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/ftfont.py',
   'PYMODULE'),
  ('pygame.locals',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/locals.py',
   'PYMODULE'),
  ('pygame.macosx',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/macosx.py',
   'PYMODULE'),
  ('pygame.midi',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/midi.py',
   'PYMODULE'),
  ('pygame.pkgdata',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/pkgdata.py',
   'PYMODULE'),
  ('pygame.sndarray',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/sndarray.py',
   'PYMODULE'),
  ('pygame.sprite',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/sprite.py',
   'PYMODULE'),
  ('pygame.surfarray',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/surfarray.py',
   'PYMODULE'),
  ('pygame.sysfont',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/sysfont.py',
   'PYMODULE'),
  ('pygame.tests',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/__init__.py',
   'PYMODULE'),
  ('pygame.tests.__main__',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/__main__.py',
   'PYMODULE'),
  ('pygame.tests.base_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/base_test.py',
   'PYMODULE'),
  ('pygame.tests.blit_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/blit_test.py',
   'PYMODULE'),
  ('pygame.tests.bufferproxy_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/bufferproxy_test.py',
   'PYMODULE'),
  ('pygame.tests.camera_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/camera_test.py',
   'PYMODULE'),
  ('pygame.tests.color_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/color_test.py',
   'PYMODULE'),
  ('pygame.tests.constants_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/constants_test.py',
   'PYMODULE'),
  ('pygame.tests.controller_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/controller_test.py',
   'PYMODULE'),
  ('pygame.tests.cursors_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/cursors_test.py',
   'PYMODULE'),
  ('pygame.tests.display_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/display_test.py',
   'PYMODULE'),
  ('pygame.tests.docs_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/docs_test.py',
   'PYMODULE'),
  ('pygame.tests.draw_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/draw_test.py',
   'PYMODULE'),
  ('pygame.tests.event_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/event_test.py',
   'PYMODULE'),
  ('pygame.tests.font_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/font_test.py',
   'PYMODULE'),
  ('pygame.tests.freetype_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/freetype_tags.py',
   'PYMODULE'),
  ('pygame.tests.freetype_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/freetype_test.py',
   'PYMODULE'),
  ('pygame.tests.ftfont_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/ftfont_tags.py',
   'PYMODULE'),
  ('pygame.tests.ftfont_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/ftfont_test.py',
   'PYMODULE'),
  ('pygame.tests.gfxdraw_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/gfxdraw_test.py',
   'PYMODULE'),
  ('pygame.tests.image__save_gl_surface_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/image__save_gl_surface_test.py',
   'PYMODULE'),
  ('pygame.tests.image_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/image_tags.py',
   'PYMODULE'),
  ('pygame.tests.image_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/image_test.py',
   'PYMODULE'),
  ('pygame.tests.imageext_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/imageext_tags.py',
   'PYMODULE'),
  ('pygame.tests.imageext_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/imageext_test.py',
   'PYMODULE'),
  ('pygame.tests.joystick_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/joystick_test.py',
   'PYMODULE'),
  ('pygame.tests.key_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/key_test.py',
   'PYMODULE'),
  ('pygame.tests.locals_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/locals_test.py',
   'PYMODULE'),
  ('pygame.tests.mask_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/mask_test.py',
   'PYMODULE'),
  ('pygame.tests.math_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/math_test.py',
   'PYMODULE'),
  ('pygame.tests.midi_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/midi_test.py',
   'PYMODULE'),
  ('pygame.tests.mixer_music_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/mixer_music_tags.py',
   'PYMODULE'),
  ('pygame.tests.mixer_music_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/mixer_music_test.py',
   'PYMODULE'),
  ('pygame.tests.mixer_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/mixer_tags.py',
   'PYMODULE'),
  ('pygame.tests.mixer_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/mixer_test.py',
   'PYMODULE'),
  ('pygame.tests.mouse_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/mouse_test.py',
   'PYMODULE'),
  ('pygame.tests.pixelarray_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/pixelarray_test.py',
   'PYMODULE'),
  ('pygame.tests.pixelcopy_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/pixelcopy_test.py',
   'PYMODULE'),
  ('pygame.tests.rect_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/rect_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok.fake_3_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/fake_3_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok.fake_4_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/fake_4_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok.fake_5_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/fake_5_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok.fake_6_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/fake_6_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok.no_assertions__ret_code_of_1__test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/no_assertions__ret_code_of_1__test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.all_ok.zero_tests_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/all_ok/zero_tests_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.everything',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/everything/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.everything.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/everything/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.everything.incomplete_todo_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/everything/incomplete_todo_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.everything.magic_tag_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/everything/magic_tag_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.everything.sleep_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/everything/sleep_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.exclude',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/exclude/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.exclude.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/exclude/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.exclude.invisible_tag_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/exclude/invisible_tag_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.exclude.magic_tag_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/exclude/magic_tag_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.failures1',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/failures1/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.failures1.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/failures1/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.failures1.fake_3_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/failures1/fake_3_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.failures1.fake_4_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/failures1/fake_4_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.incomplete',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/incomplete/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.incomplete.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/incomplete/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.incomplete.fake_3_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/incomplete/fake_3_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.incomplete_todo',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/incomplete_todo/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.incomplete_todo.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/incomplete_todo/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.incomplete_todo.fake_3_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/incomplete_todo/fake_3_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.infinite_loop',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/infinite_loop/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.infinite_loop.fake_1_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/infinite_loop/fake_1_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.infinite_loop.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/infinite_loop/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stderr',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stderr/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stderr.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stderr/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stderr.fake_3_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stderr/fake_3_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stderr.fake_4_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stderr/fake_4_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stdout',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stdout/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stdout.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stdout/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stdout.fake_3_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stdout/fake_3_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.print_stdout.fake_4_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/print_stdout/fake_4_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.run_tests__test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/run_tests__test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.timeout',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/timeout/__init__.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.timeout.fake_2_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/timeout/fake_2_test.py',
   'PYMODULE'),
  ('pygame.tests.run_tests__tests.timeout.sleep_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/run_tests__tests/timeout/sleep_test.py',
   'PYMODULE'),
  ('pygame.tests.rwobject_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/rwobject_test.py',
   'PYMODULE'),
  ('pygame.tests.scrap_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/scrap_tags.py',
   'PYMODULE'),
  ('pygame.tests.scrap_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/scrap_test.py',
   'PYMODULE'),
  ('pygame.tests.sndarray_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/sndarray_tags.py',
   'PYMODULE'),
  ('pygame.tests.sndarray_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/sndarray_test.py',
   'PYMODULE'),
  ('pygame.tests.sprite_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/sprite_test.py',
   'PYMODULE'),
  ('pygame.tests.surface_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/surface_test.py',
   'PYMODULE'),
  ('pygame.tests.surfarray_tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/surfarray_tags.py',
   'PYMODULE'),
  ('pygame.tests.surfarray_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/surfarray_test.py',
   'PYMODULE'),
  ('pygame.tests.surflock_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/surflock_test.py',
   'PYMODULE'),
  ('pygame.tests.sysfont_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/sysfont_test.py',
   'PYMODULE'),
  ('pygame.tests.test_utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/__init__.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.arrinter',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/arrinter.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.async_sub',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/async_sub.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.buftools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/buftools.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.endian',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/endian.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.png',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/png.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.run_tests',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/run_tests.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.test_machinery',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/test_machinery.py',
   'PYMODULE'),
  ('pygame.tests.test_utils.test_runner',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/test_utils/test_runner.py',
   'PYMODULE'),
  ('pygame.tests.threads_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/threads_test.py',
   'PYMODULE'),
  ('pygame.tests.time_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/time_test.py',
   'PYMODULE'),
  ('pygame.tests.touch_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/touch_test.py',
   'PYMODULE'),
  ('pygame.tests.transform_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/transform_test.py',
   'PYMODULE'),
  ('pygame.tests.version_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/version_test.py',
   'PYMODULE'),
  ('pygame.tests.video_test',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/tests/video_test.py',
   'PYMODULE'),
  ('pygame.threads',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/threads/__init__.py',
   'PYMODULE'),
  ('pygame.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/pygame/version.py',
   'PYMODULE'),
  ('queue',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/queue.py',
   'PYMODULE'),
  ('quopri',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/quopri.py',
   'PYMODULE'),
  ('random',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/random.py',
   'PYMODULE'),
  ('rlcompleter',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/runpy.py',
   'PYMODULE'),
  ('secrets',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/secrets.py',
   'PYMODULE'),
  ('selectors',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/selectors.py',
   'PYMODULE'),
  ('setuptools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/command/build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/command/build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/command/sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/compat/py39.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_distutils/versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/jaraco/functools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/tomli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/tomli/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/tomli/_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/tomli/_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel._setuptools_logging',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.bdist_wheel',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/_vendor/wheel/wheelfile.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/command/setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/compat/py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/compat/py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/compat/py39.py',
   'PYMODULE'),
  ('setuptools.config',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/config/setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/setuptools/windows_support.py',
   'PYMODULE'),
  ('shlex',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/shlex.py',
   'PYMODULE'),
  ('shutil',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/shutil.py',
   'PYMODULE'),
  ('signal',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/signal.py',
   'PYMODULE'),
  ('site',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site.py',
   'PYMODULE'),
  ('sitecustomize',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/sitecustomize.py',
   'PYMODULE'),
  ('socket',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/socket.py',
   'PYMODULE'),
  ('socketserver',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/socketserver.py',
   'PYMODULE'),
  ('ssl',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/ssl.py',
   'PYMODULE'),
  ('statistics',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/statistics.py',
   'PYMODULE'),
  ('string',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/string.py',
   'PYMODULE'),
  ('stringprep',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tarfile.py',
   'PYMODULE'),
  ('tempfile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tempfile.py',
   'PYMODULE'),
  ('test',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/__init__.py',
   'PYMODULE'),
  ('test.__main__',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/__main__.py',
   'PYMODULE'),
  ('test.libregrtest',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/__init__.py',
   'PYMODULE'),
  ('test.libregrtest.cmdline',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/cmdline.py',
   'PYMODULE'),
  ('test.libregrtest.filter',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/filter.py',
   'PYMODULE'),
  ('test.libregrtest.findtests',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/findtests.py',
   'PYMODULE'),
  ('test.libregrtest.logger',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/logger.py',
   'PYMODULE'),
  ('test.libregrtest.main',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/main.py',
   'PYMODULE'),
  ('test.libregrtest.pgo',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/pgo.py',
   'PYMODULE'),
  ('test.libregrtest.refleak',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/refleak.py',
   'PYMODULE'),
  ('test.libregrtest.result',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/result.py',
   'PYMODULE'),
  ('test.libregrtest.results',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/results.py',
   'PYMODULE'),
  ('test.libregrtest.run_workers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/run_workers.py',
   'PYMODULE'),
  ('test.libregrtest.runtests',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/runtests.py',
   'PYMODULE'),
  ('test.libregrtest.save_env',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/save_env.py',
   'PYMODULE'),
  ('test.libregrtest.setup',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/setup.py',
   'PYMODULE'),
  ('test.libregrtest.single',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/single.py',
   'PYMODULE'),
  ('test.libregrtest.testresult',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/testresult.py',
   'PYMODULE'),
  ('test.libregrtest.tsan',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/tsan.py',
   'PYMODULE'),
  ('test.libregrtest.utils',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/utils.py',
   'PYMODULE'),
  ('test.libregrtest.win_utils',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/win_utils.py',
   'PYMODULE'),
  ('test.libregrtest.worker',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/libregrtest/worker.py',
   'PYMODULE'),
  ('test.support',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/support/__init__.py',
   'PYMODULE'),
  ('test.support.import_helper',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/support/import_helper.py',
   'PYMODULE'),
  ('test.support.os_helper',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/support/os_helper.py',
   'PYMODULE'),
  ('test.support.script_helper',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/support/script_helper.py',
   'PYMODULE'),
  ('test.support.threading_helper',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/test/support/threading_helper.py',
   'PYMODULE'),
  ('textwrap',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/threading.py',
   'PYMODULE'),
  ('token',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/token.py',
   'PYMODULE'),
  ('tokenize',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tokenize.py',
   'PYMODULE'),
  ('tomllib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tomllib/__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tomllib/_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tomllib/_re.py',
   'PYMODULE'),
  ('tomllib._types',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tomllib/_types.py',
   'PYMODULE'),
  ('trace',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/trace.py',
   'PYMODULE'),
  ('tracemalloc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tracemalloc.py',
   'PYMODULE'),
  ('tty',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/tty.py',
   'PYMODULE'),
  ('typing',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/typing.py',
   'PYMODULE'),
  ('typing_extensions',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/__init__.py',
   'PYMODULE'),
  ('unittest._log',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.case',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/case.py',
   'PYMODULE'),
  ('unittest.loader',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/loader.py',
   'PYMODULE'),
  ('unittest.main',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/main.py',
   'PYMODULE'),
  ('unittest.mock',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/mock.py',
   'PYMODULE'),
  ('unittest.result',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/result.py',
   'PYMODULE'),
  ('unittest.runner',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/runner.py',
   'PYMODULE'),
  ('unittest.signals',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/signals.py',
   'PYMODULE'),
  ('unittest.suite',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/suite.py',
   'PYMODULE'),
  ('unittest.util',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/unittest/util.py',
   'PYMODULE'),
  ('urllib',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.error',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/error.py',
   'PYMODULE'),
  ('urllib.parse',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/parse.py',
   'PYMODULE'),
  ('urllib.request',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/request.py',
   'PYMODULE'),
  ('urllib.response',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/urllib/response.py',
   'PYMODULE'),
  ('vlc',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/vlc.py',
   'PYMODULE'),
  ('webbrowser',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/webbrowser.py',
   'PYMODULE'),
  ('xml',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/__init__.py',
   'PYMODULE'),
  ('xml.etree',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/etree/__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/etree/ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/etree/ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/etree/ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/etree/cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.sax',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/xmlrpc/client.py',
   'PYMODULE'),
  ('yaml',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/emitter.py',
   'PYMODULE'),
  ('yaml.error',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/error.py',
   'PYMODULE'),
  ('yaml.events',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/events.py',
   'PYMODULE'),
  ('yaml.loader',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/parser.py',
   'PYMODULE'),
  ('yaml.reader',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/reader.py',
   'PYMODULE'),
  ('yaml.representer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/yaml/tokens.py',
   'PYMODULE'),
  ('zipfile',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipfile/_path/glob.py',
   'PYMODULE'),
  ('zipimport',
   '/usr/local/Cellar/python@3.12/3.12.9/Frameworks/Python.framework/Versions/3.12/lib/python3.12/zipimport.py',
   'PYMODULE'),
  ('zipp',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/zipp/__init__.py',
   'PYMODULE'),
  ('zipp._functools',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/zipp/_functools.py',
   'PYMODULE'),
  ('zipp.compat',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/zipp/compat/__init__.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/zipp/compat/py310.py',
   'PYMODULE'),
  ('zipp.glob',
   '/Users/<USER>/coding/p312/lib/python3.12/site-packages/zipp/glob.py',
   'PYMODULE')])
