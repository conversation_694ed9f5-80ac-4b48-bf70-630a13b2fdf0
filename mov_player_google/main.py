# main.py
import sys
import os
import cv2
import threading
import numpy as np
import tempfile
import shutil
import subprocess
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QSplitter, QPushButton, QFileDialog, QScrollBar, QSizePolicy, QMessageBox, QProgressDialog, QListWidgetItem)

from PyQt5.QtCore import Qt, QTimer, QUrl, QThread, pyqtSignal, QEvent
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtGui import QImage, QPixmap
import locale

from custom_video_widget import CustomVideoWidget  # Import the components
from sidebar import Sidebar
from control_panel import ControlPanel
from file_info_panel import FileInfoPanel
from utils import get_ffmpeg_path, get_ffprobe_path, format_time, get_video_codec, copy_files_to_target, FFmpegWorker # Import utils


class VideoPlayer(QMainWindow):
    def __init__(self, ffmpeg_path=None):
        super().__init__()
        self.setWindowTitle("Yeson Take1 & 2 비교하기")
        self.setGeometry(100, 100, 1200, 700)

        self.ffmpeg_path = ffmpeg_path or get_ffmpeg_path()
        self.ffprobe_path = get_ffprobe_path(self.ffmpeg_path)
        self.loopEnabled = False
        self.filename_a = ""
        self.filename_b = ""
        self.cap_a = None
        self.cap_b = None
        self.frame_a = None
        self.frame_b = None
        self.current_frame = 0
        self.setFocusPolicy(Qt.StrongFocus)
        self.setAcceptDrops(True)
        self.mediaPlayer = QMediaPlayer(self)
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.next_frame)
        self.frame_rate = 24.0  # 기본 프레임 레이트 설정

        self.current_folder_path = ""
        self.playlist = []
        self.current_playlist_index = -1
        self.is_playlist_playing = False
        self.current_item = None
        self.use_gpu = False
        self.gpu_backend = None
        self.show_overlay = False
        self.is_playing = False # 비디오 재생 상태
        self.isFullScreen = False # 풀스크린 여부
        self.total_frames = 0  # 총 프레임 수 초기화

        self.initUI() # UI 초기화
        self.installEventFilter(self) # 이벤트 필터 설치
        self.connectSignals() # 시그널/슬롯 연결


    def initUI(self):
        self.videoWidget = CustomVideoWidget(self)
        self.sidebar = Sidebar(self)
        self.controlPanel = ControlPanel(self)
        self.fileInfoPanel = FileInfoPanel(self)

        self.positionScroll = QScrollBar(Qt.Horizontal)
        self.positionScroll.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.positionScroll.setMinimumSize(100, 20)
        self.positionScroll.setToolTip('Position')
        self.positionScroll.sliderMoved.connect(self.change_position)
        self.positionScroll.installEventFilter(self)

        # Layouts
        videoWidgetContainer = QWidget()
        videoWidgetLayout = QVBoxLayout(videoWidgetContainer)
        videoWidgetLayout.addWidget(self.videoWidget)
        videoWidgetLayout.setContentsMargins(0, 0, 0, 0)
        videoWidgetLayout.setSpacing(0)

        splitter = QSplitter(Qt.Vertical)
        splitter.addWidget(videoWidgetContainer)
        splitter.addWidget(self.fileInfoPanel)
        splitter.setSizes([700, 100])

        selectFolderButton = QPushButton('Select Folder')
        selectFolderButton.clicked.connect(self.select_folder)
        selectFolderButton.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        sidebarLayout = QVBoxLayout()
        sidebarLayout.addWidget(selectFolderButton)
        sidebarLayout.addWidget(self.sidebar)
        sidebarLayout.setContentsMargins(0, 0, 0, 0)
        sidebarLayout.setSpacing(5)

        sidebarContainer = QWidget()
        sidebarContainer.setLayout(sidebarLayout)

        mainSplitter = QSplitter(Qt.Horizontal)
        mainSplitter.addWidget(sidebarContainer)
        mainSplitter.addWidget(self.controlPanel)  # ControlPanel added directly
        mainSplitter.addWidget(splitter)
        mainSplitter.setStretchFactor(0, 0)
        mainSplitter.setStretchFactor(1, 0)
        mainSplitter.setStretchFactor(2, 1)
        mainSplitter.setSizes([200, 100, 1000])

        controlLayoutRight = QVBoxLayout() # 오른쪽 컨트롤 레이아웃 (스크롤바, Play/Stop)
        controlLayoutRight.addWidget(self.positionScroll)
        controlLayoutRight.addWidget(self.controlPanel.playPauseButton)  # ControlPanel에서 가져옴
        controlLayoutRight.addWidget(self.controlPanel.playSelectedPauseButton)
        # controlLayoutRight.addWidget(stopButton) # 이 버튼은 control panel에 있음.
        controlLayoutRight.setContentsMargins(0, 0, 0, 0)
        controlLayoutRight.setSpacing(5)

        mainLayout = QVBoxLayout()  # Changed to QVBoxLayout
        mainLayout.addWidget(mainSplitter)
        mainLayout.addLayout(controlLayoutRight)  # Add controlLayoutRight
        mainLayout.setContentsMargins(0, 0, 0, 0)
        mainLayout.setSpacing(5)

        centralWidget = QWidget()
        centralWidget.setLayout(mainLayout)
        self.setCentralWidget(centralWidget)

        self.control_widgets = [ # 컨트롤 위젯 리스트 (V 누르면 보였다 안보였다 하는 위젯들.)
            sidebarContainer, self.controlPanel, selectFolderButton
            # Add other buttons and controls here
        ]
        self.controls_visible = True

    def connectSignals(self):
        self.videoWidget.wipePositionChanged.connect(self.update_frame)
        self.videoWidget.fitToWindowChanged.connect(self.on_fit_to_window_changed)

        # Sidebar Signals
        self.sidebar.file_selected.connect(lambda file_path: self.open_file('A', file_path))
        self.sidebar.request_open_video.connect(self.open_file)
        self.sidebar.request_copy_files.connect(self.copy_selected_files)
        self.sidebar.request_show_info.connect(self.show_video_info)
        self.sidebar.request_merge_videos.connect(self.merge_videos)

        # Control Panel Signals
        self.controlPanel.openFileRequested.connect(self.open_file)
        self.controlPanel.prevFrameRequested.connect(self.prev_frame)
        self.controlPanel.nextFrameRequested.connect(self.next_frame)
        self.controlPanel.toggleFullscreenRequested.connect(self.toggle_fullscreen)
        self.controlPanel.toggleLoopPlayRequested.connect(self.toggle_loop_play)
        self.controlPanel.volumeChanged.connect(self.setVolume)
        self.controlPanel.frameRateChanged.connect(self.set_frame_rate) # 슬롯 추가
        self.controlPanel.toggleWipeEffectRequested.connect(self.toggle_wipe_effect)
        self.controlPanel.toggleWipeXRequested.connect(self.toggle_wipe_x)
        self.controlPanel.toggleWipeYRequested.connect(self.toggle_wipe_y)
        self.controlPanel.playSelectedRequested.connect(self.play_selected_files)
        self.controlPanel.togglePlayPauseSelectedRequested.connect(self.toggle_play_pause_selected)
        self.controlPanel.gpuBackendChanged.connect(self.change_gpu_backend)
        self.controlPanel.workSelected.connect(self.update_season_list)
        self.controlPanel.seasonSelected.connect(self.update_job_list)
        self.controlPanel.jobSelected.connect(self.update_playlist)

        # Play/Pause Button
        self.controlPanel.playPauseButton.clicked.connect(self.toggle_play_pause)

    def process_frame(self, frame):
        if self.use_gpu:
            if self.gpu_backend == 'cuda':
                gpu_frame = cv2.cuda_GpuMat()
                gpu_frame.upload(frame)
                gpu_frame = cv2.cuda.cvtColor(gpu_frame, cv2.COLOR_BGR2RGB)
                return gpu_frame.download()
            elif self.gpu_backend == 'opencl':
                gpu_frame = cv2.UMat(frame)
                return cv2.cvtColor(gpu_frame, cv2.COLOR_BGR2RGB).get()
        return cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

    def change_gpu_backend(self, index):
        backend = self.controlPanel.gpuComboBox.currentText()
        if backend == "CPU":
            self.use_gpu = False
            self.gpu_backend = None
        elif backend == "NVIDIA (CUDA)":
            self.use_gpu = True
            self.gpu_backend = 'cuda'
            cv2.cuda.setDevice(0)
        elif backend == "AMD (OpenCL)":
            self.use_gpu = True
            self.gpu_backend = 'opencl'
            cv2.ocl.setUseOpenCL(True)

        if self.cap_a:
            current_pos = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES))
            self.seek_to_frame(current_pos)

    def update_season_list(self, selected_work):
            # selected_work = self.controlPanel.workComboBox.currentText() # 필요없음.
            self.controlPanel.seasonComboBox.clear()
            self.controlPanel.seasonComboBox.addItem("시즌선택")

            try:
                if selected_work == "BB":
                    path = "/usadata3/Bento_Project"
                    season_list = [file for file in os.listdir(path) if "BB_Season" in file]
                elif selected_work == "KOTH":
                    path = "/usadata2/Disney/KOTH"
                    season_list = [file for file in os.listdir(path) if "KOTH_Season" in file]
                elif selected_work == "Big_Mouth":
                    path = "/usadata2/Titmouse/Big_Mouth"
                    season_list = [file for file in os.listdir(path) if "BM_Season" in file]
                elif selected_work == "TGN":
                    path = "/usadata3/Bento_Project2/Great_North"
                    season_list = [file for file in os.listdir(path) if "GN_Season" in file]
                elif selected_work == "BB_Animatic":
                    path = "/Users/<USER>/Desktop/usadata4/BentoBox_Data/BentoBox_data/BB_New/"
                    season_list = [file for file in os.listdir(path) if "season" in file]
                elif selected_work == "KOTH_Animatic":
                    path = "/Volumes/bgfinal/colordata/KOTH"
                    season_list = [file for file in os.listdir(path) if "KOTH_SEASON" in file]
                elif selected_work == "BM_Animatic":
                    path = "/Volumes/bgfinal/colordata/BIG_MOUTH"
                    season_list = [file for file in os.listdir(path) if "BM_SEASON" in file]
                elif selected_work == "TGN_Animatic":
                    path = "/Users/<USER>/Desktop/usadata4/BentoBox_Data/BentoBox_data/"
                    season_list = [file for file in os.listdir(path) if "TGN_Season" in file]
                else:
                    season_list = []

                season_list.sort()
                self.controlPanel.set_season_items(season_list) # control panel의 콤보박스 업데이트

            except FileNotFoundError:
                print(f"Directory not found: {path}")

    def update_job_list(self, selected_season):
        # selected_season = self.controlPanel.seasonComboBox.currentText() # 필요없음.
        if selected_season != "시즌선택":
            # self.controlPanel.jobComboBox.clear() # set_job_items에서 처리
            # self.controlPanel.jobComboBox.addItem("job선택")
            selected_work = self.controlPanel.workComboBox.currentText()

            try:
                if selected_work == "BB":
                    path = os.path.join("/usadata3/Bento_Project", selected_season)
                elif selected_work == "KOTH":
                    path = os.path.join("/usadata2/Disney/KOTH", selected_season)
                elif selected_work == "Big_Mouth":
                    path = os.path.join("/usadata2/Titmouse/Big_Mouth", selected_season)
                elif selected_work == "TGN":
                    path = os.path.join("/usadata3/Bento_Project2/Great_North", selected_season)
                elif selected_work == "BB_Animatic":
                    path = os.path.join("/Users/<USER>/Desktop/usadata4/BentoBox_Data/BentoBox_data/BB_New/", selected_season)
                elif selected_work == "KOTH_Animatic":
                    path = os.path.join("/Volumes/bgfinal/colordata/KOTH/", selected_season)
                elif selected_work == "BM_Animatic":
                    path = os.path.join("/Volumes/bgfinal/colordata/BIG_MOUTH/", selected_season)
                elif selected_work == "TGN_Animatic":
                    path = os.path.join("/Users/<USER>/Desktop/usadata4/BentoBox_Data/BentoBox_data/", selected_season)
                else:
                    path = None

                if path:
                    job_list = [
                        file for file in os.listdir(path)
                        if os.path.isdir(os.path.join(path, file))
                        and not file.startswith('.')
                        and file not in ["@eaDir", "EXPORT", "Yeson_BB_Model", "Yeson_GN_Model", "KOTH_library", "TEST_IB_4A", "KOTH_Main_Models", "Yeson_BM_Model", "Yeson_KOTH_Model", "A_Season13_IB_Model"]
                    ]
                    job_list.sort()
                    self.controlPanel.set_job_items(job_list) # control panel의 job 콤보박스 업데이트.

            except FileNotFoundError:
                print(f"Directory not found: {path}")

    def update_playlist(self, selected_job):
        # selected_job = self.controlPanel.jobComboBox.currentText()
        if selected_job != "job선택":
            # self.sidebar.clear()  # Moved clearing to clear_and_add_videos
            selected_work = self.controlPanel.workComboBox.currentText()
            selected_season = self.controlPanel.seasonComboBox.currentText()

            try:
                if selected_work == "BB":
                    base_path = os.path.join("/usadata3/Bento_Project", selected_season, selected_job)
                elif selected_work == "KOTH":
                    base_path = os.path.join("/usadata2/Disney/KOTH", selected_season, selected_job)
                elif selected_work == "Big_Mouth":
                    base_path = os.path.join("/usadata2/Titmouse/Big_Mouth", selected_season, selected_job)
                elif selected_work == "TGN":
                    base_path = os.path.join("/usadata3/Bento_Project2/Great_North", selected_season, selected_job)
                elif selected_work == "BB_Animatic":
                    base_path = os.path.join("/Users/<USER>/Desktop/usadata4/BentoBox_Data/BentoBox_data/BB_New/", selected_season, selected_job)
                elif selected_work == "KOTH_Animatic":
                    base_path = os.path.join("/Volumes/bgfinal/colordata/KOTH/", selected_season, selected_job)
                elif selected_work == "BM_Animatic":
                    base_path = os.path.join("/Volumes/bgfinal/colordata/BIG_MOUTH/", selected_season, selected_job)
                elif selected_work == "TGN_Animatic":
                    base_path = os.path.join("/Users/<USER>/Desktop/usadata4/BentoBox_Data/BentoBox_data/", selected_season, selected_job)
                else:
                    base_path = None

                if base_path and os.path.isdir(base_path):
                    def update_playlist_thread():
                        all_mov_files = []

                        # Helper function to add .mov and .mp4 files
                        def add_video_files(directory):
                            if os.path.isdir(directory):
                                mov_files = [f for f in os.listdir(directory) if f.lower().endswith(('.mov', '.mp4')) and not f.startswith('.')]
                                for mov_file in mov_files:
                                    full_path = os.path.join(directory, mov_file)
                                    all_mov_files.append((mov_file, full_path))

                        # Add files from specific subfolders
                        if selected_work in ["BB", "KOTH", "Big_Mouth", "TGN"]:
                            for folder in os.listdir(base_path):
                                frames_path = os.path.join(base_path, folder, "frames")
                                add_video_files(frames_path)
                        elif selected_work == "BB_Animatic":
                            for folder in os.listdir(base_path):
                                quicktime_path = os.path.join(base_path, folder, "Quicktime")
                                add_video_files(quicktime_path)
                        elif selected_work == "TGN_Animatic":
                            for folder in os.listdir(base_path):
                                animatic_path = os.path.join(base_path, folder, "ANIMATIC")
                                add_video_files(animatic_path)
                        elif selected_work == "BM_Animatic":
                            animatic_path = os.path.join(base_path, "ANIMATIC")
                            add_video_files(animatic_path)
                        elif selected_work == "KOTH_Animatic":
                            animatic_path = os.path.join(base_path, "POST-LOCK ANIMATIC")
                            add_video_files(animatic_path)

                        # Add files from the parent folder
                        add_video_files(base_path)


                        # Sort files
                        def sort_key(item):
                            filename = item[0]
                            is_hd = '_HD' in filename
                            parts = filename.split('_')
                            if len(parts) >= 4:
                                series_ep = parts[0] + '_' + parts[1]
                                scene = parts[2]
                                scene_letter = scene[0]
                                scene_number = int(scene[1:]) if scene[1:].isdigit() else 0
                                take = parts[3]
                                return (is_hd, series_ep, scene_letter, scene_number, take)
                            return (is_hd, filename.lower())

                        hd_files = [f for f in all_mov_files if '_HD' in f[0]]
                        non_hd_files = [f for f in all_mov_files if '_HD' not in f[0]]

                        import locale
                        locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        hd_files.sort(key=lambda x: locale.strxfrm(x[0]))
                        non_hd_files.sort(key=lambda x: locale.strxfrm(x[0]))
                        all_mov_files = non_hd_files + hd_files # HD, non-HD 순서로 정렬.

                        self.sidebar.clear_and_add_videos(all_mov_files)  # Update sidebar

                        # Close previous video files
                        if self.cap_a:
                            self.cap_a.release()
                            self.cap_a = None
                        if self.cap_b:
                            self.cap_b.release()
                            self.cap_b = None

                        self.filename_a = ""
                        self.filename_b = ""
                        self.frame_a = None
                        self.frame_b = None

                        if all_mov_files:
                            self.playlist = [full_path for _, full_path in all_mov_files]
                            self.open_file('A', self.playlist[0]) # 첫번째 파일 자동 열기

                    thread = threading.Thread(target=update_playlist_thread)
                    thread.start()
                else:
                    print(f"Directory not found or invalid: {base_path}")
            except Exception as e:
                print(f"An error occurred: {e}")

    def select_folder(self):
        folder_path = QFileDialog.getExistingDirectory(self, "Select Folder")
        if folder_path:
            self.current_folder_path = folder_path
            self.load_videos_from_folder()

    def load_videos_from_folder(self):
        self.playlist.clear()
        self.sidebar.clear() # QListWidget clear
        files = sorted(os.listdir(self.current_folder_path)) # 폴더 내의 파일 정렬
        for file in files:
            if any(file.lower().endswith(ext) for ext in [".MP4",".MOV",".mp4",".mov"]):
                full_path = os.path.join(self.current_folder_path, file)
                self.playlist.append(full_path)
                item = QListWidgetItem(file) # QListWidgetItem 생성 후
                item.setToolTip(full_path)  # 툴크 설정
                self.sidebar.addItem(item) # 사이드바에 추가
        if self.playlist:
            self.current_playlist_index = 0
            self.open_file('A', self.playlist[0])
            self.sidebar.selectAll()

    def open_file(self, video_type, file_path=None):
        if file_path is None:
            file_path, _ = QFileDialog.getOpenFileName(self, f"Open Video {video_type}")

        if file_path:
            file_url = QUrl.fromLocalFile(file_path)
            # file_path = file_url.toLocalFile() # QUrl에서 다시 LocalFile로 변환할 필요 없음

            if video_type == 'A':
                self.filename_a = file_path
                self.cap_a = cv2.VideoCapture(self.filename_a)
                if not self.cap_a.isOpened():
                    print(f"Failed to open video file: {file_path}")
                    return

                if self.use_gpu and self.gpu_backend == 'cuda':
                    self.cap_a.set(cv2.CAP_PROP_CUDA_DEVICE, 0)

                self.mediaPlayer.setMedia(QMediaContent(file_url))
                self.mediaPlayer.setPosition(0)

                self.total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
                self.positionScroll.setMaximum(self.total_frames - 1)
                self.current_frame = 0
            elif video_type == 'B':
                self.filename_b = file_path
                self.cap_b = cv2.VideoCapture(self.filename_b)
                if not self.cap_b.isOpened():
                    print(f"Failed to open video file: {file_path}")
                    return
                if self.use_gpu and self.gpu_backend == 'cuda':
                    self.cap_b.set(cv2.CAP_PROP_CUDA_DEVICE, 0)

            self.detect_fps()
            QTimer.singleShot(100, self.display_first_frame)  # Display the first frame

    def detect_fps(self):
        if self.cap_a:
            fps_a = self.cap_a.get(cv2.CAP_PROP_FPS)
            fps_b = self.cap_b.get(cv2.CAP_PROP_FPS) if self.cap_b else 0
            avg_fps = fps_a if fps_b == 0 else (fps_a + fps_b) / 2.0
            self.controlPanel.frameRateComboBox.setCurrentText(str(int(avg_fps)))

    def display_first_frame(self):
        if self.cap_a:
            self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, 0)
            ret_a, self.frame_a = self.cap_a.read()
            if ret_a:
                self.frame_a = self.process_frame(self.frame_a)  # Process for GPU if needed
                self.update_frame()
                self.update_position_info(1)  # Update UI for the first frame (frame 1)
                if not self.is_playlist_playing:
                    self.videoWidget.fit_to_window()
                else:
                    self.videoWidget.apply_zoom()


    def update_frame(self):
        if self.frame_a is not None:
            frame_copy = self.frame_a.copy()

            if self.show_overlay:
                self.overlay_text(frame_copy)

            if self.videoWidget.wipeEnabled:
                self.videoWidget.updateFrame(frame_copy, self.frame_b)
            else:
                self.videoWidget.updateFrame(frame_copy, None)

            self.update_position_info(int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES)))


    def overlay_text(self, frame):
        height, width = frame.shape[:2]
        x = 10
        y = height - 20
        text = f"File: {os.path.basename(self.filename_a)}, Current Frame: {int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES))}, Total Frames: {int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))}"
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        text_color = (0, 0, 0)
        bg_color = (255, 255, 255)
        thickness = 2

        (text_width, text_height), _ = cv2.getTextSize(text, font, font_scale, thickness)
        cv2.rectangle(frame, (x - 5, y - text_height - 5), (x + text_width + 5, y + 5), bg_color, -1)
        cv2.putText(frame, text, (x, y), font, font_scale, text_color, thickness)

    def change_position(self, position):
        if self.cap_a:
            total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
            target_frame = int((position / (self.positionScroll.maximum() - self.positionScroll.minimum())) * (total_frames -1)) # -1 추가

            self.seek_to_frame(target_frame)

    def update_position_info(self, current_frame):
        if self.cap_a:
            total_frames_a = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
            if total_frames_a <= 0:
                return  # 비디오가 로드되지 않았거나 프레임이 없는 경우
                
            current_frame = min(current_frame, total_frames_a) # 현재 프레임 제한
            total_frames_b = int(self.cap_b.get(cv2.CAP_PROP_FRAME_COUNT)) if self.cap_b and self.videoWidget.wipeEnabled else 0

            scroll_max = self.positionScroll.maximum()
            if scroll_max > 0:
                new_scroll_position = int((current_frame / total_frames_a) * scroll_max)
                if new_scroll_position != self.positionScroll.value():
                    self.positionScroll.setValue(new_scroll_position) # 스크롤바 위치 업데이트

            fps = self.cap_a.get(cv2.CAP_PROP_FPS)
            current_time = current_frame / fps if fps > 0 else 0
            total_time = total_frames_a / fps if fps > 0 else 0

            self.fileInfoPanel.update_file_info(
                self.filename_a, self.filename_b,
                current_frame, total_frames_a, total_frames_b,
                current_time, total_time, fps
            )  # FileInfoPanel 업데이트

    def get_frame_rate(self):
        frame_rate_str = self.controlPanel.frameRateComboBox.currentText().strip() # 수정
        try:
            return float(frame_rate_str)
        except ValueError:
            return 24.0

    def set_frame_rate(self, frame_rate_str):
        """Sets the frame rate for playback."""
        try:
            self.frame_rate = float(frame_rate_str)
            if self.timer.isActive():  # 타이머가 동작 중이면 새로운 주기로 재시작
                self.timer.setInterval(int(1000 / self.frame_rate))
        except ValueError:
            print("Invalid frame rate. Using default (24 FPS).")
            self.frame_rate = 24.0

    def next_frame(self):
        if self.cap_a is None:
            return

        total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
        current_frame = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES))

        if current_frame < total_frames:
            ret, frame_a = self.cap_a.read()
            if ret:
                self.frame_a = self.process_frame(frame_a)

                if self.cap_b and self.videoWidget.wipeEnabled:
                    ret_b, frame_b = self.cap_b.read()
                    if ret_b:
                        self.frame_b = self.process_frame(frame_b)
                    else:
                        self.frame_b = np.zeros_like(self.frame_a)  # Fill with black if no frame

                current_pos = int(self.cap_a.get(cv2.CAP_PROP_POS_MSEC))
                if abs(current_pos - self.mediaPlayer.position()) > 500:
                    self.mediaPlayer.setPosition(current_pos)

                self.current_frame += 1
                self.update_frame()
                self.update_ui()  # UI 업데이트 (예: 프레임 카운트 등)

                if current_frame == total_frames -1 : # 마지막 프레임에 도달하면
                    self.handle_video_end() # 비디오 끝 처리
        else: # 이미 마지막 프레임에 도달
            self.handle_video_end() # 비디오 끝 처리

    def prev_frame(self):
        if self.cap_a:
            pos_frame = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES))
            self.seek_to_frame(max(0, pos_frame - 2))  # Go to the previous frame

    def play_selected_files(self):
        selected_items = self.sidebar.selectedItems()
        if selected_items:
            selected_items.sort(key=lambda x: self.sidebar.row(x))  # Sort by sidebar order

            self.playlist = [item.toolTip() for item in selected_items]
            self.current_playlist_index = 0
            self.is_playlist_playing = True
            self.play_current_playlist_item() # 선택된 파일 재생
        else:
            print("No files selected")

    def play_current_playlist_item(self):

        if self.current_playlist_index < len(self.playlist):
            current_file = self.playlist[self.current_playlist_index]

            # Reset and highlight the current item in the sidebar
            self.sidebar.reset_colors()
            self.sidebar.set_current_item_style(current_file)

            self.open_file('A', current_file)  # Open the file
            self.display_first_frame()
            self.mediaPlayer.setMedia(QMediaContent(QUrl.fromLocalFile(current_file)))
            self.play_video()

            if not self.videoWidget.wipeEnabled: # 와이프 모드에 따라 파일 이름 설정
                self.fileInfoPanel.filenameLabel.setText(f"File: {os.path.basename(self.filename_a)}")
                self.filename_b = None
                self.cap_b = None
            else:
                self.fileInfoPanel.filenameLabel.setText(f"Files: {os.path.basename(self.filename_a)}, {os.path.basename(self.filename_b)}")

            self.update_frame()
            if not self.is_playlist_playing: # 플레이리스트 모드에 따라 줌 상태 설정
                self.videoWidget.fit_to_window()
            else:
                self.videoWidget.apply_zoom()

            self.controlPanel.set_play_pause_selected_button_state(True) # Play/Pause Selected 버튼 상태 업데이트

    def play_next_in_playlist(self):
        if self.current_playlist_index < len(self.playlist) - 1:
            self.current_playlist_index += 1
            self.open_file('A', self.playlist[self.current_playlist_index])
            self.mediaPlayer.stop()
            self.mediaPlayer.setMedia(QMediaContent(QUrl.fromLocalFile(self.playlist[self.current_playlist_index])))
            self.mediaPlayer.play()

            if self.cap_a:
                self.cap_a.release()
                self.cap_a = None

            self.cap_a = cv2.VideoCapture(self.filename_a)
            if not self.cap_a.isOpened():
                print(f"Failed to open video file: {self.filename_a}")
                return

            self.total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
            self.positionScroll.setMaximum(self.total_frames - 1)
            self.current_frame = 0
            self.display_first_frame()
            self.seek_to_frame(0)
            self.play_video()

            self.sidebar.reset_colors() # 사이드바 스타일 초기화
            self.sidebar.set_current_item_style(self.playlist[self.current_playlist_index]) # 현재 아이템 스타일 설정
            self.controlPanel.set_play_pause_selected_button_state(True) # Play/Pause Selected 버튼 상태 업데이트

        else:
            self.is_playlist_playing = False
            self.stop_video() # 플레이리스트 끝났을 때 비디오 정지

    def play_previous_in_playlist(self): # 이전 파일 재생 (Playlist에서)
        if self.current_playlist_index > 0:
            self.current_playlist_index -= 1
            self.open_file('A', self.playlist[self.current_playlist_index])
            self.mediaPlayer.stop()
            self.mediaPlayer.setMedia(QMediaContent(QUrl.fromLocalFile(self.playlist[self.current_playlist_index])))
            self.mediaPlayer.play()

            if self.cap_a:
                self.cap_a.release()
                self.cap_a = None

            self.cap_a = cv2.VideoCapture(self.filename_a)
            if not self.cap_a.isOpened():
                print(f"Failed to open video file: {self.filename_a}")
                return

            self.total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
            self.positionScroll.setMaximum(self.total_frames - 1)
            self.current_frame = 0
            self.display_first_frame()
            self.seek_to_frame(0)
            self.play_video()
            self.sidebar.reset_colors()  # 사이드바 스타일 초기화
            self.sidebar.set_current_item_style(self.playlist[self.current_playlist_index])  # 현재 아이템 스타일 설정


    def update_ui(self):
        self.update_position_info(self.current_frame + 1) # UI 정보 업데이트 (프레임, 스크롤바 등)

    def handle_video_end(self): # 비디오 끝 처리
        if self.is_playlist_playing: # 플레이리스트 재생 중이면
            self.play_next_in_playlist() # 다음 파일 재생
        elif self.loopEnabled:  # 루프 활성화 시
            self.seek_to_frame(0)  # 처음으로 이동
        else:
            print("Last frame reached")  # 루프 모드가 아닐 때만 메시지 출력
            self.stop_video()   # 비디오 정지
            self.controlPanel.set_play_pause_button_state(False)

    def play_video(self): # 비디오 재생
        if self.cap_a is None:
            print("No video loaded. Please open a video file first.")
            return
            
        if not self.timer.isActive():
            current_frame = int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES))
            total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))

            if current_frame >= total_frames - 1: # 현재 프레임이 마지막 프레임이면
                self.seek_to_frame(0)          # 처음으로

            if self.videoWidget.is_fit_to_window: # fit_to_window 상태이면
                self.videoWidget.fit_to_window()    # fit_to_window 적용

            fps = self.cap_a.get(cv2.CAP_PROP_FPS)
            if fps > 0:
                interval = int(1000 / fps)
            else:
                interval = int(1000 / self.frame_rate)  # 기본 프레임 레이트 사용
                
            # 메인 스레드에서 타이머 시작
            self.timer.setInterval(interval)
            self.timer.start()

            current_pos = int(self.cap_a.get(cv2.CAP_PROP_POS_MSEC)) # 비디오, 오디오 동기화
            self.mediaPlayer.setPosition(current_pos)
            self.mediaPlayer.play()

            self.is_playing = True  # 재생 상태 설정
            self.controlPanel.set_play_pause_button_state(True, self.loopEnabled) # Play/Pause 버튼 상태 업데이트

    def pause_video(self): # 비디오 일시정지
        if self.timer.isActive():
            self.timer.stop()
            self.mediaPlayer.pause()
            self.is_playing = False # 일시정지 상태 설정
            self.controlPanel.set_play_pause_button_state(False) # Play/Pause 버튼 상태 업데이트

    def stop_video(self): # 비디오 정지
        self.timer.stop()
        self.mediaPlayer.stop()
        self.current_frame = 0
        if self.cap_a:
            total_frames_a = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
            self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, total_frames_a - 1) # 마지막 프레임으로
            ret_a, self.frame_a = self.cap_a.read()
            if ret_a:
                self.frame_a = cv2.cvtColor(self.frame_a, cv2.COLOR_BGR2RGB)

        if self.cap_b and self.videoWidget.wipeEnabled: # 와이프 모드일 때 B 비디오 처리
            total_frames_b = int(self.cap_b.get(cv2.CAP_PROP_FRAME_COUNT))
            if total_frames_a > total_frames_b:
                self.frame_b = np.zeros_like(self.frame_a) # A가 더 길면 B는 검은색
            else: # B가 더 길거나 같으면
                self.cap_b.set(cv2.CAP_PROP_POS_FRAMES, total_frames_a -1) # A 마지막에 맞춤
                ret_b, self.frame_b = self.cap_b.read()
                if ret_b:
                    self.frame_b = cv2.cvtColor(self.frame_b, cv2.COLOR_BGR2RGB)

        self.update_ui() # UI 업데이트
        self.is_playlist_playing = False # 플레이리스트 재생 상태 해제
        self.is_playing = False  # 재생 상태 해제
        self.sidebar.reset_colors() # 사이드바 색상 초기화
        self.current_item = None   # 현재 아이템 없음

        self.update_frame()     # 프레임 업데이트
        self.fileInfoPanel.clear_info()  # 파일 정보 패널 초기화
        self.controlPanel.set_play_pause_button_state(False)    # Play/Pause 버튼 상태 업데이트
        self.controlPanel.set_play_pause_selected_button_state(False) # Play/Pause Selected 버튼 상태 업데이트

    def seek_to_frame(self, frame_number):  # 프레임 탐색
        if self.cap_a:
            self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, frame_number - 1) # OpenCV는 0부터 시작
            ret_a, frame_a = self.cap_a.read()
            if ret_a:
                self.frame_a = self.process_frame(frame_a) # GPU 처리
            else:
                self.frame_a = None

            if self.cap_b and self.videoWidget.wipeEnabled: # 와이프 모드일 때 B 비디오 처리
                self.cap_b.set(cv2.CAP_PROP_POS_FRAMES, frame_number - 1)
                ret_b, frame_b = self.cap_b.read()
                if ret_b:
                    self.frame_b = self.process_frame(frame_b) # GPU 처리
                else:
                    self.frame_b = None

            self.mediaPlayer.setPosition(int((frame_number - 1) / self.cap_a.get(cv2.CAP_PROP_FPS) * 1000)) # mediaPlayer 위치 업데이트
            self.update_frame()    # 프레임 업데이트
            self.update_position_info(frame_number)   # UI 업데이트

    def setVolume(self, volume):
        self.mediaPlayer.setVolume(volume) # 볼륨 설정

    def on_fit_to_window_changed(self, is_fit):
        self.is_fit_to_window = is_fit

    def toggle_play_pause(self): # 재생/일시정지 토글
        try:
            if self.timer.isActive():
                self.pause_video()
                self.controlPanel.set_play_pause_button_state(False) # 버튼 스타일
            else:
                if self.cap_a is None:
                    print("No video loaded. Please open a video file first.")
                    return

                if self.cap_a.get(cv2.CAP_PROP_POS_FRAMES) == self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT) - 1:
                    self.seek_to_frame(0)
                self.is_playlist_playing = False  # 단일 파일 재생 모드
                self.play_video()
                self.controlPanel.set_play_pause_button_state(True, self.loopEnabled) # 버튼 스타일
        except Exception as e:
            print(f"An error occurred: {str(e)}")
            self.pause_video()
            self.controlPanel.set_play_pause_button_state(False)
            if self.cap_a:
                self.cap_a.set(cv2.CAP_PROP_POS_FRAMES, 0)
            self.update_frame()

    def toggle_play_pause_selected(self): # 선택된 파일 재생/일시정지 토글
        if self.is_playlist_playing:
            if self.timer.isActive():
                self.pause_video()
                self.controlPanel.set_play_pause_selected_button_state(False) # 버튼 스타일
            else:
                self.play_video()
                self.controlPanel.set_play_pause_selected_button_state(True)  # 버튼 스타일
        else:
            # 플레이리스트가 재생되지 않은 상태에서만 메시지 출력
            if self.cap_a is not None:  # 비디오가 로드된 경우에만
                print("No playlist is playing.")

    def toggle_wipe_effect(self, state):
        enabled = state == Qt.Checked
        self.videoWidget.toggleWipeEffect(enabled)  # 와이프 효과 토글
        if self.frame_a is not None:
            self.update_frame()  # 프레임 업데이트
        if enabled: # 와이프 활성화/비활성화 시 파일 이름 라벨 업데이트
            if self.filename_a and self.filename_b:
                self.fileInfoPanel.filenameLabel.setText(f'Files: {os.path.basename(self.filename_a)}, {os.path.basename(self.filename_b)}')
        else:
            if self.filename_a:
                self.fileInfoPanel.filenameLabel.setText(f'File: {os.path.basename(self.filename_a)}')

            self.filename_b = None # B 비디오 정보 초기화
            self.cap_b = None
            self.update_position_info(int(self.cap_a.get(cv2.CAP_PROP_POS_FRAMES)))

    def toggle_wipe_x(self, checked): # X축 와이프 토글
        self.videoWidget.wipe_enabled_x = checked
        self.videoWidget.updateWipeLine()
        self.update_frame()

    def toggle_wipe_y(self, checked):  # Y축 와이프 토글
        self.videoWidget.wipe_enabled_y = checked
        self.videoWidget.updateWipeLine()
        self.update_frame()

    def copy_selected_files(self, file_paths): # 파일 복사
        target_path = QFileDialog.getExistingDirectory(self, "Select Folder to Copy")
        if target_path:
            copy_files_to_target(file_paths, target_path) # 실제 복사 , utils.py로 이동

    def show_video_info(self, file_path): # 비디오 정보 표시
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("미디어 정보")

        try:
            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened():
                raise Exception("비디오 파일을 열 수 없습니다.")

            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration_sec = total_frames / fps if fps > 0 else 0
            duration_str = format_time(duration_sec, fps)
            codec = get_video_codec(file_path, self.ffprobe_path)  # FFprobe 경로 사용, utils.py로 이동

            msg = (f"**해상도:** {width} x {height}\n"
                   f"**코덱:** {codec}\n"
                   f"**길이:** {duration_str}\n"
                   f"**프레임:** {total_frames}\n"
                   f"**프레임 속도 (FPS):** {fps:.2f}\n"
                   f"**파일 경로:** {file_path}")

            cap.release()
        except Exception as e:
            msg = f"미디어 정보를 가져오는 데 실패했습니다: {e}"
            if "No such file or directory: 'ffprobe'" in str(e):
                msg += "\n\nFFmpeg (ffprobe)가 설치되어 있고 시스템 PATH에 등록되었는지 확인하십시오."

        msg_box.setText(msg)
        msg_box.exec_()

    def merge_videos(self, file_paths): # 비디오 병합
        if not file_paths:
            return

        output_file_path, _ = QFileDialog.getSaveFileName(self, "병합된 비디오 저장", "", "비디오 파일 (*.mov *.mp4)")
        if not output_file_path:
            return

        if not output_file_path.endswith('.mov'):
            output_file_path += '.mov'

        self.progress_dialog = QProgressDialog("비디오 병합 중...", "취소", 0, 100, self)
        self.progress_dialog.setWindowModality(Qt.WindowModal)
        self.progress_dialog.setAutoReset(False)
        self.progress_dialog.setAutoClose(False)
        self.progress_dialog.show()

        self.worker = FFmpegWorker(lambda: self._merge_videos_process(file_paths, output_file_path))
        self.worker.progress.connect(self.update_progress)
        self.worker.finished.connect(self.on_merge_finished)
        self.worker.error.connect(self.on_merge_error)
        self.worker.start()

    def _merge_videos_process(self, file_paths, output_file_path): # 실제 병합 (FFmpeg) , utils.py로 이동
        temp_dir = tempfile.mkdtemp()
        temp_files = []

        try:
            total_steps = 2  # 비디오 변환 및 최종 병합
            current_step = 0

            video_list_path = os.path.join(temp_dir, "video_list.txt")
            with open(video_list_path, "w") as f:
                for file_path in file_paths:
                    f.write(f"file '{file_path}'\n")
            temp_files.append(video_list_path)

            current_step += 1
            self.worker.progress.emit(int(current_step / total_steps * 100))

            cmd = [
                self.ffmpeg_path,
                '-f', 'concat',
                '-safe', '0',
                '-i', video_list_path,
                '-vf', "blackdetect=d=0.1:pix_th=0.1,blackframe=0:32",
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '23',
                '-c:a', 'aac',
                '-b:a', '192k',
                '-pix_fmt', 'yuv420p',
                '-movflags', 'faststart',
                '-f', 'mov',
                output_file_path
            ]
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)

            while True:
                output = process.stderr.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    progress = int((current_step + 0.5) / total_steps * 100)
                    self.worker.progress.emit(progress)

            self.worker.progress.emit(100)

        except subprocess.CalledProcessError as e:
            error_msg = f"FFmpeg 처리 중 오류 발생:\nCommand: {e.cmd}\nReturn code: {e.returncode}\nOutput: {e.stdout}\nError: {e.stderr}"
            raise Exception(error_msg)
        except Exception as e:
            print(f"예상치 못한 오류 발생: {str(e)}")
            raise
        finally:
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)

    def update_progress(self, value):  # 병합 진행률 업데이트
        self.progress_dialog.setValue(value)

    def on_merge_finished(self):  # 병합 완료
        self.progress_dialog.close()
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setText("애니매틱 하나로 합치기가 완료되었습니다.")
        msg_box.setWindowTitle("완료")
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()

    def on_merge_error(self, error_message): # 병합 에러
        self.progress_dialog.close()
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setText(f"오류가 발생했습니다: {error_message}")
        msg_box.setWindowTitle("오류")
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()

    def toggle_fullscreen(self): # 전체화면 토글
        if self.isFullScreen:
            self.showNormal()
            self.isFullScreen = False
            self.controlPanel.fullscreenButton.setText('Full screen')
        else:
            self.showFullScreen()
            self.isFullScreen = True
            self.controlPanel.fullscreenButton.setText('Exit Full screen')
            self.videoWidget.resizeEvent(None)

    def toggle_loop_play(self, state): # 루프 재생 토글
        self.loopEnabled = state == Qt.Checked
        if self.timer.isActive() and self.mediaPlayer.state() == QMediaPlayer.PlayingState:
            self.controlPanel.set_play_pause_button_state(self.is_playing, self.loopEnabled) # 버튼 스타일

    def keyPressEvent(self, event): # 키보드 이벤트
        if event.key() == Qt.Key_B:  # B: 오버레이 토글
            self.show_overlay = not self.show_overlay
            self.update_frame()
        elif event.key() in [Qt.Key_Comma, Qt.Key_Left]: # , or Left: 이전 프레임
            self.prev_frame()
        elif event.key() == Qt.Key_Space: # Space: 재생/일시정지
            self.toggle_play_pause()
        elif event.key() == Qt.Key_F:    # F: 전체화면
            self.toggle_fullscreen()
        elif event.key() == Qt.Key_Plus or event.key() == Qt.Key_2: # +, 2: 줌 인
            self.videoWidget.zoom_in()
        elif event.key() == Qt.Key_Minus or event.key() == Qt.Key_1: # -, 1: 줌 아웃
            self.videoWidget.zoom_out()
        elif event.key() == Qt.Key_3: # 3: 창 크기에 맞춤
            self.videoWidget.fit_to_window()
            self.videoWidget.is_fit_to_window = True
            self.videoWidget.reset_view()
        elif event.key() == Qt.Key_4: # 4: 원본 크기
            self.videoWidget.show_original_size()
        elif event.key() in [Qt.Key_Period, Qt.Key_Right]: # ., Right: 다음 프레임
            self.next_frame()
        else:
            super().keyPressEvent(event)

    def resizeEvent(self, event): # 창 크기 변경 이벤트
        super().resizeEvent(event)
        self.videoWidget.resizeEvent(event)
        self.update_frame()

    def eventFilter(self, source, event): # 이벤트 필터
        if event.type() == QEvent.KeyPress and event.key() == Qt.Key_V: # V: 컨트롤 보이기/숨기기
            self.controls_visible = not self.controls_visible
            for widget in self.control_widgets:
                widget.setVisible(self.controls_visible)

            self.positionScroll.setVisible(True)
            self.videoWidget.setVisible(True)
            return True

        elif event.type() == QEvent.Wheel:  # Wheel: 스크롤
            delta = event.angleDelta().y()
            frame_step = 3

            if source == self.videoWidget: # 비디오 영역에서 휠
                if delta < 0:
                    new_value = max(self.positionScroll.value() - frame_step, self.positionScroll.minimum())
                else:
                    new_value = min(self.positionScroll.value() + frame_step, self.positionScroll.maximum())

                self.positionScroll.setValue(new_value)
                return True

            if delta < 0:
                new_value = max(self.positionScroll.value() - frame_step, self.positionScroll.minimum())
            else:
                new_value = min(self.positionScroll.value() + frame_step, self.positionScroll.maximum())

            self.positionScroll.setValue(new_value) # 스크롤바 값 설정
            if self.cap_a:
                total_frames = int(self.cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
                if new_value == self.positionScroll.minimum(): # 최소값이면
                    target_frame = 1  # 1 프레임
                else:  # 아니면 계산
                    target_frame = int((new_value / self.positionScroll.maximum()) * total_frames)
                    target_frame = max(target_frame, 1)  # 최소 1
                self.seek_to_frame(target_frame)    # 프레임 이동
            return True
        return super().eventFilter(source, event)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    player = VideoPlayer()

    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        print(f"Opening file: {file_path}")
        if os.path.isfile(file_path):
            player.open_file('A', file_path)
        else:
            print(f"File does not exist: {file_path}")

    player.show()
    sys.exit(app.exec_())