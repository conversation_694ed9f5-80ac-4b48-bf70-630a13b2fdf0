# utils.py
import sys
import os
import subprocess
import shutil
import platform
import tempfile
import re
from PyQt5.QtCore import QThread, pyqtSignal


def get_ffmpeg_path():
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(os.path.dirname(os.path.dirname(sys.executable)), 'Frameworks', 'ffmpeg')
    return "ffmpeg"


def get_ffprobe_path(ffmpeg_path):
    if hasattr(sys, '_MEIPASS'):
        bundled_ffprobe = os.path.join(os.path.dirname(ffmpeg_path), "ffprobe")
        return bundled_ffprobe if os.path.exists(bundled_ffprobe) else "ffprobe"
    return "ffprobe"

def format_time(seconds, fps):
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds_int = int(seconds % 60)
    milliseconds = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{seconds_int:02d}.{milliseconds:03d}"

def get_video_codec(file_path, ffprobe_exe):
    try:
        result = subprocess.run(
            [ffprobe_exe, '-v', 'error', '-select_streams', 'v:0',
             '-show_entries', 'stream=codec_name', '-of',
             'default=noprint_wrappers=1:nokey=1', file_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=True
        )
        codec_name = result.stdout.strip()
        return codec_name if codec_name else "코덱 정보를 찾을 수 없습니다."
    except subprocess.CalledProcessError as e:
        return f"ffprobe 실행 오류: {e.returncode}"
    except Exception as e:
        return f"오류 발생: {str(e)}"

def copy_files_to_target(file_paths, target_path): # 플랫폼별 복사
    current_platform = platform.system()
    if current_platform == 'Darwin':
        copy_files_mac(file_paths, target_path)
    elif current_platform == 'Windows':
        copy_files_windows(file_paths, target_path)
    else:  # Linux
        copy_files_linux(file_paths, target_path)

def copy_files_mac(file_paths, target_path): # macOS 파일 복사
    for file_path in file_paths:
        try:
            shutil.copy2(file_path, target_path)
            print(f"File copied to {target_path}")
        except Exception as e:
            print(f"Error copying file: {str(e)}")

def copy_files_windows(file_paths, target_path): # Windows 파일 복사
    for file_path in file_paths:
        cmd = f'robocopy /copyall /e /dcopy:DAT /B /r:3 /w:60 /is /nfl /ndl /np /MT:32 "{os.path.dirname(file_path)}" "{target_path}" "{os.path.basename(file_path)}"'
        try:
            subprocess.run(cmd, shell=True, check=True)
            print(f"File copied to {target_path}")
        except subprocess.CalledProcessError as e:
            print(f"Error copying file: {str(e)}")

def copy_files_linux(file_paths, target_path): # Linux 파일 복사
    for file_path in file_paths:
        try:
            shutil.copy2(file_path, target_path)
            print(f"File copied to {target_path}")
        except Exception as e:
            print(f"Error copying file: {str(e)}")

class FFmpegWorker(QThread):
    progress = pyqtSignal(int)
    finished = pyqtSignal()
    error = pyqtSignal(str)

    def __init__(self, merge_function):
        super().__init__()
        self.merge_function = merge_function

    def run(self):
        try:
            self.merge_function()
            self.finished.emit()
        except Exception as e:
            self.error.emit(str(e))