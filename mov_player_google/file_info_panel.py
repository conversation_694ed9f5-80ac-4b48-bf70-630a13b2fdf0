# file_info_panel.py
import os
from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QLabel)
from PyQt5.QtGui import QFont
from utils import format_time  # Import from utils.py

class FileInfoPanel(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()

    def initUI(self):
        self.filenameLabel = QLabel()
        self.labelFrameCount = QLabel()
        self.labelTotalFrames = QLabel()
        self.timeLabel = QLabel("00:00:00 / 00:00:00")

        font = QFont()
        font.setPointSize(16)
        self.filenameLabel.setFont(font)
        self.labelFrameCount.setFont(font)
        self.labelTotalFrames.setFont(font)
        self.timeLabel.setFont(QFont("Arial", 16))

        self.labelFrameCount.setFixedWidth(200)
        self.labelTotalFrames.setFixedWidth(200)

        self.filenameLabel.setStyleSheet("padding: 10px;")
        self.labelFrameCount.setStyleSheet("padding: 10px;")
        self.labelTotalFrames.setStyleSheet("padding: 10px;")

        layout = QHBoxLayout()
        layout.addWidget(self.filenameLabel)
        layout.addWidget(self.labelFrameCount)
        layout.addWidget(self.labelTotalFrames)
        layout.addStretch(1)
        layout.addWidget(self.timeLabel)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        self.setLayout(layout)

    def update_file_info(self, filename_a, filename_b, current_frame, total_frames_a, total_frames_b, current_time, total_time, fps):
        if filename_a and filename_b:
            self.filenameLabel.setText(f"Video A: {os.path.basename(filename_a)}, Video B: {os.path.basename(filename_b)}")
        elif filename_a:
            self.filenameLabel.setText(f"Video A: {os.path.basename(filename_a)}")
        else:
            self.filenameLabel.setText("")

        self.labelFrameCount.setText(f'Current Frame: {current_frame}')
        self.labelTotalFrames.setText(f'Total Frames A: {total_frames_a}, Total Frames B: {total_frames_b}')
        self.timeLabel.setText(f"{format_time(current_time, fps)} / {format_time(total_time, fps)}")

    def clear_info(self):
        self.filenameLabel.setText("")
        self.labelFrameCount.setText("")
        self.labelTotalFrames.setText("")
        self.timeLabel.setText("00:00:00 / 00:00:00")