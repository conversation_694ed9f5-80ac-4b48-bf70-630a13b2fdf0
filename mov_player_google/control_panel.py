# control_panel.py
import cv2
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                             QLabel, QScrollBar, QComboBox, QSizePolicy, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal

class ControlPanel(QWidget):
    openFileRequested = pyqtSignal(str)
    prevFrameRequested = pyqtSignal()
    nextFrameRequested = pyqtSignal()
    toggleFullscreenRequested = pyqtSignal()
    toggleLoopPlayRequested = pyqtSignal(int)
    volumeChanged = pyqtSignal(int)
    frameRateChanged = pyqtSignal(str)
    toggleWipeEffectRequested = pyqtSignal(int)
    toggleWipeXRequested = pyqtSignal(bool)
    toggleWipeYRequested = pyqtSignal(bool)
    playSelectedRequested = pyqtSignal()
    togglePlayPauseSelectedRequested = pyqtSignal()
    gpuBackendChanged = pyqtSignal(int)
    workSelected = pyqtSignal(str)
    seasonSelected = pyqtSignal(str)
    jobSelected = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()

    def initUI(self):

        # GPU Acceleration Combobox
        self.gpuComboBox = QComboBox()
        self.gpuComboBox.addItem("CPU")
        if cv2.cuda.getCudaEnabledDeviceCount() > 0:
            self.gpuComboBox.addItem("NVIDIA (CUDA)")
        if cv2.ocl.haveOpenCL():
            self.gpuComboBox.addItem("AMD (OpenCL)")
        self.gpuComboBox.currentIndexChanged.connect(self.gpuBackendChanged)
        self.gpuLabel = QLabel("GPU Acceleration:")

        # Yeson Work Comboboxes
        workLabel = QLabel("Yeson 작품")
        workLabel.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.workComboBox = QComboBox()
        self.workComboBox.addItem("작품선택")
        self.workComboBox.addItems(["BB", "KOTH", "Big_Mouth", "TGN", "BB_Animatic", "KOTH_Animatic", "BM_Animatic", "TGN_Animatic"])
        self.workComboBox.currentIndexChanged.connect(lambda i: self.workSelected.emit(self.workComboBox.currentText()))

        self.seasonComboBox = QComboBox()
        self.seasonComboBox.addItem("시즌선택")
        self.seasonComboBox.currentIndexChanged.connect(lambda i: self.seasonSelected.emit(self.seasonComboBox.currentText()))

        self.jobComboBox = QComboBox()
        self.jobComboBox.addItem("job선택")
        self.jobComboBox.currentIndexChanged.connect(lambda i: self.jobSelected.emit(self.jobComboBox.currentText()))


        openButtonA = QPushButton('Open Video A')
        openButtonA.clicked.connect(lambda: self.openFileRequested.emit('A'))

        openButtonB = QPushButton('Open Video B')
        openButtonB.clicked.connect(lambda: self.openFileRequested.emit('B'))

        prevButton = QPushButton('Previous Frame')
        prevButton.clicked.connect(self.prevFrameRequested.emit)

        nextButton = QPushButton('Next Frame')
        nextButton.clicked.connect(self.nextFrameRequested.emit)

        self.fullscreenButton = QPushButton('Full screen')
        self.fullscreenButton.clicked.connect(self.toggleFullscreenRequested.emit)

        self.loopCheckBox = QCheckBox("Loop Play")
        self.loopCheckBox.setToolTip('Enable loop playback')
        self.loopCheckBox.stateChanged.connect(self.toggleLoopPlayRequested.emit)


        self.playPauseButton = QPushButton('Play')
        # self.playPauseButton.clicked.connect(self.toggle_play_pause) # Main window에서 연결
        self.playPauseButton.setFixedHeight(80)

        stopButton = QPushButton('Stop')
        # stopButton.clicked.connect(self.stop_video) # Main window에서 연결
        stopButton.setFixedHeight(80)


        self.frameRateLabel = QLabel("FPS")
        self.frameRateComboBox = QComboBox()
        self.frameRateComboBox.setToolTip('Frame rate (frames per second)')
        self.frameRateComboBox.addItems([str(i) for i in range(1, 61)])
        self.frameRateComboBox.setCurrentText('24')
        self.frameRateComboBox.currentIndexChanged.connect(lambda: self.frameRateChanged.emit(self.frameRateComboBox.currentText()))

        self.wipeCheckBox = QCheckBox('Enable Wipe Effect')
        self.wipeCheckBox.setChecked(False)
        self.wipeCheckBox.stateChanged.connect(self.toggleWipeEffectRequested.emit)


        self.wipeXButton = QPushButton('X-Axis Wipe')
        self.wipeXButton.setCheckable(True)
        self.wipeXButton.toggled.connect(self.toggleWipeXRequested.emit)

        self.wipeYButton = QPushButton('Y-Axis Wipe')
        self.wipeYButton.setCheckable(True)
        self.wipeYButton.toggled.connect(self.toggleWipeYRequested.emit)

        self.volumeSlider = QScrollBar(Qt.Horizontal)
        self.volumeSlider.setRange(0, 100)
        self.volumeSlider.setValue(50)
        self.volumeSlider.setFixedWidth(100)
        self.volumeSlider.valueChanged.connect(self.volumeChanged.emit)

        volumeLabel = QLabel("Volume:")

        self.playSelectedButton = QPushButton('Play Selected')
        self.playSelectedButton.clicked.connect(self.playSelectedRequested.emit)
        self.playSelectedButton.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        self.playSelectedPauseButton = QPushButton('Play/Pause Selected')
        self.playSelectedPauseButton.clicked.connect(self.togglePlayPauseSelectedRequested.emit)
        self.playSelectedPauseButton.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)


        # Layouts
        controlLayoutMiddle = QVBoxLayout()
        for widget in [openButtonA, openButtonB, prevButton, nextButton, self.fullscreenButton,
                       self.loopCheckBox, volumeLabel, self.volumeSlider, self.frameRateLabel,
                       self.frameRateComboBox, self.wipeCheckBox, self.wipeXButton, self.wipeYButton,
                       self.playSelectedButton, self.playSelectedPauseButton, self.gpuLabel, self.gpuComboBox,
                       workLabel, self.workComboBox, self.seasonComboBox, self.jobComboBox]:
            controlLayoutMiddle.addWidget(widget)
        controlLayoutMiddle.setContentsMargins(0, 0, 0, 0)
        controlLayoutMiddle.setSpacing(5)

                # Main Layout
        mainLayout = QHBoxLayout()
        mainLayout.addLayout(controlLayoutMiddle)
        mainLayout.addStretch(1)  # Add stretch to push controls to the left
        self.setLayout(mainLayout)

    def set_season_items(self, items):
        self.seasonComboBox.clear()
        self.seasonComboBox.addItem("시즌선택")
        self.seasonComboBox.addItems(items)

    def set_job_items(self, items):
        self.jobComboBox.clear()
        self.jobComboBox.addItem("job선택")
        self.jobComboBox.addItems(items)

    def set_play_pause_button_state(self, is_playing, is_loop=False):
        if is_playing:
            self.playPauseButton.setText('Pause')
            if is_loop:
                self.playPauseButton.setStyleSheet("background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 yellow, stop:1 orange);")
            else:
                self.playPauseButton.setStyleSheet("background-color: yellow;")
        else:
            self.playPauseButton.setText('Play')
            self.playPauseButton.setStyleSheet("")

    def set_play_pause_selected_button_state(self, is_playing):
        if is_playing:
            self.playSelectedPauseButton.setText('Selected Pause')
            self.playSelectedPauseButton.setStyleSheet("background-color: yellow;")
        else:
            self.playSelectedPauseButton.setText('Selected Play')
            self.playSelectedPauseButton.setStyleSheet("")