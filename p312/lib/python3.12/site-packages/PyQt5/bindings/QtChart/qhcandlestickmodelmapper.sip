// qhcandlestickmodelmapper.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtChart_5_8_0 -)

namespace QtCharts
{
%TypeHeaderCode
#include <qhcandlestickmodelmapper.h>
%End

    class QHCandlestickModelMapper : public QtCharts::QCandlestickModelMapper
    {
%TypeHeaderCode
#include <qhcandlestickmodelmapper.h>
%End

    public:
        explicit QHCandlestickModelMapper(QObject *parent /TransferThis/ = 0);
        virtual Qt::Orientation orientation() const;
        void setTimestampColumn(int timestampColumn);
        int timestampColumn() const;
        void setOpenColumn(int openColumn);
        int openColumn() const;
        void setHighColumn(int highColumn);
        int highColumn() const;
        void setLowColumn(int lowColumn);
        int lowColumn() const;
        void setCloseColumn(int closeColumn);
        int closeColumn() const;
        void setFirstSetRow(int firstSetRow);
        int firstSetRow() const;
        void setLastSetRow(int lastSetRow);
        int lastSetRow() const;

    signals:
        void timestampColumnChanged();
        void openColumnChanged();
        void highColumnChanged();
        void lowColumnChanged();
        void closeColumnChanged();
        void firstSetRowChanged();
        void lastSetRowChanged();
    };
};

%End
