// qvalueaxis.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtChart_1_1_0 -)

namespace QtCharts
{
%TypeHeaderCode
#include <qvalueaxis.h>
%End

    class QValueAxis : public QtCharts::QAbstractAxis
    {
%TypeHeaderCode
#include <qvalueaxis.h>
%End

    public:
        explicit QValueAxis(QObject *parent /TransferThis/ = 0);
        virtual ~QValueAxis();
        virtual QtCharts::QAbstractAxis::AxisType type() const;
        void setMin(qreal min);
        qreal min() const;
        void setMax(qreal max);
        qreal max() const;
        void setRange(qreal min, qreal max);
        void setTickCount(int count);
        int tickCount() const;
        void setLabelFormat(const QString &format);
        QString labelFormat() const;
%If (- QtChart_2_0_0)
        void setNiceNumbersEnabled(bool enable = true);
%End
%If (- QtChart_2_0_0)
        bool niceNumbersEnabled() const;
%End

    public slots:
%If (QtChart_1_2_0 -)
        void applyNiceNumbers();
%End

    signals:
        void minChanged(qreal min);
        void maxChanged(qreal max);
        void rangeChanged(qreal min, qreal max);
%If (QtChart_1_2_0 -)
        void tickCountChanged(int tickCount);
%End
%If (QtChart_1_2_0 -)
        void labelFormatChanged(const QString &format);
%End

    public:
%If (QtChart_2_1_0 -)
        void setMinorTickCount(int count);
%End
%If (QtChart_2_1_0 -)
        int minorTickCount() const;
%End

    signals:
%If (QtChart_2_1_0 -)
        void minorTickCountChanged(int tickCount);
%End

    public:
%If (QtChart_5_12_0 -)

        enum TickType
        {
            TicksDynamic,
            TicksFixed,
        };

%End
%If (QtChart_5_12_0 -)
        void setTickAnchor(qreal anchor);
%End
%If (QtChart_5_12_0 -)
        qreal tickAnchor() const;
%End
%If (QtChart_5_12_0 -)
        void setTickInterval(qreal insterval);
%End
%If (QtChart_5_12_0 -)
        qreal tickInterval() const;
%End
%If (QtChart_5_12_0 -)
        void setTickType(QtCharts::QValueAxis::TickType type);
%End
%If (QtChart_5_12_0 -)
        QtCharts::QValueAxis::TickType tickType() const;
%End

    signals:
%If (QtChart_5_12_0 -)
        void tickIntervalChanged(qreal interval);
%End
%If (QtChart_5_12_0 -)
        void tickAnchorChanged(qreal anchor);
%End
%If (QtChart_5_12_0 -)
        void tickTypeChanged(QtCharts::QValueAxis::TickType type /ScopesStripped=1/);
%End
    };
};

%End
