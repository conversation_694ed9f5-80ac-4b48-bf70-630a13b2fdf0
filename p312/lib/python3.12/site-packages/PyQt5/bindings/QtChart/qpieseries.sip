// qpieseries.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QtCharts
{
%TypeHeaderCode
#include <qpieseries.h>
%End

    class QPieSeries : public QtCharts::QAbstractSeries
    {
%TypeHeaderCode
#include <qpieseries.h>
%End

    public:
        explicit QPieSeries(QObject *parent /TransferThis/ = 0);
        virtual ~QPieSeries();
        virtual QtCharts::QAbstractSeries::SeriesType type() const;
        bool append(QtCharts::QPieSlice *slice /Transfer/);
        bool append(QList<QtCharts::QPieSlice *> slices /Transfer/);
        QtCharts::QPieSeries &operator<<(QtCharts::QPieSlice *slice /Transfer/);
        QtCharts::QPieSlice *append(QString label, qreal value);
        bool insert(int index, QtCharts::QPieSlice *slice /Transfer/);
        bool remove(QtCharts::QPieSlice *slice);
        void clear();
        QList<QtCharts::QPieSlice *> slices() const;
        int count() const /__len__/;
        bool isEmpty() const;
        qreal sum() const;
        void setHorizontalPosition(qreal relativePosition);
        qreal horizontalPosition() const;
        void setVerticalPosition(qreal relativePosition);
        qreal verticalPosition() const;
        void setPieSize(qreal relativeSize);
        qreal pieSize() const;
        void setPieStartAngle(qreal startAngle);
        qreal pieStartAngle() const;
        void setPieEndAngle(qreal endAngle);
        qreal pieEndAngle() const;
        void setLabelsVisible(bool visible = true);

    signals:
        void added(QList<QtCharts::QPieSlice *> slices /ScopesStripped=1/);
        void removed(QList<QtCharts::QPieSlice *> slices /ScopesStripped=1/);
        void clicked(QtCharts::QPieSlice *slice /ScopesStripped=1/);
        void hovered(QtCharts::QPieSlice *slice /ScopesStripped=1/, bool state);
        void countChanged();
        void sumChanged();

    public:
%If (QtChart_1_1_0 -)
        bool take(QtCharts::QPieSlice *slice);
%End
%If (QtChart_1_1_0 -)
        void setHoleSize(qreal holeSize);
%End
%If (QtChart_1_1_0 -)
        qreal holeSize() const;
%End
%If (QtChart_1_1_0 -)
        void setLabelsPosition(QtCharts::QPieSlice::LabelPosition position);
%End

    signals:
%If (QtChart_2_0_0 -)
        void pressed(QtCharts::QPieSlice *slice /ScopesStripped=1/);
%End
%If (QtChart_2_0_0 -)
        void released(QtCharts::QPieSlice *slice /ScopesStripped=1/);
%End
%If (QtChart_2_0_0 -)
        void doubleClicked(QtCharts::QPieSlice *slice /ScopesStripped=1/);
%End
    };
};
