// qhpiemodelmapper.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QtCharts
{
%TypeHeaderCode
#include <qhpiemodelmapper.h>
%End

    class QHPieModelMapper : QObject
    {
%TypeHeaderCode
#include <qhpiemodelmapper.h>
%End

    public:
        explicit QHPieModelMapper(QObject *parent /TransferThis/ = 0);
        int valuesRow() const;
        void setValuesRow(int valuesRow);
        int labelsRow() const;
        void setLabelsRow(int labelsRow);
        QAbstractItemModel *model() const;
        void setModel(QAbstractItemModel *model /KeepReference/);
        QtCharts::QPieSeries *series() const;
        void setSeries(QtCharts::QPieSeries *series);
        int firstColumn() const;
        void setFirstColumn(int firstColumn);
        int columnCount() const;
        void setColumnCount(int columnCount);

    signals:
        void seriesReplaced();
        void modelReplaced();
        void valuesRowChanged();
        void labelsRowChanged();
        void firstColumnChanged();
        void columnCountChanged();
    };
};
