// qboxset.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtChart_1_3_0 -)

namespace QtCharts
{
%TypeHeaderCode
#include <qboxset.h>
%End

    class QBoxSet : public QObject
    {
%TypeHeaderCode
#include <qboxset.h>
%End

    public:
        enum ValuePositions
        {
            LowerExtreme,
            LowerQuartile,
            Median,
            UpperQuartile,
            UpperExtreme,
        };

        QBoxSet(const QString label = QString(), QObject *parent /TransferThis/ = 0);
        QBoxSet(const qreal le, const qreal lq, const qreal m, const qreal uq, const qreal ue, const QString label = QString(), QObject *parent /TransferThis/ = 0);
        virtual ~QBoxSet();
        void append(const qreal value);
        void append(const QList<qreal> &values);
        void clear();
        void setLabel(const QString label);
        QString label() const;
        QtCharts::QBoxSet &operator<<(const qreal &value);
        void setValue(const int index, const qreal value);
        qreal at(const int index) const;
        qreal operator[](const int index) const;
        int count() const /__len__/;
        void setPen(const QPen &pen);
        QPen pen() const;
        void setBrush(const QBrush &brush);
        QBrush brush() const;

    signals:
        void clicked();
        void hovered(bool status);
        void penChanged();
        void brushChanged();
        void valuesChanged();
        void valueChanged(int index);
        void cleared();
%If (QtChart_2_0_0 -)
        void pressed();
%End
%If (QtChart_2_0_0 -)
        void released();
%End
%If (QtChart_2_0_0 -)
        void doubleClicked();
%End
    };
};

%End
