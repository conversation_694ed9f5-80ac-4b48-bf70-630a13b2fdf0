// qhbarmodelmapper.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QtCharts
{
%TypeHeaderCode
#include <qhbarmodelmapper.h>
%End

    class QHBarModelMapper : QObject
    {
%TypeHeaderCode
#include <qhbarmodelmapper.h>
%End

    public:
        explicit QHBarModelMapper(QObject *parent /TransferThis/ = 0);
        int firstBarSetRow() const;
        void setFirstBarSetRow(int firstBarSetRow);
        int lastBarSetRow() const;
        void setLastBarSetRow(int lastBarSetRow);
        QAbstractItemModel *model() const;
        void setModel(QAbstractItemModel *model /KeepReference/);
        QtCharts::QAbstractBarSeries *series() const;
        void setSeries(QtCharts::QAbstractBarSeries *series);
        int firstColumn() const;
        void setFirstColumn(int firstColumn);
        int columnCount() const;
        void setColumnCount(int columnCount);

    signals:
        void seriesReplaced();
        void modelReplaced();
        void firstBarSetRowChanged();
        void lastBarSetRowChanged();
        void firstColumnChanged();
        void columnCountChanged();
    };
};
