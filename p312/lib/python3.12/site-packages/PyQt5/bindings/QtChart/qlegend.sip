// qlegend.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QtCharts
{
%TypeHeaderCode
#include <qlegend.h>
%End

    class QLegend : public QGraphicsWidget
    {
%TypeHeaderCode
#include <qlegend.h>
%End

    public:
        virtual ~QLegend();
        virtual void paint(Q<PERSON>ain<PERSON> *painter, const QStyleOptionGraphicsItem *option, QWidget *widget = 0);
        void setBrush(const QBrush &brush);
        QBrush brush() const;
        void setPen(const QPen &pen);
        QPen pen() const;
        void setAlignment(Qt::Alignment alignment);
        Qt::Alignment alignment() const;
        void detachFromChart();
        void attachToChart();
        bool isAttachedToChart();
        void setBackgroundVisible(bool visible = true);
        bool isBackgroundVisible() const;
        void setColor(QColor color);
        QColor color();
        void setBorderColor(QColor color);
        QColor borderColor();
        void setFont(const QFont &font);
        QFont font() const;
        void setLabelBrush(const QBrush &brush);
        QBrush labelBrush() const;
        void setLabelColor(QColor color);
        QColor labelColor() const;
%If (QtChart_1_2_0 -)
        QList<QtCharts::QLegendMarker *> markers(QtCharts::QAbstractSeries *series = 0) const;
%End

    signals:
        void backgroundVisibleChanged(bool visible);
        void colorChanged(QColor color);
        void borderColorChanged(QColor color);
        void fontChanged(QFont font);
        void labelColorChanged(QColor color);

    protected:
        virtual void hideEvent(QHideEvent *event);
        virtual void showEvent(QShowEvent *event);

    public:
%If (QtChart_1_4_0 -)
        bool reverseMarkers();
%End
%If (QtChart_1_4_0 -)
        void setReverseMarkers(bool reverseMarkers = true);
%End

    signals:
%If (QtChart_1_4_0 -)
        void reverseMarkersChanged(bool reverseMarkers);
%End

    public:
%If (QtChart_5_7_0 -)
        bool showToolTips() const;
%End
%If (QtChart_5_7_0 -)
        void setShowToolTips(bool show);
%End

    signals:
%If (QtChart_5_7_0 -)
        void showToolTipsChanged(bool showToolTips);
%End

    public:
%If (QtChart_5_9_0 -)

        enum MarkerShape
        {
            MarkerShapeDefault,
            MarkerShapeRectangle,
            MarkerShapeCircle,
            MarkerShapeFromSeries,
        };

%End
%If (QtChart_5_9_0 -)
        QtCharts::QLegend::MarkerShape markerShape() const;
%End
%If (QtChart_5_9_0 -)
        void setMarkerShape(QtCharts::QLegend::MarkerShape shape);
%End

    signals:
%If (QtChart_5_9_0 -)
        void markerShapeChanged(QtCharts::QLegend::MarkerShape shape /ScopesStripped=2/);
%End

    private:
        explicit QLegend(QtCharts::QChart *chart);
    };
};
