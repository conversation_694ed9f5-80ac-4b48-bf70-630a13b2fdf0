import sys
import json
import os
import subprocess
import psutil
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QPushButton, QLineEdit,
                            QHBoxLayout, QComboBox, QLabel, QTreeWidget, QTreeWidgetItem, QStyle,
                            QFrame, QSplitter, QStatusBar, QGraphicsDropShadowEffect, QHeaderView,
                            QStyleFactory, QSizePolicy, QSpacerItem, QScrollArea, QFileDialog,
                            QTextEdit, QProgressBar, QMessageBox, QToolButton, QCheckBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QSize, pyqtSlot, QMetaObject, Q_ARG, QStandardPaths
from PyQt5.QtGui import QColor, QPalette, QFont, QIcon, QLinearGradient, QBrush, QPixmap, QKeySequence, QTextCursor, QTextDocument, QTextCharFormat
from PyQt5.QtWidgets import QShortcut
import shutil
import time
import threading
import queue
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from concurrent.futures import ThreadPoolExecutor, wait
import uuid

# --- 스타일 및 헬퍼 클래스 ---
class StyleManager:
    DARK_THEME = {
        "BG": "#1E2526",
        "LIGHT_BG": "#2A3439",
        "ACCENT": "#D4AF37", # 금색 계열
        "TEXT": "#E8ECEF",
        "MEDIUM_GRAY": "#A9B1B3",
        "DARK_ACCENT": "#3A4A50",
        "ERROR_RED": "#E57373" # 오류 메시지용 붉은색 추가
    }

    LIGHT_THEME = {
        "BG": "#F5F6F5",
        "LIGHT_BG": "#FFFFFF",
        "ACCENT": "#F4A261", # 주황색 계열
        "TEXT": "#2A3439",
        "MEDIUM_GRAY": "#6B7280",
        "DARK_ACCENT": "#E5E7EB",
        "ERROR_RED": "#D32F2F" # 오류 메시지용 붉은색 추가
    }

    @staticmethod
    def apply_style(app, theme="dark"):
        colors = StyleManager.DARK_THEME if theme == "dark" else StyleManager.LIGHT_THEME
        app.setStyle(QStyleFactory.create("Fusion"))
        font = QFont("Helvetica", 10)
        app.setFont(font)
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(colors["BG"]))
        palette.setColor(QPalette.WindowText, QColor(colors["TEXT"]))
        palette.setColor(QPalette.Base, QColor(colors["LIGHT_BG"]))
        palette.setColor(QPalette.AlternateBase, QColor(colors["DARK_ACCENT"]))
        palette.setColor(QPalette.ToolTipBase, QColor(colors["LIGHT_BG"]))
        palette.setColor(QPalette.ToolTipText, QColor(colors["TEXT"]))
        palette.setColor(QPalette.Text, QColor(colors["TEXT"]))
        palette.setColor(QPalette.Button, QColor(colors["DARK_ACCENT"]))
        palette.setColor(QPalette.ButtonText, QColor(colors["TEXT"]))
        palette.setColor(QPalette.BrightText, QColor(colors["ACCENT"]))
        palette.setColor(QPalette.Link, QColor(colors["ACCENT"]))
        palette.setColor(QPalette.Highlight, QColor(colors["ACCENT"]))
        palette.setColor(QPalette.HighlightedText, QColor(colors["BG"]))
        app.setPalette(palette)

        app.setStyleSheet(f"""
            QMainWindow {{ background: {colors["BG"]}; }}
            QWidget {{ background: {colors["BG"]}; }}
            QLabel {{ color: {colors["TEXT"]}; font-size: 11px; font-weight: 400; padding: 4px; }}
            QLabel#headerLabel {{ font-size: 13px; font-weight: bold; color: {colors["ACCENT"]}; padding: 8px; }}
            QLabel#jobStatusLabel {{ font-size: 11px; font-weight: bold; }}
            QLabel#errorLabel {{ color: {colors["ERROR_RED"]}; font-style: italic; font-size: 10px; }}
            QComboBox {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; padding: 4px 8px; font-size: 11px; background: {colors["LIGHT_BG"]}; color: {colors["TEXT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; }}
            QComboBox::drop-down {{ subcontrol-origin: padding; subcontrol-position: right center; width: 20px; border-left: 1px solid {colors["DARK_ACCENT"]}; border-top-right-radius: 4px; border-bottom-right-radius: 4px; }}
            QComboBox QAbstractItemView {{ font-size: 11px; background: {colors["LIGHT_BG"]}; border: 1px solid {colors["DARK_ACCENT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; padding: 4px; }}
            QLineEdit {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; padding: 4px 8px; font-size: 11px; background: {colors["LIGHT_BG"]}; color: {colors["TEXT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; }}
            QLineEdit:read-only {{ background: {colors["DARK_ACCENT"]}; border: 1px solid {colors["DARK_ACCENT"]}; }}
            QPushButton {{ background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {colors["ACCENT"]}, stop:1 {StyleManager.adjust_brightness(colors["ACCENT"], -20)}); color: {colors["BG"]}; border: none; border-radius: 4px; padding: 6px 12px; font-size: 11px; font-weight: bold; }}
            QPushButton:hover {{ background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {StyleManager.adjust_brightness(colors["ACCENT"], 20)}, stop:1 {colors["ACCENT"]}); }}
            QPushButton:pressed {{ background-color: {StyleManager.adjust_brightness(colors["ACCENT"], -40)}; }}
            QPushButton:disabled {{ background-color: {colors["MEDIUM_GRAY"]}; color: {colors["DARK_ACCENT"]}; }}
            QPushButton#stopButton {{
                background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {colors["ERROR_RED"]}, stop:1 {StyleManager.adjust_brightness(colors["ERROR_RED"], -20)});
                color: {colors["LIGHT_BG"]};
                font-weight: bold;
                padding: 4px 8px;
                font-size: 10px;
            }}
            QPushButton#stopButton:hover {{ background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {StyleManager.adjust_brightness(colors["ERROR_RED"], 20)}, stop:1 {colors["ERROR_RED"]}); }}
            QPushButton#stopButton:pressed {{ background-color: {StyleManager.adjust_brightness(colors["ERROR_RED"], -40)}; }}
            QToolButton {{ background-color: transparent; border: none; padding: 2px; }}
            QTreeWidget {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; font-size: 11px; background: {colors["LIGHT_BG"]}; alternate-background-color: {colors["DARK_ACCENT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; padding: 4px; }}
            QTreeWidget::item {{ padding: 4px; border-bottom: 1px solid {colors["DARK_ACCENT"]}; }}
            QTreeWidget::item:selected {{ background-color: {colors["ACCENT"]}; color: {colors["BG"]}; }}
            QHeaderView::section {{ background-color: {colors["DARK_ACCENT"]}; color: {colors["TEXT"]}; padding: 4px; border: none; border-right: 1px solid {colors["MEDIUM_GRAY"]}; border-bottom: 1px solid {colors["MEDIUM_GRAY"]}; font-size: 11px; font-weight: bold; }}
            QStatusBar {{ background-color: {colors["DARK_ACCENT"]}; color: {colors["TEXT"]}; font-size: 11px; padding: 4px 8px; }}
            QFrame#separator {{ background-color: {colors["MEDIUM_GRAY"]}; max-height: 1px; margin: 6px 0px; }}
            QToolTip {{ background-color: {colors["LIGHT_BG"]}; color: {colors["TEXT"]}; font-size: 11px; border: 1px solid {colors["DARK_ACCENT"]}; padding: 4px; }}
            QFrame.CardWidget {{ background: {colors["LIGHT_BG"]}; border-radius: 4px; border: 1px solid {colors["DARK_ACCENT"]}; }}
            QTextEdit {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; background: {colors["LIGHT_BG"]}; color: {colors["TEXT"]}; padding: 4px; font-size: 10px; }}
            QProgressBar {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; background: {colors["LIGHT_BG"]}; text-align: center; font-size: 10px; color: {colors["TEXT"]}; height: 16px; }}
            QProgressBar::chunk {{ background-color: {colors["ACCENT"]}; border-radius: 2px; }}
            QScrollArea {{ border: none; }}
            QSplitter::handle {{ background-color: {colors["MEDIUM_GRAY"]}; height: 3px; }}
        """)

    @staticmethod
    def adjust_brightness(color_hex, amount):
        color = QColor(color_hex)
        r, g, b = color.red(), color.green(), color.blue()
        r = max(0, min(255, r + amount))
        g = max(0, min(255, g + amount))
        b = max(0, min(255, b + amount))
        return f"#{r:02x}{g:02x}{b:02x}"

class AnimationHelper:
    @staticmethod
    def fade_in(widget, duration=300):
        widget.show()

    @staticmethod
    def add_drop_shadow(widget, radius=8, x_offset=1, y_offset=1, color=QColor(0, 0, 0, 60)):
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(radius)
        shadow.setXOffset(x_offset)
        shadow.setYOffset(y_offset)
        shadow.setColor(color)
        widget.setGraphicsEffect(shadow)

# --- UI 위젯 클래스 ---
class ChartTreeWidget(QTreeWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAlternatingRowColors(True)
        self.setAnimated(True)
        self.setHeaderLabels(['씬 이름', 'Frames', 'FEET', '백업상태'])
        self.setSelectionMode(QTreeWidget.ExtendedSelection)
        header = self.header()
        header.setSectionResizeMode(QHeaderView.Interactive)
        header.setSectionsMovable(False)
        header.setStretchLastSection(False)
        self.setColumnWidth(0, 250)
        self.setColumnWidth(1, 80)
        self.setColumnWidth(2, 80)
        self.setColumnWidth(3, 80)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

    def update_scene_status_in_tree(self, scene_name, status):
        for i in range(self.topLevelItemCount()):
            item = self.topLevelItem(i)
            if item.text(0) == scene_name:
                item.setText(3, status)
                break
        self.viewport().update()

    def reset_all_scene_status_in_tree(self):
        for i in range(self.topLevelItemCount()):
            item = self.topLevelItem(i)
            item.setText(3, "")

class CardWidget(QFrame):
    def __init__(self, title=None, parent=None, object_name="CardWidget"):
        super().__init__(parent)
        self.setObjectName(object_name)
        self.setFrameShape(QFrame.StyledPanel)
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(10, 10, 10, 10)
        self.layout.setSpacing(8)
        if title:
            self.title = QLabel(title)
            self.title.setObjectName("headerLabel")
            self.layout.addWidget(self.title)
            separator = QFrame()
            separator.setFrameShape(QFrame.HLine)
            separator.setFrameShadow(QFrame.Sunken)
            separator.setObjectName("separator")
            self.layout.addWidget(separator)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

class ShowSelectionWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("작품 선택", parent, "ShowSelectionCard")
        self.show_layout = QHBoxLayout()
        self.show_layout.setSpacing(8)
        show_label = QLabel("작품:")
        show_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.show_layout.addWidget(show_label)
        self.show_combo = QComboBox()
        self.show_combo.addItem("작품 선택")
        self.show_combo.addItems(["BB", "GN", "BM", "KOTH", "Test", "Yeson_DANG", "Yeson_Test", "Yeson_Test_4K"])
        self.show_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.show_layout.addWidget(self.show_combo)
        self.show_backup_btn = QPushButton("전체 백업")
        self.show_backup_btn.setToolTip("선택한 작품 전체를 초기 동기화하고 실시간 백업 시작")
        self.show_backup_btn.setCursor(Qt.PointingHandCursor)
        self.show_backup_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.show_layout.addWidget(self.show_backup_btn)
        self.show_watch_btn = QPushButton("실시간 감시")
        self.show_watch_btn.setToolTip("선택한 작품 경로에 대해 기존 백업 폴더를 지정하고 실시간 감시만 시작")
        self.show_watch_btn.setCursor(Qt.PointingHandCursor)
        self.show_watch_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.show_layout.addWidget(self.show_watch_btn)
        self.show_layout.addStretch()
        self.layout.addLayout(self.show_layout)

        self.season_layout = QHBoxLayout()
        self.season_layout.setSpacing(8)
        season_label = QLabel("시즌:")
        season_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.season_layout.addWidget(season_label)
        self.season_combo = QComboBox()
        self.season_combo.addItem("시즌 선택")
        self.season_combo.setEnabled(False)
        self.season_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.season_layout.addWidget(self.season_combo)
        self.season_backup_btn = QPushButton("전체 백업")
        self.season_backup_btn.setToolTip("선택한 시즌 전체를 초기 동기화하고 실시간 백업 시작")
        self.season_backup_btn.setCursor(Qt.PointingHandCursor)
        self.season_backup_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.season_layout.addWidget(self.season_backup_btn)
        self.season_watch_btn = QPushButton("실시간 감시")
        self.season_watch_btn.setToolTip("선택한 시즌 경로에 대해 기존 백업 폴더를 지정하고 실시간 감시만 시작")
        self.season_watch_btn.setCursor(Qt.PointingHandCursor)
        self.season_watch_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.season_layout.addWidget(self.season_watch_btn)
        self.season_layout.addStretch()
        self.layout.addLayout(self.season_layout)

        self.episode_layout = QHBoxLayout()
        self.episode_layout.setSpacing(8)
        episode_label = QLabel("화수:")
        episode_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.episode_layout.addWidget(episode_label)
        self.episode_combo = QComboBox()
        self.episode_combo.addItem("화수 선택")
        self.episode_combo.setEnabled(False)
        self.episode_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.episode_layout.addWidget(self.episode_combo)
        self.episode_backup_btn = QPushButton("전체 백업")
        self.episode_backup_btn.setToolTip("선택한 화수 전체를 초기 동기화하고 실시간 백업 시작")
        self.episode_backup_btn.setCursor(Qt.PointingHandCursor)
        self.episode_backup_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.episode_layout.addWidget(self.episode_backup_btn)
        self.episode_watch_btn = QPushButton("실시간 감시")
        self.episode_watch_btn.setToolTip("선택한 화수 경로에 대해 기존 백업 폴더를 지정하고 실시간 감시만 시작")
        self.episode_watch_btn.setCursor(Qt.PointingHandCursor)
        self.episode_watch_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.episode_layout.addWidget(self.episode_watch_btn)
        self.episode_layout.addStretch()
        self.layout.addLayout(self.episode_layout)

class SummaryWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("에피소드 정보", parent, "SummaryCard")
        summary_layout = QHBoxLayout()
        summary_layout.setSpacing(10)
        frame_layout = QVBoxLayout()
        frame_title = QLabel("총 프레임 수:")
        frame_title.setAlignment(Qt.AlignCenter)
        frame_layout.addWidget(frame_title)
        self.total_frames_display = QLineEdit()
        self.total_frames_display.setAlignment(Qt.AlignCenter)
        self.total_frames_display.setReadOnly(True)
        self.total_frames_display.setMinimumWidth(80)
        self.total_frames_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        frame_layout.addWidget(self.total_frames_display)
        summary_layout.addLayout(frame_layout)
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("separator")
        summary_layout.addWidget(separator)
        feet_layout = QVBoxLayout()
        feet_title = QLabel("총 FEET:")
        feet_title.setAlignment(Qt.AlignCenter)
        feet_layout.addWidget(feet_title)
        self.episode_feet_display = QLineEdit()
        self.episode_feet_display.setAlignment(Qt.AlignCenter)
        self.episode_feet_display.setReadOnly(True)
        self.episode_feet_display.setMinimumWidth(80)
        self.episode_feet_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        feet_layout.addWidget(self.episode_feet_display)
        summary_layout.addLayout(feet_layout)
        self.layout.addLayout(summary_layout)

class CalculationWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("선택 씬 계산", parent, "CalculationCard")
        calc_layout = QHBoxLayout()
        calc_layout.setSpacing(8)
        self.calc_button = QPushButton("선택 씬 계산")
        self.calc_button.setCursor(Qt.PointingHandCursor)
        self.calc_button.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        calc_layout.addWidget(self.calc_button)
        calc_layout.addSpacing(10)
        feet_label = QLabel("선택된 씬 총 FEET:")
        feet_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        calc_layout.addWidget(feet_label)
        self.total_feet_display = QLineEdit()
        self.total_feet_display.setAlignment(Qt.AlignCenter)
        self.total_feet_display.setReadOnly(True)
        self.total_feet_display.setMinimumWidth(100)
        self.total_feet_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        calc_layout.addWidget(self.total_feet_display)
        calc_layout.addStretch()
        self.layout.addLayout(calc_layout)

class SceneListWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("씬 목록", parent, "SceneListCard")
        self.scene_tree = ChartTreeWidget()
        self.scene_tree.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.layout.addWidget(self.scene_tree)
        button_layout = QHBoxLayout()
        self.export_button = QPushButton("엑셀로 내보내기")
        self.export_button.setCursor(Qt.PointingHandCursor)
        self.export_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        button_layout.addWidget(self.export_button)
        button_layout.addStretch()
        selection_label = QLabel("Tip: 여러 씬을 선택하려면 Ctrl 또는 Shift 키를 사용하세요.")
        selection_label.setAlignment(Qt.AlignRight)
        selection_label.setStyleSheet("font-style: italic; font-size: 10px;")
        button_layout.addWidget(selection_label)
        self.layout.addLayout(button_layout)

# --- SingleJobStatusWidget ---
# SingleJobStatusWidget 클래스 수정
class SingleJobStatusWidget(QFrame):
    stop_requested = pyqtSignal(str)
    sync_interval_changed = pyqtSignal(str, int)  # 동기화 간격 변경 신호
    backup_mode_changed = pyqtSignal(str, str)  # 백업 모드 변경 신호 (job_id, mode)

    def __init__(self, job_id, source_path, dest_path, parent=None, sync_interval=0, backup_mode="mirror"):
        super().__init__(parent)
        self.job_id = job_id
        self.source_path_display = os.path.basename(source_path)
        self.dest_path_display = dest_path
        self.current_sync_interval = sync_interval
        self.current_backup_mode = backup_mode

        self.setFrameShape(QFrame.StyledPanel)
        self.setObjectName(f"JobCard_{job_id}")
        theme = "dark" if QApplication.instance().palette().window().color().name() == StyleManager.DARK_THEME["BG"] else "light"
        colors = StyleManager.DARK_THEME if theme == "dark" else StyleManager.LIGHT_THEME
        self.setStyleSheet(f"QFrame#{self.objectName()} {{ background: {colors['LIGHT_BG']}; border-radius: 4px; border: 1px solid {colors['DARK_ACCENT']}; }}")

        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(8, 8, 8, 8)
        self.main_layout.setSpacing(6)

        top_layout = QHBoxLayout()
        top_layout.setSpacing(6)

        path_label = QLabel(f"<b>{self.source_path_display}</b> → ...{os.sep}{os.path.basename(self.dest_path_display)}")
        path_label.setToolTip(f"Source: {source_path}\nDestination: {dest_path}")
        path_label.setWordWrap(True)
        top_layout.addWidget(path_label, 1)

        # 설정 레이아웃 (동기화 간격 + 백업 모드)
        settings_layout = QHBoxLayout()
        settings_layout.setSpacing(8)
        
        # 동기화 간격 설정
        sync_layout = QHBoxLayout()
        sync_layout.setSpacing(4)
        sync_label = QLabel("동기화:")
        sync_label.setToolTip("주기적 전체 동기화 간격")
        sync_layout.addWidget(sync_label)
        
        self.sync_interval_combo = QComboBox()
        self.sync_interval_combo.setToolTip("주기적 전체 동기화 간격을 설정합니다")
        self.sync_interval_combo.addItem("무설정", 0)  # 추가: "무설정" 옵션
        self.sync_interval_combo.addItem("30초", 30)
        self.sync_interval_combo.addItem("1분", 60)
        self.sync_interval_combo.addItem("2분", 120)
        self.sync_interval_combo.addItem("5분", 300)
        self.sync_interval_combo.addItem("10분", 600)
        self.sync_interval_combo.addItem("30분", 1800)
        self.sync_interval_combo.addItem("1시간", 3600)
        
        # 초기 간격 설정
        index = self.sync_interval_combo.findData(sync_interval)
        if index >= 0:
            self.sync_interval_combo.setCurrentIndex(index)
        
        self.sync_interval_combo.activated.connect(self.on_sync_interval_changed)
        sync_layout.addWidget(self.sync_interval_combo)
        settings_layout.addLayout(sync_layout)
        
        # 백업 모드 설정 추가
        mode_layout = QHBoxLayout()
        mode_layout.setSpacing(4)
        mode_label = QLabel("백업 모드:")
        mode_label.setToolTip("백업 방식 선택")
        mode_layout.addWidget(mode_label)
        
        self.backup_mode_combo = QComboBox()
        self.backup_mode_combo.setToolTip("백업 방식을 선택합니다")
        self.backup_mode_combo.addItem("미러링", "mirror")
        self.backup_mode_combo.addItem("인크리멘탈", "incremental")
        
        # 초기 모드 설정
        index = self.backup_mode_combo.findData(backup_mode)
        if index >= 0:
            self.backup_mode_combo.setCurrentIndex(index)
            
        self.backup_mode_combo.activated.connect(self.on_backup_mode_changed)
        mode_layout.addWidget(self.backup_mode_combo)
        settings_layout.addLayout(mode_layout)
        
        top_layout.addLayout(settings_layout)

        self.status_label = QLabel("대기 중...")
        self.status_label.setObjectName("jobStatusLabel")
        self.status_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        top_layout.addWidget(self.status_label)

        self.log_toggle_button = QToolButton()
        self.log_toggle_button.setCheckable(True)
        self.log_toggle_button.setChecked(True)
        self.log_toggle_button.setIcon(QApplication.style().standardIcon(QStyle.SP_ArrowDown))
        self.log_toggle_button.setToolTip("로그 보기/숨기기")
        self.log_toggle_button.clicked.connect(self.toggle_log_visibility)
        top_layout.addWidget(self.log_toggle_button)

        self.stop_button = QPushButton("중지")
        self.stop_button.setObjectName("stopButton")
        self.stop_button.setCursor(Qt.PointingHandCursor)
        self.stop_button.clicked.connect(self.request_stop)
        top_layout.addWidget(self.stop_button)

        self.main_layout.addLayout(top_layout)

        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.main_layout.addWidget(self.progress_bar)

        # 로그 검색 기능 추가 - 컨테이너 위젯 사용
        self.log_search_container = QWidget()
        self.log_search_layout = QHBoxLayout(self.log_search_container)
        self.log_search_layout.setContentsMargins(0, 0, 0, 0)
        self.log_search_field = QLineEdit()
        self.log_search_field.setPlaceholderText("로그 검색어 입력...")
        self.log_search_field.setClearButtonEnabled(True)
        self.log_search_field.returnPressed.connect(self.search_log)

        self.log_search_layout.addWidget(self.log_search_field)

        self.search_next_button = QPushButton("다음")
        self.search_next_button.setToolTip("다음 검색 결과")
        self.search_next_button.clicked.connect(self.search_next)
        self.search_next_button.setFixedWidth(60)
        self.log_search_layout.addWidget(self.search_next_button)

        self.search_prev_button = QPushButton("이전")
        self.search_prev_button.setToolTip("이전 검색 결과")
        self.search_prev_button.clicked.connect(self.search_prev)
        self.search_prev_button.setFixedWidth(60)
        self.log_search_layout.addWidget(self.search_prev_button)

        self.log_highlight_case = QCheckBox("대소문자 구분")
        self.log_highlight_case.setChecked(False)
        self.log_highlight_case.stateChanged.connect(self.search_log)
        self.log_search_layout.addWidget(self.log_highlight_case)

        self.main_layout.addWidget(self.log_search_container)

        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setMinimumHeight(50)
        self.log_display.setMaximumHeight(150)
        self.log_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.main_layout.addWidget(self.log_display)

        # 검색 결과 카운터 레이블 추가 - 컨테이너 위젯 사용
        self.search_status_container = QWidget()
        self.search_status_layout = QHBoxLayout(self.search_status_container)
        self.search_status_layout.setContentsMargins(0, 0, 0, 0)
        self.search_result_label = QLabel("")
        self.search_status_layout.addWidget(self.search_result_label)
        self.search_status_layout.addStretch()

        # 로그 저장 버튼
        self.save_log_button = QPushButton("로그 저장")
        self.save_log_button.setToolTip("현재 로그를 파일로 저장")
        self.save_log_button.clicked.connect(self.save_log_to_file)
        self.save_log_button.setFixedWidth(80)
        self.search_status_layout.addWidget(self.save_log_button)

        self.main_layout.addWidget(self.search_status_container)
        
        self.main_layout.addLayout(self.search_status_layout)

        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        # 검색 관련 변수
        self.current_search_text = ""
        self.search_results = []
        self.current_result_index = -1
        
        # 로그 저장 관련 변수
        self.auto_scroll_enabled = True

    # 초기화 시
        self.log_search_container = QWidget()
        self.log_search_layout = QHBoxLayout(self.log_search_container)
        # ... 검색 위젯들 추가 ...
        self.main_layout.addWidget(self.log_search_container)
        

    def on_sync_interval_changed(self):
        try:
            new_interval = self.sync_interval_combo.currentData()
            if new_interval is None:
                print(f"Error: Failed to get interval data")
                return
                
            if new_interval != self.current_sync_interval:
                old_interval = self.current_sync_interval
                self.current_sync_interval = new_interval
                
                self.append_log(f"주기적 동기화 간격이 {self.format_interval(old_interval)}에서 {self.format_interval(new_interval)}으로 변경됩니다.")
                
                QApplication.processEvents()
                
                self.sync_interval_changed.emit(self.job_id, new_interval)
        except Exception as e:
            print(f"Error in on_sync_interval_changed: {e}")
            self.append_log(f"동기화 간격 변경 중 오류 발생: {str(e)}")
            
    def on_backup_mode_changed(self):
        try:
            new_mode = self.backup_mode_combo.currentData()
            if new_mode is None:
                print(f"Error: Failed to get backup mode data")
                return
                
            if new_mode != self.current_backup_mode:
                old_mode = self.current_backup_mode
                self.current_backup_mode = new_mode
                
                old_mode_display = "미러링" if old_mode == "mirror" else "인크리멘탈"
                new_mode_display = "미러링" if new_mode == "mirror" else "인크리멘탈"
                
                self.append_log(f"백업 모드가 {old_mode_display}에서 {new_mode_display}로 변경됩니다.")
                
                QApplication.processEvents()
                
                self.backup_mode_changed.emit(self.job_id, new_mode)
        except Exception as e:
            print(f"Error in on_backup_mode_changed: {e}")
            self.append_log(f"백업 모드 변경 중 오류 발생: {str(e)}")

    def format_interval(self, seconds):
        if seconds <= 0:
            return "무설정"
        elif seconds < 60:
            return f"{seconds}초"
        elif seconds < 3600:
            return f"{seconds // 60}분"
        else:
            return f"{seconds // 3600}시간"

    def toggle_log_visibility(self):
        is_visible = self.log_toggle_button.isChecked()
        self.log_display.setVisible(is_visible)
        
        # 컨테이너 위젯을 통해 한 번에 가시성 제어
        self.log_search_container.setVisible(is_visible)
        self.search_status_container.setVisible(is_visible)
        
        icon = QStyle.SP_ArrowDown if is_visible else QStyle.SP_ArrowRight
        self.log_toggle_button.setIcon(QApplication.style().standardIcon(icon))

    def request_stop(self):
        self.stop_requested.emit(self.job_id)
        self.set_status("중지 요청 중...")
        self.stop_button.setEnabled(False)

    @staticmethod
    def format_log_message(message):
        if not message or not message.strip():
            return None
        current_time = time.strftime("%H:%M:%S", time.localtime())
        lower_msg = message.lower()
        color = None
        theme = "dark" if QApplication.instance().palette().window().color().name() == StyleManager.DARK_THEME["BG"] else "light"
        colors = StyleManager.DARK_THEME if theme == "dark" else StyleManager.LIGHT_THEME

        if "오류" in lower_msg or "실패" in lower_msg or "error" in lower_msg or "fail" in lower_msg:
            color = colors["ERROR_RED"]

        if color:
            return f'<span style="color:{color};">[{current_time}] {message}</span>'
        else:
            return f"[{current_time}] {message}"

    @pyqtSlot(str)
    def append_log(self, message):
        formatted_message = self.format_log_message(message)
        if formatted_message:
            # 로그 추가
            self.log_display.append(formatted_message)
            
            # 로그 라인 수 제한 (최대 500줄)
            self.limit_log_lines(100)
            
            if self.auto_scroll_enabled:
                self.log_display.ensureCursorVisible()
                
            # 검색어가 입력되어 있는 경우 새로운 로그 메시지에서도 검색 결과 하이라이트
            if self.current_search_text:
                self.search_log(update_cursor=False)

    def limit_log_lines(self, max_lines):
        """로그 라인 수를 제한하여 성능 최적화"""
        doc = self.log_display.document()
        line_count = doc.blockCount()
        
        if line_count > max_lines:
            # 삭제할 라인 수 계산 (10% 정도 삭제하여 자주 삭제 작업이 발생하지 않도록)
            lines_to_remove = min(int(max_lines * 0.1), line_count - max_lines)
            lines_to_remove = max(lines_to_remove, 1)  # 최소 1줄은 삭제
            
            # 현재 커서 위치 저장
            old_cursor = self.log_display.textCursor()
            cursor = QTextCursor(doc)
            
            # 문서의 처음으로 이동
            cursor.movePosition(QTextCursor.Start)
            
            # 삭제할 라인 수만큼 선택
            for _ in range(lines_to_remove):
                cursor.movePosition(QTextCursor.Down, QTextCursor.KeepAnchor)
            
            # 선택된 텍스트 삭제
            cursor.removeSelectedText()
            
            # 커서 위치 복원 (필요한 경우)
            if not old_cursor.isNull():
                self.log_display.setTextCursor(old_cursor)
                
            # 검색 결과가 있다면 다시 검색 실행 (삭제된 라인으로 인해 결과가 변경됨)
            if self.current_search_text:
                self.search_log(update_cursor=False)

    @pyqtSlot(str, int)
    def set_status(self, status_text, progress=-1):
        self.status_label.setText(status_text)
        if progress >= 0:
            self.progress_bar.setValue(progress)
        if "완료" in status_text or "오류" in status_text or "중지" in status_text or "중지 요청 중" in status_text:
            self.stop_button.setEnabled(False)
        else:
            self.stop_button.setEnabled(True)

    def set_progress(self, value):
        if 0 <= value <= 100:
            self.progress_bar.setValue(value)
            
    # 로그 검색 관련 기능
    def search_log(self, update_cursor=True):
        """로그 내용에서 검색어 검색"""
        search_text = self.log_search_field.text()
        if not search_text:
            # 검색어 없을 경우 하이라이트 제거
            self.clear_search_highlights()
            self.search_result_label.setText("")
            self.current_search_text = ""
            self.search_results = []
            self.current_result_index = -1
            return
            
        # 검색 수행
        self.current_search_text = search_text
        case_sensitive = self.log_highlight_case.isChecked()
        
        # 현재 커서 위치 저장
        old_cursor = self.log_display.textCursor()
        
        # 모든 검색 결과 찾기
        cursor = QTextCursor(self.log_display.document())
        search_flags = QTextDocument.FindFlags()
        if case_sensitive:
            search_flags |= QTextDocument.FindCaseSensitively
            
        self.search_results = []
        while True:
            cursor = self.log_display.document().find(search_text, cursor.position(), search_flags)
            if cursor.isNull():
                break
            self.search_results.append(QTextCursor(cursor))
            
        # 검색 결과 하이라이트
        if self.search_results:
            # 이전 하이라이트 제거
            self.clear_search_highlights()
            
            # 검색 결과에 하이라이트 적용
            highlight_format = QTextCharFormat()
            highlight_color = QApplication.palette().color(QPalette.Highlight)
            highlight_format.setBackground(highlight_color)
            highlight_format.setForeground(QApplication.palette().color(QPalette.HighlightedText))
            
            for result in self.search_results:
                result.mergeCharFormat(highlight_format)
            
            # 첫 번째 검색 결과 선택
            if update_cursor:
                self.current_result_index = 0
                self.log_display.setTextCursor(self.search_results[0])
                self.log_display.ensureCursorVisible()
            else:
                # 커서 위치 복원
                self.log_display.setTextCursor(old_cursor)
                
            self.update_search_status()
        else:
            self.clear_search_highlights()
            self.current_result_index = -1
            self.search_result_label.setText(f"검색 결과 없음: '{search_text}'")
            
            # 커서 위치 복원
            if not update_cursor:
                self.log_display.setTextCursor(old_cursor)
    
    def search_next(self):
        """다음 검색 결과로 이동"""
        if not self.search_results:
            self.search_log()
            return
            
        if self.current_result_index < len(self.search_results) - 1:
            self.current_result_index += 1
        else:
            self.current_result_index = 0  # 마지막 결과에서 첫 번째로 순환
            
        self.log_display.setTextCursor(self.search_results[self.current_result_index])
        self.log_display.ensureCursorVisible()
        self.update_search_status()
    
    def search_prev(self):
        """이전 검색 결과로 이동"""
        if not self.search_results:
            self.search_log()
            return
            
        if self.current_result_index > 0:
            self.current_result_index -= 1
        else:
            self.current_result_index = len(self.search_results) - 1  # 첫 번째 결과에서 마지막으로 순환
            
        self.log_display.setTextCursor(self.search_results[self.current_result_index])
        self.log_display.ensureCursorVisible()
        self.update_search_status()
    
    def clear_search_highlights(self):
        """검색 하이라이트 제거"""
        # 이전 하이라이트 제거
        cursor = QTextCursor(self.log_display.document())
        cursor.select(QTextCursor.Document)
        
        # 기본 서식으로 복원
        default_format = QTextCharFormat()
        cursor.setCharFormat(default_format)
    
    def update_search_status(self):
        """검색 상태 업데이트"""
        if self.search_results:
            self.search_result_label.setText(f"검색 결과: {self.current_result_index + 1}/{len(self.search_results)}")
        else:
            self.search_result_label.setText("")
            
    def save_log_to_file(self):
        """현재 로그를 파일로 저장"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "로그 저장", 
                f"log_{self.source_path_display}_{time.strftime('%Y%m%d_%H%M%S')}.txt",
                "텍스트 파일 (*.txt)"
            )
            
            if not file_path:
                return  # 사용자가 취소함
                
            # HTML을 일반 텍스트로 변환하여 저장
            log_content = self.log_display.toPlainText()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(log_content)
                
            self.append_log(f"로그가 파일로 저장되었습니다: {file_path}")
        except Exception as e:
            self.append_log(f"로그 저장 중 오류 발생: {str(e)}")

# --- ActiveJobsWidget ---
class ActiveJobsWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("활성 백업 작업", parent, "ActiveJobsCard")
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.content_widget = QWidget()
        self.jobs_layout = QVBoxLayout(self.content_widget)
        self.jobs_layout.setContentsMargins(0, 0, 0, 0)
        self.jobs_layout.setSpacing(8)
        self.jobs_layout.addStretch(1)
        self.scroll_area.setWidget(self.content_widget)
        self.layout.addWidget(self.scroll_area)
        self.job_widgets = {}

    def add_job_widget(self, job_id, widget):
        if job_id not in self.job_widgets:
            self.jobs_layout.insertWidget(self.jobs_layout.count() - 1, widget)
            self.job_widgets[job_id] = widget
            self.update_visibility()

    def remove_job_widget(self, job_id):
        widget = self.job_widgets.pop(job_id, None)
        if widget:
            self.jobs_layout.removeWidget(widget)
            widget.deleteLater()
            self.update_visibility()

    def update_visibility(self):
        self.setVisible(len(self.job_widgets) > 0)

    def get_job_widget(self, job_id) -> SingleJobStatusWidget | None:
        return self.job_widgets.get(job_id)
# --- BackupInitializer ---
class BackupInitializer(QThread):
    progress_signal = pyqtSignal(str, str, int)
    initialization_finished = pyqtSignal(str, bool)

    def __init__(self, job_id, source_path, dest_path, max_workers=4):
        super().__init__()
        self.job_id = job_id
        self.source_path = source_path
        self.dest_path = dest_path
        self.max_workers = max_workers
        self.running = True
        self.success = True

    def emit_progress(self, message, progress):
        self.progress_signal.emit(self.job_id, message, progress)

    def get_subdirs(self, path):
        try:
            return [os.path.join(path, d) for d in os.listdir(path)
                    if os.path.isdir(os.path.join(path, d))]
        except Exception as e:
            self.emit_progress(f"하위 디렉토리 목록 읽기 오류: {e}", -1)
            return []

    def run(self):
        try:
            self.emit_progress("rsync 버전 확인 중...", 0)
            try:
                result = subprocess.run(['rsync', '--version'], check=True, capture_output=True, text=True)
                version_line = result.stdout.split('\n')[0]
                self.emit_progress(f"rsync 버전: {version_line}", -1)
            except (subprocess.SubprocessError, FileNotFoundError):
                self.emit_progress("오류: rsync가 설치되어 있지 않습니다.", -1)
                self.success = False
                self.initialization_finished.emit(self.job_id, self.success)
                return

            try:
                os.makedirs(self.dest_path, exist_ok=True)
                self.emit_progress(f"대상 경로 생성/확인됨: {self.dest_path}", -1)
            except Exception as e:
                self.emit_progress(f"오류: 대상 경로 생성 실패 - {e}", -1)
                self.success = False
                self.initialization_finished.emit(self.job_id, self.success)
                return

            # --delete 옵션은 실시간 워커의 주기적 동기화에서 사용
            rsync_cmd = ['rsync', '-avhi', '--block-size=4K', '--progress', f"{self.source_path}{os.sep}", self.dest_path]
            self.emit_progress(f"초기 동기화 시작 - 소스: {self.source_path}", 0)
            self.emit_progress(f"초기 동기화 대상: {self.dest_path}", -1)
            self.emit_progress(f"실행 명령: {' '.join(rsync_cmd)}", -1)

            try:
                my_env = os.environ.copy()
                my_env["LANG"] = "C.UTF-8"
                my_env["LC_ALL"] = "C.UTF-8"

                self.emit_progress("rsync 프로세스 시작...", -1)
                process = subprocess.Popen(
                    rsync_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1,
                    universal_newlines=True,
                    env=my_env
                )

                total_files = 0
                processed_files = 0
                error_output = []
                last_progress_update_time = time.time()
                total_progress_pattern = 0
                last_file_name = ""

                self.emit_progress("파일 스캔 및 동기화 중...", 5)
                for line in iter(process.stdout.readline, ''):
                    if not self.running:
                        process.terminate()
                        process.wait()
                        self.emit_progress("동기화 중단됨", -1)
                        self.success = False
                        break

                    line = line.strip()
                    if line:
                        # 파일 개수 관련 로그는 완전히 생략
                        if "files..." in line:
                            continue  # 이 로그는 표시하지 않음
                            
                        # 파일 전송 시작
                        elif line.startswith('>f') or line.startswith('cd.'):
                            processed_files += 1
                            try:
                                # 파일명 추출
                                last_file_name = line.split(' ', 1)[1] if ' ' in line else "파일"
                                # 파일 이름만 로그에 표시
                                self.emit_progress(f"파일 처리 중: {last_file_name}", -1)
                                
                                # 주기적 진행률 업데이트 (파일 개수 기반)
                                if processed_files % 10 == 0:  # 10개 파일마다 업데이트
                                    # 시작 시에는 10% 기준, 그 후로는 파일 처리량에 따라 점진적 증가
                                    total_progress_pattern += 2
                                    progress_val = min(95, 10 + total_progress_pattern)
                                    self.emit_progress(f"동기화 진행 중... ({progress_val}%)", progress_val)
                            except Exception as e:
                                print(f"파일 이름 파싱 오류: {e}")
                                
                        # rsync 진행률 파싱 시도
                        elif '%' in line and ('bytes/sec' in line or 'xfr#' in line):
                            try:
                                parts = line.split()
                                self.emit_progress(f"전송 상태: {line}", -1)  # 전체 전송 상태 로그
                                for part in parts:
                                    if '%' in part and part.replace('%', '').replace('.', '', 1).isdigit():
                                        try:
                                            progress_val = int(float(part.replace('%', '')))
                                            # 너무 잦은 업데이트 방지 (0.5초마다)
                                            current_time = time.time()
                                            if current_time - last_progress_update_time > 0.5:
                                                # 최소 10%부터 시작, 최대 95%까지만 (완료는 100%로 따로 표시)
                                                progress_val = max(10, min(95, progress_val))
                                                bytes_info = ' '.join([p for p in parts if 'bytes/sec' in p]) if any('bytes/sec' in p for p in parts) else ""
                                                self.emit_progress(f"동기화 진행 중... {bytes_info} ({progress_val}%)", progress_val)
                                                last_progress_update_time = current_time
                                            break
                                        except ValueError:
                                            pass
                            except Exception as e:
                                print(f"진행률 파싱 오류: {e}")
                                
                        # 개별 파일 진행률 파싱
                        elif '%' in line:
                            try:
                                # 더 상세한 로그 출력
                                self.emit_progress(f"파일 진행 상태: {line}", -1)
                                
                                # 더 일반적인 진행률 포맷 처리
                                percent_part = [p for p in line.split() if '%' in p]
                                if percent_part:
                                    try:
                                        progress_str = percent_part[0].replace('%', '')
                                        if progress_str.replace('.', '', 1).isdigit():
                                            progress_val = int(float(progress_str))
                                            # 최소 1초마다 업데이트
                                            current_time = time.time()
                                            if current_time - last_progress_update_time > 1.0:
                                                # 범위 조정: 시작 10% ~ 최대 95%
                                                display_progress = max(10, min(95, progress_val))
                                                self.emit_progress(f"파일 동기화 중: {last_file_name} ({display_progress}%)", display_progress)
                                                last_progress_update_time = current_time
                                    except Exception as e:
                                        self.emit_progress(f"파일 진행률 변환 오류: {e}", -1)
                            except Exception as e:
                                print(f"파일 진행률 파싱 오류: {e}")
                                
                        elif 'total size is' in line:
                            self.emit_progress(f"동기화 요약: {line}", 98)  # 최종 단계지만 아직 완료는 아님
                        
                        # 다른 중요한 로그만 표시
                        elif any(keyword in line for keyword in ["error", "오류", "warning", "skipping", "speedup"]):
                            self.emit_progress(f"rsync: {line}", -1)

                if not self.running:
                    self.initialization_finished.emit(self.job_id, self.success)
                    return

                stderr_output = process.stderr.read()
                if stderr_output:
                    error_lines = stderr_output.strip().split('\n')
                    for err_line in error_lines:
                        if err_line.strip():
                            error_output.append(err_line.strip())
                            self.emit_progress(f"rsync 오류: {err_line.strip()}", -1)

                process.wait()

                if not self.running:
                    self.emit_progress("동기화 중단 완료됨", -1)
                    self.success = False
                elif process.returncode == 0:
                    self.emit_progress(f"초기 동기화 완료 ({processed_files}개 항목 처리 추정)", 100)
                    self.success = True
                else:
                    error_msg = f"rsync 종료 코드: {process.returncode}"
                    if error_output: error_msg += "\n" + "\n".join(error_output)
                    self.emit_progress(f"초기 동기화 실패: {error_msg}", -1)
                    self.success = False

            except Exception as e:
                self.emit_progress(f"rsync 실행 중 예외 발생: {e}", -1)
                import traceback
                self.emit_progress(f"상세 오류: {traceback.format_exc()}", -1)
                self.success = False

        except Exception as e:
            self.emit_progress(f"초기 동기화 중 예상치 못한 오류: {str(e)}", -1)
            import traceback
            self.emit_progress(f"상세 오류 추적: {traceback.format_exc()}", -1)
            self.success = False
        finally:
            self.initialization_finished.emit(self.job_id, self.success)

    def stop(self):
        self.running = False
        self.emit_progress("초기 동기화 중단 요청됨...", -1)

# --- BackupEventHandler ---
class BackupEventHandler(FileSystemEventHandler):
    def __init__(self, backup_queue, source_path, dest_path, is_source=True):
        super().__init__()
        self.backup_queue = backup_queue
        self.source_path = source_path
        self.dest_path = dest_path
        self.is_source = is_source
        self.pending_tasks = set()
        self.debounce_timer = {}
        self.debounce_lock = threading.Lock()
        self.debounce_duration = 1.5  # 디바운스 시간
        # 추가: 이벤트 레이트 제한
        self.event_count = 0
        self.event_limit = 100  # 최대 이벤트 수 (조정 가능)
        self.event_window = 10   # 시간 윈도우(초)
        self.last_reset = time.time()
        self.event_lock = threading.Lock()

    # 모든 이벤트 핸들러에서 호출할 메서드 추가
    def check_event_limit(self):
        with self.event_lock:
            current_time = time.time()
            # 시간 윈도우가 지났으면 카운터 리셋
            if current_time - self.last_reset > self.event_window:
                self.event_count = 0
                self.last_reset = current_time
                
            self.event_count += 1
            if self.event_count > self.event_limit:
                # 이벤트 한도 초과 - 이벤트 드롭
                return False
                
            return True
        
    def on_created(self, event):
        if not self.check_event_limit():
            return  # 이벤트 처리 중단
        if event.is_directory:
            self.queue_backup(event.src_path, "created", True)
        else:
            self._debounce_event(event.src_path, "created", False)

    def on_modified(self, event):
        if not event.is_directory:
            self._debounce_event(event.src_path, "modified", False)
            # 인크리멘탈 모드에서도 수정 시 오버라이트를 위해 "modified" 이벤트 유지
            # 디바운스 처리로 중복 호출 방지

    def on_deleted(self, event):
        self.queue_backup(event.src_path, "deleted", event.is_directory)

    def on_moved(self, event):
        if self.is_source:
            # 소스에서 이름 변경 시 기존 목적지 파일 삭제 후 새 이름으로 복사
            self._handle_renamed_file(event.src_path, event.dest_path, event.is_directory)
            # _handle_renamed_file 메서드가 이미 delete_target와 created를 큐에 추가하므로 추가 수정 불필요
        # 목적지에서의 moved 이벤트는 무시 (인크리멘탈 모드에서는 소스 주도)

    def _handle_renamed_file(self, old_path, new_path, is_directory):
        try:
            normalized_watch_path = os.path.normpath(self.source_path)
            normalized_old_path = os.path.normpath(old_path)
            normalized_new_path = os.path.normpath(new_path)

            if not normalized_new_path.startswith(normalized_watch_path): return

            try:
                old_rel_path = os.path.relpath(normalized_old_path, normalized_watch_path)
                new_rel_path = os.path.relpath(normalized_new_path, normalized_watch_path)
            except ValueError: return

            old_dest_path = os.path.join(self.dest_path, old_rel_path)
            new_dest_path = os.path.join(self.dest_path, new_rel_path)

            self.backup_queue.put(("delete_target", old_dest_path, "deleted", is_directory))
            self.backup_queue.put((normalized_new_path, new_dest_path, "created", is_directory))
            print(f"Debug: Queued rename as delete/create: {old_rel_path} -> {new_rel_path}")
        except Exception as e:
            print(f"이름 변경 이벤트 처리 중 오류: {e}")

    def _debounce_event(self, src_path, event_type, is_directory):
        with self.debounce_lock:
            key = (src_path, event_type)
            if key in self.debounce_timer and self.debounce_timer[key].is_alive():
                self.debounce_timer[key].cancel()
            timer = threading.Timer(self.debounce_duration, self.queue_backup, args=[src_path, event_type, is_directory])
            timer.daemon = True
            self.debounce_timer[key] = timer
            timer.start()

    def queue_backup(self, event_path, event_type, is_directory=False):
        try:
            norm_event_path = os.path.normpath(event_path)
            handler_type = "(Source)" if self.is_source else "(Dest)"
            # print(f"--- Event Handler {handler_type} ---")
            # print(f"Event: {event_type}, Path: {norm_event_path}, IsDir: {is_directory}")

            try: real_event_path = os.path.realpath(norm_event_path)
            except Exception: real_event_path = norm_event_path

            watch_path_base = self.source_path if self.is_source else self.dest_path
            norm_watch_path = os.path.normpath(watch_path_base)
            try: real_watch_path = os.path.realpath(norm_watch_path)
            except Exception: real_watch_path = norm_watch_path

            # print(f" -> Watch Path (real): {real_watch_path}")
            # print(f" -> Event Path (real): {real_event_path}")

            is_within_path = False
            if real_event_path.startswith(real_watch_path):
                if real_event_path == real_watch_path:
                     # print(f" -> Event is on the watch root itself: '{real_event_path}'")
                     if not self.is_source and event_type == "deleted" and is_directory:
                          source_equivalent = self.source_path
                          if os.path.exists(source_equivalent):
                              task_key = (source_equivalent, "recreate_root")
                              if task_key not in self.pending_tasks:
                                   self.backup_queue.put((source_equivalent, real_watch_path, "recreate_dir", True))
                                   self.pending_tasks.add(task_key)
                                   # print(f" -> Destination root deleted, queued RECREATE_DIR task for source root.")
                              # else: print(" -> Recreate root task already pending. Ignoring.")
                          # else: print(" -> Source root doesn't exist either. Ignoring destination root deletion.")
                     return
                else:
                    is_within_path = True
            # else: # Alternative checks removed for simplicity

            if not is_within_path:
                # print(f" -> Event determined to be outside watch path. Ignoring.")
                return

            rel_path = None
            try: rel_path = os.path.relpath(real_event_path, real_watch_path)
            except ValueError:
                 try: rel_path = os.path.relpath(norm_event_path, norm_watch_path)
                 except ValueError:
                      print(f"!!! Error calculating relative path for '{norm_event_path}' from '{norm_watch_path}'. Ignoring.")
                      return
            if not rel_path or rel_path == '.':
                 # print(f" -> Calculated relative path is empty or '.'. Ignoring event.")
                 return

            # print(f" -> Relative Path: {rel_path}")

            if self.is_source:
                dest_path_full = os.path.join(self.dest_path, rel_path)
                # print(f" -> Target Dest Path: {dest_path_full}")
                if event_type in ["created", "modified"]:
                    if not os.path.lexists(real_event_path): return
                    task_key = (real_event_path, event_type)
                    if task_key not in self.pending_tasks:
                        self.backup_queue.put((real_event_path, dest_path_full, event_type, is_directory))
                        self.pending_tasks.add(task_key)
                        # print(f" -> Source Event Queued: {event_type} - {rel_path}")
                    # else: print(f" -> Task already pending: {task_key}. Ignoring.")
                elif event_type == "deleted":
                    task_key = (dest_path_full, event_type)
                    if task_key not in self.pending_tasks:
                        self.backup_queue.put(("delete_target", dest_path_full, event_type, is_directory))
                        self.pending_tasks.add(task_key)
                        # print(f" -> Source Delete Event Queued for Target: {event_type} - {rel_path}")
                    # else: print(f" -> Delete task already pending for target: {task_key}. Ignoring.")
            else: # Destination handler
                source_file_full = os.path.join(self.source_path, rel_path)
                # print(f" -> Corresponding Source Path: {source_file_full}")
                if event_type == "deleted":
                    source_exists = os.path.lexists(source_file_full)
                    # print(f" -> Checking if source exists: {source_exists}")
                    if source_exists:
                        real_source_path = os.path.realpath(source_file_full)
                        task_key = (real_source_path, "restore")
                        # print(f" -> Restore task key: {task_key}")
                        # print(f" -> Is restore task pending? {task_key in self.pending_tasks}")
                        if task_key not in self.pending_tasks:
                            restore_task = (real_source_path, real_event_path, "restore", is_directory)
                            self.backup_queue.put(restore_task)
                            self.pending_tasks.add(task_key)
                            # print(f" -> Target Delete Detected. Queued RESTORE task: {restore_task}")
                        # else: print(f" -> Restore task already pending for {task_key}. Ignoring.")
                    # else: print(f" -> Source file does not exist. No restore needed for {rel_path}.")

        except Exception as e:
            print(f"!!! Unhandled Exception in Event Handler {handler_type} for event {event_type} on {event_path}: {e}")
            import traceback
            traceback.print_exc()

    def task_completed(self, task_key_tuple):
        self.pending_tasks.discard(task_key_tuple)
        # print(f"Debug: Task completed, removed from pending: {task_key_tuple}")

# --- BackupWorker (with Periodic Sync) ---
class BackupWorker(QThread):
    update_signal = pyqtSignal(str, str, int)
    scene_backup_status_signal = pyqtSignal(str, str, str)
    restart_observer_signal = pyqtSignal(str, str)
    job_finished_signal = pyqtSignal(str)

    def __init__(self, job_id, backup_queue, source_handler, dest_handler, scene_tree_ref, source_path, dest_path, max_workers=4, periodic_sync_interval=120, backup_mode="mirror"):
        super().__init__()
        self.job_id = job_id
        self.running = True
        self.backup_queue = backup_queue
        self.source_handler = source_handler
        self.dest_handler = dest_handler
        self.scene_tree_ref = scene_tree_ref
        self.source_path = source_path
        self.dest_path = dest_path
        self.backed_up_scenes = set()
        self.retry_limit = 3
        self.executor = None
        self.max_workers = max_workers
        self.active_futures = set()
        self.task_lock = threading.Lock()
        self.total_queued_tasks = 0
        self.completed_tasks = 0
        self.last_progress_emit_time = 0

        # 주기적 동기화 관련 속성
        self.periodic_sync_interval = periodic_sync_interval
        self.periodic_sync_timer = None
        self.is_periodic_sync_running = False
        self.periodic_sync_lock = threading.Lock()
        self.interval_change_lock = threading.Lock()
        
        # 백업 모드 설정
        self.backup_mode = backup_mode


    def monitor_memory(self):
        process = psutil.Process()
        memory_mb = process.memory_info().rss / (1024 * 1024)
        return memory_mb  # MB 단위 반환

    def check_memory_threshold(self):
        memory_mb = self.monitor_memory()
        if memory_mb > 2500:  # 2.5GB 임계값
            self.emit_progress(f"경고: 높은 메모리 사용량 감지 ({memory_mb:.1f}MB). 메모리 정리 수행 중...", -1)
            self.perform_memory_cleanup()
            return True
        return False

    def perform_memory_cleanup(self):
        # 강제 메모리 정리
        import gc
        gc.collect()
        
        # 필요한 경우 작업 큐 제한
        if self.backup_queue.qsize() > 10000:
            old_size = self.backup_queue.qsize()
            # 큐 크기를 절반으로 줄임
            for _ in range(old_size // 2):
                try:
                    self.backup_queue.get_nowait()
                except queue.Empty:
                    break
            self.emit_progress(f"메모리 관리: 작업 큐 크기 조정 ({old_size} → {self.backup_queue.qsize()})", -1)
        
        # 검색 결과 및 로그 정리
        for job_id, widget in self.active_jobs_widget.job_widgets.items():
            if widget:
                widget.search_results = []
                widget.limit_log_lines(50)  # 로그 라인 수 대폭 제한

    # BackupWorker 클래스에 Observer 관리 코드 추가
    def manage_observers(self):
        # 감시자 상태 확인 및 재시작 필요시 조치
        if self.source_observer and not self.source_observer.is_alive():
            self.emit_progress("소스 감시자가 중지됨, 재시작 시도...", -1)
            try:
                self.source_observer.stop()
                self.source_observer.join(timeout=3)
                # 새 감시자 설정 로직
            except Exception as e:
                self.emit_progress(f"감시자 재설정 오류: {e}", -1)

    def emit_progress(self, message, progress):
        if message or progress != -1:
             self.update_signal.emit(self.job_id, message, progress)

    def update_backup_mode(self, new_mode):
        if not self.running:
            return False
            
        # 모드가 같으면 아무것도 하지 않음
        if self.backup_mode == new_mode:
            return True
            
        old_mode = self.backup_mode
        self.backup_mode = new_mode
        
        old_mode_display = "미러링" if old_mode == "mirror" else "인크리멘탈"
        new_mode_display = "미러링" if new_mode == "mirror" else "인크리멘탈"
        
        self.emit_progress(f"백업 모드가 {old_mode_display}에서 {new_mode_display}로 변경되었습니다.", -1)
        
        # 모드 변경 후 다음 주기적 동기화에서 새 모드 적용됨
        if self.periodic_sync_timer:
            self.emit_progress(f"다음 주기적 동기화({self.periodic_sync_interval}초 후)부터 {new_mode_display} 모드로 실행됩니다.", -1)
            
        return True

    def extract_scene_name(self, path):
        # Existing method unchanged
        if not path or not self.source_path: return None
        try:
            norm_path = os.path.normpath(path)
            norm_source_path = os.path.normpath(self.source_path)
            norm_dest_path = os.path.normpath(self.dest_path)

            if norm_path.startswith(norm_source_path): base_path = norm_source_path
            elif norm_path.startswith(norm_dest_path): base_path = norm_dest_path
            else: return None

            rel_path = os.path.relpath(norm_path, base_path)
            parts = rel_path.split(os.sep)
            for part in parts:
                if part.startswith('scene-') and part.split('-')[-1].isdigit():
                    return part
            return None
        except ValueError: return None
        except Exception as e:
             self.emit_progress(f"씬 이름 추출 오류: {e} (Path: {path})", -1)
             return None

    def is_scene_fully_backed_up(self, scene_name):
        # Existing method unchanged
        scene_source_path = os.path.join(self.source_path, scene_name)
        scene_dest_path = os.path.join(self.dest_path, scene_name)
        try:
            if not os.path.isdir(scene_source_path) or not os.path.isdir(scene_dest_path):
                return False
            source_items = set(os.listdir(scene_source_path))
            dest_items = set(os.listdir(scene_dest_path))
            if source_items == dest_items: return True
            else: return False
        except OSError as e:
             # self.emit_progress(f"씬 상태 확인 오류 ({scene_name}): {e}", -1) # Too verbose
             return False
        except Exception as e:
             self.emit_progress(f"씬 상태 확인 중 예외 ({scene_name}): {e}", -1)
             return False

    def _copy_file_with_retry(self, src, dst):
        self.emit_progress(f"파일 복사 시작: {os.path.basename(src)} -> {os.path.basename(dst)}", -1)
        try:
            self.emit_progress(f"대상 디렉토리 생성/확인 중: {os.path.dirname(dst)}", -1)
            os.makedirs(os.path.dirname(dst), exist_ok=True)
        except Exception as e:
            self.emit_progress(f"대상 디렉토리 생성 실패: {os.path.dirname(dst)}, 오류: {e}", -1)
            return False
            
        for attempt in range(self.retry_limit):
            if not self.running: 
                self.emit_progress(f"작업이 중단되어 파일 복사 취소됨: {os.path.basename(src)}", -1)
                return False
            try:
                self.emit_progress(f"파일 복사 시도 {attempt + 1}/{self.retry_limit}: {os.path.basename(src)}", -1)
                start_time = time.time()
                shutil.copy2(src, dst)
                duration = time.time() - start_time
                self.emit_progress(f"파일 복사 성공: {os.path.basename(src)} (소요 시간: {duration:.2f}초)", -1)
                return True
            except Exception as e:
                if attempt == 0: 
                    self.emit_progress(f"파일 복사 실패 (시도 {attempt + 1}/{self.retry_limit}): {os.path.basename(dst)}, 오류: {e}", -1)
                if attempt < self.retry_limit - 1: 
                    sleep_time = 0.5 * (attempt + 1)
                    self.emit_progress(f"재시도 대기 중: {sleep_time:.1f}초", -1)
                    time.sleep(sleep_time)
                else: 
                    self.emit_progress(f"최대 재시도 초과. 파일 복사 최종 실패: {os.path.basename(dst)}", -1)
                    return False
        return False

    def _copy_directory_with_retry(self, src, dst):
        self.emit_progress(f"디렉토리 복사 시작: {os.path.basename(src)} -> {os.path.basename(dst)}", -1)
        try:
            self.emit_progress(f"대상 상위 디렉토리 생성/확인 중: {os.path.dirname(dst)}", -1)
            os.makedirs(os.path.dirname(dst), exist_ok=True)
        except Exception as e:
            self.emit_progress(f"대상 상위 디렉토리 생성 실패: {os.path.dirname(dst)}, 오류: {e}", -1)
            return False
            
        for attempt in range(self.retry_limit):
            if not self.running: 
                self.emit_progress(f"작업이 중단되어 디렉토리 복사 취소됨: {os.path.basename(src)}", -1)
                return False
            try:
                if os.path.lexists(dst):
                    if os.path.isdir(dst): 
                        self.emit_progress(f"기존 대상 디렉토리 제거 중: {os.path.basename(dst)}", -1)
                        shutil.rmtree(dst, ignore_errors=True)
                    else: 
                        self.emit_progress(f"기존 대상 파일 제거 중: {os.path.basename(dst)}", -1)
                        os.remove(dst)
                
                self.emit_progress(f"디렉토리 복사 시도 {attempt + 1}/{self.retry_limit}: {os.path.basename(src)}", -1)
                start_time = time.time()
                shutil.copytree(src, dst, symlinks=True)
                duration = time.time() - start_time
                self.emit_progress(f"디렉토리 복사 성공: {os.path.basename(src)} (소요 시간: {duration:.2f}초)", -1)
                return True
            except Exception as e:
                if attempt == 0: 
                    self.emit_progress(f"디렉토리 복사 실패 (시도 {attempt + 1}/{self.retry_limit}): {os.path.basename(dst)}, 오류: {e}", -1)
                if attempt < self.retry_limit - 1:
                    sleep_time = 0.5 * (attempt + 1)
                    self.emit_progress(f"재시도 대기 중: {sleep_time:.1f}초", -1)
                    time.sleep(sleep_time)
                    if os.path.lexists(dst):
                        try:
                            if os.path.isdir(dst): 
                                self.emit_progress(f"재시도 전 실패한 대상 디렉토리 제거 중: {os.path.basename(dst)}", -1)
                                shutil.rmtree(dst, ignore_errors=True)
                            else: 
                                self.emit_progress(f"재시도 전 실패한 대상 파일 제거 중: {os.path.basename(dst)}", -1)
                                os.remove(dst)
                        except Exception as cleanup_err:
                            self.emit_progress(f"실패한 대상 정리 중 오류: {cleanup_err}", -1)
                else: 
                    self.emit_progress(f"최대 재시도 초과. 디렉토리 복사 최종 실패: {os.path.basename(dst)}", -1)
                    return False
        return False

    def _delete_item_with_retry(self, target_path, is_directory):
        item_type = "디렉토리" if is_directory else "파일"
        self.emit_progress(f"{item_type} 삭제 시작: {os.path.basename(target_path)}", -1)
        
        for attempt in range(self.retry_limit):
            if not self.running: 
                self.emit_progress(f"작업이 중단되어 삭제 취소됨: {os.path.basename(target_path)}", -1)
                return False
            try:
                if not os.path.lexists(target_path): 
                    self.emit_progress(f"삭제 대상이 이미 존재하지 않음: {os.path.basename(target_path)}", -1)
                    return True
                    
                self.emit_progress(f"{item_type} 삭제 시도 {attempt + 1}/{self.retry_limit}: {os.path.basename(target_path)}", -1)
                start_time = time.time()
                if is_directory: 
                    shutil.rmtree(target_path)
                else: 
                    os.remove(target_path)
                    
                duration = time.time() - start_time
                self.emit_progress(f"{item_type} 삭제 성공: {os.path.basename(target_path)} (소요 시간: {duration:.2f}초)", -1)
                return True
            except Exception as e:
                if attempt == 0: 
                    self.emit_progress(f"{item_type} 삭제 실패 (시도 {attempt + 1}/{self.retry_limit}): {os.path.basename(target_path)}, 오류: {e}", -1)
                if attempt < self.retry_limit - 1: 
                    sleep_time = 0.5 * (attempt + 1)
                    self.emit_progress(f"재시도 대기 중: {sleep_time:.1f}초", -1)
                    time.sleep(sleep_time)
                else: 
                    self.emit_progress(f"최대 재시도 초과. {item_type} 삭제 최종 실패: {os.path.basename(target_path)}", -1)
                    return False
        return False

    def _recreate_directory_tree(self, src_root, dest_root):
        # Existing method unchanged
        self.emit_progress(f"디렉토리 트리 재생성 시작: {os.path.basename(dest_root)}", -1)
        success_count = 0; fail_count = 0
        if not self.running: return False
        try: os.makedirs(dest_root, exist_ok=True)
        except Exception as e: self.emit_progress(f"대상 루트 생성 실패: {dest_root}, 오류: {e}", -1); return False

        items_to_process = []
        try: items_to_process = os.listdir(src_root)
        except Exception as e: self.emit_progress(f"소스 디렉토리 읽기 실패: {src_root}, 오류: {e}", -1); return False

        for item in items_to_process:
            if not self.running: return False
            src_item_path = os.path.join(src_root, item)
            dest_item_path = os.path.join(dest_root, item)
            try:
                if os.path.isdir(src_item_path):
                    if self._copy_directory_with_retry(src_item_path, dest_item_path): success_count += 1
                    else: fail_count += 1
                elif os.path.isfile(src_item_path):
                    if self._copy_file_with_retry(src_item_path, dest_item_path): success_count += 1
                    else: fail_count += 1
            except Exception as e: self.emit_progress(f"항목 처리 중 예외 ({item}): {e}", -1); fail_count += 1

        result_msg = f"디렉토리 트리 재생성 완료: {os.path.basename(dest_root)} (성공: {success_count}, 실패: {fail_count})"
        self.emit_progress(result_msg, -1)

        if fail_count == 0:
             self.restart_observer_signal.emit(self.job_id, dest_root)
             self.emit_progress(f"감시자 재설정 요청됨: {dest_root}", -1)
             return True
        else:
             self.emit_progress(f"오류 발생으로 감시자 재설정 건너뜀: {dest_root}", -1)
             return False

    def _process_task(self, task_data):
        try:
            src_path, dest_path, event_type, is_directory = task_data
        except ValueError:
            print(f"!!! Worker Error: Invalid task data received: {task_data}")
            return None, None, None, False, None

        success = False
        task_key_for_handler = None
        self.emit_progress(f"작업 처리 시작 - 유형: {event_type}, 소스: {os.path.basename(src_path) if isinstance(src_path, str) else src_path}, 대상: {os.path.basename(dest_path)}, 디렉토리: {is_directory}", -1)

        try:
            if not self.running:
                return src_path, dest_path, event_type, False, None

            if src_path == "delete_target":
                task_key_for_handler = (dest_path, event_type)
                # 인크리멘탈 모드에서는 삭제 작업을 건너뜀
                if self.backup_mode == "incremental":
                    self.emit_progress(f"인크리멘탈 모드: 삭제 작업 무시됨 - {os.path.basename(dest_path)}", -1)
                    success = True  # 작업을 처리한 것으로 간주 (삭제하지 않음)
                else:
                    # 미러링 모드에서는 삭제 수행
                    self.emit_progress(f"미러링 모드: 대상 삭제 시작 - {os.path.basename(dest_path)}", -1)
                    success = self._delete_item_with_retry(dest_path, is_directory)
                    if success:
                        self.emit_progress(f"대상 삭제 성공: {os.path.basename(dest_path)}", -1)
                    else:
                        self.emit_progress(f"대상 삭제 실패: {os.path.basename(dest_path)}", -1)
            elif event_type == "restore":
                task_key_for_handler = (src_path, event_type)
                self.emit_progress(f"복구 시작: {os.path.basename(src_path)} -> {os.path.basename(dest_path)}", -1)
                if is_directory:
                    success = self._copy_directory_with_retry(src_path, dest_path)
                else:
                    success = self._copy_file_with_retry(src_path, dest_path)
                status = "성공" if success else "실패"
                self.emit_progress(f"복구 {status}: {os.path.basename(dest_path)}", -1)
            elif event_type == "recreate_dir":
                task_key_for_handler = (src_path, "recreate_root")
                self.emit_progress(f"디렉토리 재생성 시작: {os.path.basename(src_path)} -> {os.path.basename(dest_path)}", -1)
                success = self._recreate_directory_tree(src_path, dest_path)
                status = "성공" if success else "실패"
                self.emit_progress(f"디렉토리 재생성 {status}: {os.path.basename(dest_path)}", -1)
            elif event_type in ["created", "modified"]:
                task_key_for_handler = (src_path, event_type)
                if not os.path.lexists(src_path):
                    self.emit_progress(f"소스가 더 이상 존재하지 않음, 작업 건너뜀: {os.path.basename(src_path)}", -1)
                    success = True  # Skip copy if source disappeared
                else:
                    operation = "생성" if event_type == "created" else "수정"
                    self.emit_progress(f"파일 {operation} 처리 시작: {os.path.basename(src_path)} -> {os.path.basename(dest_path)}", -1)
                    if is_directory:
                        success = self._copy_directory_with_retry(src_path, dest_path)
                    else:
                        success = self._copy_file_with_retry(src_path, dest_path)
                    status = "성공" if success else "실패"
                    self.emit_progress(f"파일 {operation} {status}: {os.path.basename(src_path)}", -1)
            else:
                self.emit_progress(f"알 수 없는 이벤트 유형: {event_type}, 작업 건너뜀", -1)
                success = True  # Unknown event type treated as success

        except Exception as e:
            print(f"!!! Worker Error during processing task {task_data}: {e}")
            self.emit_progress(f"작업 처리 중 예외 발생 ({os.path.basename(src_path or dest_path)}): {e}", -1)
            import traceback
            self.emit_progress(f"상세 오류: {traceback.format_exc()}", -1)
            success = False
        finally:
            self.emit_progress(f"작업 처리 완료 - 성공: {success}", -1)
            return src_path, dest_path, event_type, success, task_key_for_handler
        # 태스크 시작 전에 짧은 지연 추가
        time.sleep(0.05)  # 50ms 지연

    def _task_callback(self, future):
        # Existing method unchanged
        if not self.running: return

        try: src_path, dest_path, event_type, success, task_key_for_handler = future.result()
        except Exception as e: print(f"!!! Worker Error: Exception retrieving future result: {e}"); success = False; event_type = "unknown_error"; task_key_for_handler = None

        with self.task_lock:
            self.completed_tasks += 1
            self.active_futures.discard(future)

            current_time = time.time()
            is_last_task = self.backup_queue.empty() and not self.active_futures
            should_update_progress = (current_time - self.last_progress_emit_time > 0.5) or is_last_task

            if should_update_progress:
                remaining_tasks = self.backup_queue.qsize() + len(self.active_futures)
                estimated_total = max(1, self.completed_tasks + remaining_tasks)  # 0으로 나누기 방지
                progress = min(99, int(self.completed_tasks / estimated_total * 100))
                final_progress = 100 if is_last_task else progress
                status_msg = "모든 대기 작업 처리 완료" if is_last_task and progress >= 99 else f"실시간 처리 중... (완료: {self.completed_tasks}, 대기: {remaining_tasks})"
                # print(f" -> Progress Update: {status_msg} ({final_progress}%)")
                self.emit_progress(status_msg, final_progress)
                self.last_progress_emit_time = current_time

            if task_key_for_handler:
                 handler_notified = False
                 try:
                     task_path_or_flag = task_key_for_handler[0]
                     task_type = task_key_for_handler[1]

                     if event_type in ["created", "modified"] and self.source_handler and isinstance(task_path_or_flag, str) and task_path_or_flag.startswith(self.source_path):
                          self.source_handler.task_completed(task_key_for_handler); handler_notified = True
                     elif src_path == "delete_target" and self.dest_handler:
                          if task_path_or_flag == dest_path and task_type == 'deleted':
                              self.dest_handler.task_completed(task_key_for_handler); handler_notified = True
                     elif event_type == "restore" and self.dest_handler:
                          if isinstance(task_path_or_flag, str) and task_path_or_flag.startswith(self.source_path) and task_type == 'restore':
                              self.dest_handler.task_completed(task_key_for_handler); handler_notified = True
                     elif event_type == "recreate_dir" and self.dest_handler:
                          if isinstance(task_path_or_flag, str) and task_path_or_flag.startswith(self.source_path) and task_type == 'recreate_root':
                              self.dest_handler.task_completed(task_key_for_handler); handler_notified = True

                 except Exception as e: print(f"!!! Worker Error: Exception while notifying handler for key {task_key_for_handler}: {e}")
                 # if not handler_notified: print(f" -> Warning: No handler notified for key: {task_key_for_handler}, Event: {event_type}")

            if success and event_type != "deleted" and src_path != "delete_target":
                path_to_check = src_path if event_type != "restore" else dest_path
                if path_to_check:
                    scene_name = self.extract_scene_name(path_to_check)
                    if scene_name and scene_name not in self.backed_up_scenes:
                        if self.is_scene_fully_backed_up(scene_name):
                            self.backed_up_scenes.add(scene_name)
                            self.scene_backup_status_signal.emit(self.job_id, scene_name, "백업완료")
                            # print(f" -> Scene '{scene_name}' marked as backed up for Job {self.job_id}")

        # print(f"--- Task Callback Finished ---")
    def _safe_cancel_timer(self):
        try:
            if self.periodic_sync_timer:
                self.periodic_sync_timer.cancel()
                return True
        except Exception as e:
            print(f"Error canceling timer: {e}")
        return False
    
    # update_sync_interval 메서드 수정
    def update_sync_interval(self, new_interval):
        if not self.running:
            return False
            
        if self.periodic_sync_interval == new_interval:
            return True
        
        old_interval = self.periodic_sync_interval
        self.periodic_sync_interval = new_interval
        
        QMetaObject.invokeMethod(self, "_reschedule_sync_timer", 
                                Qt.QueuedConnection)
        
        self.emit_progress(f"주기적 동기화 간격이 {old_interval}초에서 {new_interval}초로 변경되었습니다.", -1)
        return True


    # --- Periodic Sync Methods with minimal changes ---
    @pyqtSlot()
    def _reschedule_sync_timer(self):
        try:
            self._safe_cancel_timer()
            
            if self.running:
                self._schedule_periodic_sync()
                self.emit_progress(f"동기화 일정이 재설정되었습니다. 다음 동기화까지 {self.periodic_sync_interval}초", -1)
        except Exception as e:
            self.emit_progress(f"동기화 일정 재설정 중 오류: {e}", -1)
    
    # _schedule_periodic_sync 메서드 수정
    def _schedule_periodic_sync(self):
        if not self.running: 
            return
            
        self._safe_cancel_timer()
        
        # 무설정(0) 값인 경우 타이머를 실행하지 않음
        if self.periodic_sync_interval <= 0:
            self.emit_progress("주기적 동기화가 비활성화되었습니다.", -1)
            return
            
        try:
            # 타이머 객체 생성 및 시작
            self.periodic_sync_timer = threading.Timer(self.periodic_sync_interval, self._trigger_periodic_sync)
            self.periodic_sync_timer.daemon = True  # 데몬 스레드로 설정
            self.periodic_sync_timer.start()
            
            # 디버깅 정보 추가 (선택사항)
            self.emit_progress(f"다음 동기화 예약됨: {self.periodic_sync_interval}초 후", -1)
        except Exception as e:
            self.emit_progress(f"타이머 설정 중 오류: {e}", -1)
            print(f"Error scheduling timer: {e}")

    def _trigger_periodic_sync(self):
        if not self.running: return
        with self.periodic_sync_lock:
            if self.is_periodic_sync_running:
                print(f"Job {self.job_id}: Periodic sync already running. Skipping this interval.")
                self._schedule_periodic_sync() # Re-schedule
                return
            self.is_periodic_sync_running = True

        self.emit_progress("주기적 동기화 시작...", 0)
        # print(f"Job {self.job_id}: Submitting periodic sync task.")
        # Use the existing executor, no callback needed here
        if self.executor and not self.executor._shutdown:
            self.executor.submit(self._perform_periodic_sync)
        else:
             print(f"Job {self.job_id}: Executor not available for periodic sync. Aborting.")
             with self.periodic_sync_lock: self.is_periodic_sync_running = False
             if self.running: self._schedule_periodic_sync() # Re-schedule if still running


    def _perform_periodic_sync(self):
        if not self.running:
            with self.periodic_sync_lock: self.is_periodic_sync_running = False
            return

        # 백업 모드에 따라 rsync 명령 구성
        rsync_cmd = ['rsync', '-avhi', '--block-size=4K', '--progress', f"{self.source_path}{os.sep}", self.dest_path]
        
        # 미러링 모드일 때만 --delete 옵션 추가 (대상에서 소스에 없는 파일 삭제)
        if self.backup_mode == "mirror":
            rsync_cmd.append('--delete')
            self.emit_progress(f"미러링 모드로 주기적 동기화 시작...", 0)
        else:
            # 인크리멘탈 모드에서는 --delete 옵션을 추가하지 않음
            self.emit_progress(f"인크리멘탈 모드로 주기적 동기화 시작...", 0)
            
        rsync_cmd.extend(['--progress', f"{self.source_path}{os.sep}", self.dest_path])
        
        self.emit_progress(f"주기적 동기화 소스: {self.source_path}", -1)
        self.emit_progress(f"주기적 동기화 대상: {self.dest_path}", -1)
        self.emit_progress(f"실행 명령: {' '.join(rsync_cmd)}", -1)

        success = False
        processed_files = 0
        error_output = []
        try:
            my_env = os.environ.copy()
            my_env["LANG"] = "C.UTF-8"; my_env["LC_ALL"] = "C.UTF-8"
            
            self.emit_progress("rsync 프로세스 시작...", -1)
            process = subprocess.Popen(rsync_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                    text=True, bufsize=1, universal_newlines=True, env=my_env)

            self.emit_progress("파일 스캔 및 동기화 중...", 5)
            for line in iter(process.stdout.readline, ''):
                if not self.running:
                    process.terminate(); process.wait()
                    self.emit_progress("주기적 동기화 중단됨", -1)
                    raise InterruptedError("Periodic sync interrupted")
                line = line.strip()
                if line:
                    # 파일 개수 관련 로그는 완전히 생략
                    if "files..." in line:
                        continue  # 이 로그는 표시하지 않음
                    
                    # 파일 전송 관련 로그만 표시
                    elif line.startswith('>f') or line.startswith('cd.'):
                        processed_files += 1
                        try:
                            # 파일명 추출 및 상세 로깅
                            file_name = line.split(' ', 1)[1] if ' ' in line else "파일"
                            self.emit_progress(f"파일 처리 중: {file_name}", -1)
                        except Exception as e:
                            self.emit_progress(f"파일 이름 파싱 오류: {e}", -1)
                    
                    # 진행률 관련 로그
                    elif '%' in line and 'bytes/sec' in line:
                        try:
                            parts = line.split()
                            for part in parts:
                                if '%' in part:
                                    progress_val = int(part.replace('%', ''))
                                    mode_text = "미러링" if self.backup_mode == "mirror" else "인크리멘탈"
                                    bytes_info = ' '.join([p for p in parts if 'bytes/sec' in p]) if any('bytes/sec' in p for p in parts) else ""
                                    self.emit_progress(f"{mode_text} 동기화 진행 중... {bytes_info} ({progress_val}%)", progress_val)
                                    break
                        except Exception as e:
                            self.emit_progress(f"진행률 파싱 오류: {e}", -1)
                    
                    # 그 외의 유용한 정보만 로그에 표시
                    elif any(keyword in line for keyword in ["error", "오류", "warning", "skipping", "total size", "speedup"]):
                        self.emit_progress(f"rsync: {line}", -1)

            stderr_output = process.stderr.read()
            if stderr_output:
                error_lines = stderr_output.strip().split('\n')
                for err_line in error_lines:
                    if err_line.strip(): error_output.append(err_line.strip()); self.emit_progress(f"rsync 오류: {err_line.strip()}", -1)

            process.wait()

            if not self.running: raise InterruptedError("Periodic sync interrupted after wait")

            if process.returncode == 0:
                mode_text = "미러링" if self.backup_mode == "mirror" else "인크리멘탈"
                self.emit_progress(f"{mode_text} 동기화 완료 ({processed_files}개 항목 변경 감지/처리)", 100)
                success = True
            else:
                error_msg = f"rsync 종료 코드: {process.returncode}"
                if error_output: error_msg += "\n" + "\n".join(error_output)
                self.emit_progress(f"주기적 동기화 실패: {error_msg}", -1)
                success = False

        except InterruptedError: 
            pass # Already logged
        except Exception as e:
            self.emit_progress(f"주기적 rsync 실행 중 예외 발생: {e}", -1)
            import traceback
            self.emit_progress(f"상세 오류: {traceback.format_exc()}", -1)
            success = False
        finally:
            with self.periodic_sync_lock: self.is_periodic_sync_running = False
            if self.running: self._schedule_periodic_sync() # Schedule next sync
            self.emit_progress(f"주기적 동기화 작업 완료 - 성공: {success}", -1)

    def job_cleanup(self):
        # 명시적으로 모든 스레드 정리
        if hasattr(self, 'executor') and self.executor:
            try:
                self.executor.shutdown(wait=False)
                self.executor = None
            except Exception as e:
                self.emit_progress(f"스레드 풀 종료 중 오류: {e}", -1)
        
        # 소스 감시자 정리
        if hasattr(self, 'source_observer') and self.source_observer:
            try:
                self.source_observer.stop()
                self.source_observer.join(timeout=3)
                self.source_observer = None
                self.emit_progress("소스 감시자 정리 완료", -1)
            except Exception as e:
                self.emit_progress(f"소스 감시자 정리 중 오류: {e}", -1)
        
        # 대상 감시자 정리
        if hasattr(self, 'dest_observer') and self.dest_observer:
            try:
                self.dest_observer.stop()
                self.dest_observer.join(timeout=3)
                self.dest_observer = None
                self.emit_progress("대상 감시자 정리 완료", -1)
            except Exception as e:
                self.emit_progress(f"대상 감시자 정리 중 오류: {e}", -1)

        # 타이머 취소
        if hasattr(self, 'periodic_sync_timer') and self.periodic_sync_timer:
            try:
                self.periodic_sync_timer.cancel()
                self.periodic_sync_timer = None
                self.emit_progress("동기화 타이머 정리 완료", -1)
            except Exception as e:
                self.emit_progress(f"타이머 정리 중 오류: {e}", -1)
                
        # 백업 큐 비우기
        if hasattr(self, 'backup_queue') and self.backup_queue:
            try:
                while not self.backup_queue.empty():
                    try:
                        self.backup_queue.get_nowait()
                    except queue.Empty:
                        break
                self.emit_progress("작업 대기열 정리 완료", -1)
            except Exception as e:
                self.emit_progress(f"작업 대기열 정리 중 오류: {e}", -1)

    def run_segmented_sync(self):
        # 최상위 디렉토리 목록 가져오기
        top_dirs = []
        try:
            top_dirs = [d for d in os.listdir(self.source_path) 
                    if os.path.isdir(os.path.join(self.source_path, d))]
        except Exception as e:
            self.emit_progress(f"디렉토리 목록 가져오기 실패: {e}", -1)
            return False
        
        if not top_dirs:
            self.emit_progress("처리할 디렉토리가 없습니다", -1)
            return False
        
        self.emit_progress(f"{len(top_dirs)}개 디렉토리를 점진적으로 처리합니다", -1)
        
        success_count = 0
        for idx, directory in enumerate(top_dirs):
            if not self.running:
                break
                
            src_dir = os.path.join(self.source_path, directory)
            dst_dir = os.path.join(self.dest_path, directory)
            
            self.emit_progress(f"디렉토리 동기화 중 ({idx+1}/{len(top_dirs)}): {directory}", -1)
            
            # rsync 명령 구성
            rsync_cmd = ['rsync', '-avhi', '--block-size=4K', '--progress', f"{self.source_path}{os.sep}", self.dest_path]
            
            if self.backup_mode == "mirror":
                rsync_cmd.append('--delete')
                
            rsync_cmd.extend(['--progress', f"{src_dir}{os.sep}", dst_dir])
            
            try:
                # rsync 실행
                process = subprocess.Popen(
                    rsync_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1,
                    universal_newlines=True
                )
                
                # 출력 처리 (간소화)
                for line in iter(process.stdout.readline, ''):
                    if not self.running:
                        process.terminate()
                        break
                    if '%' in line and ('bytes/sec' in line or 'xfr#' in line):
                        self.emit_progress(f"진행 중: {directory} - {line.strip()}", -1)
                
                process.wait(timeout=300)  # 최대 5분 대기
                
                if process.returncode == 0:
                    success_count += 1
                    self.emit_progress(f"디렉토리 완료: {directory}", -1)
                else:
                    self.emit_progress(f"디렉토리 처리 실패: {directory} (코드: {process.returncode})", -1)
                    
            except Exception as e:
                self.emit_progress(f"디렉토리 처리 중 오류: {directory} - {e}", -1)
                
            # 각 디렉토리 처리 후 메모리 정리
            self.perform_memory_cleanup()
            
            # 메모리 사용량 확인 및 로깅
            memory_mb = self.monitor_memory()
            self.emit_progress(f"현재 메모리 사용량: {memory_mb:.1f}MB", -1)
        
        return success_count > 0
    
    # --- Main Worker Loop (run) ---
    def run(self):
        # 스레드 풀 사이즈 제한
        max_threads = min(self.max_workers, 4)  # 최대 4개로 제한
        self.emit_progress(f"스레드 풀 생성: 최대 {max_threads}개 제한", -1)
        self.executor = ThreadPoolExecutor(max_workers=max_threads)
        
        # 큐 크기 모니터링 추가
        self.queue_monitor_timer = threading.Timer(30, self.monitor_queue_size)
        self.queue_monitor_timer.daemon = True
        self.queue_monitor_timer.start()
        # Ensure executor is created before scheduling sync
        try:
            # 스레드 풀 사이즈를 명시적으로 작게 설정
            self.executor = ThreadPoolExecutor(max_workers=min(4, self.max_workers))
            self.emit_progress(f"스레드 풀 생성: 최대 {min(4, self.max_workers)}개 스레드 사용", -1)
        except Exception as e:
            self.emit_progress(f"스레드 풀 생성 실패: {e}", -1)
            self.job_finished_signal.emit(self.job_id)
            return

        self.emit_progress("실시간 백업 워커 시작됨", 0)

        if self.periodic_sync_interval > 0:
            self.emit_progress(f"주기적 동기화 활성화 (간격: {self.periodic_sync_interval}초)", -1)
            self._schedule_periodic_sync()
        else:
            self.emit_progress("주기적 동기화 비활성화됨", -1)

        while self.running:
            tasks_to_submit = []
            try:
                task_data = self.backup_queue.get(timeout=0.5)
                tasks_to_submit.append(task_data)
                self.total_queued_tasks += 1
                while len(tasks_to_submit) < self.max_workers * 2:
                    try:
                        task_data = self.backup_queue.get_nowait()
                        tasks_to_submit.append(task_data)
                        self.total_queued_tasks += 1
                    except queue.Empty: break
            except queue.Empty:
                time.sleep(0.1)
                continue

            with self.task_lock:
                for task in tasks_to_submit:
                    if not self.running: break
                    if self.executor and not self.executor._shutdown:
                         try:
                              future = self.executor.submit(self._process_task, task)
                              future.add_done_callback(self._task_callback)
                              self.active_futures.add(future)
                         except RuntimeError as e: # Handle "cannot schedule new futures after shutdown"
                              self.emit_progress(f"작업 제출 오류 (Executor 종료됨?): {e}", -1)
                              print(f"Job {self.job_id}: Could not submit task, executor likely shut down.")
                              self.running = False # Stop the worker loop
                              break
                    else:
                         self.emit_progress("작업 제출 오류: Executor 사용 불가", -1)
                         self.running = False # Stop the worker loop
                         break

            if not self.running: break # Exit loop immediately if stopped

        # --- Cleanup ---
        self.emit_progress("백업 워커 종료 절차 시작...", -1)
        if self.periodic_sync_timer:
            self.periodic_sync_timer.cancel()
            self.emit_progress("주기적 동기화 타이머 중지됨.", -1)

        with self.task_lock:
            active_count = len(self.active_futures)
            if active_count > 0:
                 self.emit_progress(f"{active_count}개 활성 작업 취소 시도...", -1)
                 cancelled_count = 0
                 for future in list(self.active_futures):
                     if future.cancel(): cancelled_count += 1
                 self.emit_progress(f"{cancelled_count}개 작업 취소됨.", -1)

        if self.executor:
             # Use cancel_futures=True if Python 3.9+
             cancel_opt = {}
             if sys.version_info >= (3, 9):
                 cancel_opt['cancel_futures'] = True
             self.executor.shutdown(wait=True, **cancel_opt)
             self.emit_progress("백업 워커 스레드 풀 종료됨.", -1)

            # 새로 추가: job_cleanup 호출
        self.job_cleanup()

        self.emit_progress("백업 워커 종료 완료.", 100)
        self.job_finished_signal.emit(self.job_id)
        # 스레드 우선순위 낮추기 (Linux/Mac)
        try:
            import os
            os.nice(10)  # 우선순위를 낮춤 (Linux/Mac 전용)
        except (ImportError, AttributeError, PermissionError):
            self.emit_progress("스레드 우선순위 조정 실패 (무시됨)", -1)

    # --- Stop Method ---
    def stop(self):
        if self.running:
             self.running = False
             self.emit_progress("백업 워커 중단 요청됨...", -1)
             # 새로 추가: job_cleanup 호출
             self.job_cleanup()
             if self.periodic_sync_timer:
                 self.periodic_sync_timer.cancel()
                 self.emit_progress("주기적 동기화 타이머 중지됨.", -1)
             # self.backup_queue.put(None) # Optional: uncomment to force exit from queue.get

    # 큐 모니터링 메서드 추가
    def monitor_queue_size(self):
        if not self.running:
            return
            
        try:
            queue_size = self.backup_queue.qsize()
            if queue_size > 5000:  # 큐가 너무 크면
                self.emit_progress(f"경고: 백업 큐 크기가 너무 큽니다 ({queue_size}개). 일부 작업을 건너뜁니다.", -1)
                # 큐 비우기 (일부만)
                items_to_remove = min(queue_size - 1000, queue_size // 2)
                for _ in range(items_to_remove):
                    try:
                        self.backup_queue.get_nowait()
                    except queue.Empty:
                        break
        except:
            pass
            
        # 주기적 확인 계속
        if self.running:
            self.queue_monitor_timer = threading.Timer(30, self.monitor_queue_size)
            self.queue_monitor_timer.daemon = True
            self.queue_monitor_timer.start()

# --- 메인 애플리케이션 (ValidationApp) ---
class ValidationApp(QMainWindow):
    MAX_CONCURRENT_JOBS = 5
    DEFAULT_WORKER_THREADS = 2
    DEFAULT_PERIODIC_SYNC_INTERVAL = 0 # Default sync interval in seconds
    DEFAULT_BACKUP_MODE = "mirror"  # 기본값: 미러링 모드
    JOB_STATE_FILENAME = "active_backup_jobs.json"

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Yeson Entertainment 씬 관리 시스템 (v2 - Periodic Sync)")
        self.setGeometry(100, 100, 1000, 800)
        self.setMinimumSize(700, 500)
        self.current_theme = "dark"
        app = QApplication.instance()
        StyleManager.apply_style(app, self.current_theme)
        self.path_manager = PathManager()
        self.calculator = FrameCalculator()
        self.db_manager = SceneDbManager(self.path_manager)
        self.active_jobs = {}
        self.job_queues = {}
        self.scene_backup_status = {}
        self._job_state_file_path = self._get_job_state_file_path()
        print(f"Job state file path: {self._job_state_file_path}")
        self.setup_ui()
        self.connect_signals()
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("애플리케이션 준비 완료.")
        self.theme_shortcut = QShortcut(QKeySequence("Ctrl+T"), self)
        self.theme_shortcut.activated.connect(self.toggle_theme)
        self.load_active_jobs()

    def setup_ui(self):
        self.central_container = QWidget()
        main_layout_for_container = QVBoxLayout(self.central_container)
        main_layout_for_container.setContentsMargins(0,0,0,0)
        main_layout_for_container.setSpacing(0)
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        main_layout_for_container.addWidget(self.scroll_area)
        self.setCentralWidget(self.central_container)
        self.main_widget = QWidget()
        self.scroll_area.setWidget(self.main_widget)
        self.main_layout = QVBoxLayout(self.main_widget)
        self.main_layout.setSpacing(12)
        self.main_layout.setContentsMargins(15, 15, 15, 15)

        header_layout = QHBoxLayout()
        self.app_title = QLabel("Yeson Entertainment 씬 관리 시스템")
        self.app_title.setStyleSheet("font-size: 16px; font-weight: bold; padding: 8px;")
        header_layout.addWidget(self.app_title)
        header_layout.addStretch()
        self.theme_button = QPushButton()
        self.theme_button.setCursor(Qt.PointingHandCursor)
        self.theme_button.setFixedSize(QSize(30, 30))
        self.theme_button.setIconSize(QSize(20, 20))
        self.update_theme_button_icon()
        self.theme_button.setToolTip("테마 전환 (Ctrl+T)")
        self.theme_button.clicked.connect(self.toggle_theme)
        header_layout.addWidget(self.theme_button)
        self.main_layout.addLayout(header_layout)

        self.show_selection = ShowSelectionWidget()
        info_layout = QHBoxLayout()
        info_layout.setSpacing(10)
        self.summary = SummaryWidget()
        self.calculation = CalculationWidget()
        info_layout.addWidget(self.summary, 1)
        info_layout.addWidget(self.calculation, 1)
        self.scene_list_widget = SceneListWidget()
        self.scene_list_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.active_jobs_widget = ActiveJobsWidget()
        self.active_jobs_widget.setVisible(False)
        self.active_jobs_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        self.splitter = QSplitter(Qt.Vertical)
        self.splitter.setHandleWidth(5)
        top_section_widget = QWidget()
        top_section_layout = QVBoxLayout(top_section_widget)
        top_section_layout.setContentsMargins(0,0,0,0)
        top_section_layout.setSpacing(10)
        top_section_layout.addWidget(self.show_selection)
        top_section_layout.addLayout(info_layout)
        top_section_layout.addWidget(self.scene_list_widget)
        self.splitter.addWidget(top_section_widget)
        self.splitter.addWidget(self.active_jobs_widget)
        self.splitter.setStretchFactor(0, 6)
        self.splitter.setStretchFactor(1, 4)
        self.main_layout.addWidget(self.splitter)

    def update_theme_button_icon(self):
        # Placeholder icons, replace with actual moon/sun icons if available
        icon_name = QStyle.SP_DialogYesButton if self.current_theme == "dark" else QStyle.SP_DialogNoButton
        icon = QApplication.style().standardIcon(icon_name)
        self.theme_button.setIcon(icon)

    def connect_signals(self):
        self.show_selection.show_combo.currentTextChanged.connect(self.on_show_changed)
        self.show_selection.season_combo.currentTextChanged.connect(self.on_season_changed)
        self.show_selection.episode_combo.currentTextChanged.connect(self.on_episode_changed)
        self.calculation.calc_button.clicked.connect(self.calculate_selected_scenes)
        self.scene_list_widget.export_button.clicked.connect(self.export_to_excel)
        self.show_selection.show_backup_btn.clicked.connect(lambda: self.initiate_backup("show", full_sync=True))
        self.show_selection.season_backup_btn.clicked.connect(lambda: self.initiate_backup("season", full_sync=True))
        self.show_selection.episode_backup_btn.clicked.connect(lambda: self.initiate_backup("episode", full_sync=True))
        self.show_selection.show_watch_btn.clicked.connect(lambda: self.initiate_backup("show", full_sync=False))
        self.show_selection.season_watch_btn.clicked.connect(lambda: self.initiate_backup("season", full_sync=False))
        self.show_selection.episode_watch_btn.clicked.connect(lambda: self.initiate_backup("episode", full_sync=False))

    def initiate_backup(self, level, full_sync=True):
        # print(f"--- Initiating Backup --- Level: {level}, Full Sync: {full_sync}")
        show = self.show_selection.show_combo.currentText()
        season = self.show_selection.season_combo.currentText()
        episode = self.show_selection.episode_combo.currentText()
        source_path = None
        path_description = ""

        if level == "show":
            if show == "작품 선택": self.statusBar.showMessage("작품을 선택해주세요."); return
            source_path = self.path_manager.get_show_path(show); path_description = f"{show} (작품)"
        elif level == "season":
            if show == "작품 선택" or season == "시즌 선택": self.statusBar.showMessage("시즌을 선택해주세요."); return
            show_path = self.path_manager.get_show_path(show)
            if show_path: source_path = os.path.join(show_path, season); path_description = f"{show}/{season} (시즌)"
        elif level == "episode":
            if show == "작품 선택" or season == "시즌 선택" or episode == "화수 선택": self.statusBar.showMessage("화수를 선택해주세요."); return
            show_path = self.path_manager.get_show_path(show)
            if show_path: source_path = os.path.join(show_path, season, episode); path_description = f"{show}/{season}/{episode} (화수)"

        if not source_path or not os.path.exists(source_path):
            self.statusBar.showMessage(f"선택된 경로를 찾을 수 없습니다: {path_description}"); return

        if len(self.active_jobs) >= self.MAX_CONCURRENT_JOBS:
            QMessageBox.warning(self, "작업 제한 도달", f"최대 {self.MAX_CONCURRENT_JOBS}개의 동시 작업만 실행 가능합니다."); return

        if source_path in [job['source_path'] for job in self.active_jobs.values()]:
             reply = QMessageBox.question(self, "작업 중복 확인", f"이미 실행 중인 작업 경로:\n{source_path}\n\n기존 작업을 중지하고 새로 시작하시겠습니까?", QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
             if reply == QMessageBox.Yes:
                  existing_job_id = next((jid for jid, job in self.active_jobs.items() if job['source_path'] == source_path), None)
                  if existing_job_id: self.stop_job(existing_job_id); time.sleep(1)
             else: self.statusBar.showMessage("작업 시작이 취소되었습니다."); return

        # 백업 모드 선택 다이얼로그
        if full_sync:  # 전체 백업 시에만 모드 선택 표시
            mode_dialog = QMessageBox(self)
            mode_dialog.setWindowTitle("백업 모드 선택")
            mode_dialog.setText(f"{path_description}에 대한 백업 모드를 선택하세요.")
            mode_dialog.setInformativeText("미러링: 소스와 대상을 완전히 일치시킵니다 (소스에 없는 파일은 대상에서 삭제)\n인크리멘탈: 소스의 변경사항만 대상에 추가합니다 (대상의 기존 파일 유지)")
            mirror_button = mode_dialog.addButton("미러링", QMessageBox.ActionRole)
            incremental_button = mode_dialog.addButton("인크리멘탈", QMessageBox.ActionRole)
            cancel_button = mode_dialog.addButton("취소", QMessageBox.RejectRole)
            
            mode_dialog.exec()
            
            if mode_dialog.clickedButton() == cancel_button:
                self.statusBar.showMessage("백업 시작이 취소되었습니다."); return
            elif mode_dialog.clickedButton() == mirror_button:
                backup_mode = "mirror"
            elif mode_dialog.clickedButton() == incremental_button:
                backup_mode = "incremental"
            else:
                backup_mode = self.DEFAULT_BACKUP_MODE
        else:
            # 실시간 감시만 시작할 경우 기본 모드 사용
            backup_mode = self.DEFAULT_BACKUP_MODE

        dest_dir_base = QFileDialog.getExistingDirectory(self, f"{path_description} - 백업 대상 상위 폴더 선택", os.path.expanduser("~"))
        if not dest_dir_base: self.statusBar.showMessage("백업 경로 선택이 취소되었습니다."); return

        # Determine relative path from a known root to maintain structure
        possible_roots = sorted([p for p in self.path_manager.possible_paths if os.path.isdir(p)], key=len, reverse=True)
        common_root = ""
        for root in possible_roots:
            if source_path.startswith(root):
                 common_root = root
                 break
        if not common_root: common_root = os.path.dirname(source_path) # Fallback if not under known roots

        rel_path_from_root = os.path.relpath(source_path, common_root)
        if rel_path_from_root == '.' or not rel_path_from_root : rel_path_from_root = os.path.basename(source_path)

        full_dest_path = os.path.normpath(os.path.join(dest_dir_base, rel_path_from_root))

        # print(f"Source Path: {source_path}")
        # print(f"Destination Base Path: {dest_dir_base}")
        # print(f"Calculated Full Destination Path: {full_dest_path}")

        if not os.path.exists(full_dest_path):
            try: os.makedirs(full_dest_path, exist_ok=True)
            except Exception as e: QMessageBox.critical(self, "오류", f"대상 경로 생성 실패:\n{full_dest_path}\n\n오류: {e}"); return
        elif not os.path.isdir(full_dest_path):
             QMessageBox.critical(self, "오류", f"대상 경로가 디렉토리가 아닙니다:\n{full_dest_path}"); return

        job_id = str(uuid.uuid4())
        
        # 백업 모드를 포함하여 작업 시작
        mode_display = "미러링" if backup_mode == "mirror" else "인크리멘탈"
        if full_sync:
            # 전체 백업일 경우 현재 선택된 모드 사용
            self.statusBar.showMessage(f"{path_description}에 대한 {mode_display} 백업 작업을 시작합니다...")
        else:
            # 실시간 감시만 시작할 경우 기본값 사용 (미러링)
            self.statusBar.showMessage(f"{path_description}에 대한 실시간 감시를 시작합니다...")
        
        # 작업 시작시 백업 모드 전달
        self.start_new_job_with_mode(job_id, source_path, full_dest_path, perform_initial_sync=full_sync, backup_mode=backup_mode)

        # 실시간 감시를 즉시 시작하고, 초기 동기화는 병렬로 실행
        if full_sync:
            self.statusBar.showMessage(f"{path_description}에 대한 {mode_display} 백업 작업과 실시간 감시를 동시에 시작합니다...")
        else:
            self.statusBar.showMessage(f"{path_description}에 대한 실시간 감시를 시작합니다...")
        
        # 작업 시작시 백업 모드 전달 (full_sync 여부와 상관없이 실시간 감시 즉시 시작)
        self.start_job_with_parallel_sync(job_id, source_path, full_dest_path, perform_initial_sync=full_sync, backup_mode=backup_mode)

    def start_job_with_parallel_sync(self, job_id, source_path, dest_path, perform_initial_sync, backup_mode):
        """실시간 감시를 즉시 시작하고 초기 동기화는 병렬로 수행"""
        job_widget = SingleJobStatusWidget(
            job_id, source_path, dest_path, 
            sync_interval=self.DEFAULT_PERIODIC_SYNC_INTERVAL,
            backup_mode=backup_mode
        )
        job_widget.stop_requested.connect(self.stop_job)
        job_widget.sync_interval_changed.connect(self.handle_sync_interval_change)
        job_widget.backup_mode_changed.connect(self.handle_backup_mode_change)
        self.active_jobs_widget.add_job_widget(job_id, job_widget)

        self.active_jobs[job_id] = {
            "job_id": job_id, "source_path": source_path, "dest_path": dest_path,
            "status": "Initializing", "initializer": None, "worker": None,
            "source_observer": None, "dest_observer": None,
            "backup_queue": queue.Queue(), "ui_widget": job_widget,
            "perform_initial_sync": perform_initial_sync, "scene_backup_status": {},
            "sync_interval": self.DEFAULT_PERIODIC_SYNC_INTERVAL,
            "backup_mode": backup_mode
        }
        self.job_queues[job_id] = self.active_jobs[job_id]["backup_queue"]

        # 실시간 감시 먼저 시작
        job_widget.set_status("실시간 감시 시작 중...")
        self.start_realtime_monitoring(job_id)
        
        # 초기 동기화는 병렬로 실행 (실시간 감시가 이미 작동 중)
        if perform_initial_sync:
            job_widget.append_log(f"{backup_mode} 모드로 백그라운드 초기 동기화를 시작합니다.")
            initializer = BackupInitializer(job_id, source_path, dest_path)
            initializer.progress_signal.connect(self.handle_initializer_progress)
            initializer.initialization_finished.connect(self.handle_parallel_initialization_finished)
            self.active_jobs[job_id]["initializer"] = initializer
            initializer.start()

    # 초기 동기화 완료 처리 메서드 추가
    def handle_parallel_initialization_finished(self, job_id, success):
        job_info = self.active_jobs.get(job_id)
        if not job_info: return
        job_widget = job_info["ui_widget"]
        job_info["initializer"] = None

        if success:
            job_widget.append_log("백그라운드 초기 동기화가 완료되었습니다.")
            job_widget.set_status("실시간 감시 중 (초기 동기화 완료)", -1)
            self.reset_and_update_job_scene_status(job_id)
        else:
            job_widget.append_log("백그라운드 초기 동기화가 실패했지만, 실시간 감시는 계속 진행됩니다.")
            job_widget.set_status("실시간 감시 중 (초기 동기화 실패)", -1)

    # 백업 모드를 인수로 받는 새 메서드 추가
    def start_new_job_with_mode(self, job_id, source_path, dest_path, perform_initial_sync, backup_mode):
        # 기존 start_new_job 메서드와 유사하지만 백업 모드를 인수로 받음
        job_widget = SingleJobStatusWidget(
            job_id, source_path, dest_path, 
            sync_interval=self.DEFAULT_PERIODIC_SYNC_INTERVAL,
            backup_mode=backup_mode  # 지정된 백업 모드 사용
        )
        job_widget.stop_requested.connect(self.stop_job)
        job_widget.sync_interval_changed.connect(self.handle_sync_interval_change)
        job_widget.backup_mode_changed.connect(self.handle_backup_mode_change)
        self.active_jobs_widget.add_job_widget(job_id, job_widget)

        self.active_jobs[job_id] = {
            "job_id": job_id, "source_path": source_path, "dest_path": dest_path,
            "status": "Initializing", "initializer": None, "worker": None,
            "source_observer": None, "dest_observer": None,
            "backup_queue": queue.Queue(), "ui_widget": job_widget,
            "perform_initial_sync": perform_initial_sync, "scene_backup_status": {},
            "sync_interval": self.DEFAULT_PERIODIC_SYNC_INTERVAL,
            "backup_mode": backup_mode  # 지정된 백업 모드 저장
        }
        self.job_queues[job_id] = self.active_jobs[job_id]["backup_queue"]

        job_widget.set_status("준비 중...")
        
        mode_display = "미러링" if backup_mode == "mirror" else "인크리멘탈"
        self.statusBar.showMessage(f"새 작업 시작 준비: {os.path.basename(source_path)} ({mode_display} 모드)")

        if perform_initial_sync:
            job_widget.set_status(f"{mode_display} 모드로 초기 동기화 중...", 0)
            initializer = BackupInitializer(job_id, source_path, dest_path)
            initializer.progress_signal.connect(self.handle_initializer_progress)
            initializer.initialization_finished.connect(self.handle_initialization_finished)
            self.active_jobs[job_id]["initializer"] = initializer
            initializer.start()
        else:
            job_widget.set_status(f"{mode_display} 모드로 파일 검증 및 감시 시작 중...")
            self.start_realtime_monitoring(job_id)

    def handle_backup_mode_change(self, job_id, new_mode):
        try:
            job_info = self.active_jobs.get(job_id)
            if not job_info:
                print(f"Warning: Job {job_id} not found for mode change")
                return
                
            # 저장된 값 업데이트
            job_info["backup_mode"] = new_mode
            
            # 상태바 업데이트
            self.statusBar.showMessage(f"작업 {os.path.basename(job_info['source_path'])} 백업 모드를 변경 중...", 3000)
            
            # worker가 있는지 확인하고 모드 업데이트
            worker = job_info.get("worker")
            if not worker:
                print(f"Warning: No worker for job {job_id}")
                return
                
            success = worker.update_backup_mode(new_mode)
            
            if success:
                QApplication.processEvents()
                mode_display = "미러링" if new_mode == "mirror" else "인크리멘탈"
                self.statusBar.showMessage(f"작업 {os.path.basename(job_info['source_path'])} 백업 모드가 {mode_display}로 변경되었습니다.", 3000)
            else:
                self.statusBar.showMessage(f"작업 {os.path.basename(job_info['source_path'])} 백업 모드 변경 실패.", 3000)
        except Exception as e:
            print(f"Error in handle_backup_mode_change: {e}")
            import traceback
            traceback.print_exc()
            self.statusBar.showMessage(f"백업 모드 변경 중 오류 발생: {str(e)}", 5000)

    # start_new_job 메서드 수정
    def start_new_job(self, job_id, source_path, dest_path, perform_initial_sync):
        # 기본 백업 모드로 작업 위젯 생성
        job_widget = SingleJobStatusWidget(
            job_id, source_path, dest_path, 
            sync_interval=self.DEFAULT_PERIODIC_SYNC_INTERVAL,
            backup_mode=self.DEFAULT_BACKUP_MODE
        )
        job_widget.stop_requested.connect(self.stop_job)
        job_widget.sync_interval_changed.connect(self.handle_sync_interval_change)
        job_widget.backup_mode_changed.connect(self.handle_backup_mode_change)  # 새 신호 연결
        self.active_jobs_widget.add_job_widget(job_id, job_widget)

        self.active_jobs[job_id] = {
            "job_id": job_id, "source_path": source_path, "dest_path": dest_path,
            "status": "Initializing", "initializer": None, "worker": None,
            "source_observer": None, "dest_observer": None,
            "backup_queue": queue.Queue(), "ui_widget": job_widget,
            "perform_initial_sync": perform_initial_sync, "scene_backup_status": {},
            "sync_interval": self.DEFAULT_PERIODIC_SYNC_INTERVAL,
            "backup_mode": self.DEFAULT_BACKUP_MODE  # 백업 모드 저장
        }
        self.job_queues[job_id] = self.active_jobs[job_id]["backup_queue"]

        job_widget.set_status("준비 중...")
        self.statusBar.showMessage(f"새 작업 시작 준비: {os.path.basename(source_path)}")

        if perform_initial_sync:
            job_widget.set_status("초기 동기화 중...", 0)
            initializer = BackupInitializer(job_id, source_path, dest_path)
            initializer.progress_signal.connect(self.handle_initializer_progress)
            initializer.initialization_finished.connect(self.handle_initialization_finished)
            self.active_jobs[job_id]["initializer"] = initializer
            initializer.start()
        else:
            job_widget.set_status("파일 검증 및 감시 시작 중...")
            self.start_realtime_monitoring(job_id)

    def handle_sync_interval_change(self, job_id, new_interval):
        try:
            job_info = self.active_jobs.get(job_id)
            if not job_info:
                print(f"Warning: Job {job_id} not found for interval change")
                return
                
            # 먼저 저장된 값 업데이트 (worker 호출과 분리)
            job_info["sync_interval"] = new_interval
            
            # 상태바 업데이트
            self.statusBar.showMessage(f"작업 {os.path.basename(job_info['source_path'])} 동기화 간격을 변경 중...", 3000)
            
            # worker가 있는지 확인하고 interval 업데이트
            worker = job_info.get("worker")
            if not worker:
                print(f"Warning: No worker for job {job_id}")
                return
                
            # 워커의 메서드 호출은 블로킹되지 않아야 함
            success = worker.update_sync_interval(new_interval)
            
            if success:
                # UI 갱신 및 상태 업데이트
                QApplication.processEvents()
                self.statusBar.showMessage(f"작업 {os.path.basename(job_info['source_path'])} 동기화 간격이 변경되었습니다.", 3000)
            else:
                self.statusBar.showMessage(f"작업 {os.path.basename(job_info['source_path'])} 동기화 간격 변경 실패.", 3000)
        except Exception as e:
            print(f"Error in handle_sync_interval_change: {e}")
            import traceback
            traceback.print_exc()
            self.statusBar.showMessage(f"동기화 간격 변경 중 오류 발생: {str(e)}", 5000)

    def handle_initializer_progress(self, job_id, message, progress):
        job_widget = self.active_jobs_widget.get_job_widget(job_id)
        if job_widget:
            if message: job_widget.append_log(message)
            if progress >= 0:
                # 직접 progress_bar 값 업데이트
                job_widget.progress_bar.setValue(progress)
                if progress < 100: 
                    job_widget.status_label.setText(f"초기 동기화 중... ({progress}%)")

    def handle_initialization_finished(self, job_id, success):
        job_info = self.active_jobs.get(job_id)
        if not job_info: return
        job_widget = job_info["ui_widget"]
        job_info["initializer"] = None

        if success:
            job_widget.set_status("초기 동기화 완료, 감시 시작 중...", 100)
            self.reset_and_update_job_scene_status(job_id)
            self.start_realtime_monitoring(job_id)
        else:
            job_widget.set_status("오류: 초기 동기화 실패", 100)
            job_widget.append_log("초기 동기화에 실패하여 실시간 감시를 시작할 수 없습니다.")
            self.active_jobs[job_id]["status"] = "Error"

    def start_realtime_monitoring(self, job_id):
        job_info = self.active_jobs.get(job_id)
        if not job_info: return
        job_widget = job_info["ui_widget"]
        source_path = job_info["source_path"]
        dest_path = job_info["dest_path"]
        backup_queue = job_info["backup_queue"]
        sync_interval = job_info["sync_interval"]
        backup_mode = job_info["backup_mode"]  # 백업 모드 가져오기

        job_widget.set_status("실시간 감시 시작 중...")
        # print(f"--- Starting Realtime Monitoring for Job {job_id} ---")
        # print(f"Source: {source_path}"); print(f"Destination: {dest_path}")
        try:
            source_handler = BackupEventHandler(backup_queue, source_path, dest_path, is_source=True)
            dest_handler = BackupEventHandler(backup_queue, source_path, dest_path, is_source=False)
            # print(" -> Handlers created.")

            source_observer = Observer(timeout=5.0)
            source_observer.schedule(source_handler, source_path, recursive=True)
            source_observer.daemon = True  # 데몬 스레드로 설정하여 주 스레드 종료 시 자동 종료
            source_observer.start(); job_info["source_observer"] = source_observer
            job_widget.append_log(f"소스 감시 시작: {source_path}")
            # print(" -> Source observer started.")

            dest_observer = Observer()
            dest_observer.schedule(dest_handler, dest_path, recursive=True)
            dest_observer.start(); job_info["dest_observer"] = dest_observer
            job_widget.append_log(f"대상 감시 시작: {dest_path}")
            # print(" -> Destination observer started.")

            # 백업 모드 전달
            worker = BackupWorker(
                job_id, backup_queue, source_handler, dest_handler,
                self.scene_list_widget.scene_tree,
                source_path, dest_path,
                max_workers=self.DEFAULT_WORKER_THREADS,
                periodic_sync_interval=sync_interval,
                backup_mode=backup_mode  # 백업 모드 전달
            )

            worker.update_signal.connect(self.handle_worker_update)
            worker.scene_backup_status_signal.connect(self.handle_scene_backup_status)
            worker.restart_observer_signal.connect(self.handle_restart_observer)
            worker.job_finished_signal.connect(self.handle_worker_finished)
            job_info["worker"] = worker
            # print(" -> Worker created.")
            worker.start()
            # print(" -> Worker started.")

            job_info["status"] = "Running"
            
            # 백업 모드에 따른 상태 메시지
            mode_display = "미러링" if backup_mode == "mirror" else "인크리멘탈"
            job_widget.set_status(f"{mode_display} 모드로 실시간 감시 중", 0)
            self.statusBar.showMessage(f"작업 시작됨: {os.path.basename(source_path)} ({mode_display} 모드)")

        except Exception as e:
            print(f"!!! ERROR starting realtime monitoring: {e}")
            job_widget.set_status("오류: 감시 시작 실패")
            job_widget.append_log(f"실시간 감시/백업 시작 중 오류: {str(e)}")
            self.active_jobs[job_id]["status"] = "Error"
            self.stop_job_resources(job_id) # Attempt cleanup

    def handle_worker_update(self, job_id, message, progress):
        job_widget = self.active_jobs_widget.get_job_widget(job_id)
        if job_widget:
            if message: job_widget.append_log(message)
            current_status = job_widget.status_label.text()
            
            # 항상 진행 상황을 업데이트
            if progress >= 0:
                job_widget.progress_bar.setValue(progress)
                
                # 상태 텍스트 업데이트
                if "실시간 백업 워커 시작됨" in message:
                    job_widget.set_status("실시간 감시 중", progress)
                elif any(phrase in message for phrase in ["처리 중...", "대기 작업 완료", "동기화 진행 중...", "주기적 동기화 완료", "주기적 rsync 실행 중"]):
                    if not any(s in current_status for s in ["완료", "오류", "중지"]):
                        status_text = "실시간 처리 중"
                        if "동기화" in message: status_text = "주기적 동기화 중"
                        job_widget.set_status(f"{status_text} ({progress}%)", progress)
                elif "백업 워커 종료 완료" in message: 
                    pass # Handled by finished signal
                # 모든 작업이 완료된 경우 진행률을 100%로 설정
                elif "모든 대기 작업 처리 완료" in message:
                    job_widget.set_status("실시간 처리 완료", 100)
                else:
                    # 다른 메시지의 경우 진행률만 업데이트
                    job_widget.progress_bar.setValue(progress)
                    
            # 삭제 작업 완료 메시지 처리
            elif "Target deletion" in message and "succeeded" in message:
                # 삭제 작업 성공 시 100% 표시
                if not any(s in current_status for s in ["완료", "오류", "중지"]):
                    job_widget.set_status("실시간 처리 완료", 100)
                    job_widget.progress_bar.setValue(100)

    def handle_scene_backup_status(self, job_id, scene_name, status):
        job_info = self.active_jobs.get(job_id)
        if job_info and self.scene_list_widget.scene_tree:
             job_info["scene_backup_status"][scene_name] = status
             self.update_combined_scene_status_in_tree(scene_name)

    def handle_restart_observer(self, job_id, path_to_restart):
        job_info = self.active_jobs.get(job_id)
        job_widget = self.active_jobs_widget.get_job_widget(job_id)
        if not job_info or not job_widget: return

        job_widget.append_log(f"감시자 재설정 시도: {path_to_restart}")
        observer_to_restart = None; is_source = False

        if path_to_restart == job_info["source_path"]: observer_to_restart = job_info["source_observer"]; is_source = True
        elif path_to_restart == job_info["dest_path"]: observer_to_restart = job_info["dest_observer"]; is_source = False
        else: job_widget.append_log(f"오류: 재시작 경로 불일치"); return

        if not observer_to_restart or not observer_to_restart.is_alive(): job_widget.append_log(f"오류: 감시자 없음/미실행"); return

        try:
            observer_to_restart.stop(); observer_to_restart.join(timeout=5)
            job_widget.append_log("기존 감시자 중지됨.")
            new_handler = BackupEventHandler(job_info["backup_queue"], job_info["source_path"], job_info["dest_path"], is_source=is_source)
            new_observer = Observer()
            new_observer.schedule(new_handler, path_to_restart, recursive=True)
            new_observer.start()

            if is_source:
                job_info["source_observer"] = new_observer
                if job_info["worker"]: job_info["worker"].source_handler = new_handler
            else:
                job_info["dest_observer"] = new_observer
                if job_info["worker"]: job_info["worker"].dest_handler = new_handler

            job_widget.append_log(f"경로 {path_to_restart} 감시자 재설정 완료.")
            self.statusBar.showMessage(f"작업 {os.path.basename(job_info['source_path'])} 감시자 재설정 완료.", 3000)
        except Exception as e:
             job_widget.append_log(f"감시자 재설정 중 오류 발생: {e}")
             job_info["status"] = "Error"; job_widget.set_status("오류: 감시자 재설정 실패")

    def handle_worker_finished(self, job_id):
        job_info = self.active_jobs.get(job_id)
        if job_info:
            job_info["worker"] = None
            if job_info["status"] not in ["Stopping", "Stopped", "Error"]:
                 job_info["status"] = "Stopped"
                 job_widget = job_info["ui_widget"]
                 if job_widget:
                     job_widget.set_status("작업 완료/중지됨", 100)
                     job_widget.append_log("백업 워커가 정상적으로 종료되었습니다.")
                     job_widget.stop_button.setEnabled(False)

    def stop_job(self, job_id):
        job_info = self.active_jobs.get(job_id)
        if not job_info: return
        job_widget = job_info["ui_widget"]
        if job_widget:
             job_widget.set_status("중지 중...")
             job_widget.append_log("작업 중지 요청됨...")
             job_widget.stop_button.setEnabled(False)
        job_info["status"] = "Stopping"
        self.statusBar.showMessage(f"작업 중지 중: {os.path.basename(job_info['source_path'])}")
        thread = threading.Thread(target=self.stop_job_resources, args=(job_id,))
        thread.daemon = True
        thread.start()

    def stop_job_resources(self, job_id):
        job_info = self.active_jobs.get(job_id)
        if not job_info: return
        job_widget = job_info["ui_widget"]
        try:
            initializer = job_info.get("initializer")
            if initializer and initializer.isRunning():
                if job_widget: self.safe_append_log(job_widget, "초기 동기화 중단 시도...")
                initializer.stop(); initializer.wait(5000)
                if job_widget: self.safe_append_log(job_widget, "초기 동기화 스레드 중지됨.")
            job_info["initializer"] = None

            worker = job_info.get("worker")
            if worker and worker.isRunning():
                if job_widget: self.safe_append_log(job_widget, "백업 워커 중단 시도...")
                worker.stop(); worker.wait(8000) # Wait for worker thread to finish
                if job_widget: self.safe_append_log(job_widget, "백업 워커 스레드 중지 시도 완료.")
            job_info["worker"] = None # Ensure worker ref is cleared even if wait times out

            source_observer = job_info.get("source_observer")
            if source_observer and source_observer.is_alive():
                if job_widget: self.safe_append_log(job_widget, "소스 감시자 중단 시도...")
                source_observer.stop(); source_observer.join(timeout=5)
                if job_widget: self.safe_append_log(job_widget, "소스 감시자 중지됨.")
            job_info["source_observer"] = None

            dest_observer = job_info.get("dest_observer")
            if dest_observer and dest_observer.is_alive():
                if job_widget: self.safe_append_log(job_widget, "대상 감시자 중단 시도...")
                dest_observer.stop(); dest_observer.join(timeout=5)
                if job_widget: self.safe_append_log(job_widget, "대상 감시자 중지됨.")
            job_info["dest_observer"] = None

            backup_queue = job_info.get("backup_queue")
            if backup_queue:
                while not backup_queue.empty():
                    try: backup_queue.get_nowait()
                    except queue.Empty: break
                if job_widget: self.safe_append_log(job_widget, "대기열 작업 비워짐.")

            job_info["status"] = "Stopped"
            if job_widget:
                self.safe_set_status(job_widget, "작업 중지됨")
                self.safe_append_log(job_widget, "모든 관련 프로세스가 중지되었습니다.")

            QMetaObject.invokeMethod(self.statusBar, "showMessage", Qt.QueuedConnection, Q_ARG(str, f"작업 중지 완료: {os.path.basename(job_info['source_path'])}"))
            QMetaObject.invokeMethod(self, "remove_job", Qt.QueuedConnection, Q_ARG(str, job_id))

        except Exception as e:
            print(f"Job({job_id}) 중지 중 오류: {e}")
            job_info["status"] = "Error"
            if job_widget: self.safe_set_status(job_widget, "오류: 중지 실패"); self.safe_append_log(job_widget, f"작업 중지 중 오류 발생: {e}")
            QMetaObject.invokeMethod(self.statusBar, "showMessage", Qt.QueuedConnection, Q_ARG(str, f"작업 중지 중 오류 발생: {os.path.basename(job_info['source_path'])}"))

    def safe_append_log(self, widget: SingleJobStatusWidget, message: str):
        if widget: QMetaObject.invokeMethod(widget, "append_log", Qt.QueuedConnection, Q_ARG(str, message))

    def safe_set_status(self, widget: SingleJobStatusWidget, status: str, progress: int = -1):
        if widget: QMetaObject.invokeMethod(widget, "set_status", Qt.QueuedConnection, Q_ARG(str, status), Q_ARG(int, progress))

    @pyqtSlot(str)
    def remove_job(self, job_id):
        if job_id in self.active_jobs: del self.active_jobs[job_id]
        if job_id in self.job_queues: del self.job_queues[job_id]
        self.active_jobs_widget.remove_job_widget(job_id)
        self.recalculate_and_update_all_scene_statuses()
        # print(f"Job {job_id} 제거 완료.")

    def stop_all_jobs(self):
        current_job_ids = list(self.active_jobs.keys())
        if not current_job_ids: self.statusBar.showMessage("중지할 활성 작업이 없습니다."); return
        self.statusBar.showMessage(f"{len(current_job_ids)}개 작업 전체 중지 시작...")
        for job_id in current_job_ids: self.stop_job(job_id); time.sleep(0.1)

    def reset_and_update_job_scene_status(self, job_id):
        job_info = self.active_jobs.get(job_id)
        if not job_info or not self.scene_list_widget.scene_tree: return
        tree = self.scene_list_widget.scene_tree
        job_info["scene_backup_status"].clear()

        # Create a temporary worker instance JUST for using its helper method
        # It won't be started or run. Provide minimal valid args.
        temp_worker = BackupWorker(job_id, None, None, None, None,
                                   job_info["source_path"], job_info["dest_path"],
                                   periodic_sync_interval=0) # No sync needed

        for i in range(tree.topLevelItemCount()):
            item = tree.topLevelItem(i)
            scene_name = item.text(0)
            if scene_name.startswith('scene-'):
                is_backed_up = temp_worker.is_scene_fully_backed_up(scene_name)
                status = "백업완료" if is_backed_up else ""
                job_info["scene_backup_status"][scene_name] = status

        self.recalculate_and_update_all_scene_statuses()
        # Clean up the temporary worker instance if needed (Python GC should handle it)
        del temp_worker


    def recalculate_and_update_all_scene_statuses(self):
        if not self.scene_list_widget.scene_tree: return
        tree = self.scene_list_widget.scene_tree
        combined_status = {}
        all_scene_names = []
        for i in range(tree.topLevelItemCount()):
            item = tree.topLevelItem(i)
            scene_name = item.text(0)
            if scene_name.startswith('scene-'):
                 all_scene_names.append(scene_name)
                 combined_status[scene_name] = ""

        for job_id, job_info in self.active_jobs.items():
            job_scene_statuses = job_info.get("scene_backup_status", {})
            for scene_name, status in job_scene_statuses.items():
                if scene_name in combined_status and status == "백업완료":
                    combined_status[scene_name] = "백업완료"

        tree.reset_all_scene_status_in_tree()
        for scene_name, final_status in combined_status.items():
            if final_status: tree.update_scene_status_in_tree(scene_name, final_status)

    def update_combined_scene_status_in_tree(self, scene_name):
        if not self.scene_list_widget.scene_tree: return
        final_status = ""
        for job_id, job_info in self.active_jobs.items():
            job_scene_statuses = job_info.get("scene_backup_status", {})
            if job_scene_statuses.get(scene_name) == "백업완료":
                final_status = "백업완료"; break
        self.scene_list_widget.scene_tree.update_scene_status_in_tree(scene_name, final_status)

    def toggle_theme(self):
        self.current_theme = "light" if self.current_theme == "dark" else "dark"
        self.update_theme_button_icon()
        StyleManager.apply_style(QApplication.instance(), self.current_theme)
        # Update job widget styles if necessary by re-applying stylesheet parts
        theme = "dark" if self.current_theme == "dark" else "light"
        colors = StyleManager.DARK_THEME if theme == "dark" else StyleManager.LIGHT_THEME
        for job_id, widget in self.active_jobs_widget.job_widgets.items():
            widget.setStyleSheet(f"QFrame#{widget.objectName()} {{ background: {colors['LIGHT_BG']}; border-radius: 4px; border: 1px solid {colors['DARK_ACCENT']}; }}")
        self.statusBar.showMessage(f"{self.current_theme.capitalize()} 테마 적용됨.")

    # --- UI Update Methods (on_show_changed, on_season_changed, etc.) ---
    # These methods remain largely the same as before.
    # Key points:
    # - Clearing UI elements on selection change.
    # - Loading seasons/episodes/scenes based on selection.
    # - Calling `recalculate_and_update_all_scene_statuses` after loading scenes.
    def on_show_changed(self, show):
        self.scene_list_widget.scene_tree.clear()
        self.show_selection.season_combo.clear(); self.show_selection.season_combo.addItem("시즌 선택")
        self.show_selection.episode_combo.clear(); self.show_selection.episode_combo.addItem("화수 선택")
        self.summary.total_frames_display.clear(); self.summary.episode_feet_display.clear()
        self.calculation.total_feet_display.clear()

        if show == "작품 선택":
            self.show_selection.season_combo.setEnabled(False); self.show_selection.episode_combo.setEnabled(False)
            self.statusBar.showMessage("작품을 선택해주세요"); return

        project_path = self.path_manager.get_show_path(show)
        if not project_path:
            self.statusBar.showMessage(f"{show} 작품 경로를 찾을 수 없습니다")
            self.show_selection.season_combo.setEnabled(False); self.show_selection.episode_combo.setEnabled(False); return

        if show == "Yeson_DANG":
            self.show_selection.season_combo.setEnabled(False); self.show_selection.episode_combo.setEnabled(False)
            self.load_scene_folders(project_path); self.update_scene_list()
            self.statusBar.showMessage(f"{show} 작품이 선택되었습니다"); return

        self.show_selection.season_combo.setEnabled(True)
        seasons = []
        if show in ["BB", "GN", "BM", "KOTH", "Test"]: seasons = self.path_manager.show_paths[show].get("seasons", [])
        elif show in ["Yeson_Test", "Yeson_Test_4K"]:
             try: seasons = sorted([d for d in os.listdir(project_path) if os.path.isdir(os.path.join(project_path, d)) and not d.startswith('.') and d != "DANG"])
             except OSError as e: print(f"시즌 폴더 검색 중 오류 ({project_path}): {e}"); self.statusBar.showMessage(f"시즌 폴더 검색 오류: {e}", 5000)

        if seasons:
             self.show_selection.season_combo.addItems(seasons)
             is_season_only = show in ["Test", "Yeson_Test", "Yeson_Test_4K"]
             self.show_selection.episode_combo.setEnabled(not is_season_only)
             self.statusBar.showMessage(f"{show} 작품 선택됨. 시즌을 선택하세요.")
        else:
             self.show_selection.season_combo.setEnabled(False); self.show_selection.episode_combo.setEnabled(False)
             self.load_scene_folders(project_path); self.update_scene_list()
             self.statusBar.showMessage(f"{show} 작품 로드됨 (시즌 없음).")

    def on_season_changed(self, season):
        self.scene_list_widget.scene_tree.clear()
        self.show_selection.episode_combo.clear(); self.show_selection.episode_combo.addItem("화수 선택")
        self.summary.total_frames_display.clear(); self.summary.episode_feet_display.clear()
        self.calculation.total_feet_display.clear()

        if season == "시즌 선택": self.show_selection.episode_combo.setEnabled(False); return
        show = self.show_selection.show_combo.currentText(); project_path = self.path_manager.get_show_path(show)
        if not project_path: return
        season_path = os.path.join(project_path, season)

        if show in ["Test", "Yeson_Test", "Yeson_Test_4K"]:
            self.load_scene_folders(season_path); self.update_scene_list()
            self.show_selection.episode_combo.setEnabled(False)
            self.statusBar.showMessage(f"{show} - {season} 시즌 선택됨."); return

        self.show_selection.episode_combo.setEnabled(True)
        self.update_episode_list(show, season, project_path)
        self.statusBar.showMessage(f"{show} - {season} 시즌 선택됨. 화수를 선택하세요.")

    def on_episode_changed(self, episode):
        self.scene_list_widget.scene_tree.clear()
        self.summary.total_frames_display.clear(); self.summary.episode_feet_display.clear()
        self.calculation.total_feet_display.clear()

        if episode == "화수 선택": return
        show = self.show_selection.show_combo.currentText(); season = self.show_selection.season_combo.currentText()
        project_path = self.path_manager.get_show_path(show)
        if not project_path: return
        episode_path = os.path.join(project_path, season, episode)
        self.load_scene_folders(episode_path); self.update_scene_list()
        self.statusBar.showMessage(f"{show} - {season} - {episode} 로드 완료.")

    def load_scene_folders(self, path):
        if not os.path.exists(path): self.statusBar.showMessage(f"경로를 찾을 수 없습니다: {path}"); return
        scene_folders = []
        try: scene_folders = sorted([item for item in os.listdir(path) if os.path.isdir(os.path.join(path, item)) and item.startswith('scene-')])
        except OSError as e: self.statusBar.showMessage(f"폴더 접근 오류: {e}"); return

        self.scene_list_widget.scene_tree.clear()
        for scene in scene_folders:
            item = QTreeWidgetItem([scene, "N/A", "N/A", ""]); self.scene_list_widget.scene_tree.addTopLevelItem(item)

        if scene_folders: self.statusBar.showMessage(f"{len(scene_folders)}개 씬 폴더 로드됨.")
        else: self.statusBar.showMessage("씬 폴더를 찾을 수 없습니다.")
        self.recalculate_and_update_all_scene_statuses() # Update status based on current jobs

    def update_episode_list(self, show, season, project_path):
        season_path = os.path.join(project_path, season)
        if not os.path.exists(season_path): self.statusBar.showMessage(f"시즌 경로를 찾을 수 없습니다: {season_path}"); return
        episodes = []
        try:
            items = os.listdir(season_path)
            # Simplified filter logic placeholder - use original complex logic if needed
            if show == "BB": prefix = "DASA" if "Season13" in season else ("EASA" if "Season14" in season else None)
            elif show == "GN": prefix = season.replace("GN_Season", "") + "LBW" if "GN_Season" in season else None
            elif show == "BM": prefix = "BM_8"
            elif show == "KOTH": prefix = "EABE" if "Season14" in season else ("15" if "Season15" in season else None)
            else: prefix = None # Default case

            if prefix:
                 episodes = sorted([item for item in items if os.path.isdir(os.path.join(season_path, item)) and item.startswith(prefix)])
            else: # Fallback or other shows
                 episodes = sorted([item for item in items if os.path.isdir(os.path.join(season_path, item)) and not item.startswith('.')]) # Basic dir filter

        except OSError as e: self.statusBar.showMessage(f"폴더 접근 오류: {e}"); return

        self.show_selection.episode_combo.clear(); self.show_selection.episode_combo.addItem("화수 선택")
        if episodes: self.show_selection.episode_combo.addItems(episodes); self.statusBar.showMessage(f"{len(episodes)}개 에피소드 발견.")
        else: self.statusBar.showMessage("해당 시즌에 에피소드를 찾을 수 없습니다.")

    def update_scene_list(self):
        show = self.show_selection.show_combo.currentText()
        season = self.show_selection.season_combo.currentText()
        episode = self.show_selection.episode_combo.currentText()
        if show == "작품 선택": return

        # 식별자 결정 (프레임 정보 로드 기준)
        identifier = None
        if show in ["Test", "Yeson_DANG", "Yeson_Test", "Yeson_Test_4K"]:
            if show == "Yeson_DANG": identifier = show
            elif season != "시즌 선택": identifier = season
        elif season != "시즌 선택" and episode != "화수 선택": identifier = episode

        # 필요한 정보가 선택되지 않은 경우 빈 컬렉션으로 초기화
        if not identifier:
            frames_info = {}
            latest_scenes = {}
            total_frames = 0
            total_feet = "0.00"
        else:
            # 식별자가 있는 경우 DB에서 프레임 정보 로드
            frames_info = self.db_manager.get_frames_info(identifier)
            if not frames_info:
                self.statusBar.showMessage(f"[{identifier}] 프레임 정보를 DB에서 가져올 수 없습니다.", 5000)
                
            # filter_scene_folders는 frames_info 기반으로 작동
            latest_scenes = self.db_manager.filter_scene_folders(frames_info)
            total_frames = sum(int(f) for f in frames_info.values() if str(f).isdigit())
            total_feet = self.calculator.calculate_sheet_length(total_frames)

        # 트리 위젯 업데이트 (프레임 정보가 없어도 폴더 목록은 표시됨)
        self.update_tree_widget_with_frames(latest_scenes, frames_info)

        # 요약 정보 업데이트
        self.summary.total_frames_display.setText(str(total_frames))
        self.summary.episode_feet_display.setText(self.calculator.format_feet_display(total_feet))
        self.statusBar.showMessage(f"씬 목록 업데이트 완료 ({len(latest_scenes)}개 유효 씬).")

    def update_tree_widget_with_frames(self, latest_scenes, frames_info):
         tree = self.scene_list_widget.scene_tree
         valid_scene_names = set(latest_scenes.keys())
         for i in range(tree.topLevelItemCount()):
             item = tree.topLevelItem(i)
             scene_name = item.text(0)
             if scene_name in valid_scene_names:
                 frames = frames_info.get(scene_name, "N/A")
                 sheet_length = self.calculator.calculate_sheet_length(frames) if str(frames).isdigit() else "N/A"
                 feet_display = self.calculator.format_feet_display(sheet_length) if sheet_length != "N/A" else "N/A"
                 item.setText(1, str(frames)); item.setText(2, feet_display)
             else: item.setText(1, "N/A"); item.setText(2, "N/A")

    def calculate_selected_scenes(self):
        selected_items = self.scene_list_widget.scene_tree.selectedItems()
        if not selected_items: self.calculation.total_feet_display.setText("0F 00f"); self.statusBar.showMessage("선택된 씬이 없습니다."); return
        total_frames = 0; valid_item_count = 0
        for item in selected_items:
            frames_str = item.text(1)
            if frames_str.isdigit(): total_frames += int(frames_str); valid_item_count += 1
        total_feet = self.calculator.calculate_sheet_length(total_frames)
        feet_display = self.calculator.format_feet_display(total_feet)
        self.calculation.total_feet_display.setText(feet_display)
        self.statusBar.showMessage(f"{valid_item_count}개 씬 선택됨. 총 FEET: {feet_display}")

    def export_to_excel(self):
        tree = self.scene_list_widget.scene_tree
        if tree.topLevelItemCount() == 0: self.statusBar.showMessage("내보낼 씬 목록이 없습니다."); return
        show = self.show_selection.show_combo.currentText(); season = self.show_selection.season_combo.currentText(); episode = self.show_selection.episode_combo.currentText()
        total_frames_str = self.summary.total_frames_display.text(); total_feet_str = self.summary.episode_feet_display.text()
        scene_data = [[item.text(c) for c in range(4)] for item in [tree.topLevelItem(i) for i in range(tree.topLevelItemCount())]]

        name_parts = [p for p in [show, season, episode] if p and p not in ["작품 선택", "시즌 선택", "화수 선택"]]
        default_filename = "_".join(name_parts) + "_scenes.xlsx" if name_parts else "scenes.xlsx"
        file_path, _ = QFileDialog.getSaveFileName(self, "엑셀 파일로 저장", default_filename, "Excel Files (*.xlsx)")
        if not file_path: self.statusBar.showMessage("파일 저장이 취소되었습니다."); return

        try:
            wb = Workbook(); ws = wb.active; ws.title = "SceneList"
            theme = "dark" if self.current_theme == "dark" else "light"
            colors = StyleManager.DARK_THEME if theme == "dark" else StyleManager.LIGHT_THEME
            hex_accent = colors["ACCENT"][1:]; hex_text = colors["TEXT"][1:]
            hex_dark_accent = colors["DARK_ACCENT"][1:]; hex_light_bg = colors["LIGHT_BG"][1:]
            header_font = Font(name="Helvetica", size=12, bold=True, color=hex_accent)
            subheader_font = Font(name="Helvetica", size=11, bold=True, color=hex_text)
            text_font = Font(name="Helvetica", size=11, color=hex_text)
            border = Border(left=Side(style="thin", color=hex_dark_accent), right=Side(style="thin", color=hex_dark_accent), top=Side(style="thin", color=hex_dark_accent), bottom=Side(style="thin", color=hex_dark_accent))
            fill_light_bg = PatternFill(start_color=hex_light_bg, end_color=hex_light_bg, fill_type="solid")
            fill_header = PatternFill(start_color=hex_dark_accent, end_color=hex_dark_accent, fill_type="solid")
            center_align = Alignment(horizontal="center", vertical="center")

            ws.append(["작품", show])
            ws.append(["시즌", season if season != "시즌 선택" else "N/A"])
            ws.append(["화수", episode if episode != "화수 선택" else "N/A"])
            ws.append([])
            ws.append(["총 프레임 수", total_frames_str])
            ws.append(["총 FEET", total_feet_str])
            ws.append([])
            for row in ws['A1:B7']: # Adjusted range
                 for cell in row:
                     if cell.value: # Apply only if cell has value
                         if cell.column == 1: cell.font = subheader_font
                         else: cell.font = text_font
                         cell.fill = fill_light_bg; cell.border = border

            headers = ["씬 이름", "Frames", "FEET", "백업상태"]
            ws.append(headers); header_row_num = ws.max_row
            for col, header in enumerate(headers, start=1):
                 cell = ws.cell(row=header_row_num, column=col)
                 cell.font = header_font # Use bolder font for main headers
                 cell.fill = fill_header; cell.border = border; cell.alignment = center_align

            for scene in scene_data:
                 formatted_scene = []
                 for idx, value in enumerate(scene):
                     new_value = value
                     try:
                         if idx == 1 and str(value).isdigit(): new_value = int(value)
                         elif idx == 2 and isinstance(value, str):
                              cleaned_value = value.replace('F','').replace('f','').replace(' ','')
                              if '.' in cleaned_value: new_value = float(cleaned_value)
                     except: pass # Ignore conversion errors
                     formatted_scene.append(new_value)
                 ws.append(formatted_scene)

            for row in ws.iter_rows(min_row=header_row_num + 1, max_row=ws.max_row):
                 for cell in row:
                     cell.font = text_font; cell.fill = fill_light_bg; cell.border = border
                     cell.alignment = center_align
                     if isinstance(cell.value, int): cell.number_format = '0'
                     elif isinstance(cell.value, float): cell.number_format = '0.00'

            ws.column_dimensions["A"].width = 40; ws.column_dimensions["B"].width = 15
            ws.column_dimensions["C"].width = 15; ws.column_dimensions["D"].width = 20

            wb.save(file_path)
            self.statusBar.showMessage(f"씬 목록이 {file_path}에 저장되었습니다.")
        except Exception as e:
            QMessageBox.critical(self, "엑셀 저장 오류", f"엑셀 파일 저장 중 오류 발생:\n{e}")
            self.statusBar.showMessage("엑셀 파일 저장 실패.")

    # --- Job State Persistence ---
    def _get_job_state_file_path(self):
        data_dir = QStandardPaths.writableLocation(QStandardPaths.AppLocalDataLocation)
        if not data_dir: data_dir = os.path.expanduser("~/.config/YesonBackupTool") # Fallback
        try: os.makedirs(data_dir, exist_ok=True)
        except OSError as e: print(f"Warning: Could not create data directory '{data_dir}'. Error: {e}"); return self.JOB_STATE_FILENAME
        return os.path.join(data_dir, self.JOB_STATE_FILENAME)

    def save_active_jobs(self):
        jobs_to_save = []
        for job_id, job_info in self.active_jobs.items():
            savable_info = {
                "job_id": job_id,
                "source_path": job_info.get("source_path"),
                "dest_path": job_info.get("dest_path"),
                "perform_initial_sync_originally": job_info.get("perform_initial_sync", False),
                "sync_interval": job_info.get("sync_interval", self.DEFAULT_PERIODIC_SYNC_INTERVAL),
                "backup_mode": job_info.get("backup_mode", self.DEFAULT_BACKUP_MODE)  # 백업 모드 저장
            }
            if savable_info["source_path"] and savable_info["dest_path"]:
                jobs_to_save.append(savable_info)

        if not jobs_to_save:
            if os.path.exists(self._job_state_file_path):
                try: os.remove(self._job_state_file_path); print("Removed empty job state file.")
                except OSError as e: print(f"Warning: Could not remove job state file '{self._job_state_file_path}'. Error: {e}")
            return

        try:
            with open(self._job_state_file_path, 'w', encoding='utf-8') as f:
                json.dump(jobs_to_save, f, indent=4, ensure_ascii=False)
            # print(f"Successfully saved {len(jobs_to_save)} active jobs.")
        except Exception as e:
            print(f"Error saving active jobs: {e}")
            QMessageBox.warning(self, "저장 오류", f"활성 작업 목록 저장 실패:\n{e}")

    # load_active_jobs 메서드 수정 - 백업 모드 로드
    def load_active_jobs(self):
        if not os.path.exists(self._job_state_file_path): return
        print(f"Loading active jobs from {self._job_state_file_path}")
        try:
            with open(self._job_state_file_path, 'r', encoding='utf-8') as f: loaded_jobs_info = json.load(f)
        except Exception as e:
            print(f"Error loading job state file: {e}")
            QMessageBox.warning(self, "로드 오류", f"이전 작업 목록 로드 실패:\n{self._job_state_file_path}\n\n오류: {e}")
            return

        restarted_count = 0; skipped_count = 0
        for job_info in loaded_jobs_info:
            if len(self.active_jobs) >= self.MAX_CONCURRENT_JOBS:
                print(f"Max job limit ({self.MAX_CONCURRENT_JOBS}) reached. Skipping remaining saved jobs.")
                QMessageBox.warning(self, "작업 제한 도달", f"저장된 작업 로드 중 최대 작업 제한({self.MAX_CONCURRENT_JOBS}) 도달."); break

            job_id = job_info.get("job_id", str(uuid.uuid4()))
            source_path = job_info.get("source_path"); dest_path = job_info.get("dest_path")
            sync_interval = job_info.get("sync_interval", self.DEFAULT_PERIODIC_SYNC_INTERVAL)
            backup_mode = job_info.get("backup_mode", self.DEFAULT_BACKUP_MODE)  # 백업 모드 로드

            if not source_path or not dest_path: skipped_count += 1; continue
            if not os.path.exists(source_path): print(f"Warning: Source path missing for saved job '{source_path}'. Skipping."); skipped_count += 1; continue
            if not os.path.exists(dest_path): print(f"Warning: Dest path missing for saved job '{dest_path}'. Skipping."); skipped_count += 1; continue
            if source_path in [j['source_path'] for j in self.active_jobs.values()]: print(f"Skipping saved job for already active source path: {source_path}"); skipped_count += 1; continue

            try:
                # Create job structure first
                job_widget = SingleJobStatusWidget(
                    job_id, source_path, dest_path, 
                    sync_interval=sync_interval,
                    backup_mode=backup_mode  # 백업 모드 전달
                )
                job_widget.stop_requested.connect(self.stop_job)
                job_widget.sync_interval_changed.connect(self.handle_sync_interval_change)
                job_widget.backup_mode_changed.connect(self.handle_backup_mode_change)  # 새 시그널 연결
                self.active_jobs_widget.add_job_widget(job_id, job_widget)

                self.active_jobs[job_id] = {
                    "job_id": job_id, "source_path": source_path, "dest_path": dest_path,
                    "status": "Initializing", "initializer": None, "worker": None,
                    "source_observer": None, "dest_observer": None,
                    "backup_queue": queue.Queue(), "ui_widget": job_widget,
                    "perform_initial_sync": False, "scene_backup_status": {},
                    "sync_interval": sync_interval,
                    "backup_mode": backup_mode  # 백업 모드 저장
                }
                self.job_queues[job_id] = self.active_jobs[job_id]["backup_queue"]
                
                # 백업 모드 포함하여 모니터링 시작
                self.start_realtime_monitoring(job_id)
                restarted_count += 1
                
            except Exception as e:
                print(f"Error restarting saved job (ID: {job_id}, Source: {source_path}): {e}")
                skipped_count += 1
                if job_id in self.active_jobs: self.remove_job(job_id) # Cleanup failed restart attempt

        message = f"{restarted_count}개의 이전 작업 복원 완료."
        if skipped_count > 0: message += f" ({skipped_count}개 건너뜀)"
        if restarted_count > 0 or skipped_count > 0 : self.statusBar.showMessage(message, 5000)
        # print(f"Job loading finished. Restarted: {restarted_count}, Skipped: {skipped_count}")

    def closeEvent(self, event):
        print("Close event triggered.")
        self.save_active_jobs()
        if self.active_jobs:
            reply = QMessageBox.question(self, "종료 확인", f"{len(self.active_jobs)}개 작업 실행 중.\n종료하시겠습니까? (작업은 백그라운드에서 중지됩니다)", QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)
            if reply == QMessageBox.Yes:
                self.statusBar.showMessage("애플리케이션 종료 중... 작업 중지 시도...")
                QApplication.processEvents()
                self.stop_all_jobs() # Initiate stops
                print("Initiated stop for all jobs. Accepting close event.")
                event.accept()
            else: print("Close event ignored."); event.ignore()
        else: print("No active jobs. Accepting close event."); event.accept()

# --- PathManager, FrameCalculator, SceneDbManager (Unchanged from previous version) ---
class PathManager:
    def __init__(self):
        self.show_paths = {
            "BB": {"base": "Bento_Project", "seasons": ["BB_Season13", "BB_Season14"]},
            "GN": {"base": "Bento_Project2/Great_North", "seasons": ["GN_Season4", "GN_Season5"]},
            "BM": {"base": "Titmouse/Big_Mouth", "seasons": ["BM_Season8"]},
            "KOTH": {"base": "Disney/KOTH", "seasons": ["KOTH_Season14", "KOTH_Season15"]},
            "Test": {"base": "Test", "seasons": ["TEST_SYSTEM"]},
            "Yeson_DANG": {"base": "Yeson_Test/DANG", "seasons": []},
            "Yeson_Test": {"base": "Yeson_Test", "seasons": []},
            "Yeson_Test_4K": {"base": "Yeson_Test_4K", "seasons": []}
        }
        self.possible_paths = list(set(os.path.normpath(p) for p in [
            "/usadata2", "/usadata3",
            "/System/Volumes/Data/mnt/usadata2", "/System/Volumes/Data/mnt/usadata3",
            "/System/Volumes/data/mnt/usadata2", "/System/Volumes/data/mnt/usadata3",
            "/Volumes/usadata2", "/Volumes/usadata3",
            # Add local test paths if needed
            os.path.expanduser("~/mount/usadata2"), os.path.expanduser("~/mount/usadata3")
        ]))
        self.db_paths = list(set(os.path.normpath(p) for p in [
            "/USA_DB/db_jobs",
            "/System/Volumes/Data/mnt/USA_DB/db_jobs",
            "/System/Volumes/data/mnt/USA_DB/db_jobs",
            "/Volumes/USA_DB/db_jobs",
            os.path.expanduser("~/USA_DB/db_jobs"),
            os.path.expanduser("~/mount/USA_DB/db_jobs")
        ]))
        self.path_cache = {}
        self.base_path_root = self.find_base_path() # Find and cache the main root path

    def find_base_path(self):
        for path in self.possible_paths:
            if os.path.isdir(path):
                # print(f"Using base path root: {path}")
                return path
        print("Warning: No valid base path root found in possible_paths.")
        return None

    def get_show_path(self, show):
        if show == "작품 선택": return None
        if show in self.path_cache: return self.path_cache[show]

        show_info = self.show_paths.get(show)
        if not show_info: return None
        relative_path = show_info["base"]

        # Try finding under the cached base path first for efficiency
        if self.base_path_root:
             project_path = os.path.normpath(os.path.join(self.base_path_root, relative_path))
             if os.path.isdir(project_path):
                  # Check Yeson project constraint if applicable
                  is_yeson = show.startswith("Yeson_")
                  is_usadata3 = "usadata3" in self.base_path_root.lower()
                  if not (is_yeson and not is_usadata3): # Allow if not Yeson OR if Yeson on usadata3
                      # print(f"Found show path (cached root): {project_path}")
                      self.path_cache[show] = project_path
                      return project_path

        # If not found under cached root, search all possible paths
        for path_root in self.possible_paths:
            if path_root == self.base_path_root: continue # Skip already checked path
            project_path = os.path.normpath(os.path.join(path_root, relative_path))
            if os.path.isdir(project_path):
                is_yeson = show.startswith("Yeson_")
                is_usadata3 = "usadata3" in path_root.lower()
                if not (is_yeson and not is_usadata3):
                    # print(f"Found show path (secondary search): {project_path}")
                    self.path_cache[show] = project_path
                    return project_path

        print(f"Show path not found for: {show}")
        return None

    def get_possible_db_paths(self, identifier):
        if not identifier: return []
        return [os.path.normpath(os.path.join(base_path, identifier, "scene.db")) for base_path in self.db_paths]

class FrameCalculator:
    @staticmethod
    def calculate_sheet_length(frames):
        try: frames = int(frames); return f"{frames // 16}.{frames % 16:02d}"
        except: return "0.00"
    @staticmethod
    def format_feet_display(sheet_length):
        try:
            if not isinstance(sheet_length, str) or '.' not in sheet_length: return "0F 00f"
            feet, frames = sheet_length.split('.'); return f"{int(feet)}F {int(frames):02d}f"
        except: return "0F 00f"

class SceneDbManager:
    def __init__(self, path_manager):
        self.path_manager = path_manager
        self.cache = {}
        self.dbu_path = self._find_dbu_path()
        if not self.dbu_path: print("Warning: Harmony 'dbu' executable not found. Frame info loading disabled.")

    def _find_dbu_path(self):
        harmony_versions = ["22", "21.1", "21", "20"]
        base_app_path = "/Applications"
        possible_paths = []
        for version in harmony_versions:
             for edition in ["Premium", "Advanced", "Essentials"]:
                  app_name = f"Toon Boom Harmony {version} {edition}"
                  full_path = os.path.join(base_app_path, f"{app_name}", f"{app_name}.app", "Contents", "tba", "macosx", "bin", "dbu")
                  possible_paths.append(full_path)
        for path in possible_paths:
            if os.path.exists(path):
                print(f"Found dbu executable: {path}")
                return path
        # Add Linux/Windows paths if needed
        # Example Linux: /usr/local/ToonBoomAnimation/harmony<version>/lnx86_64/bin/dbu
        # Example Windows: C:\Program Files (x86)\Toon Boom Animation\Toon Boom Harmony <version> <edition>\win64\bin\dbu.exe
        return None

    def get_frames_info(self, identifier):
        if not identifier or identifier in ["작품 선택", "시즌 선택", "화수 선택"]:
            return {}
        if identifier in self.cache:
            return self.cache[identifier]

        frames_info = {}
        possible_db_paths = self.path_manager.get_possible_db_paths(identifier)
        if not possible_db_paths: 
            return {}
            
        # "/USA_DB/db_jobs" 경로를 먼저 확인하도록 우선순위 설정
        prioritized_paths = sorted(possible_db_paths, key=lambda x: "USA_DB" in x, reverse=True)
        found_db = False

        for db_path in prioritized_paths:
            if not os.path.exists(db_path): 
                continue
                
            # 두 번째 파일에서처럼 dbu 경로 리스트 구성
            dbu_paths_to_try = [
                "/Applications/Toon Boom Harmony 22 Premium/Harmony 22 Premium.app/Contents/tba/macosx/bin/dbu",
                "/Applications/Toon Boom Harmony 21.1 Premium/Harmony 21.1 Premium.app/Contents/tba/macosx/bin/dbu",
                "/Applications/Toon Boom Harmony 21 Premium/Harmony 21 Premium.app/Contents/tba/macosx/bin/dbu",
                "/Applications/Toon Boom Harmony 20 Premium/Harmony 20 Premium.app/Contents/tba/macosx/bin/dbu",
            ]
            
            # next() 함수를 사용하여 첫 번째 유효한 경로 선택
            dbu_command_base = next(([p] for p in dbu_paths_to_try if os.path.exists(p)), None)
            
            if not dbu_command_base:
                print("Harmony dbu 실행 파일을 찾을 수 없습니다.")
                continue

            dbu_command = dbu_command_base + ["-l", "-r", db_path]
            
            try:
                my_env = os.environ.copy()
                if sys.platform == "darwin":
                    my_env["LANG"] = "en_US.UTF-8"
                    my_env["LC_ALL"] = "en_US.UTF-8"

                # timeout을 30초로 늘림
                result = subprocess.run(dbu_command, capture_output=True, text=True, check=False, env=my_env, timeout=30)

                if result.returncode != 0:
                    error_detail = result.stderr.strip() if result.stderr else "No stderr"
                    print(f"dbu 실행 오류 (코드 {result.returncode}): {db_path}")
                    continue
                    
                print(f"DB 파일 읽기 성공: {db_path}")
                found_db = True
                current_scene = None
                
                for line in result.stdout.splitlines():
                    line = line.strip()
                    if line.startswith('Path:'): 
                        path_val = line.split('Path:', 1)[1].strip()
                        current_scene = os.path.basename(path_val)
                    elif line.startswith('Frames:') and current_scene:
                        frames_val = line.split('Frames:', 1)[1].strip()
                        if frames_val.isdigit(): 
                            frames_info[current_scene] = frames_val
                        current_scene = None
                        
                if frames_info:
                    print(f"프레임 정보 로드 완료: {len(frames_info)}개 씬 [{identifier}]")
                    self.cache[identifier] = frames_info
                    break  # 성공 시 루프 종료
                    
            except subprocess.TimeoutExpired:
                print(f"dbu 실행 시간 초과: {db_path}")
            except Exception as e:
                print(f"프레임 정보 추출 중 예외 발생 ({db_path}): {e}")

        if not found_db:
            print(f"경고: [{identifier}] 유효한 scene.db 파일 없음.")
        elif not frames_info:
            print(f"경고: DB 파일({identifier})에서 유효 프레임 정보 없음.")

        if identifier not in self.cache:
            self.cache[identifier] = frames_info
        return frames_info

    @staticmethod
    def filter_scene_folders(frames_info):
        excluded_terms = {'CHARACTERS', 'PROPS', 'Sub_Model', 'STOCK', 'Crowd', '_old', '_batch', 'batch'}
        # 반환값을 두 번째 파일과 동일하게 수정 (scene_name: scene_name 형태)
        return {
            scene_name: scene_name for scene_name in frames_info
            if not any(term in scene_name for term in excluded_terms)
        }

# --- 애플리케이션 실행 ---
if __name__ == '__main__':
    if hasattr(Qt, 'AA_EnableHighDpiScaling'): QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'): QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    app = QApplication(sys.argv)
    window = ValidationApp()
    window.show()
    sys.exit(app.exec_())