import os
import subprocess
import re
import sys

class SceneDbManager:
    def __init__(self, path_manager):
        self.path_manager = path_manager
        self.dbu_path = self._find_dbu_path()

    def _find_dbu_path(self):
        """DBU 실행 파일 경로를 찾습니다."""
        dbu_paths_to_try = [
            "/Applications/Toon Boom Harmony 22 Premium/Harmony 22 Premium.app/Contents/tba/macosx/bin/dbu",
            "/Applications/Toon Boom Harmony 21.1 Premium/Harmony 21.1 Premium.app/Contents/tba/macosx/bin/dbu",
            "/Applications/Toon Boom Harmony 21 Premium/Harmony 21 Premium.app/Contents/tba/macosx/bin/dbu",
            "/Applications/Toon Boom Harmony 20 Premium/Harmony 20 Premium.app/Contents/tba/macosx/bin/dbu",
        ]

        for path in dbu_paths_to_try:
            if os.path.exists(path):
                print(f"DBU 실행 파일을 찾았습니다: {path}")
                return path

        print("DBU 실행 파일을 찾을 수 없습니다.")
        return None

    def get_frames_info(self, identifier):
        """DBU 명령어를 사용하여 씬의 프레임 정보를 가져옵니다."""
        if not identifier or identifier in ["작품 선택", "시즌 선택", "화수 선택"]:
            return {}

        frames_info = {}
        possible_db_paths = self.path_manager.get_possible_db_paths(identifier)
        if not possible_db_paths:
            return {}

        # "/USA_DB/db_jobs" 경로를 먼저 확인하도록 우선순위 설정
        prioritized_paths = sorted(possible_db_paths, key=lambda x: "USA_DB" in x, reverse=True)
        found_db = False

        for db_path in prioritized_paths:
            if not os.path.exists(db_path):
                continue

            if not self.dbu_path:
                print("DBU 실행 파일을 찾을 수 없습니다.")
                continue

            try:
                # DBU 명령어 실행
                my_env = os.environ.copy()
                if sys.platform == "darwin":
                    my_env["LANG"] = "en_US.UTF-8"
                    my_env["LC_ALL"] = "en_US.UTF-8"

                cmd = [self.dbu_path, "-l", "-r", db_path]
                result = subprocess.run(cmd, capture_output=True, text=True, check=False, env=my_env, timeout=30)

                if result.returncode != 0:
                    error_detail = result.stderr.strip() if result.stderr else "No stderr"
                    print(f"DBU 실행 오류 (코드 {result.returncode}): {db_path}")
                    continue

                print(f"DB 파일 읽기 성공: {db_path}")
                found_db = True
                current_scene = None

                for line in result.stdout.splitlines():
                    line = line.strip()
                    if line.startswith('Path:'):
                        path_val = line.split('Path:', 1)[1].strip()
                        current_scene = os.path.basename(path_val)
                    elif line.startswith('Frames:') and current_scene:
                        frames_val = line.split('Frames:', 1)[1].strip()
                        if frames_val.isdigit():
                            frames_info[current_scene] = {
                                'total_frames': int(frames_val),
                                'ranges': [(1, int(frames_val))]
                            }
                        current_scene = None

                if frames_info:
                    print(f"프레임 정보 로드 완료: {len(frames_info)}개 씬 [{identifier}]")
                    break  # 성공 시 루프 종료

            except subprocess.TimeoutExpired:
                print(f"DBU 실행 시간 초과: {db_path}")
            except Exception as e:
                print(f"프레임 정보 추출 중 예외 발생 ({db_path}): {e}")

        if not found_db:
            print(f"경고: [{identifier}] 유효한 scene.db 파일 없음.")
        elif not frames_info:
            print(f"경고: DB 파일({identifier})에서 유효 프레임 정보 없음.")

        return frames_info

    @staticmethod
    def filter_scene_folders(frames_info):
        """프레임 정보가 있는 씬 폴더만 필터링합니다."""
        return {scene: info for scene, info in frames_info.items() if info.get('total_frames', 0) > 0}