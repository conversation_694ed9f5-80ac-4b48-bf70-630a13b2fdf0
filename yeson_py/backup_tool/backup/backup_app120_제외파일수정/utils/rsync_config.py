"""
rsync 명령어 설정을 중앙화하는 모듈입니다.
"""
import os
import subprocess
import json
from pathlib import Path

# 기본 rsync 옵션
DEFAULT_RSYNC_OPTIONS = [
    '-avhi',   # archive, verbose, human-readable, itemize changes
    '--stats', # 파일 전송 통계 출력
    '--progress',
    '--modify-window=1',  # 타임스탬프 비교 시 1초의 오차 허용
    '--ignore-errors',    # 오류 발생 시에도 계속 진행
    '--times',           # 수정 시간 보존
    '--timeout=60',      # 60초 타임아웃 설정
    '--inplace',         # 파일을 직접 수정 (임시 파일 사용하지 않음)
    '--partial',         # 부분 전송된 파일 유지
    '--exclude=.DS_Store',  # macOS 메타데이터 파일 제외
    '--exclude=._*',        # macOS 확장 속성 파일 제외
    '--exclude=.AppleDouble',  # macOS 리소스 포크 관련 파일 제외
    '--exclude=.LSOverride',   # macOS Finder 관련 파일 제외
    '--exclude=Icon?',         # macOS 아이콘 파일 제외
    '--exclude=._.DS_Store',   # macOS 메타데이터 파일 제외 (특수 케이스)
    '--exclude=.Spotlight-V100',  # macOS Spotlight 관련 파일 제외
    '--exclude=.Trashes',         # macOS 휴지통 관련 파일 제외
    '--exclude=.fseventsd'        # macOS 파일시스템 이벤트 관련 파일 제외
]

def get_rsync_options_string():
    """rsync 옵션 문자열을 반환합니다."""
    return ' '.join(DEFAULT_RSYNC_OPTIONS)

def get_active_jobs_path():
    """active_backup_jobs.json 파일의 경로를 반환합니다."""
    return os.path.expanduser("~/Downloads/active_backup_jobs.json")

def get_exclusions(job_id=None):
    """active_backup_jobs.json에서 제외 목록을 가져옵니다.
    
    Args:
        job_id (str, optional): 특정 작업의 제외 목록만 가져올 때 사용
    """
    try:
        jobs_path = get_active_jobs_path()
        if os.path.exists(jobs_path):
            with open(jobs_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if job_id and 'active_jobs' in data and job_id in data['active_jobs']:
                    # 특정 작업의 제외 목록 반환
                    return data['active_jobs'][job_id].get('exclusions', {})
                # 전체 제외 목록 반환
                return data.get('exclusions', {})
    except Exception as e:
        print(f"제외 목록 로드 중 오류: {e}")
    return {}

def get_rsync_command(source_path, dest_path, is_mirror=False, job_id=None):
    """rsync 명령어를 생성합니다.
    
    Args:
        source_path (str): 소스 경로
        dest_path (str): 대상 경로
        is_mirror (bool): 미러링 모드 여부
        job_id (str, optional): 작업 ID. 특정 작업의 제외 패턴을 사용할 때 필요
    """
    cmd = ['rsync'] + DEFAULT_RSYNC_OPTIONS
    
    # 미러링 모드인 경우 --delete 옵션 추가
    if is_mirror:
        cmd.append('--delete')
    
    # 제외 목록 추가 (작업별 + 전체)
    exclusions = {}
    
    # 1. 전체 제외 목록 추가
    global_exclusions = get_exclusions()
    exclusions.update(global_exclusions)
    
    # 2. 작업별 제외 목록 추가 (있는 경우)
    if job_id:
        job_exclusions = get_exclusions(job_id)
        exclusions.update(job_exclusions)
    
    # 제외 패턴 적용
    for path, type_ in exclusions.items():
        # 파일 제외 패턴 (모든 작업에 적용)
        if type_ == "파일":
            # 패턴이 이미 rsync 패턴 형식인 경우 (예: .DS_Store, *.tmp)
            if '*' in path or path.startswith('.') or '/' not in path:
                cmd.extend(['--exclude', path])
            else:
                # 전체 경로인 경우 파일명만 추출
                filename = os.path.basename(path)
                cmd.extend(['--exclude', filename])
        
        # 폴더 제외 패턴 (해당 작업의 소스 디렉토리 내에 있는 경우만 적용)
        elif type_ == "폴더":
            # 패턴이 이미 rsync 패턴 형식인 경우 (예: node_modules/)
            if '*' in path or path.startswith('.') or '/' not in path:
                cmd.extend(['--exclude', path])  # 폴더 자체 제외
                cmd.extend(['--exclude', f"{path}/**"])  # 폴더 내용 제외
            else:
                # 전체 경로인 경우 소스 디렉토리 기준으로 상대 경로 확인
                try:
                    rel_path = os.path.relpath(path, source_path)
                    if not rel_path.startswith('..'):  # 소스 경로 내부의 경로인 경우만
                        cmd.extend(['--exclude', rel_path])  # 폴더 자체 제외
                        cmd.extend(['--exclude', f"{rel_path}/**"])  # 폴더 내용 제외
                except ValueError:
                    # 상대 경로 변환 실패 시 무시 (다른 작업의 폴더일 수 있음)
                    pass
    
    # 소스 경로 끝에 슬래시 추가 (디렉토리 내용만 복사)
    if os.path.isdir(source_path):
        source_path = os.path.join(source_path, '')
    
    cmd.extend([source_path, dest_path])
    return cmd 