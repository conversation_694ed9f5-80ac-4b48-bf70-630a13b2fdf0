import logging
import os
from logging.handlers import RotatingFileHandler
from datetime import datetime
from pathlib import Path
from ..config.settings import Settings
import threading
import time

class LogBuffer:
    """로그 메시지를 버퍼링하는 클래스"""
    def __init__(self, max_size=100):
        self.messages = []
        self.max_size = max_size
        self.lock = threading.Lock()

    def add_message(self, message):
        with self.lock:
            self.messages.append(message)
            if len(self.messages) > self.max_size * 0.8:
                self.messages = self.messages[len(self.messages)//2:]

    def get_messages(self):
        with self.lock:
            messages = self.messages.copy()
            self.messages.clear()
            return messages

    def clear(self):
        with self.lock:
            self.messages.clear()

class BackupLogger:
    _instance = None
    _lock = threading.Lock()
    
    # 로깅 관련 상수
    LOG_INTERVAL = 1000  # 로그 업데이트 최소 간격 (밀리초)
    UI_UPDATE_INTERVAL = 0.5  # UI 업데이트 간격 (초)
    MAX_BUFFER_SIZE = 100  # 최대 버퍼 크기

    def __new__(cls, emit_ui_error=None):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(BackupLogger, cls).__new__(cls)
                cls._instance._initialize_logger(emit_ui_error)
            elif emit_ui_error:
                cls._instance.emit_ui_error = emit_ui_error
            return cls._instance

    def _initialize_logger(self, emit_ui_error=None):
        """로거 초기화"""
        self.logger = logging.getLogger('BackupLogger')
        self.logger.setLevel(logging.DEBUG)
        self.logger.propagate = False  # 부모 로거로 전파 방지

        # 로그 디렉토리 설정 (Settings에서 가져옴)
        settings = Settings()
        log_settings = settings.get_logging_settings()
        log_dir = log_settings['log_dir']
        os.makedirs(log_dir, exist_ok=True)

        # 로그 파일 핸들러 설정
        log_file = os.path.join(log_dir, f'backup_{datetime.now().strftime("%Y%m%d")}.log')
        file_handler = RotatingFileHandler(log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 포맷터 설정
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        
        # 콘솔 핸들러 설정
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)

        # 핸들러 추가
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

        # 로그 버퍼 초기화
        self.log_buffer = LogBuffer(max_size=self.MAX_BUFFER_SIZE)
        self.last_log_time = 0
        self.last_ui_update = 0

        self.emit_ui_error = emit_ui_error

        # 초기 로그 메시지
        self.logger.info("=== 백업 로거 초기화 완료 ===")

    def _flush_handlers(self):
        """모든 핸들러의 버퍼를 강제로 디스크에 기록"""
        for handler in self.logger.handlers:
            handler.flush()
            if isinstance(handler, logging.FileHandler):
                os.fsync(handler.stream.fileno())

    def _should_update_ui(self):
        """UI 업데이트가 필요한지 확인"""
        current_time = time.time()
        if current_time - self.last_ui_update >= self.UI_UPDATE_INTERVAL:
            self.last_ui_update = current_time
            return True
        return False

    def debug(self, message):
        """디버그 레벨 로그"""
        self.logger.debug(message)
        self._flush_handlers()

    def info(self, message):
        """정보 레벨 로그"""
        self.logger.info(message)
        self._flush_handlers()

    def warning(self, message):
        """경고 레벨 로그"""
        self.logger.warning(message)
        self._flush_handlers()

    def error(self, message):
        """에러 레벨 로그 + UI 알림"""
        self.logger.error(message)
        self._flush_handlers()
        if self.emit_ui_error:
            self.emit_ui_error(message)

    def critical(self, message):
        """치명적 에러 레벨 로그 + UI 알림"""
        self.logger.critical(message)
        self._flush_handlers()
        if self.emit_ui_error:
            self.emit_ui_error(message)

    def log_rsync_output(self, process, cmd):
        """rsync 프로세스의 출력을 로깅"""
        try:
            stdout, stderr = process.communicate()
            if process.returncode == 0:
                self.info("동기화가 성공적으로 완료되었습니다.")
                return True
            elif process.returncode == 20:  # rsync was killed
                self.warning("동기화가 중단되었습니다.")
                return False
            else:
                error_msg = stderr.decode() if stderr else "알 수 없는 오류"
                self.error(
                    f"동기화 중 오류가 발생했습니다 (코드: {process.returncode}):\n"
                    f"명령어: {' '.join(cmd)}\n"
                    f"오류: {error_msg}"
                )
                return False
        except Exception as e:
            self.error(f"동기화 프로세스 처리 중 예외가 발생했습니다: {str(e)}")
            return False

    def log_progress(self, job_id, message, progress):
        """진행 상황을 로깅하고 UI 업데이트 필요 여부 반환"""
        current_time = time.time()
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        formatted_message = f"[{timestamp}] {message}"
        
        # 중요 메시지는 즉시 로깅
        if any(keyword in message.lower() for keyword in [
            "오류", "실패", "error", "failed", "완료", "시작", "초기화",
            "동기화 간격", "백업 모드"
        ]):
            self.info(f"[{job_id}] {message}")
            return True, formatted_message, progress
            
        # 일반 메시지는 버퍼에 추가
        if "파일 전송:" in message or "디렉토리 생성:" in message:
            self.log_buffer.add_message((current_time, formatted_message, progress))
            
            # UI 업데이트 필요 여부 확인
            if self._should_update_ui():
                messages = self.log_buffer.get_messages()
                if messages:
                    return True, messages, progress
        
        return False, None, None

    def log_rsync_command(self, command):
        """실행할 rsync 명령어를 로깅합니다.
        
        Args:
            command (list): rsync 명령어 리스트
        """
        if isinstance(command, list):
            command_str = ' '.join(command)
        else:
            command_str = str(command)
        self.debug(f"실행할 rsync 명령: {command_str}")

    def log_rsync_process(self, pid, action="시작"):
        """rsync 프로세스의 상태를 로깅합니다.
        
        Args:
            pid (int): 프로세스 ID
            action (str): 수행할 동작 (시작/종료 등)
        """
        self.info(f"rsync 프로세스 {action} (PID: {pid})") 