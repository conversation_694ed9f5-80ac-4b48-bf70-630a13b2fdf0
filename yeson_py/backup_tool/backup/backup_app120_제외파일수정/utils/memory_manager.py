import gc
import psutil
import os
import time
from typing import Optional
from .logger import BackupLogger

class MemoryManager:
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not MemoryManager._initialized:
            self.logger = BackupLogger()
            self.process = psutil.Process(os.getpid())
            self.memory_threshold = 0.8  # 80% 메모리 사용량 임계값
            self.last_cleanup_time = 0
            self.cleanup_interval = 300  # 5분마다 정리
            MemoryManager._initialized = True

    def get_memory_usage(self) -> float:
        """현재 메모리 사용량을 백분율로 반환합니다."""
        try:
            return self.process.memory_percent()
        except Exception as e:
            self.logger.error(f"메모리 사용량 확인 중 오류: {e}")
            return 0.0

    def should_cleanup(self) -> bool:
        """메모리 정리가 필요한지 확인합니다."""
        current_time = time.time()
        if current_time - self.last_cleanup_time < self.cleanup_interval:
            return False
        
        memory_usage = self.get_memory_usage()
        return memory_usage > self.memory_threshold

    def cleanup(self, force: bool = False) -> None:
        """메모리 정리를 수행합니다."""
        if not force and not self.should_cleanup():
            return

        try:
            # 가비지 컬렉션 실행
            collected = gc.collect()
            self.logger.debug(f"가비지 컬렉션 완료: {collected} 객체 정리됨")

            # 메모리 사용량 로깅
            memory_usage = self.get_memory_usage()
            self.logger.info(f"현재 메모리 사용량: {memory_usage:.1f}%")

            self.last_cleanup_time = time.time()
        except Exception as e:
            self.logger.error(f"메모리 정리 중 오류: {e}")

    def get_memory_info(self) -> dict:
        """메모리 사용 정보를 반환합니다."""
        try:
            memory_info = self.process.memory_info()
            return {
                'rss': memory_info.rss / 1024 / 1024,  # MB
                'vms': memory_info.vms / 1024 / 1024,  # MB
                'percent': self.process.memory_percent()
            }
        except Exception as e:
            self.logger.error(f"메모리 정보 조회 중 오류: {e}")
            return {'rss': 0, 'vms': 0, 'percent': 0}

    def is_system_memory_low(self, min_available_mb: int = None) -> bool:
        """시스템 전체 가용 메모리가 임계값 미만인지 확인"""
        import psutil
        min_mb = min_available_mb if min_available_mb is not None else 1000  # 기본 1GB
        available_mb = psutil.virtual_memory().available / (1024 * 1024)
        return available_mb < min_mb 