import os
import json
from typing import Dict, Any
from pathlib import Path

class Settings:
    _instance = None
    _initialized = False
    _default_settings = {
        'performance': {
            'max_cpu_percent': 50,
            'cpu_check_interval': 0.2,
            'cpu_throttle_time': 0.1,
            'max_processes': 2,
            'realtime_queue_size': 5,
            'realtime_process_delay': 0.05,
            'batch_size': 1000,
            'batch_interval': 60,
            'db_cleanup_days': 30,
            'max_retry_count': 3,
            'retry_delay': 300
        },
        'backup': {
            'default_sync_interval': 300,
            'max_sync_interval': 86400,
            'min_sync_interval': 0,
            'rsync_timeout': 60,
            'backup_retention_days': 30
        },
        'logging': {
            'max_log_size_mb': 10,
            'max_log_files': 5,
            'log_level': 'INFO',
            'log_dir': os.path.join(str(Path.home()), 'Downloads', 'backup_logs'),
            'log_filename_format': 'backup_{date}.log',
            'log_date_format': '%Y%m%d'
        }
    }

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not Settings._initialized:
            self.settings = self._default_settings.copy()
            Settings._initialized = True

    def get(self, section: str, key: str, default: Any = None) -> Any:
        """설정 값을 가져옵니다."""
        try:
            return self.settings[section][key]
        except KeyError:
            return default

    def set(self, section: str, key: str, value: Any) -> None:
        """설정 값을 변경합니다."""
        if section not in self.settings:
            self.settings[section] = {}
        self.settings[section][key] = value

    def get_performance_settings(self) -> Dict[str, Any]:
        """성능 관련 설정을 가져옵니다."""
        return self.settings['performance']

    def get_backup_settings(self) -> Dict[str, Any]:
        """백업 관련 설정을 가져옵니다."""
        return self.settings['backup']

    def get_logging_settings(self) -> Dict[str, Any]:
        """로깅 관련 설정을 가져옵니다."""
        return self.settings['logging']

    def get_app_data_dir(self) -> str:
        """애플리케이션 데이터 디렉토리 경로를 반환합니다."""
        app_data_dir = os.path.join(str(Path.home()), '.backup_tool')
        os.makedirs(app_data_dir, exist_ok=True)
        return app_data_dir 