import sys
import os
import signal
from PyQt5.QtCore import Qt, QTimer, QThread
from PyQt5.QtWidgets import QApplication, QMessageBox
import threading
from queue import Queue
import psutil
import subprocess
import time
import queue
import gc
from watchdog.observers import Observer
import random
import json

# Add the application root directory to Python path
app_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, app_root)

from backup_app.ui.widgets import ValidationApp
from backup_app.ui.styles import StyleManager
from backup_app.utils.logger import BackupLogger
from backup_app.utils.memory_manager import MemoryManager
from backup_app.config.settings import Settings
from backup_app.utils.rsync_config import get_rsync_command
from backup_app.utils.admin_auth import AdminAuthManager
from backup_app.utils.metadata_db import MetadataDB

def safe_terminate_process(process, logger=None):
    try:
        if process.poll() is None:
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                if logger:
                    logger.warning(f"terminate()로 종료 실패, kill() 시도: {process.pid}")
                process.kill()
                try:
                    process.wait(timeout=2)
                except subprocess.TimeoutExpired:
                    if logger:
                        logger.error(f"kill() 후에도 종료 실패: {process.pid}")
    except Exception as e:
        if logger:
            logger.error(f"프로세스 종료 중 예외: {e}")

class RsyncProcessManager:
    """rsync 프로세스의 동시 실행을 관리하는 클래스"""
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
            return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.settings = Settings()
            self.logger = BackupLogger()
            perf_settings = self.settings.get_performance_settings()
            self.max_processes = perf_settings['max_processes']
            self.current_processes = set()
            self.process_lock = threading.Lock()
            self.process_queue = Queue()
            self.realtime_queue = Queue(maxsize=perf_settings['realtime_queue_size'])
            self.is_realtime_event = False
            self.initialized = True

    def kill_rsync_processes(self, job_id=None):
        """Terminate rsync processes immediately and collect child process termination status."""
        with self.process_lock:
            # 1. Terminate currently managed processes
            for process in list(self.current_processes):
                safe_terminate_process(process, self.logger)
                self.current_processes.discard(process)

            # 2. Terminate rsync processes running in the system
            try:
                # First attempt with SIGTERM
                for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'ppid']):
                    try:
                        if proc.info['name'] == 'rsync':
                            cmdline = proc.info['cmdline']
                            if job_id is None or (cmdline and any(job_id in arg for arg in cmdline)):
                                if proc.info['ppid'] != 1:  # Not a child of init process
                                    try:
                                        pgid = os.getpgid(proc.pid)
                                        os.killpg(pgid, signal.SIGTERM)
                                        self.logger.debug(f"Attempting to terminate rsync process group (PGID: {pgid})")
                                    except (ProcessLookupError, PermissionError):
                                        proc.terminate()
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

                # Wait 0.5 seconds before checking remaining processes
                time.sleep(0.5)
                
                # Force kill remaining processes with SIGKILL
                for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'ppid']):
                    try:
                        if proc.info['name'] == 'rsync':
                            cmdline = proc.info['cmdline']
                            if job_id is None or (cmdline and any(job_id in arg for arg in cmdline)):
                                if proc.info['ppid'] != 1:
                                    try:
                                        pgid = os.getpgid(proc.pid)
                                        os.killpg(pgid, signal.SIGKILL)
                                        self.logger.warning(f"Force killing rsync process group (PGID: {pgid})")
                                    except (ProcessLookupError, PermissionError):
                                        proc.kill()
                                    self.logger.warning(f"Force killing rsync process (PID: {proc.pid})")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

            except Exception as e:
                self.logger.error(f"Error while searching/terminating rsync processes: {e}")

            # 3. Clean up zombie processes
            try:
                while True:
                    pid, status = os.waitpid(-1, os.WNOHANG)
                    if pid == 0:  # No more zombie processes
                        break
                    self.logger.debug(f"Cleaned up zombie process (PID: {pid}, Status: {status})")
            except ChildProcessError:
                pass

    def start_rsync(self, command):
        """Manage rsync process startup"""
        with self.process_lock:
            self._cleanup_finished_processes()
            
            if len(self.current_processes) >= self.max_processes:
                self.logger.warning(f"Maximum process limit reached ({self.max_processes}). Adding to queue.")
                self.process_queue.put(command)
                return None
            
            try:
                process = subprocess.Popen(
                    command,
                    shell=True,
                    preexec_fn=os.setsid  # Create process group
                )
                self.current_processes.add(process)
                self.logger.info(f"Started rsync process (PID: {process.pid})")
                return process
            except Exception as e:
                self.logger.error(f"Failed to start rsync process: {e}")
                return None

    def _cleanup_finished_processes(self):
        """종료된 프로세스 정리 및 대기 중인 프로세스 시작"""
        finished = set()
        for process in self.current_processes:
            if process.poll() is not None:
                finished.add(process)
        self.current_processes -= finished
        while not self.process_queue.empty() and len(self.current_processes) < self.max_processes:
            command = self.process_queue.get()
            self.start_rsync(command)

    def get_running_count(self):
        """현재 실행 중인 rsync 프로세스 수 반환"""
        with self.process_lock:
            self._cleanup_finished_processes()
            return len(self.current_processes)

def run_rsync_with_logging(cmd, logger, emit_progress=None):
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        stdout, stderr = process.communicate()
        if process.returncode == 0:
            logger.info(f"rsync 성공: {stdout}")
            if emit_progress:
                emit_progress("동기화 성공", 100)
            return True
        else:
            logger.error(f"rsync 실패 (코드 {process.returncode}): {stderr}")
            if emit_progress:
                emit_progress(f"동기화 실패: {stderr}", -1)
            return False
    except Exception as e:
        logger.error(f"rsync 실행 중 예외: {e}")
        if emit_progress:
            emit_progress(f"rsync 실행 중 예외: {e}", -1)
        return False

def kill_all_rsync_processes():
    """모든 rsync 프로세스를 psutil로 강제 종료"""
    # 1. terminate 시도
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'rsync' in proc.info['name'] or \
               (proc.info['cmdline'] and any('rsync' in arg for arg in proc.info['cmdline'])):
                proc.terminate()
        except Exception:
            continue
    time.sleep(0.5)
    # 2. 살아있는 rsync 강제 kill
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'rsync' in proc.info['name'] or \
               (proc.info['cmdline'] and any('rsync' in arg for arg in proc.info['cmdline'])):
                proc.kill()
        except Exception:
            continue

def cleanup_processes():
    """Force terminate all rsync processes and clean up zombie processes."""
    logger = BackupLogger()
    logger.info("=== Starting rsync process cleanup ===")
    
    # Terminate all rsync processes through RsyncProcessManager
    try:
        RsyncProcessManager().kill_rsync_processes()
    except Exception as e:
        logger.error(f"Error while terminating processes through RsyncProcessManager: {e}")
        # Only attempt kill_all_rsync_processes if RsyncProcessManager fails
        kill_all_rsync_processes()
    
    logger.info("=== Completed rsync process cleanup ===")

def main():
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = QApplication(sys.argv)
    
    # 라이트 테마 적용
    StyleManager.apply_style(app, theme="light")
    
    # 관리자 권한 확인 (UI 초기화 전에 수행)
    admin_auth = AdminAuthManager()
    if not admin_auth.check_admin_auth():
        QMessageBox.critical(None, "오류", "관리자 권한이 필요합니다. 앱을 종료합니다.")
        sys.exit(1)
    
    # 관리자 권한 확인 후 메인 UI 초기화
    window = ValidationApp()
    window.show()

    # 앱 종료 시 정리 작업 실행 (메인 이벤트 루프에서)
    def safe_cleanup():
        logger = BackupLogger()
        logger.info("=== Starting application shutdown ===")
        
        # Reset admin authentication
        admin_auth.reset_auth()
        
        # 1. Immediately terminate all rsync processes
        try:
            RsyncProcessManager().kill_rsync_processes()
        except Exception as e:
            logger.error(f"Error while terminating rsync processes: {e}")
        
        # 2. Additional cleanup
        cleanup_processes()
        
        # 3. UI cleanup
        window.cleanup_before_exit()
        
        logger.info("=== Completed application shutdown ===")
    
    app.aboutToQuit.connect(safe_cleanup)
    
    # SIGINT (Ctrl+C) 핸들러 설정
    def signal_handler(signum, frame):
        logger = BackupLogger()
        logger.info("=== 종료 시그널 수신 ===")
        safe_cleanup()
        QTimer.singleShot(100, app.quit)  # cleanup 완료 후 종료
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()