"""
프로젝트 관리를 위한 매니저 클래스
"""
from PyQt5.QtCore import QObject, pyqtSignal

class ProjectManager(QObject):
    # 프로젝트 목록이 변경될 때 발생하는 시그널
    projectsChanged = pyqtSignal()
    
    def __init__(self):
        """프로젝트 매니저를 초기화합니다."""
        super().__init__()
        self.projects = {}  # 프로젝트 목록을 저장할 딕셔너리
        self.load_projects()  # 저장된 프로젝트 정보 로드
    
    def load_projects(self):
        """저장된 프로젝트 정보를 로드합니다."""
        # 기본 작품 목록 설정
        default_projects = {
            "BB": "/System/Volumes/Data/usadata1/BB",
            "GN": "/System/Volumes/Data/usadata1/GN",
            "BM": "/System/Volumes/Data/usadata1/BM",
            "KOTH": "/System/Volumes/Data/usadata1/KOTH",
            "Test": "/System/Volumes/Data/usadata1/Test",
            "Yeson_DANG": "/System/Volumes/Data/usadata1/Yeson_DANG",
            "Yeson_Test": "/System/Volumes/Data/usadata1/Yeson_Test",
            "Yeson_Test_4K": "/System/Volumes/Data/usadata1/Yeson_Test_4K"
        }
        
        # 기본 작품 목록을 projects에 추가
        self.projects.update(default_projects)
        self.projectsChanged.emit()  # 시그널 발생
    
    def save_projects(self):
        """프로젝트 정보를 저장합니다."""
        # TODO: 프로젝트 정보를 파일에 저장하는 기능 구현
        pass
    
    def add_project(self, project_name, project_path):
        """새로운 프로젝트를 추가합니다."""
        if project_name not in self.projects:
            self.projects[project_name] = project_path
            self.save_projects()
            self.projectsChanged.emit()  # 시그널 발생
            return True
        return False
    
    def remove_project(self, project_name):
        """프로젝트를 제거합니다."""
        if project_name in self.projects:
            del self.projects[project_name]
            self.save_projects()
            self.projectsChanged.emit()  # 시그널 발생
            return True
        return False
    
    def get_project_path(self, project_name):
        """프로젝트 경로를 반환합니다."""
        return self.projects.get(project_name)
    
    def get_projects(self):
        """모든 프로젝트 목록을 반환합니다."""
        return list(self.projects.keys()) 