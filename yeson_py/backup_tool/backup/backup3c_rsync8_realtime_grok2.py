import sys
import os
import subprocess
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Border, Side, Alignment
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QPushButton, QLineEdit,
                            QHBoxLayout, QComboBox, QLabel, QTreeWidget, QTreeWidgetItem, QStyle,
                            QFrame, QSplitter, QStatusBar, QGraphicsDropShadowEffect, QHeaderView,
                            QStyleFactory, QSizePolicy, QSpacerItem, QScrollArea, QFileDialog,
                            QTextEdit, QProgressBar)
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect, QSize, QThread, pyqtSignal
from PyQt5.QtGui import QColor, QPalette, QFont, QIcon, QLinearGradient, QBrush, QPixmap, QKeySequence
from PyQt5.QtWidgets import QShortcut
import shutil
import time
import threading
import queue
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from concurrent.futures import ThreadPoolExecutor
import logging  # logging 모듈 추가

# 로깅 설정
logging.basicConfig(
    level=logging.DEBUG,  # 기본 레벨을 DEBUG로 설정
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler()  # 콘솔 출력
    ]
)
logger = logging.getLogger(__name__)

class StyleManager:
    DARK_THEME = {
        "BG": "#1E2526", "LIGHT_BG": "#2A3439", "ACCENT": "#D4AF37", "TEXT": "#E8ECEF",
        "MEDIUM_GRAY": "#A9B1B3", "DARK_ACCENT": "#3A4A50"
    }
    LIGHT_THEME = {
        "BG": "#F5F6F5", "LIGHT_BG": "#FFFFFF", "ACCENT": "#F4A261", "TEXT": "#2A3439",
        "MEDIUM_GRAY": "#6B7280", "DARK_ACCENT": "#E5E7EB"
    }

    @staticmethod
    def apply_style(app, theme="dark"):
        colors = StyleManager.DARK_THEME if theme == "dark" else StyleManager.LIGHT_THEME
        app.setStyle(QStyleFactory.create("Fusion"))
        font = QFont("Helvetica", 10)
        app.setFont(font)
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(colors["BG"]))
        palette.setColor(QPalette.WindowText, QColor(colors["TEXT"]))
        palette.setColor(QPalette.Base, QColor(colors["LIGHT_BG"]))
        palette.setColor(QPalette.AlternateBase, QColor(colors["DARK_ACCENT"]))
        palette.setColor(QPalette.ToolTipBase, QColor(colors["LIGHT_BG"]))
        palette.setColor(QPalette.ToolTipText, QColor(colors["TEXT"]))
        palette.setColor(QPalette.Text, QColor(colors["TEXT"]))
        palette.setColor(QPalette.Button, QColor(colors["DARK_ACCENT"]))
        palette.setColor(QPalette.ButtonText, QColor(colors["TEXT"]))
        palette.setColor(QPalette.BrightText, QColor(colors["ACCENT"]))
        palette.setColor(QPalette.Link, QColor(colors["ACCENT"]))
        palette.setColor(QPalette.Highlight, QColor(colors["ACCENT"]))
        palette.setColor(QPalette.HighlightedText, QColor(colors["BG"]))
        app.setPalette(palette)

        app.setStyleSheet(f"""
            QMainWindow {{ background: {colors["BG"]}; }}
            QWidget {{ background: {colors["BG"]}; }}
            QLabel {{ color: {colors["TEXT"]}; font-size: 12px; font-weight: 400; padding: 4px; }}
            QLabel#headerLabel {{ font-size: 14px; font-weight: bold; color: {colors["ACCENT"]}; padding: 8px; }}
            QComboBox {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; padding: 4px 8px; font-size: 12px; background: {colors["LIGHT_BG"]}; color: {colors["TEXT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; }}
            QComboBox::drop-down {{ subcontrol-origin: padding; subcontrol-position: right center; width: 20px; border-left: 1px solid {colors["DARK_ACCENT"]}; border-top-right-radius: 4px; border-bottom-right-radius: 4px; }}
            QComboBox QAbstractItemView {{ font-size: 12px; background: {colors["LIGHT_BG"]}; border: 1px solid {colors["DARK_ACCENT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; padding: 4px; }}
            QLineEdit {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; padding: 4px 8px; font-size: 12px; background: {colors["LIGHT_BG"]}; color: {colors["TEXT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; }}
            QLineEdit:read-only {{ background: {colors["DARK_ACCENT"]}; border: 1px solid {colors["DARK_ACCENT"]}; }}
            QPushButton {{ background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {colors["ACCENT"]}, stop:1 {StyleManager.adjust_brightness(colors["ACCENT"], -20)}); color: {colors["BG"]}; border: none; border-radius: 4px; padding: 6px 12px; font-size: 12px; font-weight: bold; }}
            QPushButton:hover {{ background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {StyleManager.adjust_brightness(colors["ACCENT"], 20)}, stop:1 {colors["ACCENT"]}); }}
            QPushButton:pressed {{ background-color: {StyleManager.adjust_brightness(colors["ACCENT"], -40)}; }}
            QPushButton:disabled {{ background-color: {colors["MEDIUM_GRAY"]}; color: {colors["DARK_ACCENT"]}; }}
            QTreeWidget {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; font-size: 12px; background: {colors["LIGHT_BG"]}; alternate-background-color: {colors["DARK_ACCENT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; padding: 4px; }}
            QTreeWidget::item {{ padding: 4px; border-bottom: 1px solid {colors["DARK_ACCENT"]}; }}
            QTreeWidget::item:selected {{ background-color: {colors["ACCENT"]}; color: {colors["BG"]}; }}
            QHeaderView::section {{ background-color: {colors["DARK_ACCENT"]}; color: {colors["TEXT"]}; padding: 4px; border: none; border-right: 1px solid {colors["MEDIUM_GRAY"]}; border-bottom: 1px solid {colors["MEDIUM_GRAY"]}; font-size: 12px; font-weight: bold; }}
            QStatusBar {{ background-color: {colors["DARK_ACCENT"]}; color: {colors["TEXT"]}; font-size: 12px; padding: 4px 8px; }}
            QFrame#separator {{ background-color: {colors["MEDIUM_GRAY"]}; max-height: 1px; margin: 6px 0px; }}
            QToolTip {{ background-color: {colors["LIGHT_BG"]}; color: {colors["TEXT"]}; font-size: 12px; border: 1px solid {colors["DARK_ACCENT"]}; padding: 4px; }}
            CardWidget {{ background: {colors["LIGHT_BG"]}; border-radius: 4px; border: 1px solid {colors["DARK_ACCENT"]}; }}
            QTextEdit {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; background: {colors["LIGHT_BG"]}; color: {colors["TEXT"]}; padding: 4px; }}
            QProgressBar {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; background: {colors["LIGHT_BG"]}; text-align: center; }}
            QProgressBar::chunk {{ background-color: {colors["ACCENT"]}; border-radius: 2px; }}
        """)

    @staticmethod
    def adjust_brightness(color_hex, amount):
        color = QColor(color_hex)
        r, g, b = color.red(), color.green(), color.blue()
        r = max(0, min(255, r + amount))
        g = max(0, min(255, g + amount))
        b = max(0, min(255, b + amount))
        return f"#{r:02x}{g:02x}{b:02x}"

class AnimationHelper:
    @staticmethod
    def fade_in(widget, duration=300):
        widget.show()

    @staticmethod
    def add_drop_shadow(widget, radius=10, x_offset=2, y_offset=2, color=QColor(0, 0, 0, 80)):
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(radius)
        shadow.setXOffset(x_offset)
        shadow.setYOffset(y_offset)
        shadow.setColor(color)
        widget.setGraphicsEffect(shadow)

class ChartTreeWidget(QTreeWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAlternatingRowColors(True)
        self.setAnimated(True)
        self.setHeaderLabels(['씬 이름', 'Frames', 'FEET', '백업상태'])
        self.setSelectionMode(QTreeWidget.ExtendedSelection)
        header = self.header()
        header.setSectionResizeMode(QHeaderView.Interactive)
        header.setSectionsMovable(False)
        header.setStretchLastSection(False)
        self.setColumnWidth(0, 250)
        self.setColumnWidth(1, 80)
        self.setColumnWidth(2, 80)
        self.setColumnWidth(3, 80)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        AnimationHelper.add_drop_shadow(self)
        self.backup_status = {}

    def set_scene_backup_status(self, scene_name, status):
        self.backup_status[scene_name] = status
        for i in range(self.topLevelItemCount()):
            item = self.topLevelItem(i)
            if item.text(0) == scene_name:
                item.setText(3, status)
                if status == "백업완료":
                    for col in range(4):
                        item.setBackground(col, QColor(StyleManager.DARK_THEME["LIGHT_BG"]))
                        item.setForeground(col, QColor(StyleManager.DARK_THEME["TEXT"]))
                break
        self.viewport().update()

    def get_scene_backup_status(self, scene_name):
        return self.backup_status.get(scene_name, "")

    def reset_backup_status(self):
        self.backup_status.clear()
        for i in range(self.topLevelItemCount()):
            item = self.topLevelItem(i)
            item.setText(3, "")
            for col in range(4):
                item.setBackground(col, QColor(StyleManager.DARK_THEME["LIGHT_BG"]))
                item.setForeground(col, QColor(StyleManager.DARK_THEME["TEXT"]))

class CardWidget(QFrame):
    def __init__(self, title=None, parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.StyledPanel)
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(10, 10, 10, 10)
        self.layout.setSpacing(8)
        if title:
            self.title = QLabel(title)
            self.title.setObjectName("headerLabel")
            self.layout.addWidget(self.title)
            separator = QFrame()
            separator.setFrameShape(QFrame.HLine)
            separator.setFrameShadow(QFrame.Sunken)
            separator.setObjectName("separator")
            self.layout.addWidget(separator)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        AnimationHelper.add_drop_shadow(self)

class ShowSelectionWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("작품 선택", parent)
        COMBOBOX_WIDTH = 200  # 모든 QComboBox의 가로 사이즈 통일
        self.show_layout = QHBoxLayout()
        self.show_layout.setSpacing(8)
        show_label = QLabel("작품:")
        self.show_layout.addWidget(show_label)
        self.show_combo = QComboBox()
        self.show_combo.addItem("작품 선택")
        self.show_combo.addItems(["BB", "GN", "BM", "KOTH", "Test", "Yeson_DANG", "Yeson_Test", "Yeson_Test_4K"])
        self.show_combo.setFixedWidth(COMBOBOX_WIDTH)
        self.show_layout.addWidget(self.show_combo)
        self.show_backup_btn = QPushButton("백업")
        self.show_backup_btn.setCursor(Qt.PointingHandCursor)
        self.show_layout.addWidget(self.show_backup_btn)
        self.show_layout.addStretch()
        self.layout.addLayout(self.show_layout)

        self.season_layout = QHBoxLayout()
        self.season_layout.setSpacing(8)
        season_label = QLabel("시즌:")
        self.season_layout.addWidget(season_label)
        self.season_combo = QComboBox()
        self.season_combo.addItem("시즌 선택")
        self.season_combo.setEnabled(False)
        self.season_combo.setFixedWidth(COMBOBOX_WIDTH)
        self.season_layout.addWidget(self.season_combo)
        self.season_backup_btn = QPushButton("백업")
        self.season_backup_btn.setCursor(Qt.PointingHandCursor)
        self.season_layout.addWidget(self.season_backup_btn)
        self.season_layout.addStretch()
        self.layout.addLayout(self.season_layout)

        self.episode_layout = QHBoxLayout()
        self.episode_layout.setSpacing(8)
        episode_label = QLabel("화수:")
        self.episode_layout.addWidget(episode_label)
        self.episode_combo = QComboBox()
        self.episode_combo.addItem("화수 선택")
        self.episode_combo.setEnabled(False)
        self.episode_combo.setFixedWidth(COMBOBOX_WIDTH)
        self.episode_layout.addWidget(self.episode_combo)
        self.episode_backup_btn = QPushButton("백업")
        self.episode_backup_btn.setCursor(Qt.PointingHandCursor)
        self.episode_layout.addWidget(self.episode_backup_btn)
        self.episode_layout.addStretch()
        self.layout.addLayout(self.episode_layout)

class SummaryWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("에피소드 정보", parent)
        summary_layout = QHBoxLayout()
        frame_layout = QVBoxLayout()
        frame_title = QLabel("총 프레임 수:")
        frame_title.setAlignment(Qt.AlignCenter)
        frame_layout.addWidget(frame_title)
        self.total_frames_display = QLineEdit()
        self.total_frames_display.setAlignment(Qt.AlignCenter)
        self.total_frames_display.setReadOnly(True)
        self.total_frames_display.setMinimumWidth(80)
        frame_layout.addWidget(self.total_frames_display)
        summary_layout.addLayout(frame_layout)
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("separator")
        summary_layout.addWidget(separator)
        feet_layout = QVBoxLayout()
        feet_title = QLabel("총 FEET:")
        feet_title.setAlignment(Qt.AlignCenter)
        feet_layout.addWidget(feet_title)
        self.episode_feet_display = QLineEdit()
        self.episode_feet_display.setAlignment(Qt.AlignCenter)
        self.episode_feet_display.setReadOnly(True)
        self.episode_feet_display.setMinimumWidth(80)
        feet_layout.addWidget(self.episode_feet_display)
        summary_layout.addLayout(feet_layout)
        self.layout.addLayout(summary_layout)

class CalculationWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("선택 씬 계산", parent)
        calc_layout = QHBoxLayout()
        self.calc_button = QPushButton("선택 씬 계산")
        self.calc_button.setCursor(Qt.PointingHandCursor)
        calc_layout.addWidget(self.calc_button)
        calc_layout.addSpacing(10)
        feet_label = QLabel("선택된 씬 총 FEET:")
        calc_layout.addWidget(feet_label)
        self.total_feet_display = QLineEdit()
        self.total_feet_display.setAlignment(Qt.AlignCenter)
        self.total_feet_display.setReadOnly(True)
        self.total_feet_display.setMinimumWidth(100)
        calc_layout.addWidget(self.total_feet_display)
        calc_layout.addStretch()
        self.layout.addLayout(calc_layout)

class SceneListWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("씬 목록", parent)
        self.scene_tree = ChartTreeWidget()
        self.layout.addWidget(self.scene_tree)
        button_layout = QHBoxLayout()
        self.export_button = QPushButton("엑셀로 내보내기")
        self.export_button.setCursor(Qt.PointingHandCursor)
        button_layout.addWidget(self.export_button)
        button_layout.addStretch()
        selection_label = QLabel("Tip: 여러 씬을 선택하려면 Ctrl 또는 Shift 키를 사용하세요.")
        selection_label.setAlignment(Qt.AlignRight)
        selection_label.setStyleSheet("font-style: italic;")
        button_layout.addWidget(selection_label)
        self.layout.addLayout(button_layout)

class BackupStatusWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("백업 진행 상황", parent)
        self.setVisible(False)
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setMinimumHeight(100)
        self.layout.addWidget(self.log_display)
        font = QFont("Helvetica", 16)
        self.log_display.setFont(font)
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.layout.addWidget(self.progress_bar)
        self.last_update_time = 0

    def append_log(self, message, level=logging.INFO):
        """로그 레벨에 따라 메시지 추가"""
        logger.log(level, message)  # 콘솔에 로그 출력
        if message and message.strip():
            timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
            level_str = logging.getLevelName(level)
            formatted_message = f"{timestamp} [{level_str}] {message}"
            self.log_display.append(formatted_message)
            self.log_display.ensureCursorVisible()

    def set_progress(self, value):
        if 0 <= value <= 100:
            self.progress_bar.setValue(value)
            logger.debug(f"Progress updated to {value}%")

    def reset(self):
        self.log_display.clear()
        self.progress_bar.setValue(0)
        self.setVisible(False)
        logger.info("Backup status widget reset")

class BackupInitializer(QThread):
    progress_signal = pyqtSignal(str, int)

    def __init__(self, source_path, dest_path, max_workers=4):
        super().__init__()
        self.source_path = source_path
        self.dest_path = dest_path
        self.max_workers = min(max_workers, os.cpu_count() or 4)
        self.running = True
        self.last_update_time = 0
        self.update_interval = 2.0

    def get_subdirs(self, path):
        subdirs = [os.path.join(path, d) for d in os.listdir(path) if os.path.isdir(os.path.join(path, d))]
        logger.debug(f"Found {len(subdirs)} subdirectories in {path}")
        return subdirs

    def copy_directory(self, src_dir, dest_dir):
        logger.info(f"Starting copy of directory: {src_dir} to {dest_dir}")
        large_files = [f for f in os.listdir(src_dir) if f.endswith('.mov') and os.path.getsize(os.path.join(src_dir, f)) > 10 * 1024 * 1024]
        small_files = [f for f in os.listdir(src_dir) if f not in large_files]
        rsync_base_cmd = ['rsync', '-ahi', '--progress', '--bwlimit=5000']

        if large_files:
            rsync_cmd = rsync_base_cmd + ['--include=*.mov', '--exclude=*', src_dir + '/', dest_dir]
            logger.debug(f"Executing rsync for large files: {' '.join(rsync_cmd)}")
            process = subprocess.Popen(rsync_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            while process.poll() is None and self.running:
                line = process.stdout.readline().strip()
                if line and "speedup" not in line:
                    current_time = time.time()
                    if current_time - self.last_update_time >= self.update_interval:
                        self.progress_signal.emit(f"Copying large files: {os.path.basename(src_dir)}", -1)
                        self.last_update_time = current_time
            stdout, stderr = process.communicate()
            if process.returncode != 0:
                logger.error(f"rsync failed for large files: {stderr}")

        if small_files:
            rsync_cmd = rsync_base_cmd + ['--exclude=*.mov', src_dir + '/', dest_dir]
            logger.debug(f"Executing rsync for small files: {' '.join(rsync_cmd)}")
            process = subprocess.Popen(rsync_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            while process.poll() is None and self.running:
                line = process.stdout.readline().strip()
                if line and "speedup" not in line:
                    current_time = time.time()
                    if current_time - self.last_update_time >= self.update_interval:
                        self.progress_signal.emit(f"Copying small files: {os.path.basename(src_dir)}", -1)
                        self.last_update_time = current_time
            stdout, stderr = process.communicate()
            if process.returncode != 0:
                logger.error(f"rsync failed for small files: {stderr}")

    def run(self):
        try:
            subdirs = self.get_subdirs(self.source_path) or [self.source_path]
            total_dirs = len(subdirs)
            self.progress_signal.emit(f"Starting parallel initial sync: {total_dirs} directories", 0)
            logger.info(f"Initial backup started with {total_dirs} directories")
            completed = 0

            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = {executor.submit(self.copy_directory, src_dir, os.path.join(self.dest_path, os.path.basename(src_dir))): src_dir for src_dir in subdirs}
                for future in futures:
                    if not self.running:
                        logger.warning("BackupInitializer stopped by user")
                        break
                    future.result()
                    completed += 1
                    progress = min(100, int(100 * completed / total_dirs))
                    current_time = time.time()
                    if current_time - self.last_update_time >= self.update_interval:
                        self.progress_signal.emit("", progress)
                        logger.debug(f"Progress: {progress}% ({completed}/{total_dirs})")
                        self.last_update_time = current_time

            if self.running:
                self.progress_signal.emit("Initial sync completed", 100)
                logger.info("Initial backup completed successfully")
        except Exception as e:
            self.progress_signal.emit(f"Error during initial sync: {str(e)}", -1)
            logger.error(f"Initial backup failed: {str(e)}", exc_info=True)

    def stop(self):
        self.running = False
        logger.info("BackupInitializer stopping")

class BackupEventHandler(FileSystemEventHandler):
    def __init__(self, backup_queue, source_path, dest_path, status_widget, is_source=True):
        super().__init__()
        self.backup_queue = backup_queue
        self.source_path = source_path
        self.dest_path = dest_path
        self.status_widget = status_widget
        self.is_source = is_source
        self.pending_tasks = set()
        self.debounce_timer = {}
        self.debounce_lock = threading.Lock()
        self.debounce_interval = 1.0

    def on_created(self, event):
        if not event.is_directory:
            logger.debug(f"{'Source' if self.is_source else 'Dest'} file created: {event.src_path}")
            self._debounce_event(event.src_path, "created", False)

    def on_modified(self, event):
        if not event.is_directory:
            logger.debug(f"{'Source' if self.is_source else 'Dest'} file modified: {event.src_path}")
            self._debounce_event(event.src_path, "modified", False)

    def on_deleted(self, event):
        logger.debug(f"{'Source' if self.is_source else 'Dest'} {'directory' if event.is_directory else 'file'} deleted: {event.src_path}")
        self._debounce_event(event.src_path, "deleted", event.is_directory)

    def _debounce_event(self, src_path, event_type, is_directory):
        with self.debounce_lock:
            key = (src_path, event_type)
            if key in self.debounce_timer:
                self.debounce_timer[key].cancel()
            timer = threading.Timer(self.debounce_interval, self.queue_backup, args=[src_path, event_type, is_directory])
            timer.daemon = True
            self.debounce_timer[key] = timer
            timer.start()
            logger.debug(f"Debounced event: {event_type} for {src_path}")

    def queue_backup(self, src_path, event_type, is_directory=False):
        try:
            rel_path = os.path.relpath(src_path, self.source_path if self.is_source else self.dest_path)
            task_key = (src_path, event_type)
            if self.is_source:
                # 소스 이벤트: 생성/수정/삭제를 목적지에 반영
                if event_type in ["created", "modified"] and os.path.exists(src_path):
                    dest_path = os.path.join(self.dest_path, rel_path)
                    if task_key not in self.pending_tasks:
                        self.backup_queue.put((src_path, dest_path, event_type, is_directory))
                        self.pending_tasks.add(task_key)
                        logger.info(f"Queued backup from source: {event_type} {src_path} -> {dest_path}")
                elif event_type == "deleted":
                    dest_path = os.path.join(self.dest_path, rel_path)
                    if task_key not in self.pending_tasks:
                        self.backup_queue.put((src_path, dest_path, "deleted", is_directory))
                        self.pending_tasks.add(task_key)
                        logger.info(f"Queued deletion from source: {src_path} -> {dest_path}")
            else:
                # 목적지 이벤트: 삭제 시 소스에서 복원
                if event_type == "deleted":
                    source_path = os.path.join(self.source_path, rel_path)
                    dest_path = os.path.join(self.dest_path, rel_path)
                    if os.path.exists(source_path) and task_key not in self.pending_tasks:
                        self.backup_queue.put((source_path, dest_path, "created", is_directory))
                        self.pending_tasks.add(task_key)
                        logger.info(f"Queued restoration from dest deletion: {source_path} -> {dest_path}")
        except Exception as e:
            logger.error(f"Error queuing backup for {src_path}: {str(e)}")

    def task_completed(self, src_path, event_type):
        self.pending_tasks.discard((src_path, event_type))
        logger.debug(f"Task completed: {event_type} {src_path}")

class BackupWorker(QThread):
    update_signal = pyqtSignal(str, int)
    scene_backup_status_signal = pyqtSignal(str, str)

    def __init__(self, backup_queue, source_handler, dest_handler, status_widget, scene_tree=None, source_path=None):
        super().__init__()
        self.running = True
        self.current_set_active = False
        self.backup_queue = backup_queue
        self.source_handler = source_handler
        self.dest_handler = dest_handler
        self.status_widget = status_widget
        self.scene_tree = scene_tree
        self.source_path = source_path
        self.backed_up_scenes = set()
        self.last_progress = -1
        self.last_update_time = 0
        self.update_interval = 2.0
        self.max_retries = 3

    def extract_scene_name(self, path):
        if not path or not self.source_path:
            return None
        try:
            rel_path = os.path.relpath(path, self.source_path)
            parts = rel_path.split(os.sep)
            for part in parts:
                if part.startswith('scene-'):
                    logger.debug(f"Extracted scene name: {part} from {path}")
                    return part
            return None
        except Exception as e:
            logger.error(f"Error extracting scene name from {path}: {str(e)}")
            return None

    def is_scene_fully_backed_up(self, scene_name, dest_path):
        scene_source_path = os.path.join(self.source_path, scene_name)
        scene_dest_path = os.path.join(dest_path, scene_name)
        if not os.path.exists(scene_source_path) or not os.path.exists(scene_dest_path):
            logger.warning(f"Scene not fully backed up: {scene_name} (source or dest missing)")
            return False
        source_files = set(os.listdir(scene_source_path))
        dest_files = set(os.listdir(scene_dest_path))
        result = source_files == dest_files
        logger.debug(f"Scene {scene_name} fully backed up: {result}")
        return result

    def copy_with_retry(self, src_path, dest_path, event_type, is_directory=False):
        for attempt in range(self.max_retries):
            try:
                if event_type == "deleted":
                    if os.path.exists(dest_path):
                        if is_directory:
                            shutil.rmtree(dest_path)
                            logger.info(f"Directory deleted: {dest_path}")
                        else:
                            os.remove(dest_path)
                            logger.info(f"File deleted: {dest_path}")
                else:
                    os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                    if is_directory:
                        if os.path.exists(dest_path):
                            shutil.rmtree(dest_path)
                        shutil.copytree(src_path, dest_path)
                        logger.info(f"Directory copied: {src_path} -> {dest_path}")
                    else:
                        shutil.copy2(src_path, dest_path)
                        logger.info(f"File copied: {src_path} -> {dest_path}")
                return True
            except (OSError, shutil.Error) as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"Attempt {attempt + 1} failed for {src_path}: {str(e)}, retrying...")
                    time.sleep(0.5 * (attempt + 1))
                    continue
                self.update_signal.emit(f"Failed after {self.max_retries} retries: {src_path} - {str(e)}", -1)
                logger.error(f"Backup failed for {src_path}: {str(e)}")
                return False
        return False

    def run(self):
        logger.info("BackupWorker started")
        while self.running:
            try:
                if self.backup_queue.empty() and self.current_set_active:
                    self.emit_progress("Task set completed", 100)
                    self.current_set_active = False
                    time.sleep(2)
                    continue

                try:
                    src_path, dest_path, event_type, is_directory = self.backup_queue.get(timeout=1)
                    logger.debug(f"Processing backup task: {src_path} -> {dest_path}")
                    
                    # 소스 경로 확인
                    if not os.path.exists(src_path):
                        logger.warning(f"Source path does not exist: {src_path}")
                        continue
                    
                    # 대상 디렉토리 생성
                    os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                    
                    # 복사 작업 수행
                    if is_directory:
                        shutil.copytree(src_path, dest_path, dirs_exist_ok=True)
                    else:
                        shutil.copy2(src_path, dest_path)
                    
                    logger.info(f"Successfully copied: {src_path} -> {dest_path}")
                
                except queue.Empty:
                    time.sleep(0.1)
                    continue
                
            except Exception as e:
                logger.error(f"Backup worker error: {str(e)}", exc_info=True)
                time.sleep(0.5)

    def emit_progress(self, message, progress):
        current_time = time.time()
        if message and (current_time - self.last_update_time >= self.update_interval):
            level = logging.INFO if "completed" in message.lower() else logging.DEBUG
            self.update_signal.emit(message, -1)
            logger.log(level, message)
            self.last_update_time = current_time
        if progress >= 0:
            self.update_signal.emit("", progress)

    def stop(self):
        self.running = False
        logger.info("BackupWorker stopping")

class ValidationApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Yeson Entertainment 씬 관리 시스템")
        self.setGeometry(100, 100, 900, 700)
        self.setMinimumSize(600, 400)

        self.current_theme = "dark"
        app = QApplication.instance()
        StyleManager.apply_style(app, self.current_theme)

        self.path_manager = PathManager()
        self.calculator = FrameCalculator()
        self.db_manager = SceneDbManager(self.path_manager)

        self.backup_queue = queue.Queue()
        self.source_observer = None
        self.dest_observer = None
        self.backup_worker = None
        self.backup_initializer = None

        self.setup_ui()
        self.connect_signals()

        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("애플리케이션이 준비되었습니다")
        logger.info("ValidationApp initialized")

        self.theme_shortcut = QShortcut(QKeySequence("1"), self)
        self.theme_shortcut.activated.connect(self.toggle_theme)
        self.last_update_time = 0

    def setup_ui(self):
        self.main_widget = QWidget()
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidget(self.main_widget)
        self.scroll_area.setWidgetResizable(True)
        self.setCentralWidget(self.scroll_area)

        self.main_layout = QVBoxLayout()
        self.main_layout.setSpacing(10)
        self.main_layout.setContentsMargins(15, 15, 15, 15)

        header_layout = QHBoxLayout()
        self.app_title = QLabel("Yeson Entertainment 씬 피트 계산")
        self.app_title.setStyleSheet("font-size: 16px; font-weight: bold; padding: 8px;")
        header_layout.addWidget(self.app_title)
        header_layout.addStretch()

        self.theme_button = QPushButton("밝은 테마로 전환" if self.current_theme == "dark" else "다크 테마로 전환")
        self.theme_button.setCursor(Qt.PointingHandCursor)
        header_layout.addWidget(self.theme_button)

        self.main_layout.addLayout(header_layout)

        self.show_selection = ShowSelectionWidget()
        info_layout = QHBoxLayout()
        info_layout.setSpacing(10)
        self.summary = SummaryWidget()
        self.calculation = CalculationWidget()
        info_layout.addWidget(self.summary, 1)
        info_layout.addWidget(self.calculation, 1)

        self.scene_list_widget = SceneListWidget()

        self.splitter = QSplitter(Qt.Vertical)
        self.splitter.addWidget(self.scene_list_widget)
        self.backup_status = BackupStatusWidget()
        self.splitter.addWidget(self.backup_status)

        self.main_layout.addWidget(self.show_selection)
        self.main_layout.addLayout(info_layout)
        self.main_layout.addWidget(self.splitter)

        self.main_widget.setLayout(self.main_layout)

    def toggle_theme(self):
        self.current_theme = "light" if self.current_theme == "dark" else "dark"
        self.theme_button.setText("밝은 테마로 전환" if self.current_theme == "dark" else "다크 테마로 전환")
        StyleManager.apply_style(QApplication.instance(), self.current_theme)
        self.statusBar.showMessage(f"{self.current_theme.capitalize()} 테마로 전환되었습니다.")
        logger.info(f"Theme switched to {self.current_theme}")

    def connect_signals(self):
        self.show_selection.show_combo.currentTextChanged.connect(self.on_show_changed)
        self.show_selection.season_combo.currentTextChanged.connect(self.on_season_changed)
        self.show_selection.episode_combo.currentTextChanged.connect(self.on_episode_changed)
        self.calculation.calc_button.clicked.connect(self.calculate_selected_scenes)
        self.scene_list_widget.export_button.clicked.connect(self.export_to_excel)

        self.show_selection.show_backup_btn.clicked.connect(self.backup_show)
        self.show_selection.season_backup_btn.clicked.connect(self.backup_season)
        self.show_selection.episode_backup_btn.clicked.connect(self.backup_episode)
        self.theme_button.clicked.connect(self.toggle_theme)

    def on_show_changed(self, show):
        self.scene_list_widget.scene_tree.clear()
        self.show_selection.season_combo.clear()
        self.show_selection.episode_combo.clear()
        self.show_selection.season_combo.addItem("시즌 선택")
        self.show_selection.episode_combo.addItem("화수 선택")
        if show == "작품 선택":
            self.show_selection.season_combo.setEnabled(False)
            self.show_selection.episode_combo.setEnabled(False)
            self.statusBar.showMessage("작품을 선택해주세요")
            logger.info("Show selection cleared")
            return
        project_path = self.path_manager.get_show_path(show)
        if not project_path:
            self.statusBar.showMessage(f"{show} 작품의 경로를 찾을 수 없습니다")
            logger.warning(f"Project path not found for show: {show}")
            return
        if show == "Yeson_DANG":
            self.show_selection.season_combo.setEnabled(False)
            self.show_selection.episode_combo.setEnabled(False)
            self.load_scene_folders(project_path)
            self.statusBar.showMessage(f"{show} 작품이 선택되었습니다")
            logger.info(f"Show {show} selected, loading scenes")
            return
        self.show_selection.season_combo.setEnabled(True)
        if show in ["BB", "GN", "BM", "KOTH", "Test"]:
            seasons = self.path_manager.show_paths[show]["seasons"]
        elif show in ["Yeson_Test", "Yeson_Test_4K"]:
            seasons = [item for item in os.listdir(project_path) if os.path.isdir(os.path.join(project_path, item)) and not item.startswith('.') and item != "DANG"]
            seasons.sort()
        self.show_selection.season_combo.addItems(seasons)
        self.show_selection.episode_combo.setEnabled(show not in ["Test", "Yeson_DANG", "Yeson_Test", "Yeson_Test_4K"])
        self.statusBar.showMessage(f"{show} 작품이 선택되었습니다. 시즌을 선택해주세요.")
        logger.info(f"Show {show} selected, seasons loaded: {seasons}")

    def on_season_changed(self, season):
        self.scene_list_widget.scene_tree.clear()
        if season == "시즌 선택":
            self.show_selection.episode_combo.clear()
            self.show_selection.episode_combo.addItem("화수 선택")
            self.show_selection.episode_combo.setEnabled(False)
            logger.info("Season selection cleared")
            return
        show = self.show_selection.show_combo.currentText()
        project_path = self.path_manager.get_show_path(show)
        if not project_path:
            return
        if show in ["Test", "Yeson_Test", "Yeson_Test_4K"]:
            season_path = os.path.join(project_path, season)
            self.load_scene_folders(season_path)
            self.show_selection.episode_combo.setEnabled(False)
            self.statusBar.showMessage(f"{show} - {season} 시즌이 선택되었습니다.")
            logger.info(f"Season {season} selected for show {show}")
            return
        self.show_selection.episode_combo.clear()
        self.show_selection.episode_combo.addItem("화수 선택")
        self.show_selection.episode_combo.setEnabled(True)
        self.update_episode_list(show, season, project_path)
        self.statusBar.showMessage(f"{show} - {season} 시즌이 선택되었습니다. 화수를 선택해주세요.")
        logger.info(f"Season {season} selected for show {show}, loading episodes")

    def on_episode_changed(self, episode):
        self.scene_list_widget.scene_tree.clear()
        if episode == "화수 선택":
            return
        show = self.show_selection.show_combo.currentText()
        season = self.show_selection.season_combo.currentText()
        project_path = self.path_manager.get_show_path(show)
        if not project_path:
            return
        episode_path = os.path.join(project_path, season, episode)
        self.load_scene_folders(episode_path)
        self.update_scene_list()
        self.statusBar.showMessage(f"{show} - {season} - {episode} 화수가 로드되었습니다.")
        logger.info(f"Episode {episode} loaded for {show} - {season}")

    def load_scene_folders(self, path):
        if not os.path.exists(path):
            self.statusBar.showMessage(f"경로를 찾을 수 없습니다: {path}")
            logger.warning(f"Path not found: {path}")
            return
        scene_folders = [item for item in os.listdir(path) if os.path.isdir(os.path.join(path, item)) and item.startswith('scene-')]
        for scene in sorted(scene_folders):
            item = QTreeWidgetItem([scene])
            self.scene_list_widget.scene_tree.addTopLevelItem(item)
        self.statusBar.showMessage(f"{len(scene_folders)}개의 씬 폴더를 찾았습니다." if scene_folders else "씬 폴더를 찾을 수 없습니다.")
        logger.info(f"Loaded {len(scene_folders)} scene folders from {path}")

    def update_episode_list(self, show, season, project_path):
        season_path = os.path.join(project_path, season)
        if not os.path.exists(season_path):
            self.statusBar.showMessage(f"시즌 경로를 찾을 수 없습니다: {season_path}")
            logger.warning(f"Season path not found: {season_path}")
            return
        episodes = []
        if show == "BB":
            for item in os.listdir(season_path):
                if "Season13" in season and "DASA" in item:
                    episodes.append(item)
                elif "Season14" in season and "EASA" in item:
                    episodes.append(item)
        elif show == "GN":
            if "GN_Season" in season:
                season_num = season.replace("GN_Season", "")
                for item in os.listdir(season_path):
                    if item.startswith(f"{season_num}LBW"):
                        episodes.append(item)
        elif show == "BM":
            for item in os.listdir(season_path):
                if item.startswith("BM_8"):
                    episodes.append(item)
        elif show == "KOTH":
            for item in os.listdir(season_path):
                if "Season14" in season and item.startswith("EABE"):
                    episodes.append(item)
                elif "Season15" in season and item.startswith("15"):
                    episodes.append(item)
        self.show_selection.episode_combo.addItems(sorted(episodes))
        self.statusBar.showMessage(f"{len(episodes)}개의 에피소드를 찾았습니다." if episodes else "에피소드를 찾을 수 없습니다.")
        logger.info(f"Found {len(episodes)} episodes for {show} - {season}")

    def update_scene_list(self):
        show = self.show_selection.show_combo.currentText()
        episode = self.show_selection.episode_combo.currentText()
        season = self.show_selection.season_combo.currentText()
        if show in ["Test", "Yeson_DANG", "Yeson_Test", "Yeson_Test_4K"]:
            identifier = season if show in ["Test", "Yeson_Test", "Yeson_Test_4K"] else show
            frames_info = self.db_manager.get_frames_info(identifier)
        else:
            frames_info = self.db_manager.get_frames_info(episode)
        if not frames_info:
            self.statusBar.showMessage("프레임 정보를 가져올 수 없습니다.")
            logger.warning(f"No frame info available for {show} - {season} - {episode}")
            return
        latest_scenes = self.db_manager.filter_scene_folders(frames_info)
        total_frames = sum(int(frames_info[scene]) for scene in latest_scenes.values())
        total_feet = self.calculator.calculate_sheet_length(total_frames)
        if show in ["Test", "Yeson_DANG", "Yeson_Test", "Yeson_Test_4K"]:
            self.update_simple_scene_list(latest_scenes, frames_info)
        else:
            self.update_sequence_scene_list(latest_scenes, frames_info, show)
        self.summary.total_frames_display.setText(str(total_frames))
        self.summary.episode_feet_display.setText(self.calculator.format_feet_display(total_feet))
        self.statusBar.showMessage(f"씬 목록이 업데이트되었습니다. 총 프레임: {total_frames}, 총 FEET: {self.calculator.format_feet_display(total_feet)}")
        logger.info(f"Scene list updated: {len(latest_scenes)} scenes, total frames: {total_frames}")

    def update_simple_scene_list(self, latest_scenes, frames_info):
        items = []
        for scene_name in sorted(latest_scenes.values()):
            frames = frames_info[scene_name]
            sheet_length = self.calculator.calculate_sheet_length(frames)
            feet_display = self.calculator.format_feet_display(sheet_length)
            item = QTreeWidgetItem([scene_name, str(frames), feet_display, ""])
            items.append(item)
        self.scene_list_widget.scene_tree.clear()
        self.scene_list_widget.scene_tree.addTopLevelItems(items)
        self.scene_list_widget.scene_tree.reset_backup_status()
        logger.debug(f"Simple scene list updated with {len(items)} items")

    def update_sequence_scene_list(self, latest_scenes, frames_info, show):
        items = []
        for scene_name in sorted(latest_scenes.values()):
            if show == "BM":
                scene_num = scene_name.replace('scene-', '')
                seq = scene_num[0]
            elif show == "KOTH":
                scene_num = scene_name.replace('scene-', '')
                try:
                    scene_number = int(scene_num[:3])
                    seq = "Act1" if scene_number < 300 else "Act2" if scene_number < 600 else "Act3"
                except ValueError:
                    seq = "-"
            else:
                seq = scene_name.split('_')[0]
            frames = frames_info[scene_name]
            sheet_length = self.calculator.calculate_sheet_length(frames)
            feet_display = self.calculator.format_feet_display(sheet_length)
            item = QTreeWidgetItem([scene_name, str(frames), feet_display, ""])
            items.append(item)
        self.scene_list_widget.scene_tree.clear()
        self.scene_list_widget.scene_tree.addTopLevelItems(items)
        self.scene_list_widget.scene_tree.reset_backup_status()
        logger.debug(f"Sequence scene list updated with {len(items)} items for {show}")

    def calculate_selected_scenes(self):
        selected_items = self.scene_list_widget.scene_tree.selectedItems()
        if not selected_items:
            self.calculation.total_feet_display.setText("0F 00f")
            self.statusBar.showMessage("선택된 씬이 없습니다.")
            logger.info("No scenes selected for calculation")
            return
        total_frames = sum(int(item.text(1)) for item in selected_items)
        total_feet = self.calculator.calculate_sheet_length(total_frames)
        feet_display = self.calculator.format_feet_display(total_feet)
        self.calculation.total_feet_display.setText(feet_display)
        self.statusBar.showMessage(f"{len(selected_items)}개 씬이 선택되었습니다. 총 프레임: {total_frames}, 총 FEET: {feet_display}")
        logger.info(f"Calculated {len(selected_items)} selected scenes: total frames {total_frames}, total feet {feet_display}")

    def export_to_excel(self):
        tree = self.scene_list_widget.scene_tree
        if tree.topLevelItemCount() == 0:
            self.statusBar.showMessage("내보낼 씬 목록이 없습니다.")
            logger.warning("No scene list to export")
            return

        show = self.show_selection.show_combo.currentText()
        season = self.show_selection.season_combo.currentText()
        episode = self.show_selection.episode_combo.currentText()
        total_frames = self.summary.total_frames_display.text()
        total_feet = self.summary.episode_feet_display.text()

        scene_data = [[tree.topLevelItem(i).text(j) for j in range(3)] for i in range(tree.topLevelItemCount())]

        default_filename = f"{show}_{season}_{episode}_scenes.xlsx" if show != "작품 선택" else "scenes.xlsx"
        file_path, _ = QFileDialog.getSaveFileName(self, "엑셀 파일로 저장", default_filename, "Excel Files (*.xlsx)")
        if not file_path:
            self.statusBar.showMessage("파일 저장이 취소되었습니다.")
            logger.info("Excel export cancelled")
            return

        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "SceneList"
            colors = StyleManager.DARK_THEME if self.current_theme == "dark" else StyleManager.LIGHT_THEME

            header_font = Font(name="Helvetica", size=14, bold=True, color=colors["ACCENT"][1:])
            subheader_font = Font(name="Helvetica", size=12, bold=True, color=colors["TEXT"][1:])
            text_font = Font(name="Helvetica", size=12, color=colors["TEXT"][1:])
            border = Border(left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin"))
            fill_bg = PatternFill(start_color=colors["BG"][1:], end_color=colors["BG"][1:], fill_type="solid")
            fill_light_bg = PatternFill(start_color=colors["LIGHT_BG"][1:], end_color=colors["LIGHT_BG"][1:], fill_type="solid")
            fill_header = PatternFill(start_color=colors["DARK_ACCENT"][1:], end_color=colors["DARK_ACCENT"][1:], fill_type="solid")
            fill_accent = PatternFill(start_color=colors["ACCENT"][1:], end_color=colors["ACCENT"][1:], fill_type="solid")
            center_align = Alignment(horizontal="center", vertical="center")

            for row in ws.iter_rows(min_row=1, max_row=100, min_col=1, max_col=10):
                for cell in row:
                    cell.fill = fill_bg

            ws["A1"] = "작품 선택"
            ws["A1"].font = header_font
            ws["A1"].fill = fill_light_bg
            ws["A1"].border = border
            ws["A1"].alignment = center_align
            ws.merge_cells("A1:C1")

            ws["A2"] = "작품"
            ws["B2"] = show
            ws["A3"] = "시즌"
            ws["B3"] = season
            ws["A4"] = "화수"
            ws["B4"] = episode
            for row in ws["A2:C4"]:
                for cell in row:
                    cell.font = text_font
                    cell.fill = fill_light_bg
                    cell.border = border
                    cell.alignment = center_align
            ws["C2"] = "선택 정보"
            ws["C2"].fill = fill_header

            ws["A6"] = "에피소드 정보"
            ws["A6"].font = header_font
            ws["A6"].fill = fill_light_bg
            ws["A6"].border = border
            ws["A6"].alignment = center_align
            ws.merge_cells("A6:C6")

            ws["A7"] = "총 프레임 수"
            ws["B7"] = total_frames
            ws["A8"] = "총 FEET"
            ws["B8"] = total_feet
            for row in ws["A7:B8"]:
                for cell in row:
                    cell.font = text_font
                    cell.fill = fill_light_bg
                    cell.border = border
                    cell.alignment = center_align
            ws["C7"] = "요약"
            ws["C7"].fill = fill_header
            ws["C8"] = ""
            ws["C8"].fill = fill_header

            ws["A10"] = "씬 목록"
            ws["A10"].font = header_font
            ws["A10"].fill = fill_light_bg
            ws["A10"].border = border
            ws["A10"].alignment = center_align
            ws.merge_cells("A10:C10")

            headers = ["씬 이름", "Frames", "FEET"]
            for col, header in enumerate(headers, start=1):
                cell = ws.cell(row=11, column=col, value=header)
                cell.font = subheader_font
                cell.fill = fill_header
                cell.border = border
                cell.alignment = center_align

            for row_idx, scene in enumerate(scene_data, start=12):
                for col_idx, value in enumerate(scene, start=1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    cell.font = text_font
                    cell.fill = fill_light_bg
                    cell.border = border
                    cell.alignment = center_align

            ws.column_dimensions["A"].width = 40
            ws.column_dimensions["B"].width = 15
            ws.column_dimensions["C"].width = 15

            ws["A5"].fill = fill_accent
            ws["B5"].fill = fill_accent
            ws["C5"].fill = fill_accent
            ws["A9"].fill = fill_accent
            ws["B9"].fill = fill_accent
            ws["C9"].fill = fill_accent

            wb.save(file_path)
            self.statusBar.showMessage(f"씬 목록이 {file_path}에 저장되었습니다.")
            logger.info(f"Scene list exported to {file_path}")
        except Exception as e:
            self.statusBar.showMessage(f"엑셀 파일 저장 중 오류 발생: {e}")
            logger.error(f"Error exporting to Excel: {str(e)}", exc_info=True)

    def backup_show(self):
        show = self.show_selection.show_combo.currentText()
        if show == "작품 선택":
            self.statusBar.showMessage("백업할 작품을 선택해주세요.")
            logger.warning("No show selected for backup")
            return
        source_path = self.path_manager.get_show_path(show)
        if not source_path or not os.path.exists(source_path):
            self.statusBar.showMessage(f"{show} 작품의 경로를 찾을 수 없습니다.")
            logger.warning(f"Source path not found for show {show}: {source_path}")
            return
        dest_dir = QFileDialog.getExistingDirectory(self, "백업 경로 선택", os.path.expanduser("~"))
        if not dest_dir:
            self.statusBar.showMessage("백업 경로 선택이 취소되었습니다.")
            logger.info("Backup destination selection cancelled")
            return
        dest_path = os.path.join(dest_dir, show)
        os.makedirs(dest_path, exist_ok=True)
        self.start_realtime_backup(source_path, dest_path)

    def backup_season(self):
        show = self.show_selection.show_combo.currentText()
        season = self.show_selection.season_combo.currentText()
        if show == "작품 선택" or season == "시즌 선택":
            self.statusBar.showMessage("백업할 시즌을 선택해주세요.")
            logger.warning("No season selected for backup")
            return
        source_path = os.path.join(self.path_manager.get_show_path(show), season)
        if not os.path.exists(source_path):
            self.statusBar.showMessage(f"{show} - {season} 시즌의 경로를 찾을 수 없습니다.")
            logger.warning(f"Source path not found for {show} - {season}: {source_path}")
            return
        dest_dir = QFileDialog.getExistingDirectory(self, "백업 경로 선택", os.path.expanduser("~"))
        if not dest_dir:
            self.statusBar.showMessage("백업 경로 선택이 취소되었습니다.")
            logger.info("Backup destination selection cancelled")
            return
        show_path = os.path.join(dest_dir, show)
        dest_path = os.path.join(show_path, season)
        os.makedirs(dest_path, exist_ok=True)
        self.start_realtime_backup(source_path, dest_path)

    def backup_episode(self):
        show = self.show_selection.show_combo.currentText()
        season = self.show_selection.season_combo.currentText()
        episode = self.show_selection.episode_combo.currentText()
        if show == "작품 선택" or season == "시즌 선택" or episode == "화수 선택":
            self.statusBar.showMessage("백업할 화수를 선택해주세요.")
            logger.warning("No episode selected for backup")
            return
        source_path = os.path.join(self.path_manager.get_show_path(show), season, episode)
        if not os.path.exists(source_path):
            self.statusBar.showMessage(f"{show} - {season} - {episode} 화수의 경로를 찾을 수 없습니다.")
            logger.warning(f"Source path not found for {show} - {season} - {episode}: {source_path}")
            return
        dest_dir = QFileDialog.getExistingDirectory(self, "백업 경로 선택", os.path.expanduser("~"))
        if not dest_dir:
            self.statusBar.showMessage("백업 경로 선택이 취소되었습니다.")
            logger.info("Backup destination selection cancelled")
            return
        show_path = os.path.join(dest_dir, show)
        season_path = os.path.join(show_path, season)
        dest_path = os.path.join(season_path, episode)
        os.makedirs(dest_path, exist_ok=True)
        self.start_realtime_backup(source_path, dest_path)

    def start_realtime_backup(self, source_path, dest_path):
        if self.source_observer and self.source_observer.is_alive():
            self.stop_realtime_backup()

        self.backup_status.reset()
        self.backup_status.setVisible(True)
        self.backup_status.append_log(f"Backup process started: {source_path} -> {dest_path}", logging.INFO)
        self.statusBar.showMessage("Starting initial file copy...")
        logger.info(f"Starting backup from {source_path} to {dest_path}")

        self.backup_initializer = BackupInitializer(source_path, dest_path)
        self.backup_initializer.progress_signal.connect(self.update_backup_status)
        self.backup_initializer.finished.connect(lambda: self.on_initial_backup_finished(source_path, dest_path))
        self.backup_initializer.start()

    def on_initial_backup_finished(self, source_path, dest_path):
        if self.backup_initializer and not self.backup_initializer.running:
            self.backup_status.append_log("Initial sync failed or stopped", logging.WARNING)
            self.statusBar.showMessage("Initial sync failed.")
            logger.warning("Initial backup did not complete successfully")
            return

        if not os.path.exists(dest_path) or not os.listdir(dest_path):
            self.backup_status.append_log("Destination directory is empty after initial copy", logging.ERROR)
            self.statusBar.showMessage("Initial copy failed: destination directory is empty.")
            logger.error(f"Destination {dest_path} is empty after initial backup")
            return

        self.scene_list_widget.scene_tree.reset_backup_status()
        self.check_all_scenes_backup_status(source_path, dest_path)

        try:
            source_handler = BackupEventHandler(self.backup_queue, source_path, dest_path, self.backup_status, is_source=True)
            self.source_observer = Observer()
            self.source_observer.schedule(source_handler, source_path, recursive=True)
            self.source_observer.start()
            if not self.source_observer.is_alive():
                raise RuntimeError("Source observer failed to start")
            self.backup_status.append_log(f"Source directory monitoring started: {source_path}", logging.INFO)

            dest_handler = BackupEventHandler(self.backup_queue, source_path, dest_path, self.backup_status, is_source=False)
            self.dest_observer = Observer()
            self.dest_observer.schedule(dest_handler, dest_path, recursive=True)
            self.dest_observer.start()
            if not self.dest_observer.is_alive():
                raise RuntimeError("Destination observer failed to start")
            self.backup_status.append_log(f"Destination directory monitoring started: {dest_path}", logging.INFO)

            self.backup_worker = BackupWorker(self.backup_queue, source_handler, dest_handler, self.backup_status, self.scene_list_widget.scene_tree, source_path)
            self.backup_worker.update_signal.connect(self.update_backup_status)
            self.backup_worker.scene_backup_status_signal.connect(self.update_scene_backup_status)
            self.backup_worker.start()

            self.statusBar.showMessage("Realtime backup started.")
            logger.info("Realtime backup process fully started")
        except Exception as e:
            self.backup_status.append_log(f"Error starting realtime backup: {str(e)}", logging.ERROR)
            self.statusBar.showMessage("Error occurred while starting realtime backup.")
            logger.error(f"Realtime backup start failed: {str(e)}", exc_info=True)
            self.stop_realtime_backup()

    def update_scene_backup_status(self, scene_name, status):
        if self.scene_list_widget and self.scene_list_widget.scene_tree:
            self.scene_list_widget.scene_tree.set_scene_backup_status(scene_name, status)
            self.statusBar.showMessage(f"씬 [{scene_name}]의 백업 상태가 '{status}'(으)로 업데이트되었습니다.")
            logger.info(f"Scene {scene_name} backup status updated to {status}")

    def check_all_scenes_backup_status(self, source_path, dest_path):
        if not self.scene_list_widget or not self.scene_list_widget.scene_tree:
            return
        tree = self.scene_list_widget.scene_tree
        for i in range(tree.topLevelItemCount()):
            item = tree.topLevelItem(i)
            scene_name = item.text(0)
            scene_source_path = os.path.join(source_path, scene_name)
            scene_dest_path = os.path.join(dest_path, scene_name)
            if os.path.exists(scene_source_path) and os.path.exists(scene_dest_path):
                source_files = set(os.listdir(scene_source_path))
                dest_files = set(os.listdir(scene_dest_path))
                if source_files == dest_files:
                    tree.set_scene_backup_status(scene_name, "백업완료")
                    logger.debug(f"Scene {scene_name} verified as fully backed up")

    def update_backup_status(self, message, progress):
        current_time = time.time()
        if message and message.strip() and (current_time - self.last_update_time >= 2.0):
            level = logging.ERROR if "error" in message.lower() else logging.INFO if "completed" in message.lower() else logging.DEBUG
            self.backup_status.append_log(message, level)
            self.last_update_time = current_time
        if progress >= 0:
            self.backup_status.set_progress(progress)
            if progress == 100 and "완료" in message:
                self.backup_status.append_log("All backup tasks completed", logging.INFO)

    def stop_realtime_backup(self):
        try:
            if self.backup_initializer and self.backup_initializer.isRunning():
                self.backup_initializer.stop()
                self.backup_initializer.wait(5000)
                self.backup_status.append_log("Initial sync stopped", logging.INFO)

            if self.backup_worker and self.backup_worker.isRunning():
                self.backup_worker.stop()
                self.backup_worker.wait(5000)
                self.backup_status.append_log("Backup worker stopped", logging.INFO)

            if self.source_observer and self.source_observer.is_alive():
                self.source_observer.stop()
                self.source_observer.join(timeout=5)
                self.backup_status.append_log("Source directory monitoring stopped", logging.INFO)

            if self.dest_observer and self.dest_observer.is_alive():
                self.dest_observer.stop()
                self.dest_observer.join(timeout=5)
                self.backup_status.append_log("Destination directory monitoring stopped", logging.INFO)

            self.statusBar.showMessage("Realtime backup stopped.")
            logger.info("Realtime backup fully stopped")
        except Exception as e:
            self.backup_status.append_log(f"Error stopping backup: {str(e)}", logging.ERROR)
            self.statusBar.showMessage("Error occurred while stopping backup.")
            logger.error(f"Stop backup failed: {str(e)}", exc_info=True)

    def closeEvent(self, event):
        self.stop_realtime_backup()
        event.accept()
        logger.info("Application closed")

class PathManager:
    def __init__(self):
        self.show_paths = {
            "BB": {"base": "Bento_Project", "seasons": ["BB_Season13", "BB_Season14"]},
            "GN": {"base": "Bento_Project2/Great_North", "seasons": ["GN_Season4", "GN_Season5"]},
            "BM": {"base": "Titmouse/Big_Mouth", "seasons": ["BM_Season8"]},
            "KOTH": {"base": "Disney/KOTH", "seasons": ["KOTH_Season14", "KOTH_Season15"]},
            "Test": {"base": "Test", "seasons": ["TEST_SYSTEM"]},
            "Yeson_DANG": {"base": "Yeson_Test/DANG", "seasons": []},
            "Yeson_Test": {"base": "Yeson_Test", "seasons": []},
            "Yeson_Test_4K": {"base": "Yeson_Test_4K", "seasons": []}
        }
        self.possible_paths = [
            "/usadata2", "/usadata3", "/System/Volumes/Data/mnt/usadata2",
            "/System/Volumes/Data/mnt/usadata3", "/System/Volumes/data/mnt/usadata2",
            "/System/Volumes/data/mnt/usadata3", "/System/Volumes/Data/System/Volumes/Data/mnt/usadata2",
            "/System/Volumes/Data/System/Volumes/Data/mnt/usadata3",
            "/System/Volumes/data/System/Volumes/data/mnt/usadata2",
            "/System/Volumes/data/System/Volumes/data/mnt/usadata3"
        ]
        self.db_paths = [
            "/USA_DB/db_jobs", "/System/Volumes/Data/mnt/USA_DB/db_jobs",
            "/System/Volumes/data/mnt/USA_DB/db_jobs",
            "/System/Volumes/Data/System/Volumes/Data/mnt/USA_DB/db_jobs",
            "/System/Volumes/data/System/Volumes/data/mnt/USA_DB/db_jobs"
        ]

    def get_show_path(self, show):
        if show == "작품 선택":
            return None
        for base_path in self.possible_paths:
            if not os.path.exists(base_path):
                continue
            if show == "BB":
                project_path = os.path.join(base_path, "Bento_Project")
            elif show == "GN":
                project_path = os.path.join(base_path, "Bento_Project2", "Great_North")
            elif show == "BM":
                project_path = os.path.join(base_path, "Titmouse", "Big_Mouth")
            elif show == "KOTH":
                project_path = os.path.join(base_path, "Disney", "KOTH")
            elif show == "Test":
                project_path = os.path.join(base_path, "Test")
            elif show == "Yeson_DANG":
                if "usadata3" not in base_path:
                    continue
                project_path = os.path.join(base_path, "Yeson_Test", "DANG")
            elif show in ["Yeson_Test", "Yeson_Test_4K"]:
                if "usadata3" not in base_path:
                    continue
                project_path = os.path.join(base_path, show)
            if os.path.exists(project_path):
                logger.debug(f"Found show path for {show}: {project_path}")
                return project_path
        logger.warning(f"No valid path found for show: {show}")
        return None

    def get_possible_db_paths(self, episode):
        paths = [os.path.join(base_path, episode, "scene.db") for base_path in self.db_paths]
        logger.debug(f"Possible DB paths for {episode}: {paths}")
        return paths

class FrameCalculator:
    @staticmethod
    def calculate_sheet_length(frames):
        try:
            frames = int(frames)
            integer_part = frames // 16
            decimal_part = frames % 16
            return f"{integer_part}.{decimal_part:02d}"
        except:
            logger.error(f"Error calculating sheet length for frames: {frames}")
            return "0.00"

    @staticmethod
    def format_feet_display(sheet_length):
        try:
            feet, frames = str(sheet_length).split('.')
            return f"{feet}F {frames}f"
        except:
            logger.error(f"Error formatting feet display: {sheet_length}")
            return "0F 00f"

class SceneDbManager:
    def __init__(self, path_manager):
        self.path_manager = path_manager
        self.cache = {}

    def get_frames_info(self, episode):
        if episode in self.cache:
            logger.debug(f"Cache hit for episode: {episode}")
            return self.cache[episode]
        frames_info = {}
        if not episode or episode in ["작품 선택", "시즌 선택", "화수 선택"]:
            return frames_info
        is_valid_episode = (
            episode.startswith(('DASA', 'EASA')) or
            episode.startswith(('4LBW', '5LBW')) or
            episode.startswith('BM_8') or
            episode.startswith(('EABE', '15')) or
            episode in ["Test", "Yeson_DANG", "Yeson_Test", "Yeson_Test_4K"]
        )
        if not is_valid_episode:
            logger.warning(f"Invalid episode format: {episode}")
            return frames_info
        possible_db_paths = self.path_manager.get_possible_db_paths(episode)
        for db_path in possible_db_paths:
            if not os.path.exists(db_path):
                continue
            try:
                with subprocess.Popen(
                    ["/Applications/Toon Boom Harmony 21.1 Premium/Harmony 21.1 Premium.app/Contents/tba/macosx/bin/dbu", "-l", "-r", db_path],
                    stdout=subprocess.PIPE, text=True, bufsize=1
                ) as process:
                    current_scene = None
                    for line in process.stdout:
                        if 'Path:' in line:
                            current_scene = os.path.basename(line.split('Path:')[1].strip())
                        elif 'Frames:' in line and current_scene:
                            frames_info[current_scene] = line.split('Frames:')[1].strip()
                if frames_info:
                    self.cache[episode] = frames_info
                    logger.info(f"Loaded frames info for {episode}: {len(frames_info)} scenes")
                    break
            except Exception as e:
                logger.error(f"Error loading frames info from {db_path}: {str(e)}")
                continue
        return frames_info

    @staticmethod
    def filter_scene_folders(frames_info):
        excluded_terms = ['CHARACTERS', 'PROPS', 'Sub_Model', 'STOCK', 'Crowd', '_old', '_batch', 'batch']
        filtered = {scene_name: scene_name for scene_name in frames_info.keys() if not any(term in scene_name for term in excluded_terms)}
        logger.debug(f"Filtered scenes: {len(filtered)} out of {len(frames_info)}")
        return filtered

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = ValidationApp()
    window.show()
    sys.exit(app.exec_())
