"""
관리자 권한 관리를 위한 모듈입니다.
"""
import os
import subprocess
import getpass
from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox
from PyQt5.QtCore import Qt, pyqtSignal

class AdminAuthDialog(QDialog):
    """관리자 인증 다이얼로그"""
    auth_success = pyqtSignal(bool)  # 인증 성공/실패 시그널

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("관리자 권한 필요")
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()
        
        # 설명 레이블
        label = QLabel("파일 권한 변경을 위해 관리자 권한이 필요합니다.\n관리자 비밀번호를 입력해주세요.")
        layout.addWidget(label)
        
        # 비밀번호 입력 필드
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.returnPressed.connect(self.verify_password)
        layout.addWidget(self.password_input)
        
        # 확인 버튼
        verify_button = QPushButton("확인")
        verify_button.clicked.connect(self.verify_password)
        layout.addWidget(verify_button)
        
        self.setLayout(layout)

    def verify_password(self):
        password = self.password_input.text()
        if not password:
            QMessageBox.warning(self, "오류", "비밀번호를 입력해주세요.")
            return

        try:
            # sudo -S 옵션으로 비밀번호를 표준 입력으로 전달
            process = subprocess.Popen(
                ['sudo', '-S', 'true'],
                stdin=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdout=subprocess.PIPE,
                text=True
            )
            stdout, stderr = process.communicate(input=password + '\n')
            
            if process.returncode == 0:
                self.auth_success.emit(True)
                self.accept()
            else:
                QMessageBox.warning(self, "인증 실패", "비밀번호가 올바르지 않습니다.")
                self.password_input.clear()
                self.auth_success.emit(False)
        except Exception as e:
            QMessageBox.critical(self, "오류", f"인증 중 오류가 발생했습니다: {str(e)}")
            self.auth_success.emit(False)

class AdminAuthManager:
    """관리자 권한 관리자"""
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not AdminAuthManager._initialized:
            self.is_authenticated = False
            self.auth_dialog = None
            AdminAuthManager._initialized = True

    def check_admin_auth(self, parent=None):
        """관리자 권한 확인 및 요청"""
        if self.is_authenticated:
            return True

        if self.auth_dialog is None:
            self.auth_dialog = AdminAuthDialog(parent)
            self.auth_dialog.auth_success.connect(self._handle_auth_result)

        # 대화상자를 모달로 표시하고 결과 대기
        result = self.auth_dialog.exec_()
        return self.is_authenticated

    def _handle_auth_result(self, success):
        """인증 결과 처리"""
        self.is_authenticated = success
        if success:
            # sudo 타임아웃 설정 (기본 15분)
            try:
                subprocess.run(['sudo', '-v'], check=True)
            except subprocess.CalledProcessError:
                self.is_authenticated = False
                if self.auth_dialog:
                    self.auth_dialog.reject()

    def execute_with_sudo(self, command, input_data=None):
        """sudo 권한으로 명령어 실행"""
        if not self.is_authenticated:
            return False, "관리자 권한이 없습니다."

        try:
            process = subprocess.Popen(
                ['sudo', '-S'] + command,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            stdout, stderr = process.communicate(input=input_data)
            return process.returncode == 0, stdout if process.returncode == 0 else stderr
        except Exception as e:
            return False, str(e)

    def reset_auth(self):
        """인증 상태 초기화"""
        self.is_authenticated = False
        if self.auth_dialog:
            self.auth_dialog.close()
            self.auth_dialog = None 