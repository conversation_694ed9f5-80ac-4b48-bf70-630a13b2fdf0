import os

class PathManager:
    def __init__(self):
        self.show_paths = {
            "BB": {"base": "Bento_Project", "seasons": ["BB_Season13", "BB_Season14"]},
            "GN": {"base": "Bento_Project2/Great_North", "seasons": ["GN_Season4", "GN_Season5"]},
            "BM": {"base": "Titmouse/Big_Mouth", "seasons": ["BM_Season8"]},
            "KOTH": {"base": "Disney/KOTH", "seasons": ["KOTH_Season14", "KOTH_Season15"]},
            "Test": {"base": "Test", "seasons": ["TEST_SYSTEM"]},
            "Yeson_DANG": {"base": "Yeson_Test/DANG", "seasons": []},
            "Yeson_Test": {"base": "Yeson_Test", "seasons": []},
            "Yeson_Test_4K": {"base": "Yeson_Test_4K", "seasons": []},
            "MatingSeason": {"base": "Titmouse/MatingSeason", "seasons": []},
            "WB": {"base": "WB", "seasons": ["KUWJ401"]}
        }
        self.possible_paths = list(set(os.path.normpath(p) for p in [
            "/usadata2", "/usadata3",
            "/System/Volumes/Data/mnt/usadata2", "/System/Volumes/Data/mnt/usadata3",
            "/System/Volumes/data/mnt/usadata2", "/System/Volumes/data/mnt/usadata3",
            "/Volumes/usadata2", "/Volumes/usadata3",
            # Add local test paths if needed
            os.path.expanduser("~/mount/usadata2"), os.path.expanduser("~/mount/usadata3")
        ]))
        self.db_paths = list(set(os.path.normpath(p) for p in [
            "/USA_DB/db_jobs",
            "/System/Volumes/Data/mnt/USA_DB/db_jobs",
            "/System/Volumes/data/mnt/USA_DB/db_jobs",
            "/Volumes/USA_DB/db_jobs",
            os.path.expanduser("~/USA_DB/db_jobs"),
            os.path.expanduser("~/mount/USA_DB/db_jobs")
        ]))
        self.path_cache = {}
        self.base_path_root = self.find_base_path() # Find and cache the main root path

    def update_show_paths(self, show_name, base_path, seasons=None):
        """작품 경로와 시즌 정보를 업데이트합니다."""
        if seasons is None:
            seasons = []
        self.show_paths[show_name] = {
            "base": base_path,
            "seasons": seasons
        }
        # 캐시 초기화
        if show_name in self.path_cache:
            del self.path_cache[show_name]

    def get_show_seasons(self, show):
        """작품의 시즌 목록을 반환합니다."""
        show_info = self.show_paths.get(show)
        if show_info:
            return show_info.get("seasons", [])
        return []

    def find_base_path(self):
        for path in self.possible_paths:
            if os.path.isdir(path):
                # print(f"Using base path root: {path}")
                return path
        print("Warning: No valid base path root found in possible_paths.")
        return None

    def get_show_path(self, show):
        if show == "작품 선택": 
            return None
        if show in self.path_cache: 
            return self.path_cache[show]

        show_info = self.show_paths.get(show)
        if not show_info: 
            return None
        relative_path = show_info["base"]

        # Try finding under the cached base path first for efficiency
        if self.base_path_root:
             project_path = os.path.normpath(os.path.join(self.base_path_root, relative_path))
             if os.path.isdir(project_path):
                  # Check Yeson project constraint if applicable
                  is_yeson = show.startswith("Yeson_")
                  is_usadata3 = "usadata3" in self.base_path_root.lower()
                  if not (is_yeson and not is_usadata3): # Allow if not Yeson OR if Yeson on usadata3
                      # print(f"Found show path (cached root): {project_path}")
                      self.path_cache[show] = project_path
                      return project_path

        # If not found under cached root, search all possible paths
        for path_root in self.possible_paths:
            if path_root == self.base_path_root: 
                continue # Skip already checked path
            project_path = os.path.normpath(os.path.join(path_root, relative_path))
            if os.path.isdir(project_path):
                is_yeson = show.startswith("Yeson_")
                is_usadata3 = "usadata3" in path_root.lower()
                if not (is_yeson and not is_usadata3):
                    # print(f"Found show path (secondary search): {project_path}")
                    self.path_cache[show] = project_path
                    return project_path

        print(f"Show path not found for: {show}")
        return None

    def get_possible_db_paths(self, identifier):
        if not identifier: 
            return []
        return [os.path.normpath(os.path.join(base_path, identifier, "scene.db")) for base_path in self.db_paths]

    def get_episode_path(self, episode_id):
        """화수 ID로 경로를 가져옵니다."""
        # BB 시리즈
        if episode_id.startswith(("DASA", "EASA")):
            season = "BB_Season13" if episode_id.startswith("DASA") else "BB_Season14"
            show_path = self.get_show_path("BB")
            if show_path:
                return os.path.join(show_path, season, episode_id)
        
        # GN 시리즈
        elif any(episode_id.endswith(f"{i}LBW") for i in range(10)):
            season_num = episode_id[-4]  # e.g., "4" from "4LBW"
            season = f"GN_Season{season_num}"
            show_path = self.get_show_path("GN")
            if show_path:
                return os.path.join(show_path, season, episode_id)
        
        # BM 시리즈
        elif episode_id.startswith("BM_8"):
            show_path = self.get_show_path("BM")
            if show_path:
                return os.path.join(show_path, "BM_Season8", episode_id)
        
        # KOTH 시리즈
        elif episode_id.startswith(("EABE", "15")):
            season = "KOTH_Season14" if episode_id.startswith("EABE") else "KOTH_Season15"
            show_path = self.get_show_path("KOTH")
            if show_path:
                return os.path.join(show_path, season, episode_id)

        # WB 시리즈
        elif episode_id.startswith("WB_1"):
            show_path = self.get_show_path("WB")
            if show_path:
                return os.path.join(show_path, "WB_Season1", episode_id)

        # 사용자 추가 작품
        else:
            # 모든 작품의 시즌을 확인
            for show, info in self.show_paths.items():
                show_path = self.get_show_path(show)
                if show_path:
                    for season in info.get("seasons", []):
                        season_path = os.path.join(show_path, season)
                        episode_path = os.path.join(season_path, episode_id)
                        if os.path.exists(episode_path):
                            return episode_path

        return None

    def get_season_path(self, season_id):
        """시즌 ID로 경로를 가져옵니다."""
        # BB 시리즈
        if season_id.startswith("BB_Season"):
            show_path = self.get_show_path("BB")
            if show_path:
                return os.path.join(show_path, season_id)
        
        # GN 시리즈
        elif season_id.startswith("GN_Season"):
            show_path = self.get_show_path("GN")
            if show_path:
                return os.path.join(show_path, season_id)
        
        # BM 시리즈
        elif season_id.startswith("BM_Season"):
            show_path = self.get_show_path("BM")
            if show_path:
                return os.path.join(show_path, season_id)
        
        # KOTH 시리즈
        elif season_id.startswith("KOTH_Season"):
            show_path = self.get_show_path("KOTH")
            if show_path:
                return os.path.join(show_path, season_id)
        
        # Test 시리즈
        elif season_id == "TEST_SYSTEM":
            show_path = self.get_show_path("Test")
            if show_path:
                return os.path.join(show_path, season_id)

        # WB 시리즈
        elif season_id.startswith("WB_Season"):
            show_path = self.get_show_path("WB")
            if show_path:
                return os.path.join(show_path, season_id)

        # 사용자 추가 작품
        else:
            # 모든 작품의 시즌을 확인
            for show, info in self.show_paths.items():
                if season_id in info.get("seasons", []):
                    show_path = self.get_show_path(show)
                    if show_path:
                        return os.path.join(show_path, season_id)

        return None

    def get_path_by_identifier(self, identifier):
        """식별자(작품/시즌/화수)에 따라 적절한 경로를 반환합니다."""
        # 먼저 화수 경로 확인
        episode_path = self.get_episode_path(identifier)
        if episode_path:
            return episode_path
        
        # 시즌 경로 확인
        season_path = self.get_season_path(identifier)
        if season_path:
            return season_path
        
        # 작품 경로 확인
        show_path = self.get_show_path(identifier)
        if show_path:
            return show_path
        
        return None

    def get_server_folders(self, server_path):
        """서버 경로에서 폴더 목록을 가져옵니다."""
        try:
            if not os.path.exists(server_path):
                return []
            
            # 서버 경로의 모든 폴더 목록을 가져옵니다.
            folders = []
            for item in os.listdir(server_path):
                item_path = os.path.join(server_path, item)
                # 숨김 폴더가 아니고 디렉토리인 경우만 추가
                if not item.startswith('.') and os.path.isdir(item_path):
                    folders.append(item)
            
            return sorted(folders)  # 알파벳 순으로 정렬하여 반환
        except Exception as e:
            print(f"Error getting server folders: {e}")
            return []