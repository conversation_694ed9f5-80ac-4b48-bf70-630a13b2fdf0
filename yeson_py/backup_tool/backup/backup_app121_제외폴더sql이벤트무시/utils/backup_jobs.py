import json
import os
from .rsync_config import get_active_jobs_path

def save_active_jobs(jobs_data):
    """active_backup_jobs.json 파일에 작업 정보를 저장합니다.
    
    Args:
        jobs_data (dict): 저장할 작업 정보
            {
                "active_jobs": {
                    "job_id": {
                        "source": "소스 경로",
                        "dest": "대상 경로",
                        "is_mirror": bool,
                        "exclusions": {  # 작업별 제외 패턴
                            "경로1": "파일",
                            "경로2": "폴더"
                        }
                    }
                },
                "servers": { ... },
                "projects": [ ... ],
                "exclusions": {  # 전체 제외 패턴
                    "경로1": "파일",
                    "경로2": "폴더"
                }
            }
    """
    try:
        jobs_path = get_active_jobs_path()
        os.makedirs(os.path.dirname(jobs_path), exist_ok=True)
        
        # 기존 데이터 로드
        existing_data = {}
        if os.path.exists(jobs_path):
            with open(jobs_path, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
        
        # 새로운 데이터와 기존 데이터 병합
        merged_data = {
            "active_jobs": {},
            "servers": existing_data.get("servers", {}),
            "projects": existing_data.get("projects", []),
            "exclusions": existing_data.get("exclusions", {})
        }
        
        # active_jobs 업데이트
        if "active_jobs" in jobs_data:
            for job_id, job_info in jobs_data["active_jobs"].items():
                # 기존 작업 정보 유지하면서 새로운 정보 업데이트
                existing_job = existing_data.get("active_jobs", {}).get(job_id, {})
                merged_data["active_jobs"][job_id] = {
                    **existing_job,
                    **job_info,
                    "exclusions": job_info.get("exclusions", existing_job.get("exclusions", {}))
                }
        
        # 전체 제외 패턴 업데이트
        if "exclusions" in jobs_data:
            merged_data["exclusions"].update(jobs_data["exclusions"])
        
        # 파일 저장
        with open(jobs_path, 'w', encoding='utf-8') as f:
            json.dump(merged_data, f, indent=2, ensure_ascii=False)
        
        return True
    except Exception as e:
        print(f"작업 정보 저장 중 오류: {e}")
        return False

def add_exclusion(job_id=None, path=None, type_=None):
    """제외 패턴을 추가합니다.
    
    Args:
        job_id (str, optional): 작업 ID. None이면 전체 제외 패턴에 추가
        path (str): 제외할 경로
        type_ (str): "파일" 또는 "폴더"
    """
    try:
        jobs_path = get_active_jobs_path()
        if not os.path.exists(jobs_path):
            return False
        
        with open(jobs_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if job_id:
            # 작업별 제외 패턴 추가
            if "active_jobs" not in data:
                data["active_jobs"] = {}
            if job_id not in data["active_jobs"]:
                data["active_jobs"][job_id] = {"exclusions": {}}
            if "exclusions" not in data["active_jobs"][job_id]:
                data["active_jobs"][job_id]["exclusions"] = {}
            data["active_jobs"][job_id]["exclusions"][path] = type_
        else:
            # 전체 제외 패턴 추가
            if "exclusions" not in data:
                data["exclusions"] = {}
            data["exclusions"][path] = type_
        
        with open(jobs_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        return True
    except Exception as e:
        print(f"제외 패턴 추가 중 오류: {e}")
        return False

def remove_exclusion(job_id=None, path=None):
    """제외 패턴을 제거합니다.
    
    Args:
        job_id (str, optional): 작업 ID. None이면 전체 제외 패턴에서 제거
        path (str): 제거할 경로
    """
    try:
        jobs_path = get_active_jobs_path()
        if not os.path.exists(jobs_path):
            return False
        
        with open(jobs_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if job_id:
            # 작업별 제외 패턴 제거
            if (job_id in data.get("active_jobs", {}) and 
                "exclusions" in data["active_jobs"][job_id] and 
                path in data["active_jobs"][job_id]["exclusions"]):
                del data["active_jobs"][job_id]["exclusions"][path]
        else:
            # 전체 제외 패턴 제거
            if "exclusions" in data and path in data["exclusions"]:
                del data["exclusions"][path]
        
        with open(jobs_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        return True
    except Exception as e:
        print(f"제외 패턴 제거 중 오류: {e}")
        return False 