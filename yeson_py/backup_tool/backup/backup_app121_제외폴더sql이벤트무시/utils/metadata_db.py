import sqlite3
import os
import time
from datetime import datetime
from typing import List, Tu<PERSON>, Optional
from backup_app.utils.logger import Backup<PERSON>ogger
from backup_app.config.settings import Settings

class MetadataDB:
    """백업 메타데이터를 관리하는 SQLite 데이터베이스 클래스"""
    
    def __init__(self, db_path: str = None):
        self.logger = BackupLogger()
        self.logger.info("=== MetadataDB 초기화 시작 ===")
        self.settings = Settings()
        self.max_retries = 3  # 최대 재시도 횟수
        self.retry_delay = 1  # 재시도 간격 (초)
        self.busy_timeout = 5000  # busy_timeout (밀리초)
        
        try:
            if db_path is None:
                # 로그 디렉토리와 동일한 위치에 DB 파일 저장
                log_settings = self.settings.get_logging_settings()
                log_dir = log_settings['log_dir']
                self.logger.info(f"설정된 로그 디렉토리: {log_dir}")
                
                # 로그 디렉토리가 절대 경로인지 확인
                if not os.path.isabs(log_dir):
                    log_dir = os.path.abspath(log_dir)
                    self.logger.info(f"절대 경로로 변환된 로그 디렉토리: {log_dir}")
                
                # 로그 디렉토리 생성 시도
                try:
                    os.makedirs(log_dir, exist_ok=True)
                    self.logger.info(f"로그 디렉토리 생성/확인 성공: {log_dir}")
                    
                    # 디렉토리 쓰기 권한 확인
                    test_file = os.path.join(log_dir, '.write_test')
                    try:
                        with open(test_file, 'w') as f:
                            f.write('test')
                        os.remove(test_file)
                        self.logger.info("로그 디렉토리 쓰기 권한 확인 성공")
                    except Exception as e:
                        self.logger.error(f"로그 디렉토리 쓰기 권한 확인 실패: {e}")
                        raise
                        
                except Exception as e:
                    self.logger.error(f"로그 디렉토리 생성 실패: {e}")
                    # 폴백: 현재 디렉토리에 logs 폴더 생성
                    log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
                    os.makedirs(log_dir, exist_ok=True)
                    self.logger.info(f"대체 로그 디렉토리 사용: {log_dir}")
                
                db_path = os.path.join(log_dir, 'backup_metadata.db')
                self.logger.info(f"DB 파일 경로 설정: {db_path}")
            
            # DB 파일 경로 로깅
            self.logger.info(f"최종 DB 파일 경로: {db_path}")
            
            # DB 디렉토리가 없으면 생성
            db_dir = os.path.dirname(db_path)
            try:
                os.makedirs(db_dir, exist_ok=True)
                self.logger.info(f"DB 디렉토리 생성/확인 성공: {db_dir}")
                
                # DB 디렉토리 쓰기 권한 확인
                test_file = os.path.join(db_dir, '.write_test')
                try:
                    with open(test_file, 'w') as f:
                        f.write('test')
                    os.remove(test_file)
                    self.logger.info("DB 디렉토리 쓰기 권한 확인 성공")
                except Exception as e:
                    self.logger.error(f"DB 디렉토리 쓰기 권한 확인 실패: {e}")
                    raise
                    
            except Exception as e:
                self.logger.error(f"DB 디렉토리 생성 실패: {e}")
                raise
            
            self.db_path = db_path
            self._init_db()
            self.logger.info("=== MetadataDB 초기화 완료 ===")
            
        except Exception as e:
            self.logger.error(f"MetadataDB 초기화 중 치명적 오류 발생: {e}")
            raise
    
    def _init_db(self):
        """데이터베이스를 초기화합니다."""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # change_events 테이블 생성
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS change_events (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        path TEXT NOT NULL,
                        event_type TEXT NOT NULL,
                        is_directory INTEGER NOT NULL,
                        timestamp REAL NOT NULL,
                        processed INTEGER DEFAULT 0,
                        priority INTEGER DEFAULT 0,
                        error_message TEXT,
                        new_path TEXT,
                        batch_id TEXT,
                        created_at REAL,
                        last_update_attempt REAL,
                        update_attempt_count INTEGER DEFAULT 0,
                        processing_time REAL,
                        retry_count INTEGER DEFAULT 0,
                        last_retry_time REAL,
                        status TEXT DEFAULT '대기'
                    )
                ''')
                
                # backup_stats 테이블 생성
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS backup_stats (
                        id INTEGER PRIMARY KEY CHECK (id = 1),
                        total_events INTEGER DEFAULT 0,
                        processed_events INTEGER DEFAULT 0,
                        failed_events INTEGER DEFAULT 0,
                        last_sync_time REAL
                    )
                ''')
                
                # 기본 인덱스 생성
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_change_events_timestamp 
                    ON change_events(timestamp)
                ''')
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_change_events_event_type 
                    ON change_events(event_type)
                ''')
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_change_events_processed 
                    ON change_events(processed)
                ''')
                
                # 초기 통계 레코드 생성
                cursor.execute('''
                    INSERT OR IGNORE INTO backup_stats (id) VALUES (1)
                ''')
                
                conn.commit()
                self.logger.info("데이터베이스 초기화 완료")
                
                # 스키마 업데이트 실행
                self._update_schema(cursor)
                
        except Exception as e:
            self.logger.error(f"데이터베이스 초기화 실패: {str(e)}")
            raise

    def _update_schema(self, cursor):
        """기존 데이터베이스 스키마를 업데이트합니다."""
        try:
            # change_events 테이블 컬럼 존재 여부 확인 및 추가
            cursor.execute("PRAGMA table_info(change_events)")
            existing_columns = {row[1] for row in cursor.fetchall()}
            
            # 복구 관련 컬럼 추가
            new_columns = {
                'restore_source': 'TEXT',
                'restore_timestamp': 'REAL',
                'restore_attempts': 'INTEGER DEFAULT 0'
            }
            
            for column, type_def in new_columns.items():
                if column not in existing_columns:
                    try:
                        cursor.execute(f'ALTER TABLE change_events ADD COLUMN {column} {type_def}')
                        self.logger.info(f"컬럼 추가 완료: {column}")
                    except sqlite3.OperationalError as e:
                        if "duplicate column name" not in str(e).lower():
                            raise
                        self.logger.debug(f"컬럼이 이미 존재함: {column}")
            
            # backup_stats 테이블 컬럼 존재 여부 확인 및 추가
            cursor.execute("PRAGMA table_info(backup_stats)")
            existing_columns = {row[1] for row in cursor.fetchall()}
            
            # 복구 통계 관련 컬럼 추가
            new_stats_columns = {
                'restore_count': 'INTEGER DEFAULT 0',
                'last_restore_time': 'REAL',
                'total_restore_size': 'INTEGER DEFAULT 0'
            }
            
            for column, type_def in new_stats_columns.items():
                if column not in existing_columns:
                    try:
                        cursor.execute(f'ALTER TABLE backup_stats ADD COLUMN {column} {type_def}')
                        self.logger.info(f"통계 컬럼 추가 완료: {column}")
                    except sqlite3.OperationalError as e:
                        if "duplicate column name" not in str(e).lower():
                            raise
                        self.logger.debug(f"통계 컬럼이 이미 존재함: {column}")
            
            # 인덱스 생성
            try:
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_change_events_restore 
                    ON change_events(event_type, restore_timestamp)
                ''')
            except sqlite3.OperationalError as e:
                if "index already exists" not in str(e).lower():
                    raise
                self.logger.debug("복구 인덱스가 이미 존재함")
            
            self.logger.info("데이터베이스 스키마 업데이트 완료")
            
        except Exception as e:
            self.logger.error(f"스키마 업데이트 중 오류 발생: {str(e)}")
            raise

    def _get_connection(self):
        """데이터베이스 연결을 생성하고 반환합니다."""
        retries = 0
        while retries < self.max_retries:
            try:
                conn = sqlite3.connect(self.db_path, timeout=self.busy_timeout/1000)  # timeout은 초 단위
                conn.execute(f"PRAGMA busy_timeout = {self.busy_timeout}")
                return conn
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e).lower():
                    retries += 1
                    if retries < self.max_retries:
                        self.logger.warning(f"DB 잠금 감지, {self.retry_delay}초 후 재시도 ({retries}/{self.max_retries})")
                        time.sleep(self.retry_delay)
                    else:
                        self.logger.error("DB 연결 최대 재시도 횟수 초과")
                        raise
                else:
                    raise
            except Exception as e:
                self.logger.error(f"DB 연결 중 예상치 못한 오류: {e}")
                raise

    def add_change(self, path: str, event_type: str, is_directory: bool) -> int:
        """새로운 변경 이벤트 추가"""
        try:
            self.logger.info(f"이벤트 추가 시도: {path} (타입: {event_type}, 디렉토리: {is_directory})")
            conn = self._get_connection()
            try:
                cursor = conn.cursor()
                timestamp = time.time()
                
                # 현재 처리 중인 배치가 있는지 확인
                cursor.execute('''
                    SELECT id FROM batch_processing 
                    WHERE status = 'processing' 
                    ORDER BY start_time DESC LIMIT 1
                ''')
                batch_result = cursor.fetchone()
                batch_id = batch_result[0] if batch_result else None
                self.logger.debug(f"현재 처리 중인 배치 ID: {batch_id}")
                
                # 이벤트 추가
                cursor.execute('''
                    INSERT INTO change_events 
                    (path, event_type, is_directory, timestamp, created_at, batch_id)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (path, event_type, is_directory, timestamp, timestamp, batch_id))
                
                # 배치가 있으면 total_events 업데이트
                if batch_id is not None:
                    cursor.execute('''
                        UPDATE batch_processing 
                        SET total_events = total_events + 1 
                        WHERE id = ?
                    ''', (batch_id,))
                    self.logger.debug(f"배치 {batch_id}의 total_events 업데이트")
                
                conn.commit()
                event_id = cursor.lastrowid
                self.logger.info(f"이벤트 추가 성공 (ID: {event_id}, 배치: {batch_id}): {path}")
                
                # 추가된 이벤트 확인
                cursor.execute('SELECT * FROM change_events WHERE id = ?', (event_id,))
                event_data = cursor.fetchone()
                self.logger.debug(f"추가된 이벤트 데이터: {event_data}")
                
                return event_id
            except sqlite3.Error as e:
                self.logger.error(f"SQLite 오류로 이벤트 추가 실패: {e}")
                conn.rollback()
                raise
            finally:
                conn.close()
        except Exception as e:
            self.logger.error(f"변경 이벤트 추가 실패: {e}")
            import traceback
            self.logger.error(f"상세 오류: {traceback.format_exc()}")
            raise
    
    def calculate_optimal_batch_size(self) -> int:
        """시스템 상태에 따른 최적의 배치 크기 계산"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 최근 처리된 배치들의 성능 데이터 조회
                cursor.execute('''
                    SELECT batch_size, avg_processing_time, memory_usage
                    FROM batch_processing
                    WHERE status = 'completed'
                    ORDER BY end_time DESC
                    LIMIT 10
                ''')
                recent_batches = cursor.fetchall()
                
                if not recent_batches:
                    return 1000  # 기본값
                
                # 시스템 메모리 사용량 확인
                import psutil
                available_memory = psutil.virtual_memory().available
                
                # 평균 처리 시간과 메모리 사용량 기반으로 배치 크기 조정
                avg_processing_time = sum(batch[1] for batch in recent_batches) / len(recent_batches)
                avg_memory_usage = sum(batch[2] for batch in recent_batches) / len(recent_batches)
                
                # 메모리 사용량이 70% 이상이면 배치 크기 감소
                if available_memory < (psutil.virtual_memory().total * 0.3):
                    return max(100, int(1000 * 0.5))
                
                # 처리 시간이 1초 이상이면 배치 크기 감소
                if avg_processing_time > 1.0:
                    return max(100, int(1000 * 0.7))
                
                # 처리 시간이 0.1초 미만이면 배치 크기 증가
                if avg_processing_time < 0.1:
                    return min(5000, int(1000 * 1.5))
                
                return 1000  # 기본값
                
        except Exception as e:
            self.logger.error(f"최적 배치 크기 계산 중 오류: {e}")
            return 1000  # 오류 시 기본값 반환

    def get_unprocessed_changes(self, batch_size: int = None) -> List[Tuple]:
        """미처리 변경 이벤트 조회 (동적 배치 크기 적용)"""
        if batch_size is None:
            batch_size = self.calculate_optimal_batch_size()
            
        try:
            conn = self._get_connection()
            try:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, path, event_type, is_directory, timestamp, priority
                    FROM change_events
                    WHERE processed = 0
                    ORDER BY priority DESC, timestamp ASC
                    LIMIT ?
                ''', (batch_size,))
                return cursor.fetchall()
            finally:
                conn.close()
        except Exception as e:
            self.logger.error(f"미처리 변경 이벤트 조회 실패: {e}")
            raise
    
    def mark_as_processed(self, event_ids: List[int], batch_id: Optional[int] = None):
        """이벤트를 처리 완료로 표시"""
        try:
            conn = self._get_connection()
            try:
                cursor = conn.cursor()
                if batch_id is not None:
                    cursor.execute('''
                        UPDATE change_events
                        SET processed = 1, batch_id = ?
                        WHERE id IN ({})
                    '''.format(','.join('?' * len(event_ids))),
                        [batch_id] + event_ids)
                else:
                    cursor.execute('''
                        UPDATE change_events
                        SET processed = 1
                        WHERE id IN ({})
                    '''.format(','.join('?' * len(event_ids))),
                        event_ids)
                conn.commit()
            finally:
                conn.close()
        except Exception as e:
            self.logger.error(f"이벤트 처리 완료 표시 실패: {e}")
            raise
    
    def mark_as_failed(self, event_id: int, error_message: str):
        """이벤트 처리 실패 표시 및 재시도 카운트 증가"""
        try:
            conn = self._get_connection()
            try:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE change_events
                    SET retry_count = retry_count + 1,
                        last_retry_time = ?,
                        error_message = ?
                    WHERE id = ?
                ''', (time.time(), error_message, event_id))
                conn.commit()
            finally:
                conn.close()
        except Exception as e:
            self.logger.error(f"이벤트 실패 표시 실패: {e}")
            raise
    
    def create_batch(self) -> int:
        """새로운 배치 처리 시작"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO batch_processing (start_time, status)
                    VALUES (?, 'processing')
                ''', (time.time(),))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            self.logger.error(f"배치 생성 실패: {e}")
            raise
    
    def complete_batch(self, batch_id: int, status: str, total_events: int, 
                      processed_events: int, error_count: int):
        """배치 처리 완료"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE batch_processing
                    SET end_time = ?,
                        status = ?,
                        total_events = ?,
                        processed_events = ?,
                        error_count = ?
                    WHERE id = ?
                ''', (time.time(), status, total_events, 
                      processed_events, error_count, batch_id))
                conn.commit()
        except Exception as e:
            self.logger.error(f"배치 완료 처리 실패: {e}")
            raise
    
    def get_pending_changes_count(self) -> int:
        """미처리 변경 이벤트 수 조회"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM change_events WHERE processed = 0')
                return cursor.fetchone()[0]
        except Exception as e:
            self.logger.error(f"미처리 이벤트 수 조회 실패: {e}")
            raise
    
    def cleanup_old_events(self, retention_policy=None):
        """보관 정책에 따른 오래된 이벤트 정리
        
        Args:
            retention_policy (dict): 보관 정책 설정
                - processed_days: 처리된 이벤트 보관 기간 (일)
                - failed_days: 실패한 이벤트 보관 기간 (일)
                - important_days: 중요 이벤트 보관 기간 (일)
                - max_db_size: 최대 DB 크기 (바이트)
        """
        try:
            if retention_policy is None:
                # 기본 보관 정책
                retention_policy = {
                    'processed_days': 30,
                    'failed_days': 7,
                    'important_days': 90,
                    'max_db_size': 1024 * 1024 * 1024  # 1GB
                }
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                current_time = time.time()
                
                # 1. 처리된 이벤트 정리
                processed_cutoff = current_time - (retention_policy['processed_days'] * 86400)
                cursor.execute('''
                    DELETE FROM change_events
                    WHERE processed = 1 
                    AND timestamp < ?
                    AND priority < 2  -- 중요도가 낮은 이벤트만
                ''', (processed_cutoff,))
                
                # 2. 실패한 이벤트 정리
                failed_cutoff = current_time - (retention_policy['failed_days'] * 86400)
                cursor.execute('''
                    DELETE FROM change_events
                    WHERE retry_count >= 3  -- 최대 재시도 횟수 초과
                    AND last_retry_time < ?
                ''', (failed_cutoff,))
                
                # 3. 중요 이벤트 정리
                important_cutoff = current_time - (retention_policy['important_days'] * 86400)
                cursor.execute('''
                    DELETE FROM change_events
                    WHERE priority >= 2  -- 중요도가 높은 이벤트
                    AND timestamp < ?
                ''', (important_cutoff,))
                
                # 4. DB 크기 기반 정리
                db_size = os.path.getsize(self.db_path)
                if db_size > retention_policy['max_db_size']:
                    # 가장 오래된 이벤트부터 삭제
                    cursor.execute('''
                        DELETE FROM change_events
                        WHERE id IN (
                            SELECT id FROM change_events
                            WHERE processed = 1
                            ORDER BY timestamp ASC
                            LIMIT ?
                        )
                    ''', (int((db_size - retention_policy['max_db_size']) / 1000),))
                
                # 5. 성능 메트릭 정리 (30일 이상 된 데이터)
                metrics_cutoff = current_time - (30 * 86400)
                cursor.execute('''
                    DELETE FROM performance_metrics
                    WHERE timestamp < ?
                ''', (metrics_cutoff,))
                
                # 6. 배치 처리 기록 정리 (90일 이상 된 데이터)
                batch_cutoff = current_time - (90 * 86400)
                cursor.execute('''
                    DELETE FROM batch_processing
                    WHERE end_time < ?
                ''', (batch_cutoff,))
                
                conn.commit()
                
                # 정리 결과 로깅
                self.logger.info(f"이벤트 정리 완료: {cursor.rowcount}개 이벤트 삭제됨")
                
        except Exception as e:
            self.logger.error(f"이벤트 정리 중 오류: {e}")
            raise
            
    def get_retention_stats(self):
        """현재 보관 상태 통계 조회"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                current_time = time.time()
                
                stats = {
                    'total_events': 0,
                    'processed_events': 0,
                    'failed_events': 0,
                    'important_events': 0,
                    'db_size': os.path.getsize(self.db_path),
                    'oldest_event': None,
                    'newest_event': None,
                    'retention_by_type': {}
                }
                
                # 전체 이벤트 수
                cursor.execute('SELECT COUNT(*) FROM change_events')
                stats['total_events'] = cursor.fetchone()[0]
                
                # 처리된 이벤트 수
                cursor.execute('SELECT COUNT(*) FROM change_events WHERE processed = 1')
                stats['processed_events'] = cursor.fetchone()[0]
                
                # 실패한 이벤트 수
                cursor.execute('SELECT COUNT(*) FROM change_events WHERE retry_count >= 3')
                stats['failed_events'] = cursor.fetchone()[0]
                
                # 중요 이벤트 수
                cursor.execute('SELECT COUNT(*) FROM change_events WHERE priority >= 2')
                stats['important_events'] = cursor.fetchone()[0]
                
                # 가장 오래된/최신 이벤트 시간
                cursor.execute('SELECT MIN(timestamp), MAX(timestamp) FROM change_events')
                oldest, newest = cursor.fetchone()
                stats['oldest_event'] = oldest
                stats['newest_event'] = newest
                
                # 이벤트 유형별 보관 현황
                cursor.execute('''
                    SELECT event_type, 
                           COUNT(*) as total,
                           SUM(CASE WHEN processed = 1 THEN 1 ELSE 0 END) as processed,
                           SUM(CASE WHEN retry_count >= 3 THEN 1 ELSE 0 END) as failed
                    FROM change_events
                    GROUP BY event_type
                ''')
                
                for row in cursor.fetchall():
                    stats['retention_by_type'][row[0]] = {
                        'total': row[1],
                        'processed': row[2],
                        'failed': row[3]
                    }
                
                return stats
                
        except Exception as e:
            self.logger.error(f"보관 상태 통계 조회 중 오류: {e}")
            raise
    
    def update_performance_metrics(self):
        """성능 메트릭 업데이트"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 현재 처리 중인 이벤트 수
                cursor.execute('SELECT COUNT(*) FROM change_events WHERE processed = 0')
                pending_events = cursor.fetchone()[0]
                
                # 최근 1분간 처리된 이벤트 수
                one_minute_ago = time.time() - 60
                cursor.execute('''
                    SELECT COUNT(*) FROM change_events 
                    WHERE processed = 1 AND processing_time > ?
                ''', (one_minute_ago,))
                processed_events = cursor.fetchone()[0]
                
                # 평균 처리 시간
                cursor.execute('''
                    SELECT AVG(processing_time - timestamp) 
                    FROM change_events 
                    WHERE processed = 1 AND processing_time > ?
                ''', (one_minute_ago,))
                avg_processing_time = cursor.fetchone()[0] or 0
                
                # 메모리 사용량
                import psutil
                memory_usage = psutil.Process().memory_info().rss
                
                # DB 크기
                db_size = os.path.getsize(self.db_path)
                
                # 메트릭 저장
                cursor.execute('''
                    INSERT INTO performance_metrics 
                    (timestamp, events_per_second, avg_processing_time, 
                     memory_usage, db_size, active_connections)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (time.time(), processed_events/60, avg_processing_time,
                      memory_usage, db_size, 1))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"성능 메트릭 업데이트 실패: {e}")
            raise
    
    def analyze_error_patterns(self):
        """오류 패턴 분석"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 1. 시간대별 오류 발생 빈도
                cursor.execute('''
                    SELECT 
                        strftime('%H', datetime(last_retry_time, 'unixepoch')) as hour,
                        COUNT(*) as error_count
                    FROM change_events
                    WHERE retry_count > 0
                    GROUP BY hour
                    ORDER BY hour
                ''')
                hourly_errors = {row[0]: row[1] for row in cursor.fetchall()}
                
                # 2. 이벤트 유형별 오류 비율
                cursor.execute('''
                    SELECT 
                        event_type,
                        COUNT(*) as total,
                        SUM(CASE WHEN retry_count > 0 THEN 1 ELSE 0 END) as errors,
                        AVG(CASE WHEN retry_count > 0 THEN retry_count ELSE 0 END) as avg_retries
                    FROM change_events
                    GROUP BY event_type
                ''')
                type_errors = {
                    row[0]: {
                        'total': row[1],
                        'errors': row[2],
                        'error_rate': row[2] / row[1] if row[1] > 0 else 0,
                        'avg_retries': row[3]
                    }
                    for row in cursor.fetchall()
                }
                
                # 3. 가장 빈번한 오류 메시지
                cursor.execute('''
                    SELECT 
                        error_message,
                        COUNT(*) as count
                    FROM change_events
                    WHERE error_message IS NOT NULL
                    GROUP BY error_message
                    ORDER BY count DESC
                    LIMIT 10
                ''')
                common_errors = {row[0]: row[1] for row in cursor.fetchall()}
                
                # 4. 경로별 오류 발생 빈도
                cursor.execute('''
                    SELECT 
                        substr(path, 1, instr(path, '/', 1, 2)) as base_path,
                        COUNT(*) as error_count
                    FROM change_events
                    WHERE retry_count > 0
                    GROUP BY base_path
                    ORDER BY error_count DESC
                    LIMIT 10
                ''')
                path_errors = {row[0]: row[1] for row in cursor.fetchall()}
                
                return {
                    'hourly_errors': hourly_errors,
                    'type_errors': type_errors,
                    'common_errors': common_errors,
                    'path_errors': path_errors
                }
                
        except Exception as e:
            self.logger.error(f"오류 패턴 분석 중 예외: {e}")
            raise
            
    def schedule_retry(self, event_id: int, delay_seconds: int = 300):
        """실패한 이벤트 재시도 예약"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                retry_time = time.time() + delay_seconds
                
                cursor.execute('''
                    UPDATE change_events
                    SET retry_count = retry_count + 1,
                        last_retry_time = ?,
                        processed = 0
                    WHERE id = ?
                ''', (retry_time, event_id))
                
                conn.commit()
                self.logger.info(f"이벤트 {event_id} 재시도 예약됨 ({delay_seconds}초 후)")
                
        except Exception as e:
            self.logger.error(f"이벤트 재시도 예약 중 오류: {e}")
            raise
            
    def get_retry_candidates(self, max_retries: int = 3):
        """재시도 가능한 이벤트 조회"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT id, path, event_type, error_message, retry_count, last_retry_time
                    FROM change_events
                    WHERE processed = 0
                    AND retry_count < ?
                    AND (last_retry_time IS NULL OR last_retry_time < ?)
                    ORDER BY priority DESC, timestamp ASC
                ''', (max_retries, time.time()))
                
                return cursor.fetchall()
                
        except Exception as e:
            self.logger.error(f"재시도 후보 조회 중 오류: {e}")
            raise
            
    def verify_database_integrity(self):
        """데이터베이스 무결성 검사 및 복구"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 1. 인덱스 재구축
                cursor.execute('REINDEX')
                
                # 2. 테이블 무결성 검사
                cursor.execute('PRAGMA integrity_check')
                integrity_result = cursor.fetchone()[0]
                
                if integrity_result != 'ok':
                    self.logger.warning(f"데이터베이스 무결성 문제 발견: {integrity_result}")
                    
                    # 3. 손상된 데이터 복구 시도
                    cursor.execute('PRAGMA foreign_key_check')
                    foreign_key_errors = cursor.fetchall()
                    
                    if foreign_key_errors:
                        self.logger.warning("외래 키 제약 조건 위반 발견")
                        # 손상된 레코드 복구
                        for table, rowid, parent, fkid in foreign_key_errors:
                            cursor.execute(f'DELETE FROM {table} WHERE rowid = ?', (rowid,))
                    
                    # 4. 통계 업데이트
                    cursor.execute('ANALYZE')
                    
                # 5. 최적화
                cursor.execute('PRAGMA optimize')
                
                conn.commit()
                
                return {
                    'integrity_check': integrity_result,
                    'foreign_key_errors': len(foreign_key_errors) if 'foreign_key_errors' in locals() else 0,
                    'optimized': True
                }
                
        except Exception as e:
            self.logger.error(f"데이터베이스 무결성 검사 중 오류: {e}")
            raise
            
    def backup_database(self, backup_path: str = None):
        """데이터베이스 백업"""
        try:
            if backup_path is None:
                # 기본 백업 경로 설정
                backup_dir = os.path.join(os.path.dirname(self.db_path), 'backups')
                os.makedirs(backup_dir, exist_ok=True)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_path = os.path.join(backup_dir, f'backup_metadata_{timestamp}.db')
            
            # 데이터베이스 백업
            with sqlite3.connect(self.db_path) as source_conn:
                with sqlite3.connect(backup_path) as backup_conn:
                    source_conn.backup(backup_conn)
            
            self.logger.info(f"데이터베이스 백업 완료: {backup_path}")
            return backup_path
            
        except Exception as e:
            self.logger.error(f"데이터베이스 백업 중 오류: {e}")
            raise
            
    def restore_database(self, backup_path: str):
        """데이터베이스 복원"""
        try:
            if not os.path.exists(backup_path):
                raise FileNotFoundError(f"백업 파일을 찾을 수 없음: {backup_path}")
            
            # 현재 DB 백업
            current_backup = self.backup_database()
            
            try:
                # 백업에서 복원
                with sqlite3.connect(backup_path) as backup_conn:
                    with sqlite3.connect(self.db_path) as target_conn:
                        backup_conn.backup(target_conn)
                
                # 무결성 검사
                integrity_result = self.verify_database_integrity()
                
                if integrity_result['integrity_check'] != 'ok':
                    # 복원 실패 시 현재 DB로 롤백
                    with sqlite3.connect(current_backup) as rollback_conn:
                        with sqlite3.connect(self.db_path) as target_conn:
                            rollback_conn.backup(target_conn)
                    raise Exception("복원된 데이터베이스 무결성 검사 실패")
                
                self.logger.info(f"데이터베이스 복원 완료: {backup_path}")
                return True
                
            except Exception as e:
                # 복원 실패 시 현재 DB로 롤백
                with sqlite3.connect(current_backup) as rollback_conn:
                    with sqlite3.connect(self.db_path) as target_conn:
                        rollback_conn.backup(target_conn)
                raise Exception(f"데이터베이스 복원 실패: {e}")
            
        except Exception as e:
            self.logger.error(f"데이터베이스 복원 중 오류: {e}")
            raise

    def update_event_status(self, event_id, status, processed_at=None, max_retries=3):
        """이벤트의 처리 상태를 업데이트합니다.
        
        Args:
            event_id (int): 업데이트할 이벤트 ID
            status (str): 새 상태 ("처리됨", "실패", "처리 중" 등)
            processed_at (float, optional): 처리 완료 시간. None이면 현재 시간 사용
            max_retries (int, optional): 최대 재시도 횟수. 기본값 3
            
        Returns:
            bool: 업데이트 성공 여부
        """
        if processed_at is None:
            processed_at = time.time()
            
        last_error = None
        conn = None
        
        for retry in range(max_retries):
            try:
                conn = self._get_connection()
                cursor = conn.cursor()
                
                # 이벤트 존재 여부 확인
                cursor.execute("SELECT id, processed FROM change_events WHERE id = ?", (event_id,))
                event = cursor.fetchone()
                if not event:
                    self.logger.error(f"이벤트가 존재하지 않음 (ID: {event_id})")
                    return False
                
                # 이미 처리된 이벤트는 다시 처리하지 않음
                if event[1] == 1 and status == "처리됨":
                    self.logger.info(f"이벤트가 이미 처리됨 (ID: {event_id})")
                    return True
                
                # status에 따른 processed 값 설정
                processed = 1 if status == "처리됨" else 0
                
                # 상태 업데이트 시도
                cursor.execute("""
                    UPDATE change_events 
                    SET processed = ?,
                        processing_time = ?,
                        last_update_attempt = ?,
                        update_attempt_count = update_attempt_count + 1,
                        error_message = CASE 
                            WHEN ? = '실패' THEN COALESCE(error_message, '처리 실패')
                            ELSE error_message
                        END
                    WHERE id = ?
                """, (processed, processed_at, time.time(), status, event_id))
                
                # 업데이트된 행이 있는지 확인
                if cursor.rowcount == 0:
                    raise Exception("업데이트된 행이 없음")
                
                conn.commit()
                self.logger.info(f"이벤트 상태 업데이트 성공 (ID: {event_id}, 상태: {status}, processed: {processed})")
                return True
                
            except sqlite3.Error as e:
                last_error = e
                self.logger.error(f"데이터베이스 오류 (시도 {retry + 1}/{max_retries}): {str(e)}")
                if conn:
                    try:
                        conn.rollback()
                    except:
                        pass
                time.sleep(0.5 * (2 ** retry))  # 지수 백오프
                continue
            except Exception as e:
                last_error = e
                self.logger.error(f"예상치 못한 오류 (시도 {retry + 1}/{max_retries}): {str(e)}")
                if conn:
                    try:
                        conn.rollback()
                    except:
                        pass
                time.sleep(0.5 * (2 ** retry))
                continue
            finally:
                if conn:
                    try:
                        conn.close()
                    except:
                        pass
        
        self.logger.error(f"이벤트 상태 업데이트 실패 (ID: {event_id}, 상태: {status}): {str(last_error)}")
        return False 

    def add_restore_event(self, path, is_directory, error_message=None):
        """복구 이벤트를 메타데이터 DB에 추가합니다."""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                current_time = time.time()
                
                # 복구 이벤트 추가
                cursor.execute("""
                    INSERT INTO change_events 
                    (path, event_type, is_directory, timestamp, processed, 
                     priority, error_message, created_at, last_update_attempt)
                    VALUES (?, 'restored', ?, ?, 0, 2, ?, ?, ?)
                """, (path, is_directory, current_time, error_message, 
                      current_time, current_time))
                
                event_id = cursor.lastrowid
                
                # 복구 이벤트 통계 업데이트
                cursor.execute("""
                    UPDATE backup_stats 
                    SET restore_count = restore_count + 1,
                        last_restore_time = ?
                    WHERE id = 1
                """, (current_time,))
                
                conn.commit()
                self.logger.info(f"복구 이벤트 추가 완료 (ID: {event_id}): {path}")
                return event_id
                
        except Exception as e:
            self.logger.error(f"복구 이벤트 추가 실패: {str(e)}")
            import traceback
            self.logger.error(f"상세 오류: {traceback.format_exc()}")
            return None

    def get_restore_stats(self, days=30):
        """복구 이벤트 통계를 조회합니다."""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cutoff_time = time.time() - (days * 86400)
                
                # 복구 성공/실패 통계
                cursor.execute("""
                    SELECT 
                        COUNT(CASE WHEN event_type = 'restored' THEN 1 END) as success_count,
                        COUNT(CASE WHEN event_type = 'restore_failed' THEN 1 END) as failure_count,
                        COUNT(CASE WHEN event_type IN ('restored', 'restore_failed') 
                                  AND is_directory = 1 THEN 1 END) as directory_count,
                        COUNT(CASE WHEN event_type IN ('restored', 'restore_failed') 
                                  AND is_directory = 0 THEN 1 END) as file_count
                    FROM change_events
                    WHERE event_type IN ('restored', 'restore_failed')
                    AND timestamp > ?
                """, (cutoff_time,))
                
                stats = cursor.fetchone()
                
                # 최근 복구 이벤트 목록
                cursor.execute("""
                    SELECT path, event_type, timestamp, error_message
                    FROM change_events
                    WHERE event_type IN ('restored', 'restore_failed')
                    AND timestamp > ?
                    ORDER BY timestamp DESC
                    LIMIT 10
                """, (cutoff_time,))
                
                recent_events = cursor.fetchall()
                
                return {
                    'success_count': stats[0] or 0,
                    'failure_count': stats[1] or 0,
                    'directory_count': stats[2] or 0,
                    'file_count': stats[3] or 0,
                    'recent_events': recent_events
                }
                
        except Exception as e:
            self.logger.error(f"복구 통계 조회 실패: {str(e)}")
            return None

    def cleanup_old_events(self, retention_policy=None):
        """보관 정책에 따른 오래된 이벤트 정리"""
        try:
            if retention_policy is None:
                retention_policy = {
                    'processed_days': 30,
                    'failed_days': 7,
                    'important_days': 90,
                    'restore_days': 60,  # 복구 이벤트 보관 기간 추가
                    'max_db_size': 1024 * 1024 * 1024
                }
            
            with self._get_connection() as conn:
                cursor = conn.cursor()
                current_time = time.time()
                
                # ... existing cleanup code ...
                
                # 복구 이벤트 정리 추가
                restore_cutoff = current_time - (retention_policy['restore_days'] * 86400)
                cursor.execute('''
                    DELETE FROM change_events
                    WHERE event_type IN ('restored', 'restore_failed')
                    AND timestamp < ?
                    AND processed = 1
                ''', (restore_cutoff,))
                
                conn.commit()
                self.logger.info(f"이벤트 정리 완료: {cursor.rowcount}개 이벤트 삭제됨")
                
        except Exception as e:
            self.logger.error(f"이벤트 정리 중 오류: {str(e)}")
            raise 