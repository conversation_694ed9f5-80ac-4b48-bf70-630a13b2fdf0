import os
import sys
import json
import time
import threading
import queue
import uuid
import re
from datetime import datetime, timedel<PERSON>
from concurrent.futures import ThreadPoolExecutor
import logging
from queue import Queue
import weakref
import psutil
import platform
import sqlite3

from PyQt5.QtCore import (Qt, QThread, pyqtSignal, QSize, pyqtSlot, QMetaObject,
                          Q_ARG, QStandardPaths, QTimer, QPropertyAnimation, QEasingCurve)
from PyQt5.QtGui import (QIcon, QTextCursor, QTextCharFormat, QColor, QPalette, QTextDocument, QTextOption,
                        QKeySequence, QPainter)
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QPushButton, QLineEdit,
                            QHBoxLayout, QComboBox, QLabel, QTreeWidget, QTreeWidgetItem, QStyle,
                            QFrame, QSplitter, QStatusBar, QHeaderView, QSizePolicy, QSpacerItem,
                            QScrollArea, QFileDialog, QTextEdit, QProgressBar, QMessageBox,
                            QToolButton, QCheckBox, QApplication, QShortcut, QStyleFactory,
                            QListWidget, QListWidgetItem, QInputDialog, QDialog, QDialogButtonBox,
                            QTabWidget, QTableWidget, QTableWidgetItem, QGridLayout)
from PyQt5.QtChart import (QChart, QChartView, QLineSeries, QValueAxis, QDateTimeAxis, QPieSeries)  # QPieSeries 추가

from backup_app.ui.styles import StyleManager, AnimationHelper
from backup_app.backup.initializer import BackupInitializer
from backup_app.backup.worker import BackupWorker
from backup_app.backup.handlers import BackupEventHandler
from backup_app.utils.path_manager import PathManager
from backup_app.utils.frame_calculator import FrameCalculator
from backup_app.utils.db_manager import SceneDbManager
from backup_app.ui.project_manager import ProjectManager, AddProjectDialog
from backup_app.config.default_projects import DEFAULT_PROJECTS
from PyQt5.QtChart import QChart, QChartView, QLineSeries, QValueAxis, QDateTimeAxis
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtChart import QChart, QChartView, QLineSeries, QValueAxis, QDateTimeAxis
from datetime import datetime, timedelta
import time

class ChartTreeWidget(QTreeWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAlternatingRowColors(True)
        self.setAnimated(True)
        self.setHeaderLabels(['씬 이름', 'Frames', 'FEET', '백업상태'])
        self.setSelectionMode(QTreeWidget.ExtendedSelection)
        header = self.header()
        header.setSectionResizeMode(QHeaderView.Interactive)
        header.setSectionsMovable(False)
        header.setStretchLastSection(False)
        self.setColumnWidth(0, 250)
        self.setColumnWidth(1, 80)
        self.setColumnWidth(2, 80)
        self.setColumnWidth(3, 80)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 폰트 크기 설정
        font = self.font()
        font.setPointSize(18)  # 폰트 크기를 18px로 설정
        self.setFont(font)
        
        # 헤더 폰트 크기도 설정
        header_font = header.font()
        header_font.setPointSize(18)  # 헤더도 동일한 크기로
        header.setFont(header_font)

    def update_scene_status_in_tree(self, scene_name, status):
        for i in range(self.topLevelItemCount()):
            item = self.topLevelItem(i)
            if item.text(0) == scene_name:
                item.setText(3, status)
                break
        self.viewport().update()

    def reset_all_scene_status_in_tree(self):
        for i in range(self.topLevelItemCount()):
            item = self.topLevelItem(i)
            item.setText(3, "")

class CardWidget(QFrame):
    def __init__(self, title=None, parent=None, object_name="CardWidget"):
        super().__init__(parent)
        self.setObjectName(object_name)
        self.setFrameShape(QFrame.StyledPanel)
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(5, 2, 5, 2)  # 상하 마진 최소화
        self.layout.setSpacing(2)  # 내부 위젯 간격 최소화
        if title:
            self.title = QLabel(title)
            self.title.setObjectName("headerLabel")
            self.title.setFixedHeight(20)  # 타이틀 높이 고정
            self.title.setContentsMargins(0, 0, 0, 0)  # 타이틀 마진 제거
            self.layout.addWidget(self.title)
            separator = QFrame()
            separator.setFrameShape(QFrame.HLine)
            separator.setFrameShadow(QFrame.Sunken)
            separator.setObjectName("separator")
            separator.setFixedHeight(1)  # 구분선 높이 최소화
            separator.setContentsMargins(0, 0, 0, 0)  # 구분선 마진 제거
            self.layout.addWidget(separator)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

class ShowSelectionWidget(CardWidget):
    def __init__(self, parent=None, title="작품 선택"):
        super().__init__(title, parent, "ShowSelectionCard")
        self._project_manager = None
        self._drilldown_stack = []  # 드릴다운 네비게이션을 위한 경로 스택
        self.is_right = title.endswith("(우)")  # 오른쪽 위젯인지 여부 확인
        
        # 레이아웃 마진 최소화
        self.layout.setContentsMargins(5, 0, 5, 0)  # 상단 여백을 0으로 설정
        self.layout.setSpacing(1)  # 레이아웃 간 간격을 1로 설정
        
        if hasattr(self, 'title'):
            self.title.setContentsMargins(0, 0, 0, 0)  # 타이틀 마진 제거
            self.title.setFixedHeight(18)  # 타이틀 높이 축소
        
        # 작품/서버 선택 콤보박스
        self.show_combo = QComboBox()
        self.show_combo.setEditable(False)
        self.show_combo.addItem("작품 선택" if not self.is_right else "서버 선택")
        # 작품 목록 추가
        if not self.is_right:
            self.show_combo.addItems(["BB", "GN", "BM", "KOTH", "Test", "Yeson_DANG", "Yeson_Test", "Yeson_Test_4K"])
        self.show_combo.setFixedHeight(24)  # 콤보박스 높이 조정
        self.show_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        # 서버 경로 정의 (우측 위젯용)
        self.server_paths = {
            "usadata1": "/System/Volumes/Data/usadata1",
            "usadata4": "/Users/<USER>/Desktop/usadata4",
            "usadata5": "/System/Volumes/Data/usadata5",
            "usadata6": "/System/Volumes/Data/usadata6",
            "bgfinal": "/Volumes/bgfinal"
        }

        # --- show_layout ---
        self.show_layout = QHBoxLayout()
        self.show_layout.setSpacing(2)
        self.show_layout.setContentsMargins(0, 0, 0, 0)
        show_label = QLabel("작품:" if "좌" in title else "서버:")
        show_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.show_layout.addWidget(show_label)
        self.show_layout.addWidget(self.show_combo)
        self.show_backup_btn = QPushButton("전체 백업")
        self.show_backup_btn.setToolTip("선택한 작품 전체를 초기 동기화하고 실시간 백업 시작")
        self.show_backup_btn.setCursor(Qt.PointingHandCursor)
        self.show_backup_btn.setFixedHeight(24)  # 버튼 높이 조정
        self.show_backup_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.show_layout.addWidget(self.show_backup_btn)
        self.show_watch_btn = QPushButton("실시간 감시")
        self.show_watch_btn.setToolTip("선택한 작품 경로에 대해 기존 백업 폴더를 지정하고 실시간 감시만 시작")
        self.show_watch_btn.setCursor(Qt.PointingHandCursor)
        self.show_watch_btn.setFixedHeight(24)  # 버튼 높이 조정
        self.show_watch_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.show_layout.addWidget(self.show_watch_btn)
        self.layout.addLayout(self.show_layout)
        
        # --- season_layout ---
        self.season_layout = QHBoxLayout()
        self.season_layout.setSpacing(2)
        self.season_layout.setContentsMargins(0, 0, 0, 0)
        season_label = QLabel("시즌:" if "좌" in title else "서버폴더:")
        season_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.season_layout.addWidget(season_label)
        self.season_combo = QComboBox()
        self.season_combo.addItem("시즌 선택" if "좌" in title else "서버폴더 선택")
        self.season_combo.setEnabled(False)
        self.season_combo.setFixedHeight(24)  # 콤보박스 높이 조정
        self.season_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.season_layout.addWidget(self.season_combo)
        self.season_backup_btn = QPushButton("전체 백업")
        self.season_backup_btn.setToolTip("선택한 시즌 전체를 초기 동기화하고 실시간 백업 시작")
        self.season_backup_btn.setCursor(Qt.PointingHandCursor)
        self.season_backup_btn.setFixedHeight(24)  # 버튼 높이 조정
        self.season_backup_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.season_layout.addWidget(self.season_backup_btn)
        self.season_watch_btn = QPushButton("실시간 감시")
        self.season_watch_btn.setToolTip("선택한 시즌 경로에 대해 기존 백업 폴더를 지정하고 실시간 감시만 시작")
        self.season_watch_btn.setCursor(Qt.PointingHandCursor)
        self.season_watch_btn.setFixedHeight(24)  # 버튼 높이 조정
        self.season_watch_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.season_layout.addWidget(self.season_watch_btn)
        self.layout.addLayout(self.season_layout)
        
        # --- episode_layout ---
        self.episode_layout = QHBoxLayout()
        self.episode_layout.setSpacing(2)
        self.episode_layout.setContentsMargins(0, 0, 0, 0)
        episode_label = QLabel("화수:" if "좌" in title else "폴더:")
        episode_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.episode_layout.addWidget(episode_label)
        self.episode_combo = QComboBox()
        self.episode_combo.addItem("화수 선택" if "좌" in title else "폴더 선택")
        self.episode_combo.setEnabled(False)
        self.episode_combo.setFixedHeight(24)  # 콤보박스 높이 조정
        self.episode_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.episode_layout.addWidget(self.episode_combo)
        self.episode_backup_btn = QPushButton("전체 백업")
        self.episode_backup_btn.setToolTip("선택한 화수 전체를 초기 동기화하고 실시간 백업 시작")
        self.episode_backup_btn.setCursor(Qt.PointingHandCursor)
        self.episode_backup_btn.setFixedHeight(24)  # 버튼 높이 조정
        self.episode_backup_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.episode_layout.addWidget(self.episode_backup_btn)
        self.episode_watch_btn = QPushButton("실시간 감시")
        self.episode_watch_btn.setToolTip("선택한 화수 경로에 대해 기존 백업 폴더를 지정하고 실시간 감시만 시작")
        self.episode_watch_btn.setCursor(Qt.PointingHandCursor)
        self.episode_watch_btn.setFixedHeight(24)  # 버튼 높이 조정
        self.episode_watch_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.episode_layout.addWidget(self.episode_watch_btn)
        self.layout.addLayout(self.episode_layout)
        
        # 각 레이아웃 사이의 간격 조정
        self.layout.setSpacing(2)  # 레이아웃 간 간격을 2로 설정

    @property
    def project_manager(self):
        return self._project_manager

    @project_manager.setter
    def project_manager(self, manager):
        if self._project_manager:
            try:
                self._project_manager.projectsChanged.disconnect(self.on_projects_changed)
            except:
                pass
        self._project_manager = manager
        if manager:
            manager.projectsChanged.connect(self.on_projects_changed)
        self.on_projects_changed()

    def on_projects_changed(self):
        """작품/서버 목록이 변경되었을 때 호출됩니다."""
        current_text = self.show_combo.currentText()
        self.show_combo.clear()
        
        if self.is_right:
            # 서버 목록 업데이트
            self.show_combo.addItem("서버 선택")
            if hasattr(ValidationApp, 'SERVER_PATHS'):
                server_list = sorted(ValidationApp.SERVER_PATHS.keys())
                self.show_combo.addItems(server_list)
        else:
            # 작품 목록 업데이트
            self.show_combo.addItem("작품 선택")
            if self._project_manager:
                projects = self._project_manager.get_projects()
                if not projects:  # 프로젝트 목록이 비어있으면 기본 프로젝트 추가
                    from backup_app.config.default_projects import DEFAULT_PROJECTS
                    for project in DEFAULT_PROJECTS:
                        self._project_manager.add_project(project)
                    projects = self._project_manager.get_projects()
                self.show_combo.addItems(sorted(projects))
        
        # 이전 선택 항목 복원 시도
        index = self.show_combo.findText(current_text)
        if index >= 0:
            self.show_combo.setCurrentIndex(index)
            
        # 시즌과 에피소드 콤보박스 초기화
        self.season_combo.clear()
        self.season_combo.addItem("시즌 선택" if not self.is_right else "서버폴더 선택")
        self.season_combo.setEnabled(False)
        self.episode_combo.clear()
        self.episode_combo.addItem("화수 선택" if not self.is_right else "폴더 선택")
        self.episode_combo.setEnabled(False)
        
        # 이전 선택 항목 복원 시도
        index = self.show_combo.findText(current_text)
        if index >= 0:
            self.show_combo.setCurrentIndex(index)

class SummaryWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("에피소드 정보", parent, "SummaryCard")
        summary_layout = QHBoxLayout()
        summary_layout.setSpacing(10)
        frame_layout = QVBoxLayout()
        frame_title = QLabel("총 프레임 수:")
        frame_title.setAlignment(Qt.AlignCenter)
        frame_layout.addWidget(frame_title)
        self.total_frames_display = QLineEdit()
        self.total_frames_display.setAlignment(Qt.AlignCenter)
        self.total_frames_display.setReadOnly(True)
        self.total_frames_display.setMinimumWidth(80)
        self.total_frames_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        frame_layout.addWidget(self.total_frames_display)
        summary_layout.addLayout(frame_layout)
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("separator")
        summary_layout.addWidget(separator)
        feet_layout = QVBoxLayout()
        feet_title = QLabel("총 FEET:")
        feet_title.setAlignment(Qt.AlignCenter)
        feet_layout.addWidget(feet_title)
        self.episode_feet_display = QLineEdit()
        self.episode_feet_display.setAlignment(Qt.AlignCenter)
        self.episode_feet_display.setReadOnly(True)
        self.episode_feet_display.setMinimumWidth(80)
        self.episode_feet_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        feet_layout.addWidget(self.episode_feet_display)
        summary_layout.addLayout(feet_layout)
        self.layout.addLayout(summary_layout)

class CalculationWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("선택 씬 계산", parent, "CalculationCard")
        calc_layout = QHBoxLayout()
        calc_layout.setSpacing(8)
        
        # 계산 버튼
        self.calc_button = QPushButton("선택 씬 계산")
        self.calc_button.setCursor(Qt.PointingHandCursor)
        self.calc_button.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        calc_layout.addWidget(self.calc_button)
        calc_layout.addSpacing(10)
        
        # 프레임 표시 영역
        frames_label = QLabel("총 프레임:")
        frames_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        calc_layout.addWidget(frames_label)
        
        self.total_frames_display = QLineEdit()
        self.total_frames_display.setAlignment(Qt.AlignCenter)
        self.total_frames_display.setReadOnly(True)
        self.total_frames_display.setMinimumWidth(100)
        self.total_frames_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        calc_layout.addWidget(self.total_frames_display)
        
        # FEET 표시 영역
        feet_label = QLabel("총 FEET:")
        feet_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        calc_layout.addWidget(feet_label)
        
        self.total_feet_display = QLineEdit()
        self.total_feet_display.setAlignment(Qt.AlignCenter)
        self.total_feet_display.setReadOnly(True)
        self.total_feet_display.setMinimumWidth(100)
        self.total_feet_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        calc_layout.addWidget(self.total_feet_display)
        
        calc_layout.addStretch()
        self.layout.addLayout(calc_layout)

    def calculate_selected_scenes(self):
        """선택된 씬들의 프레임 수와 FEET를 계산합니다."""
        try:
            selected_items = self.scene_list_widget.scene_tree.selectedItems()
            if not selected_items:
                return

            total_frames = 0
            for item in selected_items:
                scene_name = item.text(0)
                if scene_name:
                    # 프레임 정보는 두 번째 열에서 가져옴
                    frames_text = item.text(1)
                    try:
                        if frames_text and frames_text != "N/A":
                            frames = int(frames_text)
                            total_frames += frames
                    except ValueError:
                        continue

            # FEET 계산 (1 FEET = 16 프레임)
            total_feet = total_frames / 16 if total_frames > 0 else 0

            # 결과 표시
            frames_display = f"{total_frames:,}"
            feet_display = f"{total_feet:,.2f}"

            self.calculation_widget.total_feet_display.setText(feet_display)

            # 상태바 메시지 업데이트
            self.statusBar.showMessage(
                f"선택된 씬: {len(selected_items)}개, 총 프레임: {frames_display}, FEET: {feet_display}",
                5000
            )

        except Exception as e:
            self.statusBar.showMessage(f"씬 계산 중 오류 발생: {str(e)}", 5000)
            logging.error(f"씬 계산 중 오류: {e}")

class SceneListWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("씬 목록", parent, "SceneListCard")
        self.scene_tree = ChartTreeWidget()
        self.scene_tree.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.scene_tree.itemSelectionChanged.connect(lambda: parent.calculate_selected_scenes() if parent else None)
        self.layout.addWidget(self.scene_tree)
        button_layout = QHBoxLayout()
        self.export_button = QPushButton("엑셀로 내보내기")
        self.export_button.setCursor(Qt.PointingHandCursor)
        self.export_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        button_layout.addWidget(self.export_button)
        button_layout.addStretch()
        selection_label = QLabel("Tip: 여러 씬을 선택하려면 Ctrl 또는 Shift 키를 사용하세요.")
        selection_label.setAlignment(Qt.AlignRight)
        selection_label.setStyleSheet("font-style: italic; font-size: 10px;")
        button_layout.addWidget(selection_label)
        self.layout.addLayout(button_layout)

class SingleJobStatusWidget(QFrame):
    stop_requested = pyqtSignal(str)
    sync_interval_changed = pyqtSignal(str, int)
    backup_mode_changed = pyqtSignal(str, str)
    
    def __init__(self, job_id, source_path, dest_path, parent=None, sync_interval=0, backup_mode="mirror", theme="light"):
        super().__init__(parent)
        self.job_id = job_id
        self.source_path = source_path
        self.dest_path = dest_path
        self.sync_interval = sync_interval
        self.backup_mode = backup_mode
        self.is_expanded = True
        
        # 로그 업데이트 최적화를 위한 변수들
        self.log_buffer = []
        self.last_log_update = time.time()
        self.log_update_interval = 0.5  # 0.5초마다 로그 업데이트 (2초에서 변경)
        self.log_update_timer = QTimer(self)
        self.log_update_timer.timeout.connect(self.flush_log_buffer)
        self.log_update_timer.start(500)  # 500ms (2000ms에서 변경)
        
        # 로그 관리를 위한 변수들
        self.max_log_lines = 250  # 최대 로그 라인 수
        self.cleanup_threshold = 200  # 정리 시작 기준
        self.lines_to_keep = 150  # 정리 후 유지할 라인 수
        self.transfer_count = 0  # 파일 전송 카운트
        self.last_transfer_update = time.time()
        self.transfer_update_interval = 2.0  # 전송 상태 업데이트 간격
        
        # 상태 업데이트 최적화를 위한 변수들
        self.status_update_timer = QTimer(self)
        self.status_update_timer.timeout.connect(self.update_status_display)
        self.status_update_timer.start(200)  # 200ms
        self.pending_status = None
        self.pending_progress = -1
        
        # 애니메이션 설정
        self.animation = QPropertyAnimation(self, b"minimumHeight")
        self.animation.setDuration(300)  # 300ms
        self.animation.setEasingCurve(QEasingCurve.InOutCubic)
        
        # UI 구성을 먼저 실행
        self.setup_ui()
        
        # 테마 스타일 적용
        self.update_theme_style(theme)

    def append_log(self, message):
        """로그 메시지를 버퍼에 추가합니다."""
        if not message:
            return

        # 타임스탬프 추가 (이미 있는 경우 건너뛰기)
        if not message.startswith('[20'):  # 2024년 형식의 타임스탬프로 시작하지 않는 경우
            timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
            message = f"[{timestamp}] {message}"

        # 파일 전송 로그 처리
        if "파일 전송:" in message or "디렉토리 생성:" in message:
            self.transfer_count += 1
            current_time = time.time()
            
            # 파일 전송 로그를 항상 추가
            formatted_message = f'<span style="color: #2196F3;">{message}</span>'
            self.log_buffer.append(formatted_message)
            
            # 일정 간격으로 전송 상태 요약 추가
            if current_time - self.last_transfer_update >= self.transfer_update_interval:
                summary = f'<span style="color: #4CAF50;">[{time.strftime("%Y-%m-%d %H:%M:%S")}] 지금까지 {self.transfer_count}개 항목 처리됨</span>'
                self.log_buffer.append(summary)
                self.last_transfer_update = current_time
            return

        # 중요 로그 키워드 정의
        important_keywords = [
            "시작",
            "완료",
            "동기화 간격",
            "백업 모드",
            "오류",
            "실패",
            "error",
            "failed"
        ]

        # 중요 메시지만 스타일 적용
        if any(keyword in message.lower() for keyword in ["오류", "실패", "error", "failed"]):
            formatted_message = f'<span style="color: #ff6b6b;">{message}</span>'
        elif "완료" in message:
            formatted_message = f'<span style="color: #4CAF50;">{message}</span>'
        elif any(keyword in message for keyword in ["동기화 간격", "백업 모드"]):
            formatted_message = f'<span style="color: #2196F3;">{message}</span>'
        elif any(keyword in message.lower() for keyword in important_keywords):
            formatted_message = message
        else:
            return

        self.log_buffer.append(formatted_message)

    def flush_log_buffer(self):
        """버퍼에 있는 로그를 효율적으로 화면에 표시합니다."""
        if not self.log_buffer or not self.is_expanded:
            return

        current_time = time.time()
        if current_time - self.last_log_update < self.log_update_interval:
            return

        try:
            # 로그 위젯이 숨겨져 있으면 버퍼만 비우고 반환
            if not self.log_display.isVisible():
                self.log_buffer.clear()
                return

            # 문서 수정 시작 전에 스크롤바 위치 저장
            scroll_bar = self.log_display.verticalScrollBar()
            was_at_bottom = scroll_bar.value() >= scroll_bar.maximum() - 10

            # 문서 수정 시작
            self.log_display.document().blockSignals(True)
            
            # 현재 라인 수 확인
            current_lines = self.log_display.document().lineCount()
            
            # 정리가 필요한지 확인
            if current_lines > self.cleanup_threshold:
                # 텍스트 전체를 가져와서 처리
                text = self.log_display.toPlainText()
                lines = text.splitlines()
                # 마지막 N줄만 유지
                kept_lines = lines[-self.lines_to_keep:]
                self.log_display.setPlainText('\n'.join(kept_lines))
            
            # 새 로그를 한 번에 추가
            cursor = self.log_display.textCursor()
            cursor.movePosition(cursor.End)
            cursor.insertHtml("<br>".join(self.log_buffer) + "<br>")
            
            # 문서 수정 완료
            self.log_display.document().blockSignals(False)
            
            # 이전에 스크롤바가 바닥에 있었다면 다시 바닥으로 이동
            if was_at_bottom:
                scroll_bar.setValue(scroll_bar.maximum())
            
        except Exception as e:
            print(f"로그 업데이트 중 오류: {e}")
        finally:
            self.log_buffer.clear()
            self.last_log_update = current_time

    def setup_ui(self):
        # 메인 레이아웃
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(15, 15, 15, 15)
        self.main_layout.setSpacing(10)

        # 상단 레이아웃 (경로 + 접기 버튼)
        top_layout = QHBoxLayout()
        
        # 경로 정보 - 소스는 마지막 폴더만, 목적지는 전체 경로
        source_name = os.path.basename(self.source_path)
        path_text = f"{source_name} → {self.dest_path}"
        self.path_label = QLabel(path_text)
        self.path_label.setWordWrap(True)  # 긴 경로일 경우 자동 줄바꿈
        top_layout.addWidget(self.path_label)
        
        # 접기/펼치기 버튼
        self.toggle_button = QToolButton()
        self.toggle_button.setText("▼")
        self.toggle_button.setToolTip("로그 영역 접기/펼치기")
        self.toggle_button.clicked.connect(self.toggle_log_area)
        top_layout.addWidget(self.toggle_button)
        
        self.main_layout.addLayout(top_layout)

        # 설정 영역
        self.settings_frame = QFrame()
        settings_layout = QHBoxLayout(self.settings_frame)
        settings_layout.setContentsMargins(10, 5, 10, 5)
        settings_layout.setSpacing(10)

        # 동기화 간격 설정
        sync_layout = QHBoxLayout()
        self.sync_label = QLabel("동기화:")
        sync_layout.addWidget(self.sync_label)

        self.sync_interval_combo = QComboBox()
        self.sync_interval_combo.addItem("무설정", 0)
        self.sync_interval_combo.addItem("30초", 30)
        self.sync_interval_combo.addItem("1분", 60)
        self.sync_interval_combo.addItem("2분", 120)
        self.sync_interval_combo.addItem("5분", 300)
        self.sync_interval_combo.addItem("10분", 600)
        self.sync_interval_combo.addItem("30분", 1800)
        self.sync_interval_combo.addItem("1시간", 3600)

        # 초기 값 설정
        index = self.sync_interval_combo.findData(self.sync_interval)
        if index >= 0:
            self.sync_interval_combo.setCurrentIndex(index)

        sync_layout.addWidget(self.sync_interval_combo)
        settings_layout.addLayout(sync_layout)

        # 구분선
        self.separator = QFrame()
        self.separator.setFrameShape(QFrame.VLine)
        self.separator.setFrameShadow(QFrame.Sunken)
        settings_layout.addWidget(self.separator)

        # 백업 모드 설정
        mode_layout = QHBoxLayout()
        self.mode_label = QLabel("백업 모드:")
        mode_layout.addWidget(self.mode_label)

        self.backup_mode_combo = QComboBox()
        self.backup_mode_combo.addItem("미러링", "mirror")
        self.backup_mode_combo.addItem("인크리멘탈", "incremental")
        mode_layout.addWidget(self.backup_mode_combo)
        settings_layout.addLayout(mode_layout)

        settings_layout.addStretch()

        # 폴더 열기 버튼
        self.open_folder_button = QPushButton("폴더 열기")
        self.open_folder_button.setToolTip("목적지 폴더 열기")
        self.open_folder_button.clicked.connect(self.open_dest_folder)
        settings_layout.addWidget(self.open_folder_button)

        # 중지 버튼
        self.stop_button = QPushButton("중지")
        settings_layout.addWidget(self.stop_button)

        # 상태 레이블
        self.status_label = QLabel("대기 중...")
        settings_layout.addWidget(self.status_label)

        self.main_layout.addWidget(self.settings_frame)

        # 로그 영역
        self.log_container = QWidget()
        log_container_layout = QVBoxLayout(self.log_container)
        log_container_layout.setContentsMargins(0, 0, 0, 0)
        log_container_layout.setSpacing(10)

        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        log_container_layout.addWidget(self.log_display)

        self.progress_bar = QProgressBar()
        self.progress_bar.setFormat("%p%")
        log_container_layout.addWidget(self.progress_bar)

        self.main_layout.addWidget(self.log_container)

        # 시그널 연결
        self.sync_interval_combo.currentIndexChanged.connect(self.on_sync_interval_changed)
        self.backup_mode_combo.currentIndexChanged.connect(self.on_backup_mode_changed)
        self.stop_button.clicked.connect(self.request_stop)

        # 초기 상태 설정
        index = self.sync_interval_combo.findData(self.sync_interval)
        if index >= 0:
            self.sync_interval_combo.setCurrentIndex(index)

        index = self.backup_mode_combo.findData(self.backup_mode)
        if index >= 0:
            self.backup_mode_combo.setCurrentIndex(index)

        # 로그 디스플레이 최적화
        self.log_display.document().setMaximumBlockCount(1000)
        self.log_display.setLineWrapMode(QTextEdit.NoWrap)
        self.log_display.setUndoRedoEnabled(False)
        self.log_display.setReadOnly(True)
        self.log_display.setAcceptRichText(True)
        self.log_display.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        
        # 문서 업데이트 최적화
        doc = self.log_display.document()
        doc.setUndoRedoEnabled(False)
        doc.setMaximumBlockCount(1000)
        
        # 프로그레스바 업데이트 최적화
        self.progress_bar.setTextVisible(False)

    def open_dest_folder(self):
        """목적지 폴더를 엽니다."""
        try:
            if os.path.exists(self.dest_path):
                if sys.platform == 'darwin':  # macOS
                    os.system(f'open "{self.dest_path}"')
                elif sys.platform == 'win32':  # Windows
                    os.startfile(self.dest_path)
                else:  # Linux
                    os.system(f'xdg-open "{self.dest_path}"')
                self.append_log(f"목적지 폴더를 열었습니다: {self.dest_path}")
            else:
                self.append_log(f"목적지 폴더를 찾을 수 없습니다: {self.dest_path}")
        except Exception as e:
            self.append_log(f"폴더 열기 실패: {str(e)}")

    def update_theme_style(self, theme):
        """테마에 따라 스타일시트를 업데이트합니다."""
        if theme == "dark":
            self.setStyleSheet("""
                QFrame {
                    background-color: #2d2d2d;
                    border-radius: 6px;
                    padding: 15px;
                }
                QLabel {
                    color: #e0e0e0;
                    font-size: 12px;
                }
                QPushButton {
                    background-color: #424242;
                    color: #e0e0e0;
                    border: 1px solid #555555;
                    border-radius: 3px;
                    padding: 5px 10px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #4a4a4a;
                }
                QComboBox {
                    background-color: #424242;
                    color: #e0e0e0;
                    border: 1px solid #555555;
                    border-radius: 3px;
                    padding: 3px;
                    font-size: 12px;
                    min-width: 100px;
                }
                QTextEdit {
                    background-color: #1e1e1e;
                    color: #e0e0e0;
                    border: none;
                    border-radius: 3px;
                    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                    font-size: 12px;
                    padding: 10px;
                }
                QProgressBar {
                    border: none;
                    background-color: #424242;
                    height: 20px;
                    text-align: center;
                    border-radius: 3px;
                    color: white;
                    font-size: 12px;
                }
                QProgressBar::chunk {
                    background-color: #4CAF50;
                    border-radius: 3px;
                }
                QToolButton {
                    background-color: transparent;
                    border: none;
                    color: #e0e0e0;
                    font-size: 16px;
                    padding: 2px;
                }
                QToolButton:hover {
                    background-color: #4a4a4a;
                    border-radius: 3px;
                }
            """)
            self.path_label.setStyleSheet("""
                QLabel {
                    color: #e0e0e0;
                    font-size: 13px;
                    font-weight: bold;
                    padding: 5px;
                    background-color: #333333;
                    border-radius: 3px;
                }
            """)
            self.settings_frame.setStyleSheet("""
                QFrame {
                    background-color: #333333;
                    border-radius: 3px;
                    padding: 5px;
                }
            """)
            self.separator.setStyleSheet("background-color: #555555;")
            self.stop_button.setStyleSheet("""
                QPushButton {
                    background-color: #f44336;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    padding: 5px 10px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #d32f2f;
                }
            """)
            self.status_label.setStyleSheet("""
                QLabel {
                    color: #e0e0e0;
                    font-size: 12px;
                    padding: 3px 8px;
                    background-color: #424242;
                    border-radius: 3px;
                }
            """)
            self.open_folder_button.setStyleSheet("""
                QPushButton {
                    background-color: #424242;
                    color: #e0e0e0;
                    border: 1px solid #555555;
                    border-radius: 3px;
                    padding: 5px 10px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #4a4a4a;
                }
            """)
        else:  # light theme
            self.setStyleSheet("""
                QFrame {
                    background-color: #f5f5f5;
                    border-radius: 6px;
                    padding: 15px;
                    border: 1px solid #e0e0e0;
                }
                QLabel {
                    color: #333333;
                    font-size: 12px;
                }
                QPushButton {
                    background-color: #ffffff;
                    color: #333333;
                    border: 1px solid #cccccc;
                    border-radius: 3px;
                    padding: 5px 10px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #f0f0f0;
                }
                QComboBox {
                    background-color: #ffffff;
                    color: #333333;
                    border: 1px solid #cccccc;
                    border-radius: 3px;
                    padding: 3px;
                    font-size: 12px;
                    min-width: 100px;
                }
                QTextEdit {
                    background-color: #ffffff;
                    color: #333333;
                    border: 1px solid #e0e0e0;
                    border-radius: 3px;
                    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                    font-size: 12px;
                    padding: 10px;
                }
                QProgressBar {
                    border: none;
                    background-color: #f0f0f0;
                    height: 20px;
                    text-align: center;
                    border-radius: 3px;
                    color: #333333;
                    font-size: 12px;
                }
                QProgressBar::chunk {
                    background-color: #4CAF50;
                    border-radius: 3px;
                }
                QToolButton {
                    background-color: transparent;
                    border: none;
                    color: #333333;
                    font-size: 16px;
                    padding: 2px;
                }
                QToolButton:hover {
                    background-color: #f0f0f0;
                    border-radius: 3px;
                }
            """)
            self.path_label.setStyleSheet("""
                QLabel {
                    color: #333333;
                    font-size: 13px;
                    font-weight: bold;
                    padding: 5px;
                    background-color: #ffffff;
                    border: 1px solid #e0e0e0;
                    border-radius: 3px;
                }
            """)
            self.settings_frame.setStyleSheet("""
                QFrame {
                    background-color: #ffffff;
                    border-radius: 3px;
                    padding: 5px;
                    border: 1px solid #e0e0e0;
                }
            """)
            self.separator.setStyleSheet("background-color: #e0e0e0;")
            self.stop_button.setStyleSheet("""
                QPushButton {
                    background-color: #f44336;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    padding: 5px 10px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #d32f2f;
                }
            """)
            self.status_label.setStyleSheet("""
                QLabel {
                    color: #333333;
                    font-size: 12px;
                    padding: 3px 8px;
                    background-color: #ffffff;
                    border: 1px solid #e0e0e0;
                    border-radius: 3px;
                }
            """)
            self.open_folder_button.setStyleSheet("""
                QPushButton {
                    background-color: #ffffff;
                    color: #333333;
                    border: 1px solid #cccccc;
                    border-radius: 3px;
                    padding: 5px 10px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #f0f0f0;
                }
            """)

    def toggle_log_area(self):
        """로그 영역을 부드럽게 접거나 펼칩니다."""
        self.is_expanded = not self.is_expanded
        self.toggle_button.setText("▼" if self.is_expanded else "▶")

        if self.is_expanded:
            # 펼치기: 먼저 로그 디스플레이를 보이게 설정
            self.log_display.setVisible(True)
            self.log_container.setVisible(True)
            self.animation.setStartValue(self.minimumHeight())
            self.animation.setEndValue(400)  # 펼쳤을 때 높이
        else:
            # 접기
            self.animation.setStartValue(self.minimumHeight())
            self.animation.setEndValue(150)  # 접었을 때 높이
            # 애니메이션이 끝나면 로그 디스플레이를 숨김
            self.animation.finished.connect(lambda: self.hide_log_display())

        self.animation.start()

    def hide_log_display(self):
        """로그 디스플레이를 숨깁니다."""
        if not self.is_expanded:
            self.log_display.setVisible(False)
            self.log_container.setVisible(False)

    def update_status_display(self):
        """상태 표시를 효율적으로 업데이트합니다."""
        try:
            if self.pending_status is None:
                return

            # 상태 텍스트가 변경된 경우에만 업데이트
            if self.status_label.text() != self.pending_status:
                self.status_label.setText(self.pending_status)

            # 진행률이 변경된 경우에만 업데이트
            if self.pending_progress >= 0 and self.progress_bar.value() != self.pending_progress:
                self.progress_bar.setValue(self.pending_progress)

            # 버튼 상태 업데이트 - 중지 조건 수정
            should_enable = not any(x in self.pending_status.lower() for x in ["중지됨", "오류"])
            if self.stop_button.isEnabled() != should_enable:
                self.stop_button.setEnabled(should_enable)

            self.pending_status = None
            self.pending_progress = -1

        except Exception as e:
            print(f"Error in update_status_display: {str(e)}")
            import traceback
            print(traceback.format_exc())

    def closeEvent(self, event):
        """위젯이 닫힐 때 타이머를 정리합니다."""
        try:
            # 타이머 중지
            if hasattr(self, 'log_update_timer'):
                self.log_update_timer.stop()
            if hasattr(self, 'status_update_timer'):
                self.status_update_timer.stop()
            
            # 부모 위젯에서 제거
            if self.parent():
                self.parent().layout().removeWidget(self)
            
            # 위젯 제거
            self.setParent(None)
            self.deleteLater()
            
            super().closeEvent(event)
        except Exception as e:
            print(f"위젯 닫기 중 오류: {str(e)}")
            import traceback
            print(traceback.format_exc())

    def request_stop(self):
        """작업 중지 요청"""
        try:
            self.stop_requested.emit(self.job_id)
            self.set_status("중지 요청 중...", -1)
            self.stop_button.setEnabled(False)
            
            # 활성 작업 목록에서 제거
            if hasattr(self, 'parent') and self.parent():
                parent = self.parent()
                if hasattr(parent, 'active_jobs_widget'):
                    parent.active_jobs_widget.remove_job_widget(self.job_id)
            
            # 작업 상태 저장
            if hasattr(self, 'parent') and self.parent():
                parent = self.parent()
                if hasattr(parent, 'save_active_jobs'):
                    parent.save_active_jobs()
            
            # 위젯 즉시 제거
            if self.parent():
                self.parent().layout().removeWidget(self)
            self.setParent(None)
            self.hide()
            self.deleteLater()
            
            QApplication.processEvents()  # UI 업데이트 강제 실행
        except Exception as e:
            print(f"작업 중지 요청 중 오류: {str(e)}")
            import traceback
            print(traceback.format_exc())

    def on_sync_interval_changed(self, index):
        """동기화 간격이 변경되었을 때 호출되는 메서드"""
        interval = self.sync_interval_combo.currentData()  # 데이터(초 단위)로 가져오기
        self.sync_interval = interval  # 현재 위젯의 interval 값 업데이트
        self.sync_interval_changed.emit(self.job_id, interval)  # 시그널 발생

    def on_backup_mode_changed(self, index):
        """백업 모드가 변경되었을 때 호출되는 메서드"""
        try:
            new_mode = self.backup_mode_combo.itemData(index)
            if new_mode != self.backup_mode:
                old_mode = self.backup_mode
                old_mode_display = "미러링" if old_mode == "mirror" else "인크리멘탈"
                new_mode_display = "미러링" if new_mode == "mirror" else "인크리멘탈"
                
                self.backup_mode = new_mode
                self.append_log(f"백업 모드가 {old_mode_display}에서 {new_mode_display}로 변경됩니다.")
                self.status_label.setText(f"{new_mode_display} 모드로 실시간 감시 중")
                self.backup_mode_changed.emit(self.job_id, new_mode)
        except Exception as e:
            self.append_log(f"백업 모드 변경 중 오류: {str(e)}")

    def format_interval(self, seconds):
        if seconds <= 0:
            return "무설정"
        elif seconds < 60:
            return f"{seconds}초"
        elif seconds < 3600:
            return f"{seconds // 60}분"
        else:
            return f"{seconds // 3600}시간"

    def handle_sync_complete(self):
        """동기화 완료 시 호출되는 메서드"""
        self.progress_bar.setValue(100)
        self.progress_label.setText("100%")
        self.status_label.setText("동기화 완료")
        self.status_label.setStyleSheet("color: #4CAF50;")

    def process_rsync_output(self, output):
        """rsync 출력을 처리하고 진행률을 업데이트합니다."""
        try:
            # 완료 메시지 확인
            if any(msg in output.lower() for msg in ["total size is", "speedup is"]):
                self.progress_bar.setValue(100)
                self.progress_label.setText("100%")
                self.status_label.setText("동기화 완료")
                self.status_label.setStyleSheet("color: #4CAF50;")
                return

            # 전송 시작 메시지 확인
            if "Transfer starting:" in output:
                self.progress_bar.setValue(0)
                self.progress_label.setText("0%")
                return

            # 진행률 정보 파싱
            if "to-check=" in output:
                match = re.search(r"to-check=(\d+)/(\d+)", output)
                if match:
                    remaining = int(match.group(1))
                    total = int(match.group(2))
                    if total > 0:  # 0으로 나누기 방지
                        current = total - remaining
                        percentage = int((current / total) * 100)
                        if remaining == 0 or percentage >= 95:  # 남은 파일이 없거나 95% 이상이면 100%로 설정
                            percentage = 100
                        self.progress_bar.setValue(percentage)
                        self.progress_label.setText(f"{percentage}%")

        except Exception as e:
            print(f"Error processing rsync output: {e}")

    def set_status(self, message, progress=-1):
        """작업의 상태와 진행률을 업데이트합니다."""
        try:
            # 상태 업데이트를 pending으로 설정
            self.pending_status = message
            self.pending_progress = progress

            # 상태 레이블 스타일 업데이트
            if "오류" in message or "실패" in message:
                self.status_label.setStyleSheet("color: #ff6b6b;")  # 빨간색
            elif "완료" in message or "중지" in message:
                self.status_label.setStyleSheet("color: #4CAF50;")  # 초록색
            else:
                # 현재 테마에 따른 기본 색상 사용
                self.status_label.setStyleSheet("")

            # 즉시 업데이트가 필요한 경우
            if message and self.status_label.text() != message:
                self.status_label.setText(message)
            
            if progress >= 0 and self.progress_bar.value() != progress:
                self.progress_bar.setValue(progress)

            # 버튼 상태 업데이트 - 중지 조건 수정
            should_enable = not any(x in message.lower() for x in ["중지됨", "오류"])
            if self.stop_button.isEnabled() != should_enable:
                self.stop_button.setEnabled(should_enable)

        except Exception as e:
            print(f"Error in set_status: {str(e)}")
            import traceback
            print(traceback.format_exc())

    def update_status_display(self):
        """상태 표시를 효율적으로 업데이트합니다."""
        try:
            if self.pending_status is None:
                return

            # 상태 텍스트가 변경된 경우에만 업데이트
            if self.status_label.text() != self.pending_status:
                self.status_label.setText(self.pending_status)

            # 진행률이 변경된 경우에만 업데이트
            if self.pending_progress >= 0 and self.progress_bar.value() != self.pending_progress:
                self.progress_bar.setValue(self.pending_progress)

            # 버튼 상태 업데이트 - 중지 조건 수정
            should_enable = not any(x in self.pending_status.lower() for x in ["중지됨", "오류"])
            if self.stop_button.isEnabled() != should_enable:
                self.stop_button.setEnabled(should_enable)

            self.pending_status = None
            self.pending_progress = -1

        except Exception as e:
            print(f"Error in update_status_display: {str(e)}")
            import traceback
            print(traceback.format_exc())

class ActiveJobsWidget(CardWidget):
    # 시그널 정의
    job_stopped = pyqtSignal(str)  # 작업 ID를 전달
    sync_interval_changed = pyqtSignal(str, int)  # 작업 ID와 새로운 간격을 전달
    backup_mode_changed = pyqtSignal(str, str)  # 작업 ID와 새로운 모드를 전달
    
    def __init__(self, parent=None):
        super().__init__("활성 작업 (0)", parent, "ActiveJobsWidget")
        self.job_widgets = {}  # 작업 ID를 키로 하는 작업 위젯 딕셔너리
        self.setup_ui()
        
        # 메타데이터 DB 인스턴스 캐싱
        self._metadata_db = None
        self._last_metadata_update = 0
        self._metadata_update_interval = 5  # 5초
        
        # 메타데이터 DB 업데이트 타이머
        self.metadata_update_timer = QTimer(self)
        self.metadata_update_timer.timeout.connect(self._check_metadata_update)
        self.metadata_update_timer.start(1000)  # 1초마다 체크
    
    def _check_metadata_update(self):
        """메타데이터 업데이트가 필요한지 확인하고 필요한 경우에만 업데이트"""
        current_time = time.time()
        if current_time - self._last_metadata_update >= self._metadata_update_interval:
            self.update_metadata_display()
            self._last_metadata_update = current_time
    
    def update_metadata_display(self):
        """메타데이터 DB의 내용을 표시 업데이트"""
        try:
            # DB 인스턴스 재사용
            if self._metadata_db is None:
                from backup_app.utils.metadata_db import MetadataDB
                self._metadata_db = MetadataDB()
            
            # 활성 작업이 있는 경우에만 업데이트
            if self.job_widgets:
                # 변경 이벤트 업데이트
                self.update_events_table(self._metadata_db)
                # 배치 처리 통계 업데이트
                self.update_batch_stats(self._metadata_db)
            
        except Exception as e:
            print(f"메타데이터 표시 업데이트 중 오류: {e}")
            # DB 연결 오류 시 인스턴스 초기화
            self._metadata_db = None
    
    def update_events_table(self, db):
        """변경 이벤트 테이블 업데이트"""
        try:
            # 필터 조건 설정
            event_type = self.event_type_filter.currentText()
            processed_state = self.processed_filter.currentText()
            
            # SQL 쿼리 조건 구성
            conditions = []
            params = []
            
            if event_type != "모든 이벤트":
                # 이벤트 타입 매핑
                event_type_map = {
                    "생성": "created",
                    "수정": "modified",
                    "삭제": "deleted",
                    "이동": "moved",
                    "권한 변경": "permission_changed"
                }
                mapped_type = event_type_map.get(event_type, event_type)
                conditions.append("event_type = ?")
                params.append(mapped_type)
            
            if processed_state == "미처리":
                conditions.append("processed = 0")
            elif processed_state == "처리됨":
                conditions.append("processed = 1")
            elif processed_state == "실패":
                conditions.append("processed = 1")
            
            # 쿼리 실행 - 최근 100개만 가져오도록 수정
            query = '''
                SELECT timestamp, path, event_type, is_directory, processed, error_message, batch_id
                FROM change_events
            '''
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
            query += " ORDER BY timestamp DESC LIMIT 100"  # 1000개에서 100개로 수정
            
            conn = None
            try:
                conn = sqlite3.connect(db.db_path)
                cursor = conn.cursor()
                cursor.execute(query, params)
                events = cursor.fetchall()
            finally:
                if conn:
                    conn.close()
            
            # 테이블 업데이트
            self.events_table.setRowCount(len(events))
            for row, event in enumerate(events):
                try:
                    timestamp = datetime.fromtimestamp(event[0]).strftime('%Y-%m-%d %H:%M:%S')
                    path = event[1]
                    event_type = event[2]
                    is_dir = "예" if event[3] else "아니오"
                    processed = "처리됨" if event[4] else "미처리"
                    error = event[5] or ""
                    batch_id = str(event[6]) if event[6] is not None else ""
                    
                    # 이벤트 타입 한글화
                    event_type_map = {
                        "created": "생성",
                        "modified": "수정",
                        "deleted": "삭제",
                        "moved": "이동",
                        "permission_changed": "권한 변경"
                    }
                    event_type = event_type_map.get(event_type, event_type)
                    
                    self.events_table.setItem(row, 0, QTableWidgetItem(timestamp))
                    self.events_table.setItem(row, 1, QTableWidgetItem(path))
                    self.events_table.setItem(row, 2, QTableWidgetItem(event_type))
                    self.events_table.setItem(row, 3, QTableWidgetItem(is_dir))
                    self.events_table.setItem(row, 4, QTableWidgetItem(processed))
                    self.events_table.setItem(row, 5, QTableWidgetItem(error))
                except Exception as e:
                    print(f"이벤트 행 처리 중 오류 (행 {row}): {e}")
                    continue
            
            # 컬럼 너비 조정
            self.events_table.resizeColumnsToContents()
            
        except Exception as e:
            print(f"이벤트 테이블 업데이트 중 오류: {e}")
            import traceback
            print(traceback.format_exc())
    
    def closeEvent(self, event):
        """위젯이 닫힐 때 리소스를 정리합니다."""
        try:
            # 타이머 중지
            if hasattr(self, 'metadata_update_timer'):
                self.metadata_update_timer.stop()
            
            # DB 연결 정리
            self._metadata_db = None
            
            super().closeEvent(event)
        except Exception as e:
            print(f"위젯 닫기 중 오류: {str(e)}")
            import traceback
            print(traceback.format_exc())
    
    def setup_ui(self):
        """UI 초기화"""
        # 탭 위젯 생성
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        
        # 작업 탭
        self.jobs_tab = QWidget()
        jobs_layout = QVBoxLayout(self.jobs_tab)
        jobs_layout.setContentsMargins(0, 0, 0, 0)
        
        # 스크롤 영역 설정
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 스크롤 영역에 들어갈 컨테이너 위젯
        self.jobs_container = QWidget()
        self.jobs_layout = QVBoxLayout(self.jobs_container)
        self.jobs_layout.setSpacing(10)
        self.jobs_layout.setAlignment(Qt.AlignTop)
        
        scroll_area.setWidget(self.jobs_container)
        jobs_layout.addWidget(scroll_area)
        
        # 메타데이터 모니터링 탭
        self.metadata_tab = QWidget()
        metadata_layout = QVBoxLayout(self.metadata_tab)
        
        # 변경 이벤트 테이블
        self.events_table = QTableWidget()
        self.events_table.setColumnCount(6)
        self.events_table.setHorizontalHeaderLabels([
            "시간", "경로", "이벤트 타입", "디렉토리", "처리 상태", "에러 메시지"
        ])
        self.events_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.events_table.setAlternatingRowColors(True)
        self.events_table.setSortingEnabled(True)
        
        # 필터 컨트롤
        filter_layout = QHBoxLayout()
        
        # 이벤트 타입 필터
        self.event_type_filter = QComboBox()
        self.event_type_filter.addItems(["모든 이벤트", "생성", "수정", "삭제", "이동", "권한 변경"])
        self.event_type_filter.currentTextChanged.connect(self.update_metadata_display)
        filter_layout.addWidget(QLabel("이벤트 타입:"))
        filter_layout.addWidget(self.event_type_filter)
        
        # 처리 상태 필터
        self.processed_filter = QComboBox()
        self.processed_filter.addItems(["모든 상태", "미처리", "처리됨", "실패"])
        self.processed_filter.currentTextChanged.connect(self.update_metadata_display)
        filter_layout.addWidget(QLabel("처리 상태:"))
        filter_layout.addWidget(self.processed_filter)
        
        # 새로고침 버튼
        refresh_btn = QPushButton("새로고침")
        refresh_btn.clicked.connect(self.update_metadata_display)
        filter_layout.addWidget(refresh_btn)
        
        filter_layout.addStretch()
        
        # 배치 처리 통계
        self.batch_stats = QTableWidget()
        self.batch_stats.setColumnCount(6)
        self.batch_stats.setHorizontalHeaderLabels([
            "배치 ID", "시작 시간", "종료 시간", "상태", "처리된 이벤트", "에러 수"
        ])
        self.batch_stats.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.batch_stats.setAlternatingRowColors(True)
        self.batch_stats.setSortingEnabled(True)
        
        # 메타데이터 탭 레이아웃 구성
        metadata_layout.addLayout(filter_layout)
        metadata_layout.addWidget(QLabel("최근 변경 이벤트"))
        metadata_layout.addWidget(self.events_table)
        metadata_layout.addWidget(QLabel("배치 처리 통계"))
        metadata_layout.addWidget(self.batch_stats)
        
        # 탭 추가
        self.tab_widget.addTab(self.jobs_tab, "활성 작업")
        self.tab_widget.addTab(self.metadata_tab, "메타데이터 모니터링")
        
        # 백업제외 탭 추가
        self.exclusion_tab = BackupExclusionWidget(self.parent())
        self.exclusion_tab.exclusion_changed.connect(self.parent().save_active_jobs)
        self.tab_widget.addTab(self.exclusion_tab, "백업제외")
        
        # 메인 레이아웃에 탭 위젯 추가
        self.layout.addWidget(self.tab_widget)
    
    def get_source_name(self, source_path):
        """소스 경로에서 마지막 폴더 이름을 추출"""
        return os.path.basename(source_path.rstrip('/'))
    
    def update_title(self):
        """작업 수와 소스 이름들을 포함하여 타이틀 업데이트"""
        job_count = len(self.job_widgets)
        if job_count == 0:
            self.title.setText(f"활성 작업 (0)")
            return
            
        # 각 작업의 소스 이름 수집
        source_names = []
        for job_widget in self.job_widgets.values():
            source_name = self.get_source_name(job_widget.source_path)
            if source_name not in source_names:
                source_names.append(source_name)
        
        # 소스 이름들을 문자열로 결합
        sources_str = ", ".join(source_names)
        self.title.setText(f"활성 작업 ({job_count}) - {sources_str}")
        
        # 툴팁 설정 (전체 경로 표시)
        full_paths = [f"{self.get_source_name(w.source_path)}: {w.source_path}" 
                     for w in self.job_widgets.values()]
        self.title.setToolTip("\n".join(full_paths))
    
    def add_job_widget(self, job_id, job_widget):
        """작업 위젯 추가"""
        if job_id not in self.job_widgets:
            self.job_widgets[job_id] = job_widget
            self.jobs_layout.addWidget(job_widget)
            
            # 작업 위젯의 시그널을 이 위젯의 시그널에 연결
            job_widget.stop_requested.connect(lambda: self.job_stopped.emit(job_id))
            job_widget.sync_interval_changed.connect(
                lambda interval: self.sync_interval_changed.emit(job_id, interval))
            job_widget.backup_mode_changed.connect(
                lambda mode: self.backup_mode_changed.emit(job_id, mode))
            
            # 타이틀 업데이트
            self.update_title()
    
    def remove_job_widget(self, job_id):
        """작업 위젯 제거"""
        try:
            if job_id in self.job_widgets:
                job_widget = self.job_widgets[job_id]
                
                # 1. 레이아웃에서 위젯 제거
                self.jobs_layout.removeWidget(job_widget)
                
                # 2. 위젯의 모든 자식 위젯 제거
                for child in job_widget.findChildren(QWidget):
                    child.setParent(None)
                    child.deleteLater()
                
                # 3. 위젯 자체 제거
                job_widget.setParent(None)
                job_widget.hide()
                job_widget.deleteLater()
                
                # 4. 딕셔너리에서 제거
                del self.job_widgets[job_id]
                
                # 5. 타이틀 업데이트
                self.update_title()
                
                # 6. UI 업데이트 강제 실행
                QApplication.processEvents()
                
                print(f"작업 위젯 {job_id} 제거 완료")
        except Exception as e:
            print(f"작업 위젯 제거 중 오류 발생: {str(e)}")
            import traceback
            print(traceback.format_exc())
    
    def get_job_widget(self, job_id):
        """작업 위젯 가져오기"""
        return self.job_widgets.get(job_id)

    def update_batch_stats(self, db):
        """배치 처리 통계 테이블 업데이트"""
        try:
            with sqlite3.connect(db.db_path) as conn:
                cursor = conn.cursor()
                try:
                    # 테이블 존재 여부 확인
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='batch_processing'")
                    if not cursor.fetchone():
                        print("batch_processing 테이블이 아직 생성되지 않았습니다.")
                        self.batch_stats.setRowCount(0)
                        return

                    cursor.execute('''
                        SELECT id, start_time, end_time, status,
                               total_events, processed_events, error_count
                        FROM batch_processing
                        ORDER BY start_time DESC
                        LIMIT 100
                    ''')
                    batches = cursor.fetchall()
                except sqlite3.OperationalError as e:
                    print(f"배치 통계 조회 중 오류: {e}")
                    self.batch_stats.setRowCount(0)
                    return
            
            # 테이블 업데이트
            self.batch_stats.setRowCount(len(batches))
            for row, batch in enumerate(batches):
                try:
                    batch_id = str(batch[0])  # id 컬럼 사용
                    start_time = datetime.fromtimestamp(batch[1]).strftime('%Y-%m-%d %H:%M:%S')
                    end_time = datetime.fromtimestamp(batch[2]).strftime('%Y-%m-%d %H:%M:%S') if batch[2] else "진행 중"
                    status = batch[3]
                    total = str(batch[4])
                    processed = str(batch[5])
                    errors = str(batch[6])
                    
                    self.batch_stats.setItem(row, 0, QTableWidgetItem(batch_id))
                    self.batch_stats.setItem(row, 1, QTableWidgetItem(start_time))
                    self.batch_stats.setItem(row, 2, QTableWidgetItem(end_time))
                    self.batch_stats.setItem(row, 3, QTableWidgetItem(status))
                    self.batch_stats.setItem(row, 4, QTableWidgetItem(f"{processed}/{total}"))
                    self.batch_stats.setItem(row, 5, QTableWidgetItem(errors))
                except Exception as e:
                    print(f"배치 행 처리 중 오류 (행 {row}): {e}")
                    continue
            
            # 컬럼 너비 조정
            self.batch_stats.resizeColumnsToContents()
            
        except Exception as e:
            print(f"배치 통계 업데이트 중 오류: {e}")
            import traceback
            print(traceback.format_exc())
            self.batch_stats.setRowCount(0)
    
    def _check_metadata_update(self):
        """메타데이터 업데이트가 필요한지 확인하고 필요한 경우에만 업데이트"""
        current_time = time.time()
        if current_time - self._last_metadata_update >= self._metadata_update_interval:
            self.update_metadata_display()
            self._last_metadata_update = current_time
    
    def update_metadata_display(self):
        """메타데이터 DB의 내용을 표시 업데이트"""
        try:
            # DB 인스턴스 재사용
            if self._metadata_db is None:
                from backup_app.utils.metadata_db import MetadataDB
                self._metadata_db = MetadataDB()
            
            # 활성 작업이 있는 경우에만 업데이트
            if self.job_widgets:
                # 변경 이벤트 업데이트
                self.update_events_table(self._metadata_db)
                # 배치 처리 통계 업데이트
                self.update_batch_stats(self._metadata_db)
            
        except Exception as e:
            print(f"메타데이터 표시 업데이트 중 오류: {e}")
            # DB 연결 오류 시 인스턴스 초기화
            self._metadata_db = None
    
    def update_events_table(self, db):
        """변경 이벤트 테이블 업데이트"""
        try:
            # 필터 조건 설정
            event_type = self.event_type_filter.currentText()
            processed_state = self.processed_filter.currentText()
            
            # SQL 쿼리 조건 구성
            conditions = []
            params = []
            
            if event_type != "모든 이벤트":
                # 이벤트 타입 매핑
                event_type_map = {
                    "생성": "created",
                    "수정": "modified",
                    "삭제": "deleted",
                    "이동": "moved",
                    "권한 변경": "permission_changed"
                }
                mapped_type = event_type_map.get(event_type, event_type)
                conditions.append("event_type = ?")
                params.append(mapped_type)
            
            if processed_state == "미처리":
                conditions.append("processed = 0")
            elif processed_state == "처리됨":
                conditions.append("processed = 1")
            elif processed_state == "실패":
                conditions.append("processed = 1")
            
            # 쿼리 실행 - 최근 100개만 가져오도록 수정
            query = '''
                SELECT timestamp, path, event_type, is_directory, processed, error_message, batch_id
                FROM change_events
            '''
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
            query += " ORDER BY timestamp DESC LIMIT 100"  # 1000개에서 100개로 수정
            
            conn = None
            try:
                conn = sqlite3.connect(db.db_path)
                cursor = conn.cursor()
                cursor.execute(query, params)
                events = cursor.fetchall()
            finally:
                if conn:
                    conn.close()
            
            # 테이블 업데이트
            self.events_table.setRowCount(len(events))
            for row, event in enumerate(events):
                try:
                    timestamp = datetime.fromtimestamp(event[0]).strftime('%Y-%m-%d %H:%M:%S')
                    path = event[1]
                    event_type = event[2]
                    is_dir = "예" if event[3] else "아니오"
                    processed = "처리됨" if event[4] else "미처리"
                    error = event[5] or ""
                    batch_id = str(event[6]) if event[6] is not None else ""
                    
                    # 이벤트 타입 한글화
                    event_type_map = {
                        "created": "생성",
                        "modified": "수정",
                        "deleted": "삭제",
                        "moved": "이동",
                        "permission_changed": "권한 변경"
                    }
                    event_type = event_type_map.get(event_type, event_type)
                    
                    self.events_table.setItem(row, 0, QTableWidgetItem(timestamp))
                    self.events_table.setItem(row, 1, QTableWidgetItem(path))
                    self.events_table.setItem(row, 2, QTableWidgetItem(event_type))
                    self.events_table.setItem(row, 3, QTableWidgetItem(is_dir))
                    self.events_table.setItem(row, 4, QTableWidgetItem(processed))
                    self.events_table.setItem(row, 5, QTableWidgetItem(error))
                except Exception as e:
                    print(f"이벤트 행 처리 중 오류 (행 {row}): {e}")
                    continue
            
            # 컬럼 너비 조정
            self.events_table.resizeColumnsToContents()
            
        except Exception as e:
            print(f"이벤트 테이블 업데이트 중 오류: {e}")
            import traceback
            print(traceback.format_exc())
    
    def closeEvent(self, event):
        """위젯이 닫힐 때 리소스를 정리합니다."""
        try:
            # 타이머 중지
            if hasattr(self, 'metadata_update_timer'):
                self.metadata_update_timer.stop()
            
            # DB 연결 정리
            self._metadata_db = None
            
            super().closeEvent(event)
        except Exception as e:
            print(f"위젯 닫기 중 오류: {str(e)}")
            import traceback
            print(traceback.format_exc())
    
    def setup_ui(self):
        """UI 초기화"""
        # 탭 위젯 생성
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        
        # 작업 탭
        self.jobs_tab = QWidget()
        jobs_layout = QVBoxLayout(self.jobs_tab)
        jobs_layout.setContentsMargins(0, 0, 0, 0)
        
        # 스크롤 영역 설정
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 스크롤 영역에 들어갈 컨테이너 위젯
        self.jobs_container = QWidget()
        self.jobs_layout = QVBoxLayout(self.jobs_container)
        self.jobs_layout.setSpacing(10)
        self.jobs_layout.setAlignment(Qt.AlignTop)
        
        scroll_area.setWidget(self.jobs_container)
        jobs_layout.addWidget(scroll_area)
        
        # 메타데이터 모니터링 탭
        self.metadata_tab = QWidget()
        metadata_layout = QVBoxLayout(self.metadata_tab)
        
        # 변경 이벤트 테이블
        self.events_table = QTableWidget()
        self.events_table.setColumnCount(6)
        self.events_table.setHorizontalHeaderLabels([
            "시간", "경로", "이벤트 타입", "디렉토리", "처리 상태", "에러 메시지"
        ])
        self.events_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.events_table.setAlternatingRowColors(True)
        self.events_table.setSortingEnabled(True)
        
        # 필터 컨트롤
        filter_layout = QHBoxLayout()
        
        # 이벤트 타입 필터
        self.event_type_filter = QComboBox()
        self.event_type_filter.addItems(["모든 이벤트", "생성", "수정", "삭제", "이동", "권한 변경"])
        self.event_type_filter.currentTextChanged.connect(self.update_metadata_display)
        filter_layout.addWidget(QLabel("이벤트 타입:"))
        filter_layout.addWidget(self.event_type_filter)
        
        # 처리 상태 필터
        self.processed_filter = QComboBox()
        self.processed_filter.addItems(["모든 상태", "미처리", "처리됨", "실패"])
        self.processed_filter.currentTextChanged.connect(self.update_metadata_display)
        filter_layout.addWidget(QLabel("처리 상태:"))
        filter_layout.addWidget(self.processed_filter)
        
        # 새로고침 버튼
        refresh_btn = QPushButton("새로고침")
        refresh_btn.clicked.connect(self.update_metadata_display)
        filter_layout.addWidget(refresh_btn)
        
        filter_layout.addStretch()
        
        # 배치 처리 통계
        self.batch_stats = QTableWidget()
        self.batch_stats.setColumnCount(6)
        self.batch_stats.setHorizontalHeaderLabels([
            "배치 ID", "시작 시간", "종료 시간", "상태", "처리된 이벤트", "에러 수"
        ])
        self.batch_stats.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.batch_stats.setAlternatingRowColors(True)
        self.batch_stats.setSortingEnabled(True)
        
        # 메타데이터 탭 레이아웃 구성
        metadata_layout.addLayout(filter_layout)
        metadata_layout.addWidget(QLabel("최근 변경 이벤트"))
        metadata_layout.addWidget(self.events_table)
        metadata_layout.addWidget(QLabel("배치 처리 통계"))
        metadata_layout.addWidget(self.batch_stats)
        
        # 탭 추가
        self.tab_widget.addTab(self.jobs_tab, "활성 작업")
        self.tab_widget.addTab(self.metadata_tab, "메타데이터 모니터링")
        
        # 백업제외 탭 추가
        self.exclusion_tab = BackupExclusionWidget(self.parent())
        self.exclusion_tab.exclusion_changed.connect(self.parent().save_active_jobs)
        self.tab_widget.addTab(self.exclusion_tab, "백업제외")
        
        # 메인 레이아웃에 탭 위젯 추가
        self.layout.addWidget(self.tab_widget)
    
    def get_source_name(self, source_path):
        """소스 경로에서 마지막 폴더 이름을 추출"""
        return os.path.basename(source_path.rstrip('/'))
    
    def update_title(self):
        """작업 수와 소스 이름들을 포함하여 타이틀 업데이트"""
        job_count = len(self.job_widgets)
        if job_count == 0:
            self.title.setText(f"활성 작업 (0)")
            return
            
        # 각 작업의 소스 이름 수집
        source_names = []
        for job_widget in self.job_widgets.values():
            source_name = self.get_source_name(job_widget.source_path)
            if source_name not in source_names:
                source_names.append(source_name)
        
        # 소스 이름들을 문자열로 결합
        sources_str = ", ".join(source_names)
        self.title.setText(f"활성 작업 ({job_count}) - {sources_str}")
        
        # 툴팁 설정 (전체 경로 표시)
        full_paths = [f"{self.get_source_name(w.source_path)}: {w.source_path}" 
                     for w in self.job_widgets.values()]
        self.title.setToolTip("\n".join(full_paths))
    
    def add_job_widget(self, job_id, job_widget):
        """작업 위젯 추가"""
        if job_id not in self.job_widgets:
            self.job_widgets[job_id] = job_widget
            self.jobs_layout.addWidget(job_widget)
            
            # 작업 위젯의 시그널을 이 위젯의 시그널에 연결
            job_widget.stop_requested.connect(lambda: self.job_stopped.emit(job_id))
            job_widget.sync_interval_changed.connect(
                lambda interval: self.sync_interval_changed.emit(job_id, interval))
            job_widget.backup_mode_changed.connect(
                lambda mode: self.backup_mode_changed.emit(job_id, mode))
            
            # 타이틀 업데이트
            self.update_title()
    
    def remove_job_widget(self, job_id):
        """작업 위젯 제거"""
        try:
            if job_id in self.job_widgets:
                job_widget = self.job_widgets[job_id]
                
                # 1. 레이아웃에서 위젯 제거
                self.jobs_layout.removeWidget(job_widget)
                
                # 2. 위젯의 모든 자식 위젯 제거
                for child in job_widget.findChildren(QWidget):
                    child.setParent(None)
                    child.deleteLater()
                
                # 3. 위젯 자체 제거
                job_widget.setParent(None)
                job_widget.hide()
                job_widget.deleteLater()
                
                # 4. 딕셔너리에서 제거
                del self.job_widgets[job_id]
                
                # 5. 타이틀 업데이트
                self.update_title()
                
                # 6. UI 업데이트 강제 실행
                QApplication.processEvents()
                
                print(f"작업 위젯 {job_id} 제거 완료")
        except Exception as e:
            print(f"작업 위젯 제거 중 오류 발생: {str(e)}")
            import traceback
            print(traceback.format_exc())
    
    def get_job_widget(self, job_id):
        """작업 위젯 가져오기"""
        return self.job_widgets.get(job_id)

class AddServerDialog(QDialog):
    """서버 추가를 위한 다이얼로그"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("서버 관리")
        self.setMinimumWidth(600)
        self.setMinimumHeight(400)
        self.setup_ui()

    def setup_ui(self):
        main_layout = QVBoxLayout()
        
        # 스플리터 생성
        splitter = QSplitter(Qt.Horizontal)
        
        # 왼쪽 패널 (기존 서버 목록)
        left_panel = QFrame()
        left_layout = QVBoxLayout()
        left_panel.setLayout(left_layout)
        
        existing_label = QLabel("기존 서버 목록")
        left_layout.addWidget(existing_label)
        
        self.existing_list = QListWidget()
        self.existing_list.addItems(sorted(ValidationApp.SERVER_PATHS.keys()))
        left_layout.addWidget(self.existing_list)
        
        delete_btn = QPushButton("선택 서버 삭제")
        delete_btn.clicked.connect(self.delete_selected_server)
        left_layout.addWidget(delete_btn)
        
        # 오른쪽 패널 (새 서버 추가)
        right_panel = QFrame()
        right_layout = QVBoxLayout()
        right_panel.setLayout(right_layout)
        
        right_layout.addWidget(QLabel("새 서버 추가"))
        
        # 서버명 입력
        name_layout = QHBoxLayout()
        name_label = QLabel("서버명:")
        self.name_edit = QLineEdit()
        name_layout.addWidget(name_label)
        name_layout.addWidget(self.name_edit)
        right_layout.addLayout(name_layout)

        # 경로 선택
        path_layout = QHBoxLayout()
        path_label = QLabel("경로:")
        self.path_edit = QLineEdit()
        self.path_button = QPushButton("찾아보기")
        self.path_button.clicked.connect(self.browse_path)
        path_layout.addWidget(path_label)
        path_layout.addWidget(self.path_edit)
        path_layout.addWidget(self.path_button)
        right_layout.addLayout(path_layout)
        
        # 추가 버튼
        add_btn = QPushButton("새 서버 추가")
        add_btn.clicked.connect(self.add_new_server)
        right_layout.addWidget(add_btn)
        
        right_layout.addStretch()
        
        # 스플리터에 패널 추가
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        
        main_layout.addWidget(splitter)

        # 확인/취소 버튼
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            parent=self)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        main_layout.addWidget(buttons)

        self.setLayout(main_layout)

    def browse_path(self):
        path = QFileDialog.getExistingDirectory(self, "서버 경로 선택")
        if path:
            self.path_edit.setText(path)

    def add_new_server(self):
        """새 서버를 추가합니다."""
        name = self.name_edit.text().strip()
        path = self.path_edit.text().strip()
        
        if not name:
            QMessageBox.warning(self, '경고', '서버명을 입력해주세요.')
            return

        if not path:
            QMessageBox.warning(self, '경고', '경로를 선택해주세요.')
            return

        if name in ValidationApp.SERVER_PATHS:
            QMessageBox.warning(self, '경고', '이미 존재하는 서버 이름입니다.')
            return

        # 서버 목록에 추가
        ValidationApp.SERVER_PATHS[name] = path
        self.existing_list.clear()
        self.existing_list.addItems(sorted(ValidationApp.SERVER_PATHS.keys()))
        
        # 변경사항 저장
        if isinstance(self.parent(), ValidationApp):
            self.parent().save_active_jobs()
            # 오른쪽 작품 선택 위젯 업데이트
            self.parent().right_show_widget.on_projects_changed()
        
        # 입력 필드 초기화
        self.name_edit.clear()
        self.path_edit.clear()

    def delete_selected_server(self):
        """선택된 서버를 삭제합니다."""
        selected_items = self.existing_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, '경고', '삭제할 서버를 선택해주세요.')
            return

        selected_name = selected_items[0].text()
        reply = QMessageBox.question(self, '확인', 
                                   f'"{selected_name}" 서버를 삭제하시겠습니까?',
                                   QMessageBox.Yes | QMessageBox.No)
                                   
        if reply == QMessageBox.Yes:
            # 서버 목록에서 제거
            if selected_name in ValidationApp.SERVER_PATHS:
                del ValidationApp.SERVER_PATHS[selected_name]
            
            # 리스트 위젯 업데이트
            self.existing_list.clear()
            self.existing_list.addItems(sorted(ValidationApp.SERVER_PATHS.keys()))
            
            # 변경사항 저장
            if isinstance(self.parent(), ValidationApp):
                self.parent().save_active_jobs()
                # 오른쪽 작품 선택 위젯 업데이트
                self.parent().right_show_widget.on_projects_changed()

    def accept(self):
        """다이얼로그가 승인되었을 때 호출됩니다."""
        # 변경사항 저장
        if isinstance(self.parent(), ValidationApp):
            self.parent().save_active_jobs()
        super().accept()

class ValidationApp(QMainWindow):
    MAX_CONCURRENT_JOBS = 5  # 최대 동시 작업 수
    DEFAULT_WORKER_THREADS = 8  # 백업 워커 스레드 수
    DEFAULT_PERIODIC_SYNC_INTERVAL = 120  # 초 단위 동기화 간격
    DEFAULT_BACKUP_MODE = "incremental"  # 기본값: 인크리멘탈 모드
    JOB_STATE_FILENAME = "active_backup_jobs.json"
    MAX_LOG_QUEUE_SIZE = 1000  # 로그 큐의 최대 크기
    
    # 서버 경로 정의
    SERVER_PATHS = {
        "usadata1": "/System/Volumes/Data/usadata1",
        "usadata4": "/Users/<USER>/Desktop/usadata4",
        "usadata5": "/System/Volumes/Data/usadata5",
        "usadata6": "/System/Volumes/Data/usadata6",
        "bgfinal": "/Volumes/bgfinal"
    }

    def __init__(self):
        super().__init__()
        # log_queue를 가장 먼저 정의
        self.log_queue = Queue(maxsize=self.MAX_LOG_QUEUE_SIZE)
        
        # 프로젝트 매니저 초기화
        self.project_manager = ProjectManager()
        
        # 기본 프로젝트 추가
        for project_name in DEFAULT_PROJECTS:
            self.project_manager.add_project(project_name)
        
        # 나머지 초기화 코드
        self.setWindowTitle("Yeson Entertainment 씬 관리 시스템")
        self.setMinimumSize(1200, 800)
        
        # 테마 설정
        self.current_theme = "light"
        
        # 매니저 초기화
        self.project_manager = ProjectManager(self)
        self.path_manager = PathManager()
        self.calculator = FrameCalculator()
        self.db_manager = SceneDbManager(self.path_manager)
        
        # 작업 관련 변수 초기화
        self.active_jobs = {}
        self.job_queues = {}
        self.scene_backup_status = {}
        
        # 작업 상태 파일 경로 설정 (Downloads 디렉토리에 저장)
        downloads_dir = os.path.expanduser("~/Downloads")
        self._job_state_file_path = os.path.join(downloads_dir, self.JOB_STATE_FILENAME)
        print(f"작업 상태 파일 경로: {self._job_state_file_path}")
        
        # 백업 제외 목록 초기화 및 로드
        self.backup_exclusions = {}
        try:
            if os.path.exists(self._job_state_file_path):
                with open(self._job_state_file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.backup_exclusions = data.get('exclusions', {})
                    print(f"백업 제외 항목 로드 완료: {len(self.backup_exclusions)}개")
        except Exception as e:
            print(f"백업 제외 항목 로드 중 오류: {e}")
        
        # UI 설정
        self.setup_ui()
        
        # 상태바 설정
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("애플리케이션 준비 완료.")
        
        # 시그널 연결
        self.connect_signals()
        
        # 테마 적용
        StyleManager.apply_style(self, self.current_theme)
        
        # 단축키 설정
        self.setup_shortcuts()
        
        # 활성 작업 로드
        self.load_active_jobs()

        self.max_log_lines = 1000  # 최대 로그 라인 수 제한
        self.last_cleanup_time = time.time()
        self.CLEANUP_INTERVAL = 3600  # 1시간마다 로그 정리

        # 스레드 풀 초기화
        self.thread_pool = ThreadPoolExecutor(max_workers=self.DEFAULT_WORKER_THREADS)
        self.active_workers = weakref.WeakValueDictionary()  # 활성 워커 추적
        
        # 로그 처리를 위한 타이머 설정
        self.log_timer = QTimer(self)
        self.log_timer.timeout.connect(self.process_log_queue)
        self.log_timer.start(100)  # 100ms 마다 로그 처리
        
    def process_log_queue(self):
        """로그 큐에서 메시지를 처리합니다."""
        try:
            queue_size = self.log_queue.qsize()
            if queue_size > 900:  # 큐가 거의 찼을 때 경고
                print(f"Warning: Log queue is almost full ({queue_size}/1000)")
            
            processed_count = 0
            while not self.log_queue.empty():
                job_id, message = self.log_queue.get_nowait()
                job_widget = self.active_jobs_widget.get_job_widget(job_id)
                if job_widget:
                    job_widget.append_log(message)
                processed_count += 1
            
            if processed_count > 0:
                print(f"Processed {processed_count} log messages, queue size: {self.log_queue.qsize()}")
        except Exception as e:
            logging.error(f"로그 처리 중 오류 발생: {e}")
            import traceback
            print(f"Log processing error traceback:\n{traceback.format_exc()}")
    
    def setup_ui(self):
        """UI 초기화"""
        # 메인 윈도우 설정
        self.setWindowTitle("예손 백업 툴")
        self.resize(1400, 900)  # 이미지와 비슷한 비율로 조정

        # 메인 위젯과 레이아웃
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)  # 레이아웃 간 간격을 0으로 설정
        main_widget.setLayout(main_layout)

        # 상단 버튼 영역
        button_widget = QWidget()
        button_widget.setFixedHeight(28)  # 버튼 영역 높이를 28px로 설정
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(4, 2, 4, 2)  # 상하 여백 2px로 조정
        button_layout.setSpacing(4)
        
        # 테마 토글 버튼
        self.theme_toggle_btn = QPushButton()
        self.theme_toggle_btn.setFixedSize(28, 28)  # 버튼 크기 고정
        self.theme_toggle_btn.setCursor(Qt.PointingHandCursor)
        self.theme_toggle_btn.setToolTip("테마 변경 (Ctrl+T)")
        self.update_theme_button_icon()
        self.theme_toggle_btn.clicked.connect(self.toggle_theme)
        button_layout.addWidget(self.theme_toggle_btn)
        
        # 서버 추가 버튼
        add_server_btn = QPushButton("서버 추가")
        add_server_btn.setFixedSize(80, 28)  # 버튼 크기 고정
        add_server_btn.setCursor(Qt.PointingHandCursor)
        add_server_btn.setToolTip("새로운 서버 추가")
        add_server_btn.clicked.connect(self.add_server)
        button_layout.addWidget(add_server_btn)
        
        # 프로젝트 추가 버튼
        add_project_btn = QPushButton("프로젝트 추가")
        add_project_btn.setFixedSize(100, 28)  # 버튼 크기 고정
        add_project_btn.setCursor(Qt.PointingHandCursor)
        add_project_btn.setToolTip("새로운 프로젝트 추가")
        add_project_btn.clicked.connect(self.add_project)
        button_layout.addWidget(add_project_btn)

        # 오른쪽 여백을 추가하여 버튼들을 왼쪽으로 정렬
        button_layout.addStretch()

        # 메인 레이아웃에 버튼 영역 추가
        main_layout.addWidget(button_widget)

        # 상단 선택 영역 (작품 선택과 서버 선택을 위한 수평 레이아웃)
        top_selection = QWidget()
        top_selection.setFixedHeight(120)  # 높이를 120px로 설정
        top_layout = QHBoxLayout(top_selection)
        top_layout.setContentsMargins(4, 0, 4, 2)  # 상단 여백을 0으로 설정
        top_layout.setSpacing(4)  # 간격도 줄임
        
        # 작품 선택 위젯
        self.left_show_widget = ShowSelectionWidget(self, "작품 선택 (좌)")
        self.left_show_widget.project_manager = self.project_manager
        self.left_show_widget.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        top_layout.addWidget(self.left_show_widget, 1)
        
        # 서버 선택 위젯
        self.right_show_widget = ShowSelectionWidget(self, "서버 선택 (우)")
        self.right_show_widget.project_manager = self.project_manager
        self.right_show_widget.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        top_layout.addWidget(self.right_show_widget, 1)

        # 메인 레이아웃에 상단 선택 영역 추가
        main_layout.addWidget(top_selection)

        # 하단 영역을 위한 수평 스플리터 생성
        content_splitter = QSplitter(Qt.Horizontal)
        content_splitter.setChildrenCollapsible(False)

        # 왼쪽 컨테이너 위젯 (씬 목록, 계산, 리소스)
        left_container = QWidget()
        left_layout = QVBoxLayout(left_container)
        left_layout.setContentsMargins(0, 0, 0, 0)
        
        # 왼쪽 수직 스플리터
        left_splitter = QSplitter(Qt.Vertical)
        left_splitter.setChildrenCollapsible(False)
        
        # 씬 목록 위젯
        self.scene_list_widget = SceneListWidget(self)
        left_splitter.addWidget(self.scene_list_widget)
        
        # 계산 위젯
        self.calculation_widget = CalculationWidget(self)
        left_splitter.addWidget(self.calculation_widget)
        
        # 시스템 리소스 상태 위젯
        self.resource_widget = SystemResourceWidget(self)
        left_splitter.addWidget(self.resource_widget)
        
        # 왼쪽 스플리터 크기 비율 설정 (4:2:1)
        total_height = 700
        left_splitter.setSizes([
            int(total_height * 4/7),  # 씬 목록 (4/7)
            int(total_height * 2/7),  # 계산 (2/7)
            int(total_height * 1/7)   # 리소스 (1/7)
        ])
        
        left_layout.addWidget(left_splitter)

        # 오른쪽 컨테이너 위젯 (활성 작업)
        right_container = QWidget()
        right_layout = QVBoxLayout(right_container)
        right_layout.setContentsMargins(0, 0, 0, 0)
        
        # 활성 작업 위젯
        self.active_jobs_widget = ActiveJobsWidget(self)
        right_layout.addWidget(self.active_jobs_widget)

        # 컨텐츠 스플리터에 좌우 컨테이너 추가
        content_splitter.addWidget(left_container)
        content_splitter.addWidget(right_container)
        
        # 좌우 비율 설정 (1:2)
        content_splitter.setSizes([500, 1000])  # 1:2 비율로 설정
        
        # 스플리터 스타일 설정
        splitter_style = """
            QSplitter::handle {
                background-color: #CCCCCC;
                width: 2px;
                margin: 0px 2px;
            }
            QSplitter::handle:hover {
                background-color: #999999;
            }
        """
        content_splitter.setStyleSheet(splitter_style)
        left_splitter.setStyleSheet(splitter_style.replace('width', 'height'))

        # 메인 레이아웃에 컨텐츠 스플리터 추가
        main_layout.addWidget(content_splitter)

        # 상태바 설정
        self.statusBar().showMessage("준비")
        
        # 상태바에 버튼 추가
        status_button_widget = QWidget()
        status_button_layout = QHBoxLayout(status_button_widget)
        status_button_layout.setContentsMargins(0, 0, 0, 0)
        
        # 테마 토글 버튼
        self.theme_toggle_btn = QPushButton()
        self.theme_toggle_btn.setFixedSize(28, 28)  # 버튼 크기 조정
        self.update_theme_button_icon()
        status_button_layout.addWidget(self.theme_toggle_btn)
        
        # 서버 추가 버튼
        add_server_btn = QPushButton("서버 추가")
        add_server_btn.clicked.connect(self.add_server)
        status_button_layout.addWidget(add_server_btn)
        
        # 프로젝트 추가 버튼
        add_project_btn = QPushButton("프로젝트 추가")
        add_project_btn.clicked.connect(self.add_project)
        status_button_layout.addWidget(add_project_btn)
        
        self.statusBar().addPermanentWidget(status_button_widget)

        # 단축키 설정
        self.setup_shortcuts()

    def update_theme_button_icon(self):
        """테마 버튼 아이콘 업데이트"""
        if hasattr(self, 'theme_button'):
            icon = self.style().standardIcon(
                QStyle.SP_DialogYesButton if self.current_theme == "dark" else QStyle.SP_DialogNoButton
            )
            self.theme_button.setIcon(icon)

    def connect_signals(self):
        """시그널 연결"""
        # 작품 선택 위젯 시그널 연결
        self.left_show_widget.show_combo.currentTextChanged.connect(
            lambda text: self.on_show_changed(text, False))
        self.left_show_widget.season_combo.currentTextChanged.connect(
            lambda text: self.on_season_changed(text, False))
        self.left_show_widget.episode_combo.currentTextChanged.connect(
            lambda text: self.on_episode_changed(text, False))
        
        # 작품 선택 백업/감시 버튼 시그널 연결
        self.left_show_widget.show_backup_btn.clicked.connect(
            lambda: self.initiate_backup("show", True, False))
        self.left_show_widget.season_backup_btn.clicked.connect(
            lambda: self.initiate_backup("season", True, False))
        self.left_show_widget.episode_backup_btn.clicked.connect(
            lambda: self.initiate_backup("episode", True, False))
        self.left_show_widget.show_watch_btn.clicked.connect(
            lambda: self.initiate_backup("show", False, False))
        self.left_show_widget.season_watch_btn.clicked.connect(
            lambda: self.initiate_backup("season", False, False))
        self.left_show_widget.episode_watch_btn.clicked.connect(
            lambda: self.initiate_backup("episode", False, False))
        
        # 서버 선택 위젯 시그널 연결
        self.right_show_widget.show_combo.currentTextChanged.connect(
            lambda text: self.on_show_changed(text, True))
        self.right_show_widget.season_combo.currentTextChanged.connect(
            lambda text: self.on_season_changed(text, True))
        self.right_show_widget.episode_combo.currentTextChanged.connect(
            lambda text: self.on_episode_changed(text, True))
        
        # 서버 선택 백업/감시 버튼 시그널 연결
        self.right_show_widget.show_backup_btn.clicked.connect(
            lambda: self.initiate_backup("show", True, True))
        self.right_show_widget.season_backup_btn.clicked.connect(
            lambda: self.initiate_backup("season", True, True))
        self.right_show_widget.episode_backup_btn.clicked.connect(
            lambda: self.initiate_backup("episode", True, True))
        self.right_show_widget.show_watch_btn.clicked.connect(
            lambda: self.initiate_backup("show", False, True))
        self.right_show_widget.season_watch_btn.clicked.connect(
            lambda: self.initiate_backup("season", False, True))
        self.right_show_widget.episode_watch_btn.clicked.connect(
            lambda: self.initiate_backup("episode", False, True))
        
        
        # 작업 위젯 시그널 연결
        if hasattr(self, 'active_jobs_widget'):
            self.active_jobs_widget.job_stopped.connect(self.stop_job)
            self.active_jobs_widget.sync_interval_changed.connect(
                self.handle_sync_interval_change)
            self.active_jobs_widget.backup_mode_changed.connect(
                self.handle_backup_mode_change)
        
        # 테마 토글 시그널
        self.theme_toggle_btn.clicked.connect(self.toggle_theme)
        
        # 리소스 모니터링 시작
        self.resource_widget.start_monitoring()

    def on_show_selected(self, index):
        """작품/서버 선택이 변경되었을 때의 처리"""
        self.on_show_changed(self.left_show_widget.show_combo.currentText(), False)

    def on_season_selected(self, index):
        """시즌/서버폴더 선택이 변경되었을 때의 처리"""
        self.on_season_changed(self.left_show_widget.season_combo.currentText(), False)

    def on_episode_selected(self, index):
        """화수/폴더 선택이 변경되었을 때의 처리"""
        self.on_episode_changed(self.left_show_widget.episode_combo.currentText(), False)

    def on_server_show_selected(self, index):
        """서버 선택이 변경되었을 때의 처리"""
        self.on_show_changed(self.right_show_widget.show_combo.currentText(), True)

    def on_server_season_selected(self, index):
        """서버 폴더 선택이 변경되었을 때의 처리"""
        self.on_season_changed(self.right_show_widget.season_combo.currentText(), True)

    def on_server_episode_selected(self, index):
        """서버 폴더 선택이 변경되었을 때의 처리"""
        self.on_episode_changed(self.right_show_widget.episode_combo.currentText(), True)

    def on_show_changed(self, show, is_right=False):
        """작품/서버 선택이 변경되었을 때의 처리"""
        selection_widget = self.right_show_widget if is_right else self.left_show_widget
        
        if is_right:
            # 서버 선택 처리 (우측 위젯)
            self.scene_list_widget.scene_tree.clear()
            selection_widget.season_combo.clear()
            selection_widget.season_combo.addItem("서버폴더 선택")
            selection_widget.episode_combo.clear()
            selection_widget.episode_combo.addItem("폴더 선택")

            if show == "서버 선택":
                selection_widget.season_combo.setEnabled(False)
                selection_widget.episode_combo.setEnabled(False)
                self.statusBar.showMessage("서버를 선택해주세요")
                return

            # 서버 경로 가져오기
            server_path = ValidationApp.SERVER_PATHS.get(show)
            
            if not server_path or not os.path.exists(server_path):
                self.statusBar.showMessage(f"서버 경로를 찾을 수 없습니다: {server_path}")
                selection_widget.season_combo.setEnabled(False)
                selection_widget.episode_combo.setEnabled(False)
                return

            try:
                # 서버 폴더 목록 가져오기
                folders = sorted([d for d in os.listdir(server_path)
                               if os.path.isdir(os.path.join(server_path, d))
                               and not d.startswith('.')])
                
                selection_widget.season_combo.clear()
                selection_widget.season_combo.addItem("서버폴더 선택")
                selection_widget.season_combo.addItems(folders)
                selection_widget.season_combo.setEnabled(True)
                selection_widget.episode_combo.setEnabled(False)
                
                self.statusBar.showMessage(f"{show} 서버가 선택되었습니다. 서버폴더를 선택하세요.")
            except Exception as e:
                self.statusBar.showMessage(f"서버 폴더 목록 로드 중 오류: {str(e)}")
                selection_widget.season_combo.setEnabled(False)
                selection_widget.episode_combo.setEnabled(False)
        else:
            # 기존 작품 선택 처리 (좌측 위젯)
            self.scene_list_widget.scene_tree.clear()
            selection_widget.season_combo.clear()
            selection_widget.season_combo.addItem("시즌 선택")
            selection_widget.episode_combo.clear()
            selection_widget.episode_combo.addItem("화수 선택")

            if show == "작품 선택":
                selection_widget.season_combo.setEnabled(False)
                selection_widget.episode_combo.setEnabled(False)
                self.statusBar.showMessage("작품을 선택해주세요")
                return

            # 작품 경로 가져오기
            project_path = self.path_manager.get_show_path(show)
            if not project_path:
                project_path = self.project_manager.get_project_path(show)  # 프로젝트 매니저에서도 경로 확인
            if not project_path:
                self.statusBar.showMessage(f"작품 경로를 찾을 수 없습니다: {show}")
                return

            try:
                print(f"작품 경로: {project_path}")  # 디버깅용 출력
                # 시즌 폴더 목록 가져오기
                if os.path.exists(project_path):
                    seasons = sorted([d for d in os.listdir(project_path)
                                   if os.path.isdir(os.path.join(project_path, d))
                                   and not d.startswith('.')])
                    print(f"찾은 시즌 목록: {seasons}")  # 디버깅용 출력
                    
                    selection_widget.season_combo.clear()
                    selection_widget.season_combo.addItem("시즌 선택")
                    if seasons:
                        selection_widget.season_combo.addItems(seasons)
                        selection_widget.season_combo.setEnabled(True)
                        selection_widget.episode_combo.setEnabled(False)
                        self.statusBar.showMessage(f"{show} 작품이 선택되었습니다. 시즌을 선택하세요.")
                    else:
                        selection_widget.season_combo.setEnabled(False)
                        selection_widget.episode_combo.setEnabled(False)
                        self.statusBar.showMessage(f"{show} 작품에 시즌이 없습니다.")
                else:
                    self.statusBar.showMessage(f"작품 경로가 존재하지 않습니다: {project_path}")
                    selection_widget.season_combo.setEnabled(False)
                    selection_widget.episode_combo.setEnabled(False)
            except Exception as e:
                print(f"시즌 목록 로드 중 오류: {str(e)}")  # 디버깅용 출력
                self.statusBar.showMessage(f"시즌 목록 로드 중 오류: {str(e)}")
                selection_widget.season_combo.setEnabled(False)
                selection_widget.episode_combo.setEnabled(False)

    def on_season_changed(self, season, is_right=False):
        """시즌/서버폴더 선택이 변경되었을 때의 처리"""
        selection_widget = self.right_show_widget if is_right else self.left_show_widget
        show = selection_widget.show_combo.currentText()
        
        if is_right:
            # 서버 폴더 선택 처리 (우측 위젯)
            self.scene_list_widget.scene_tree.clear()
            selection_widget.episode_combo.clear()
            selection_widget.episode_combo.addItem("폴더 선택")

            if season == "서버폴더 선택":
                selection_widget.episode_combo.setEnabled(False)
                return

            # 선택된 서버 정보 가져오기
            server = selection_widget.show_combo.currentText()
            if server == "서버 선택":
                return
            
            server_path = ValidationApp.SERVER_PATHS.get(server)
            if not server_path:
                return

            folder_path = os.path.join(server_path, season)
            
            try:
                # 하위 폴더 목록 가져오기
                subfolders = sorted([d for d in os.listdir(folder_path)
                                  if os.path.isdir(os.path.join(folder_path, d))
                                  and not d.startswith('.')])
                
                selection_widget.episode_combo.clear()
                selection_widget.episode_combo.addItem("폴더 선택")
                selection_widget.episode_combo.addItems(subfolders)
                selection_widget.episode_combo.setEnabled(True)
                
                self.statusBar.showMessage(f"{season} 폴더가 선택되었습니다. 하위 폴더를 선택하세요.")
            except Exception as e:
                self.statusBar.showMessage(f"하위 폴더 목록 로드 중 오류: {str(e)}")
                selection_widget.episode_combo.setEnabled(False)
        else:
            # 기존 시즌 선택 처리 (좌측 위젯)
            self.scene_list_widget.scene_tree.clear()
            selection_widget.episode_combo.clear()
            selection_widget.episode_combo.addItem("화수 선택")

            if hasattr(self, 'summary'):
                self.summary.total_frames_display.clear()
                if hasattr(self.summary, 'episode_feet_display'):
                    self.summary.episode_feet_display.clear()
            if hasattr(self, 'calculation'):
                self.calculation.total_feet_display.clear()

            if season == "시즌 선택":
                selection_widget.episode_combo.setEnabled(False)
                return

            show = selection_widget.show_combo.currentText()
            project_path = self.path_manager.get_show_path(show)
            if not project_path:
                project_path = self.project_manager.get_project_path(show)  # 프로젝트 매니저에서도 경로 확인
            if not project_path:
                return

            season_path = os.path.join(project_path, season)

            # Test 시리즈에서도 화수 선택을 활성화
            selection_widget.episode_combo.clear()
            selection_widget.episode_combo.addItem("화수 선택")
            selection_widget.episode_combo.setEnabled(True)

            # 화수 목록 업데이트
            self.update_episode_list(show, season, project_path, is_right)
            self.statusBar.showMessage(f"{show} - {season} 시즌이 선택되었습니다. 화수를 선택하세요.")

    def update_episode_list(self, show, season, project_path, is_right=False):
        selection_widget = self.right_show_widget if is_right else self.left_show_widget
        
        season_path = os.path.join(project_path, season)
        if not os.path.exists(season_path):
            self.statusBar.showMessage(f"시즌 경로를 찾을 수 없습니다: {season_path}")
            return

        try:
            episodes = []
            if show in ["BB", "GN", "BM", "KOTH"]:
                episodes = self.path_manager.show_paths[show].get("episodes", {}).get(season, [])
                if not episodes:  # 설정된 에피소드가 없으면 디렉토리에서 직접 찾기
                    episodes = sorted([d for d in os.listdir(season_path)
                                    if os.path.isdir(os.path.join(season_path, d))
                                    and not d.startswith('.')])
            else:
                episodes = sorted([d for d in os.listdir(season_path)
                                if os.path.isdir(os.path.join(season_path, d))
                                and not d.startswith('.')])

            if episodes:
                selection_widget.episode_combo.clear()
                selection_widget.episode_combo.addItem("화수 선택")
                selection_widget.episode_combo.addItems(episodes)
                selection_widget.episode_combo.setEnabled(True)
                self.statusBar.showMessage(f"{show} - {season} 시즌의 화수 목록이 로드되었습니다.")
            else:
                selection_widget.episode_combo.setEnabled(False)
                self.statusBar.showMessage(f"{show} - {season} 시즌에 화수가 없습니다.")

        except OSError as e:
            print(f"화수 목록 로드 중 오류 ({season_path}): {e}")
            self.statusBar.showMessage(f"화수 목록 로드 중 오류: {e}")

    def on_episode_changed(self, episode, is_right=False):
        """화수/폴더 선택이 변경되었을 때의 처리"""
        selection_widget = self.right_show_widget if is_right else self.left_show_widget
        show = selection_widget.show_combo.currentText()
        season = selection_widget.season_combo.currentText()
        
        if is_right:
            # 서버 폴더 선택 처리 (우측 위젯)
            self.scene_list_widget.scene_tree.clear()

            if episode == "폴더 선택":
                self.statusBar.showMessage("폴더를 선택해주세요")
                return

            # 서버 경로 가져오기
            server_path = os.path.join(ValidationApp.SERVER_PATHS.get(show, ""),
                                     season,
                                     episode)
            
            if not os.path.exists(server_path):
                self.statusBar.showMessage(f"서버 폴더를 찾을 수 없습니다: {server_path}")
                return

            try:
                # 씬 폴더 목록 가져오기
                self.load_scene_folders(server_path)
                self.update_scene_list()
                self.statusBar.showMessage(f"{episode} 폴더가 선택되었습니다.")
            except Exception as e:
                self.statusBar.showMessage(f"씬 폴더 목록 로드 중 오류: {str(e)}")
        else:
            # 화수 선택 처리 (좌측 위젯)
            self.scene_list_widget.scene_tree.clear()

            if episode == "화수 선택":
                self.statusBar.showMessage("화수를 선택해주세요")
                return

            # 작품 경로 가져오기
            project_path = self.path_manager.get_show_path(show)
            if not project_path:
                project_path = self.project_manager.get_project_path(show)  # 프로젝트 매니저에서도 경로 확인
            if not project_path:
                self.statusBar.showMessage(f"작품 경로를 찾을 수 없습니다: {show}")
                return

            episode_path = os.path.join(project_path, season, episode)
            if not os.path.exists(episode_path):
                self.statusBar.showMessage(f"화수 경로를 찾을 수 없습니다: {episode_path}")
                return

            try:
                # 씬 폴더 목록 가져오기
                self.load_scene_folders(episode_path)
                self.update_scene_list()
                self.statusBar.showMessage(f"{episode} 화수가 선택되었습니다.")
            except Exception as e:
                self.statusBar.showMessage(f"씬 폴더 목록 로드 중 오류: {str(e)}")

    def load_scene_folders(self, path):
        """씬 폴더를 로드하고 프레임 정보를 표시합니다."""
        if not os.path.exists(path):
            self.statusBar.showMessage(f"경로를 찾을 수 없습니다: {path}")
            return

        scene_folders = []
        try:
            scene_folders = sorted([item for item in os.listdir(path)
                                   if os.path.isdir(os.path.join(path, item))
                                   and item.startswith('scene-')])
        except OSError as e:
            self.statusBar.showMessage(f"폴더 접근 오류: {e}")
            return

        self.scene_list_widget.scene_tree.clear()
        
        # 현재 선택된 작품/시즌/화수 정보 가져오기
        show = self.left_show_widget.show_combo.currentText()
        season = self.left_show_widget.season_combo.currentText()
        episode = self.left_show_widget.episode_combo.currentText()
        
        # 식별자 구성
        if episode != "화수 선택":
            identifier = episode
        elif season != "시즌 선택":
            identifier = season
        elif show != "작품 선택":
            identifier = show
        else:
            identifier = None

        # DBU를 사용하여 프레임 정보 가져오기
        frames_info = {}
        if identifier:
            frames_info = self.db_manager.get_frames_info(identifier)

        total_frames = 0
        for scene in scene_folders:
            frames = "N/A"
            feet = "N/A"
            
            # 프레임 정보가 있으면 업데이트
            if scene in frames_info:
                scene_frames = frames_info[scene]['total_frames']
                frames = str(scene_frames)
                sheet_length = FrameCalculator.calculate_sheet_length(scene_frames)
                feet = FrameCalculator.format_feet_display(sheet_length)
                total_frames += scene_frames

            item = QTreeWidgetItem([scene, frames, feet, ""])
            self.scene_list_widget.scene_tree.addTopLevelItem(item)

        if scene_folders:
            self.statusBar.showMessage(f"{len(scene_folders)}개 씬 폴더 로드됨.")
            # 총 프레임 수와 FEET 업데이트
            if total_frames > 0:
                self.summary.total_frames_display.setText(str(total_frames))
                sheet_length = FrameCalculator.calculate_sheet_length(total_frames)
                feet_display = FrameCalculator.format_feet_display(sheet_length)
                self.summary.episode_feet_display.setText(feet_display)
        else:
            self.statusBar.showMessage("씬 폴더를 찾을 수 없습니다.")
            self.summary.total_frames_display.clear()
            self.summary.episode_feet_display.clear()

    def update_scene_list(self):
        # 실제 구현은 추후 추가
        pass

    def calculate_selected_scenes(self):
        """선택된 씬들의 프레임 수와 FEET를 계산합니다."""
        try:
            selected_items = self.scene_list_widget.scene_tree.selectedItems()
            if not selected_items:
                self.statusBar.showMessage("선택된 씬이 없습니다.", 5000)
                return

            total_frames = 0
            selected_scenes = []
            for item in selected_items:
                scene_name = item.text(0)
                if scene_name:
                    selected_scenes.append(scene_name)
                    # 프레임 정보는 두 번째 열에서 가져옴
                    frames_text = item.text(1)
                    try:
                        if frames_text and frames_text != "N/A":
                            frames = int(frames_text)
                            total_frames += frames
                    except ValueError:
                        continue

            # FrameCalculator를 사용하여 FEET 계산
            sheet_length = FrameCalculator.calculate_sheet_length(total_frames)
            feet_display = FrameCalculator.format_feet_display(sheet_length)

            # 결과 표시
            frames_display = f"{total_frames:,}"

            if hasattr(self.calculation_widget, 'total_frames_display'):
                self.calculation_widget.total_frames_display.setText(frames_display)
            if hasattr(self.calculation_widget, 'total_feet_display'):
                self.calculation_widget.total_feet_display.setText(feet_display)

            # 상태바 메시지 업데이트
            self.statusBar.showMessage(
                f"선택된 씬: {len(selected_scenes)}개, 총 프레임: {frames_display}, FEET: {feet_display}",
                5000
            )

        except Exception as e:
            self.statusBar.showMessage(f"씬 계산 중 오류 발생: {str(e)}", 5000)
            logging.error(f"씬 계산 중 오류: {e}")
            logging.error(f"계산 위젯 속성: {dir(self.calculation_widget)}")

    def export_to_excel(self):
        print("Exporting to Excel...")
        # 실제 구현은 추후 추가

    def initiate_backup(self, level, full_sync=True, is_right=False):
        selection_widget = self.right_show_widget if is_right else self.left_show_widget
        print(f"--- Initiating Backup --- Level: {level}, Full Sync: {full_sync}")
        if is_right:
            server = selection_widget.show_combo.currentText()
            server_folder = selection_widget.season_combo.currentText()
            episode = selection_widget.episode_combo.currentText()
            source_path = None
            path_description = ""
            # 드릴다운 스택이 있으면
            if hasattr(selection_widget, '_drilldown_stack') and selection_widget._drilldown_stack:
                parent_path = selection_widget._drilldown_stack[-1]
                if episode in ["상위로", "폴더 선택", None, ""]:
                    source_path = parent_path
                else:
                    candidate_path = os.path.join(parent_path, episode)
                    if os.path.isdir(candidate_path):
                        source_path = candidate_path
                    else:
                        source_path = parent_path
                path_description = source_path
            else:
                # 기존 방식 fallback
                if level == "show":
                    if server == "서버 선택": 
                        self.statusBar.showMessage("서버를 선택해주세요."); return
                    source_path = ValidationApp.SERVER_PATHS.get(server)
                    path_description = f"{server} (서버)"
                elif level == "season":
                    if server == "서버 선택" or server_folder == "서버폴더 선택": 
                        self.statusBar.showMessage("서버폴더를 선택해주세요."); return
                    server_path = ValidationApp.SERVER_PATHS.get(server)
                    if server_path: 
                        source_path = os.path.join(server_path, server_folder)
                        path_description = f"{server}/{server_folder} (서버폴더)"
                elif level == "episode":
                    if server == "서버 선택" or server_folder == "서버폴더 선택" or episode == "폴더 선택": 
                        self.statusBar.showMessage("폴더를 선택해주세요."); return
                    server_path = ValidationApp.SERVER_PATHS.get(server)
                    if server_path: 
                        source_path = os.path.join(server_path, server_folder, episode)
                        path_description = f"{server}/{server_folder}/{episode} (폴더)"
            print(f'[DEBUG] is_right={is_right}, episode={episode}, stack={getattr(selection_widget, "_drilldown_stack", None)}, source_path={source_path}, exists={os.path.exists(source_path) if source_path else None}')
            if not source_path or not os.path.exists(source_path):
                self.statusBar.showMessage(f"선택된 경로를 찾을 수 없습니다: {path_description}")
                return
        else:
            # 기존 작품 선택 처리
            show = selection_widget.show_combo.currentText()
            season = selection_widget.season_combo.currentText()
            episode = selection_widget.episode_combo.currentText()
            source_path = None
            path_description = ""
            if level == "show":
                if show == "작품 선택": 
                    self.statusBar.showMessage("작품을 선택해주세요."); return
                source_path = self.path_manager.get_show_path(show)
                path_description = f"{show} (작품)"
            elif level == "season":
                if show == "작품 선택" or season == "시즌 선택": 
                    self.statusBar.showMessage("시즌을 선택해주세요."); return
                show_path = self.path_manager.get_show_path(show)
                if show_path: 
                    source_path = os.path.join(show_path, season)
                    path_description = f"{show}/{season} (시즌)"
            elif level == "episode":
                if show == "작품 선택" or season == "시즌 선택" or episode == "화수 선택": 
                    self.statusBar.showMessage("화수를 선택해주세요."); return
                show_path = self.path_manager.get_show_path(show)
                if show_path: 
                    source_path = os.path.join(show_path, season, episode)
                    path_description = f"{show}/{season}/{episode} (화수)"
            print(f'[DEBUG] is_right={is_right}, episode={episode}, source_path={source_path}, exists={os.path.exists(source_path) if source_path else None}')
            if not source_path or not os.path.exists(source_path):
                self.statusBar.showMessage(f"선택된 경로를 찾을 수 없습니다: {path_description}")
                return

        # 백업 대상 폴더 선택 다이얼로그
        default_path = os.path.expanduser("~")
        dest_dir_base = QFileDialog.getExistingDirectory(
            self,
            f"{path_description} - 백업 대상 상위 폴더 선택",
            default_path,
            QFileDialog.ShowDirsOnly
        )
        print(f"[DEBUG] 백업 대상 폴더 선택 결과: {dest_dir_base}")
        if not dest_dir_base:
            self.statusBar.showMessage("백업 경로 선택이 취소되었습니다.", 3000)
            return

        # 경로 구조 유지를 위한 상대 경로 계산
        if is_right:
            server = selection_widget.show_combo.currentText()
            server_root = ValidationApp.SERVER_PATHS.get(server, "")
            if server_root and source_path.startswith(server_root):
                rel_path_from_root = os.path.relpath(source_path, server_root)
            else:
                rel_path_from_root = os.path.basename(source_path)
        else:
            possible_roots = sorted([p for p in self.path_manager.possible_paths if os.path.isdir(p)], key=len, reverse=True)
            common_root = ""
            for root in possible_roots:
                if source_path.startswith(root):
                    common_root = root
                    break
            if not common_root: 
                common_root = os.path.dirname(source_path)
            rel_path_from_root = os.path.relpath(source_path, common_root)
            if rel_path_from_root == '.' or not rel_path_from_root: 
                rel_path_from_root = os.path.basename(source_path)

        full_dest_path = os.path.normpath(os.path.join(dest_dir_base, rel_path_from_root))
        if not os.path.exists(full_dest_path):
            try: 
                os.makedirs(full_dest_path, exist_ok=True)
            except Exception as e: 
                QMessageBox.critical(self, "오류", f"대상 경로 생성 실패:\n{full_dest_path}\n\n오류: {e}")
                return
        elif not os.path.isdir(full_dest_path):
            QMessageBox.critical(self, "오류", f"대상 경로가 디렉토리가 아닙니다:\n{full_dest_path}")
            return

        job_id = str(uuid.uuid4())
        backup_mode = self.DEFAULT_BACKUP_MODE

        # 백업 모드 선택 다이얼로그 (전체 백업 시에만)
        if full_sync:
            try:
                self.statusBar.showMessage(f"백업 모드를 선택해주세요...")
                QApplication.processEvents()
                mode_dialog = QMessageBox(self)
                mode_dialog.setWindowTitle("백업 모드 선택")
                mode_dialog.setText(f"{path_description}에 대한 백업 모드를 선택하세요.")
                mode_dialog.setInformativeText("미러링: 소스와 대상을 완전히 일치시킵니다 (소스에 없는 파일은 대상에서 삭제)\n인크리멘털: 소스의 변경사항만 대상에 추가합니다 (대상의 기존 파일 유지)")
                mirror_button = mode_dialog.addButton("미러링", QMessageBox.ActionRole)
                incremental_button = mode_dialog.addButton("인크리멘탈", QMessageBox.ActionRole)
                cancel_button = mode_dialog.addButton("취소", QMessageBox.RejectRole)
                mode_dialog.setDefaultButton(mirror_button)
                mode_dialog.exec()
                if mode_dialog.clickedButton() == cancel_button:
                    self.statusBar.showMessage("백업 시작이 취소되었습니다.", 3000)
                    return
                elif mode_dialog.clickedButton() == mirror_button:
                    backup_mode = "mirror"
                elif mode_dialog.clickedButton() == incremental_button:
                    backup_mode = "incremental"
            except Exception as e:
                self.statusBar.showMessage(f"모드 선택 중 오류 발생: {str(e)}", 3000)
                backup_mode = self.DEFAULT_BACKUP_MODE

        # 작업 시작
        self.start_job_with_parallel_sync(
            job_id,
            source_path,
            full_dest_path,
            perform_initial_sync=full_sync,
            backup_mode=backup_mode,
            sync_interval=self.DEFAULT_PERIODIC_SYNC_INTERVAL  # 기본 동기화 간격 전달
        )

    def start_job_with_parallel_sync(self, job_id, source_path, dest_path, perform_initial_sync, backup_mode, sync_interval=None):
        """실시간 감시를 즉시 시작하고 초기 동기화는 병렬로 수행"""
        try:
            # 상태바 업데이트
            self.statusBar.showMessage(f"백업 작업 준비 중: {os.path.basename(source_path)}")
            QApplication.processEvents()  # UI 업데이트 처리

            # 동기화 간격이 지정되지 않은 경우 기본값 사용
            if sync_interval is None:
                sync_interval = self.DEFAULT_PERIODIC_SYNC_INTERVAL

            # 작업 정보 출력
            print(f"\n=== 작업 {job_id} 시작 ===")
            print(f"소스 경로: {source_path} (존재: {os.path.exists(source_path)}, 디렉토리: {os.path.isdir(source_path)})")
            print(f"대상 경로: {dest_path} (존재: {os.path.exists(dest_path)}, 디렉토리: {os.path.isdir(dest_path)})")
            print(f"백업 모드: {backup_mode}")
            print(f"동기화 간격: {sync_interval}초")
            print(f"초기 동기화: {perform_initial_sync}")

            # SingleJobStatusWidget 생성
            job_widget = SingleJobStatusWidget(
                job_id, source_path, dest_path,
                sync_interval=sync_interval,  # 전달된 동기화 간격 사용
                backup_mode=backup_mode,
                theme=self.current_theme  # 현재 앱의 테마를 전달
            )

            # 시그널 연결 (중복 방지)
            try:
                job_widget.stop_requested.disconnect()
            except Exception:
                pass
            job_widget.stop_requested.connect(self.stop_job)
            job_widget.sync_interval_changed.connect(self.handle_sync_interval_change)
            job_widget.backup_mode_changed.connect(self.handle_backup_mode_change)

            # 작업 위젯 추가
            self.active_jobs_widget.add_job_widget(job_id, job_widget)
            self.active_jobs_widget.setVisible(True)  # 작업 위젯 표시

            # 작업 정보 저장
            print(f"\n작업 {job_id} 정보 저장 중...")
            self.active_jobs[job_id] = {
                "job_id": job_id,
                "source_path": source_path,
                "dest_path": dest_path,
                "status": "Initializing",
                "sync_interval": sync_interval,  # 전달된 동기화 간격 사용
                "backup_mode": backup_mode,
                "perform_initial_sync": perform_initial_sync,
                "scene_backup_status": {},
                "backup_queue": queue.Queue(),
                "ui_widget": job_widget,
                "initializer": None,
                "worker": None,
                "source_observer": None,
                "dest_observer": None
            }
            self.job_queues[job_id] = self.active_jobs[job_id]["backup_queue"]

            # 작업 상태 파일에 즉시 저장
            self.save_active_jobs()
            print(f"작업 {job_id} 정보가 저장되었습니다.")

            # 실시간 감시 시작
            job_widget.set_status("실시간 감시 시작 중...")
            self.start_realtime_monitoring(job_id)

            # 초기 동기화 시작 (필요한 경우)
            if perform_initial_sync:
                job_widget.append_log(f"{backup_mode} 모드로 백그라운드 초기 동기화를 시작합니다.")
                initializer = BackupInitializer(job_id, source_path, dest_path)
                initializer.progress_signal.connect(self.handle_initializer_progress)
                initializer.initialization_finished.connect(self.handle_parallel_initialization_finished)
                self.active_jobs[job_id]["initializer"] = initializer
                initializer.start()

            # 상태바 업데이트
            mode_display = "미러링" if backup_mode == "mirror" else "인크리멘탈"
            self.statusBar.showMessage(f"백업 작업이 시작되었습니다: {os.path.basename(source_path)} ({mode_display} 모드)")
            print(f"=== 작업 {job_id} 시작 완료 ===\n")

        except Exception as e:
            error_msg = f"백업 작업 시작 중 오류 발생: {str(e)}"
            self.statusBar.showMessage(error_msg, 5000)
            print(f"\n!!! 작업 {job_id} 시작 중 오류 발생 !!!")
            print(f"오류: {str(e)}")
            import traceback
            error_details = traceback.format_exc()
            print(f"오류 상세 정보:\n{error_details}")

            # 작업 정리
            if job_id in self.active_jobs:
                print(f"작업 {job_id} 정리 중...")
                self.stop_job(job_id)
                del self.active_jobs[job_id]
                if job_id in self.job_queues:
                    del self.job_queues[job_id]
                self.save_active_jobs()
                print(f"작업 {job_id} 정리 완료")

            # 오류 대화 상자 표시
            QMessageBox.critical(self, "오류", f"백업 작업 시작 중 오류 발생:\n{str(e)}\n\n상세 정보는 콘솔을 확인하세요.")
            return

    def handle_initializer_progress(self, job_id, message, progress):
        """초기화 진행 상황을 처리합니다."""
        print(f"Initializer progress for job {job_id}: {message} ({progress}%)")
        self.statusBar.showMessage(message, 3000)

        job_widget = self.active_jobs_widget.get_job_widget(job_id)
        if job_widget:
            if message: job_widget.append_log(message)
            if progress >= 0:
                # 직접 progress_bar 값 업데이트
                job_widget.progress_bar.setValue(progress)
                if progress < 100:
                    job_widget.status_label.setText(f"초기 동기화 중... ({progress}%)")
                    job_widget.progress_bar.setFormat(f"%p% ({progress}%)")

    def handle_parallel_initialization_finished(self, job_id, success):
        """병렬 초기 동기화 완료를 처리합니다."""
        job_info = self.active_jobs.get(job_id)
        if not job_info: return
        job_widget = job_info["ui_widget"]
        job_info["initializer"] = None

        if success:
            job_widget.set_status("초기 동기화 완료", 100)
            job_widget.progress_bar.setValue(100)  # 진행바를 100%로 설정
            print(f"Background initialization for job {job_id} completed successfully")
            self.statusBar.showMessage(f"백그라운드 초기 동기화가 완료되었습니다.", 3000)
        else:
            job_widget.set_status("오류: 초기 동기화 실패", -1)
            job_widget.append_log("초기 동기화에 실패하였지만, 실시간 감시는 계속 진행됩니다.")
            print(f"Background initialization for job {job_id} failed")
            self.statusBar.showMessage(f"백그라운드 초기 동기화가 실패했지만, 실시간 감시는 계속 진행됩니다.", 3000)

    def start_realtime_monitoring(self, job_id):
        """실시간 모니터링을 시작합니다."""
        try:
            print(f"Starting realtime monitoring for job {job_id}")
            job_info = self.active_jobs.get(job_id)
            if not job_info:
                print(f"Job {job_id} not found in active jobs")
                return

            source_path = job_info.get("source_path")
            dest_path = job_info.get("dest_path")
            backup_mode = job_info.get("backup_mode", "incremental")
            sync_interval = job_info.get("sync_interval", 120)

            if not source_path or not dest_path:
                print(f"Invalid paths for job {job_id}")
                return

            # 핸들러 생성
            source_handler = BackupEventHandler(Queue(), source_path, dest_path, is_source=True)
            dest_handler = BackupEventHandler(Queue(), source_path, dest_path, is_source=False)

            # BackupWorker 초기화 (parent 인자 제거)
            worker = BackupWorker(
                job_id=job_id,
                backup_queue=Queue(),
                source_handler=source_handler,
                dest_handler=dest_handler,
                scene_tree_ref=self.scene_list_widget.scene_tree,
                source_path=source_path,
                dest_path=dest_path,
                max_workers=self.DEFAULT_WORKER_THREADS,
                periodic_sync_interval=sync_interval,
                backup_mode=backup_mode,
                log_queue=self.log_queue  # UI 로그 큐 전달
            )

            # 워커 시그널 연결
            worker.update_signal.connect(lambda msg, prog: self.handle_worker_update(job_id, msg, prog))
            worker.job_finished_signal.connect(lambda: self.handle_worker_finished(job_id))
            worker.scene_backup_status_signal.connect(lambda scene, status: self.handle_scene_backup_status(job_id, scene, status))
            worker.restart_observer_signal.connect(lambda path, is_source: self.handle_restart_observer(job_id, path, is_source))
            worker.events_updated_signal.connect(self.active_jobs_widget.update_metadata_display)  # ★ DB 갱신 시 UI 즉시 갱신

            # 워커 시작
            worker.start()
            job_info["worker"] = worker
            job_info["status"] = "Running"
            self.save_active_jobs()

            print(f"=== 작업 {job_id} 모니터링 시작 완료 ===")

        except Exception as e:
            print(f"Error starting realtime monitoring: {str(e)}")
            import traceback
            print(f"Traceback:\n{traceback.format_exc()}")
            if job_info and "ui_widget" in job_info:
                job_info["ui_widget"].set_status("실시간 모니터링 시작 실패")
                job_info["ui_widget"].append_log(f"오류: {str(e)}")
            job_info["status"] = "Error"
            self.save_active_jobs()

    def handle_worker_update(self, job_id, message, progress):
        """워커로부터 업데이트를 처리합니다."""
        try:
            # progress를 정수로 변환
            try:
                progress = int(float(progress))
            except (ValueError, TypeError):
                progress = -1  # 변환 실패 시 -1로 설정
            
            if progress >= 0:
                job_info = self.active_jobs.get(job_id)
                if job_info and "ui_widget" in job_info:
                    job_info["ui_widget"].set_status(message, progress)
            else:
                # progress가 없는 경우 메시지만 업데이트
                job_info = self.active_jobs.get(job_id)
                if job_info and "ui_widget" in job_info:
                    job_info["ui_widget"].append_log(message)
        except Exception as e:
            print(f"Error in handle_worker_update: {str(e)}")
            import traceback
            print(f"Traceback:\n{traceback.format_exc()}")

    def handle_scene_backup_status(self, job_id, scene_path, status):
        """씬 백업 상태를 처리합니다."""
        print(f"Scene backup status for job {job_id}: {scene_path} -> {status}")

    def handle_restart_observer(self, job_id, path_to_restart, is_source):
        """감시자 재시작을 처리합니다."""
        print(f"Restarting observer for job {job_id}: {path_to_restart} (is_source: {is_source})")

    def handle_worker_finished(self, job_id):
        """워커 완료를 처리합니다."""
        print(f"Worker finished for job {job_id}")
        self.statusBar.showMessage(f"백업 작업이 완료되었습니다.", 3000)

    def reset_and_update_job_scene_status(self, job_id):
        """작업의 씬 백업 상태를 초기화하고 업데이트합니다."""
        print(f"Resetting and updating scene status for job {job_id}")

    def handle_sync_interval_change(self, job_id, new_interval):
        """동기화 간격 변경을 처리합니다."""
        try:
            job_info = self.active_jobs.get(job_id)
            if not job_info:
                print(f"Warning: Job {job_id} not found for interval change")
                return

            # 저장된 값 업데이트
            job_info["sync_interval"] = new_interval

            # 상태바 업데이트
            self.statusBar.showMessage(f"작업 {os.path.basename(job_info['source_path'])} 동기화 간격을 변경 중...", 3000)

            # 워커가 있는 경우 직접 간격 변경
            if "worker" in job_info and job_info["worker"] is not None:
                job_info["worker"].update_sync_interval(new_interval)  # set_sync_interval을 update_sync_interval로 변경
                self.statusBar.showMessage(f"작업 {os.path.basename(job_info['source_path'])} 동기화 간격이 {new_interval}초로 변경되었습니다.", 3000)
                return

            # 워커가 없는 경우 새로 시작
            if new_interval == 0:
                self.stop_job_resources(job_id)
                self.statusBar.showMessage(f"작업 {os.path.basename(job_info['source_path'])} 동기화 간격이 무설정으로 변경되어 백업 작업이 즉시 중지되었습니다. (설정은 유지됨)", 3000)
                return

            # 기존 워커/감시자 등 리소스 완전 정리
            self.stop_job_resources(job_id)

            # 새 워커 인스턴스 생성 및 교체
            self.start_realtime_monitoring(job_id)
            # UI 갱신 및 상태 업데이트
            QApplication.processEvents()
            self.statusBar.showMessage(f"작업 {os.path.basename(job_info['source_path'])} 동기화 간격이 변경되어 즉시 재시작되었습니다.", 3000)
        except Exception as e:
            print(f"Error in handle_sync_interval_change: {e}")
            import traceback
            traceback.print_exc()
            self.statusBar.showMessage(f"동기화 간격 변경 중 오류 발생: {str(e)}", 5000)

    def handle_backup_mode_change(self, job_id, new_mode):
        """작업의 백업 모드 변경을 처리합니다."""
        try:
            print(f"Changing backup mode for job {job_id} to {new_mode}")
            job_info = self.active_jobs.get(job_id)
            if not job_info:
                print(f"Job {job_id} not found")
                return

            # 백업 모드 업데이트
            job_info["backup_mode"] = new_mode
            
            # 워커가 있으면 워커의 백업 모드도 업데이트
            worker = job_info.get("worker")
            if worker:
                worker.backup_mode = new_mode
                print(f"Updated worker backup mode to {new_mode}")
            
            # 작업 상태 저장
            self.save_active_jobs()
            print(f"Saved job state with new backup mode: {new_mode}")

        except Exception as e:
            print(f"Error in handle_backup_mode_change: {str(e)}")
            import traceback
            print(traceback.format_exc())

    def stop_job(self, job_id):
        """작업을 중지하고 관련 리소스를 정리합니다."""
        try:
            print(f"\n=== 작업 {job_id} 중지 시작 ===")
            if job_id not in self.active_jobs:
                print(f"작업을 찾을 수 없음: {job_id}")
                return

            # 1. UI 위젯 상태 업데이트
            job_widget = self.active_jobs_widget.get_job_widget(job_id)
            if job_widget:
                job_widget.set_status("중지됨", -1)
                job_widget.stop_button.setEnabled(False)
                job_widget.append_log("작업이 중지되었습니다.")
                QApplication.processEvents()

            # 2. 작업 리소스 정리
            self.stop_job_resources(job_id)

            # 3. 작업 정보 삭제
            if job_id in self.active_jobs:
                del self.active_jobs[job_id]
            if job_id in self.job_queues:
                del self.job_queues[job_id]

            # 4. UI 위젯 제거
            if job_widget:
                # 위젯의 모든 자식 위젯 제거
                for child in job_widget.findChildren(QWidget):
                    child.setParent(None)
                    child.deleteLater()
                
                # 위젯 자체 제거
                job_widget.setParent(None)
                job_widget.hide()
                job_widget.deleteLater()
                
                # ActiveJobsWidget에서 제거
                self.active_jobs_widget.remove_job_widget(job_id)
                QApplication.processEvents()

            # 5. 작업 상태 파일 업데이트
            self.save_active_jobs()
            print(f"작업 {job_id} 상태 파일에서 제거됨")

            # 6. 활성 작업이 없으면 위젯 숨기기
            if not self.active_jobs:
                self.active_jobs_widget.setVisible(False)
                QApplication.processEvents()

            print(f"=== 작업 {job_id} 중지 완료 ===\n")
            self.statusBar.showMessage(f"작업이 중지되었습니다.", 3000)

        except Exception as e:
            print(f"\n!!! 작업 {job_id} 중지 중 오류 발생 !!!")
            print(f"오류: {str(e)}")
            import traceback
            print(f"오류 상세 정보:\n{traceback.format_exc()}")
            self.statusBar.showMessage(f"작업 중지 중 오류 발생: {str(e)}", 5000)

    def stop_job_resources(self, job_id):
        """작업 리소스를 안전하게 정리합니다."""
        try:
            job_info = self.active_jobs.get(job_id)
            if not job_info:
                print(f"Job {job_id} not found in active jobs")
                return

            print(f"\n=== 작업 {job_id} 리소스 정리 시작 ===")
            job_widget = job_info.get("ui_widget")

            # 1. 워커 중지 (가장 먼저 중지)
            if job_info.get("worker"):
                try:
                    worker = job_info["worker"]
                    if hasattr(worker, "isRunning") and worker.isRunning():
                        if job_widget:
                            job_widget.append_log("백업 워커 중지 중...")
                        worker.running = False
                        worker.stop()
                        worker.wait(8000)  # 8초 대기
                        if job_widget:
                            job_widget.append_log("백업 워커 중지됨")
                    job_info["worker"] = None
                except Exception as e:
                    error_msg = f"워커 중지 중 오류: {str(e)}"
                    print(error_msg)
                    if job_widget:
                        job_widget.append_log(error_msg)

            # 2. 초기화 스레드 중지
            if job_info.get("initializer"):
                try:
                    initializer = job_info["initializer"]
                    if hasattr(initializer, "isRunning") and initializer.isRunning():
                        if job_widget:
                            job_widget.append_log("초기 동기화 중지 중...")
                        initializer.running = False
                        initializer.stop()
                        initializer.wait(5000)  # 5초 대기
                        if job_widget:
                            job_widget.append_log("초기 동기화 중지됨")
                    job_info["initializer"] = None
                except Exception as e:
                    error_msg = f"초기화 중지 중 오류: {str(e)}"
                    print(error_msg)
                    if job_widget:
                        job_widget.append_log(error_msg)

            # 3. 소스 감시자 중지
            if job_info.get("source_observer"):
                try:
                    observer = job_info["source_observer"]
                    if hasattr(observer, "is_alive") and observer.is_alive():
                        if job_widget:
                            job_widget.append_log("소스 감시자 중지 중...")
                        observer.stop()
                        observer.join(timeout=5)  # 5초 대기
                        if job_widget:
                            job_widget.append_log("소스 감시자 중지됨")
                    job_info["source_observer"] = None
                except Exception as e:
                    error_msg = f"소스 감시자 중지 중 오류: {str(e)}"
                    print(error_msg)
                    if job_widget:
                        job_widget.append_log(error_msg)

            # 4. 추가 리소스 정리
            for key in list(job_info.keys()):
                if key not in ["ui_widget"]:  # UI 위젯은 별도로 처리
                    job_info[key] = None

            print(f"=== 작업 {job_id} 리소스 정리 완료 ===\n")


        except Exception as e:
            error_msg = f"작업 리소스 정리 중 예외 발생: {str(e)}"
            print(error_msg)
            if job_widget:
                job_widget.append_log(error_msg)

    def update_progress(self, current, total):
        """진행률을 업데이트합니다."""
        if total == 0:
            percentage = 0
        else:
            percentage = int((current / total) * 100)
            if current >= total or percentage >= 95:  # 현재 값이 총 값보다 크거나 같거나, 95% 이상이면 100%
                percentage = 100
        
        self.progress_bar.setValue(percentage)
        self.progress_label.setText(f"{percentage}%")

    def handle_sync_complete(self):
        """동기화 완료 시 호출되는 메서드"""
        self.progress_bar.setValue(100)
        self.progress_label.setText("100%")
        self.status_label.setText("동기화 완료")
        self.status_label.setStyleSheet("color: #4CAF50;")

    def process_rsync_output(self, output):
        """rsync 출력을 처리하고 진행률을 업데이트합니다."""
        try:
            # 완료 메시지 확인
            if any(msg in output.lower() for msg in ["total size is", "speedup is"]):
                self.progress_bar.setValue(100)
                self.progress_label.setText("100%")
                self.status_label.setText("동기화 완료")
                self.status_label.setStyleSheet("color: #4CAF50;")
                return

            # 전송 시작 메시지 확인
            if "Transfer starting:" in output:
                self.progress_bar.setValue(0)
                self.progress_label.setText("0%")
                return

            # 진행률 정보 파싱
            if "to-check=" in output:
                match = re.search(r"to-check=(\d+)/(\d+)", output)
                if match:
                    remaining = int(match.group(1))
                    total = int(match.group(2))
                    if total > 0:  # 0으로 나누기 방지
                        current = total - remaining
                        percentage = int((current / total) * 100)
                        if remaining == 0 or percentage >= 95:  # 남은 파일이 없거나 95% 이상이면 100%로 설정
                            percentage = 100
                        self.progress_bar.setValue(percentage)
                        self.progress_label.setText(f"{percentage}%")

        except Exception as e:
            print(f"Error processing rsync output: {e}")

    def add_project(self):
        """작품 추가 다이얼로그를 표시하고 새 작품을 추가"""
        dialog = AddProjectDialog(self, self.project_manager.get_projects(), self.project_manager)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data['name'] and data['path']:  # 새 작품이 추가된 경우
                self.project_manager.add_project(data['name'], data['path'])
                
            # 왼쪽과 오른쪽 ShowSelectionWidget의 작품 목록 업데이트
            self.left_show_widget.project_manager = self.project_manager
            self.right_show_widget.project_manager = self.project_manager
            
            # 콤보박스 업데이트
            self.left_show_widget.show_combo.clear()
            self.left_show_widget.show_combo.addItem("작품 선택")
            self.left_show_widget.show_combo.addItems(sorted(self.project_manager.get_projects()))

    def toggle_theme(self):
        """테마를 비동기적으로 전환합니다."""
        # 테마 상태 즉시 변경
        self.current_theme = "light" if self.current_theme == "dark" else "dark"
        self.update_theme_button_icon()
        
        # 스타일 변경을 QTimer를 사용하여 비동기적으로 처리
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(0, lambda: self._apply_theme_changes())
    
    def _apply_theme_changes(self):
        """테마 변경을 실제로 적용합니다."""
        # 메인 윈도우 스타일 적용
        StyleManager.apply_style(self, self.current_theme)
        
        # 활성 작업 위젯들의 테마 업데이트를 개별적으로 처리
        for job_widget in self.active_jobs_widget.job_widgets.values():
            QTimer.singleShot(0, lambda w=job_widget: w.update_theme_style(self.current_theme))

    def load_active_jobs(self):
        """저장된 작업 상태를 불러옵니다."""
        try:
            print(f"\n=== 저장된 작업 로드 시작 ===")
            print(f"로드 경로: {self._job_state_file_path}")
            
            if os.path.exists(self._job_state_file_path):
                with open(self._job_state_file_path, 'r', encoding='utf-8') as f:
                    data = json.loads(f.read())
                    
                    # 서버 목록 불러오기
                    if 'servers' in data:
                        print("저장된 서버 목록 불러오기:", list(data['servers'].keys()))
                        ValidationApp.SERVER_PATHS.clear()  # 기존 서버 목록 초기화
                        ValidationApp.SERVER_PATHS.update(data['servers'])  # 저장된 서버 목록으로 업데이트
                    else:
                        print("저장된 서버 목록이 없어 기본값 사용")
                        ValidationApp.SERVER_PATHS = {
                            "usadata1": "/System/Volumes/Data/usadata1",
                            "usadata4": "/Users/<USER>/Desktop/usadata4",
                            "usadata5": "/System/Volumes/Data/usadata5",
                            "usadata6": "/System/Volumes/Data/usadata6",
                            "bgfinal": "/Volumes/bgfinal"
                        }
                    
                    # 작품 목록 불러오기
                    if 'projects' in data:
                        print("저장된 프로젝트 목록 불러오기:", data['projects'])
                        self.project_manager.load_from_settings(data['projects'])
                    
                    # 활성 작업 불러오기
                    if 'active_jobs' in data:
                        self.active_jobs = data['active_jobs']
                        print(f"저장된 작업 수: {len(self.active_jobs)}")
                        
                        # 각 작업 재시작
                        for job_id, job_info in self.active_jobs.items():
                            try:
                                print(f"\n--- 작업 {job_id} 로드 시작 ---")
                                print(f"소스 경로: {job_info['source_path']}")
                                print(f"대상 경로: {job_info['dest_path']}")
                                print(f"동기화 간격: {job_info['sync_interval']}")
                                print(f"백업 모드: {job_info['backup_mode']}")
                                
                                if not os.path.exists(job_info['source_path']):
                                    print(f"경고: 소스 경로가 존재하지 않습니다 - {job_info['source_path']}")
                                    continue

                                print("경로 확인 완료, 작업 재시작 시도...")
                                
                                # 작업 재시작
                                self.start_job_with_parallel_sync(
                                    job_id,
                                    job_info['source_path'],
                                    job_info['dest_path'],
                                    perform_initial_sync=False,  # 초기 동기화 건너뛰기
                                    backup_mode=job_info['backup_mode'],  # 저장된 백업 모드 전달
                                    sync_interval=job_info['sync_interval']  # 저장된 동기화 간격 전달
                                )
                            except Exception as e:
                                print(f"작업 {job_id} 로드 중 오류 발생: {str(e)}")
                                print("오류 상세 정보:")
                                import traceback
                                print(traceback.format_exc())
                                continue
                    # exclusions 불러오기
                    self.backup_exclusions = data.get('exclusions', {})
            else:
                print(f"저장된 설정 파일이 없습니다: {self._job_state_file_path}")
                print("기본 설정을 사용합니다.")
                # 기본값으로 초기화
                self.active_jobs = {}
                ValidationApp.SERVER_PATHS = {
                    "usadata1": "/System/Volumes/Data/usadata1",
                    "usadata4": "/Users/<USER>/Desktop/usadata4",
                    "usadata5": "/System/Volumes/Data/usadata5",
                    "usadata6": "/System/Volumes/Data/usadata6",
                    "bgfinal": "/Volumes/bgfinal"
                }
                self.backup_exclusions = {}
        except Exception as e:
            print("\n!!! 작업 로드 중 치명적 오류 발생 !!!")
            print(f"오류: {str(e)}")
            print("오류 상세 정보:")
            import traceback
            print(traceback.format_exc())
            
            # 기본값으로 초기화
            self.active_jobs = {}
            ValidationApp.SERVER_PATHS = {
                "usadata1": "/System/Volumes/Data/usadata1",
                "usadata4": "/Users/<USER>/Desktop/usadata4",
                "usadata5": "/System/Volumes/Data/usadata5",
                "usadata6": "/System/Volumes/Data/usadata6",
                "bgfinal": "/Volumes/bgfinal"
            }
            self.backup_exclusions = {}
        
        print("\n=== 저장된 작업 로드 완료 ===\n")

    def save_active_jobs(self):
        """현재 활성화된 작업 상태와 설정을 파일에 저장합니다."""
        try:
            print(f"\n=== 활성 작업 저장 시작 ===")
            print(f"저장 경로: {self._job_state_file_path}")
            
            # 저장할 데이터 준비
            data_to_save = {
                'active_jobs': {},
                'servers': dict(ValidationApp.SERVER_PATHS),  # 서버 목록을 딕셔너리로 변환하여 저장
                'projects': self.project_manager.get_projects(),  # 프로젝트 목록 추가
                'exclusions': self.backup_exclusions.copy(),  # exclusions 저장
            }
            
            # 활성 작업 정보 수집
            for job_id, job_info in self.active_jobs.items():
                print(f"\n작업 {job_id} 저장 중...")
                # 작업 위젯에서 현재 설정된 값들을 가져옴
                job_widget = self.active_jobs_widget.get_job_widget(job_id)
                if job_widget:
                    sync_interval = job_widget.sync_interval  # 직접 위젯의 interval 값 사용
                    backup_mode = job_widget.backup_mode
                else:
                    sync_interval = job_info.get("sync_interval", self.DEFAULT_PERIODIC_SYNC_INTERVAL)
                    backup_mode = job_info.get("backup_mode", self.DEFAULT_BACKUP_MODE)
                
                # 직렬화 가능한 정보만 저장
                data_to_save['active_jobs'][job_id] = {
                    "source_path": job_info["source_path"],
                    "dest_path": job_info["dest_path"],
                    "sync_interval": sync_interval,
                    "backup_mode": backup_mode,
                    "status": job_info.get("status", "Initializing"),
                    "last_saved": datetime.now().isoformat()
                }
                print(f"작업 정보: {data_to_save['active_jobs'][job_id]}")
            
            # 저장 디렉토리가 없으면 생성
            save_dir = os.path.dirname(self._job_state_file_path)
            print(f"저장 디렉토리 생성: {save_dir}")
            os.makedirs(save_dir, exist_ok=True)
            
            # 파일에 저장
            print(f"파일에 저장 중...")
            with open(self._job_state_file_path, 'w', encoding='utf-8') as f:
                json_data = json.dumps(data_to_save, ensure_ascii=False, indent=2)
                print(f"저장할 JSON 데이터:\n{json_data}")
                f.write(json_data)
            
            print(f"\n총 {len(data_to_save['active_jobs'])}개의 작업이 저장됨")
            print(f"서버 목록: {len(data_to_save['servers'])}개")
            print(f"프로젝트 목록: {len(data_to_save['projects'])}개")
            print(f"저장 파일: {self._job_state_file_path}")
            print("=== 활성 작업 저장 완료 ===\n")
            
            return True
        except Exception as e:
            print(f"\n!!! 작업 저장 중 오류 발생 !!!")
            print(f"오류: {str(e)}")
            import traceback
            print(f"오류 상세 정보:\n{traceback.format_exc()}")
            return False

    def closeEvent(self, event):
        """앱이 종료될 때 호출되는 이벤트 핸들러"""
        try:
            print("\n=== 앱 종료 시작 ===")
            
            # 모든 작업의 running 플래그를 안전하게 False로 설정
            for job_id in list(self.active_jobs.keys()):
                try:
                    job_info = self.active_jobs.get(job_id)
                    if job_info and isinstance(job_info, dict):
                        worker = job_info.get("worker")
                        if worker is not None and hasattr(worker, "running"):
                            worker.running = False
                except Exception as e:
                    print(f"작업 {job_id} 종료 중 오류: {str(e)}")
            
            # 별도 스레드에서 정리 작업 수행
            cleanup_thread = threading.Thread(target=self._cleanup_on_exit)
            cleanup_thread.daemon = True
            cleanup_thread.start()
            
            # 이벤트 수락
            event.accept()
            
        except Exception as e:
            print(f"앱 종료 중 오류 발생: {str(e)}")
            import traceback
            print(traceback.format_exc())
            event.accept()  # 오류가 발생해도 종료는 진행

    def _cleanup_on_exit(self):
        """종료 시 정리 작업을 수행하는 메서드"""
        try:
            print("\n=== 앱 종료 시작 ===")
            
            # 활성 작업 상태 저장
            self.save_active_jobs()
            
            # 모든 활성 작업 중지 (비동기)
            cleanup_threads = []
            active_job_ids = list(self.active_jobs.keys())
            print(f"활성 작업 수: {len(active_job_ids)}")
            
            for job_id in active_job_ids:
                try:
                    if job_id in self.active_jobs:
                        job_info = self.active_jobs[job_id]
                        
                        # 워커 중지
                        if "worker" in job_info and job_info["worker"] is not None:
                            job_info["worker"].running = False
                        
                        # 초기화 작업 중지
                        if "initializer" in job_info and job_info["initializer"] is not None:
                            job_info["initializer"].running = False
                        
                        # 큐 정리
                        if job_id in self.job_queues:
                            try:
                                self.job_queues[job_id].queue.clear()
                            except:
                                pass
                        
                        print(f"작업 {job_id} 리소스 정리 완료")
                except Exception as e:
                    print(f"작업 {job_id} 정리 중 오류: {str(e)}")
            
            # 최대 3초 동안만 정리 작업 대기
            for thread in cleanup_threads:
                thread.join(timeout=3.0)
            
            # 작업 상태 저장 시도
            try:
                self.save_active_jobs()
            except:
                pass
            
            print("=== 앱 종료 완료 ===\n")
            
        except Exception as e:
            print(f"정리 작업 중 오류: {str(e)}")
            import traceback
            print(traceback.format_exc())
    def setup_shortcuts(self):
        """단축키 설정"""
        # 테마 전환 단축키 (Ctrl+T)
        self.theme_shortcut = QShortcut(QKeySequence("Ctrl+T"), self)
        self.theme_shortcut.activated.connect(self.toggle_theme)
        self.theme_shortcut.activated.connect(self.toggle_theme)

    def add_server(self):
        """서버 추가 다이얼로그를 표시합니다."""
        dialog = AddServerDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            # 오른쪽 작품 선택 위젯 업데이트
            self.right_show_widget.on_projects_changed()
    def cleanup_before_exit(self):
        """앱 종료 전 모든 리소스를 정리합니다."""
        try:
            print("=== 앱 종료 정리 작업 시작 ===")
            
            # 1. 백업 작업 중지 (backup_workers가 있는 경우에만)
            if hasattr(self, 'backup_workers'):
                for job_id, worker in self.backup_workers.items():
                    try:
                        worker.stop()
                    except Exception as e:
                        print(f"작업 {job_id} 중지 중 오류: {str(e)}")
            
            # 2. 활성 작업 상태 저장
            self.save_active_jobs()
            
            # 3. 스레드 풀 종료
            self.thread_pool.shutdown(wait=True)
            
            # 4. 기존 정리 작업 수행
            self._cleanup_on_exit()
            
        except Exception as e:
            logging.error(f"종료 정리 작업 중 오류 발생: {e}")

    def handle_batch_updates(self, messages):
        """배치 단위로 로그 메시지 처리"""
        log_widget = self.findChild(QTextEdit, "logWidget")
        if not log_widget:
            return
            
        # 현재 텍스트 길이가 너무 길면 이전 로그 삭제
        current_text = log_widget.toPlainText()
        lines = current_text.split('\n')
        if len(lines) > 500:  # 최대 500줄만 유지
            log_widget.setText('\n'.join(lines[-400:]))  # 400줄만 남기고 나머지 삭제
        
        # 새 메시지 추가
        cursor = log_widget.textCursor()
        cursor.movePosition(QTextCursor.End)
        
        # 메시지 일괄 처리를 위한 버퍼
        text_buffer = []
        for timestamp, msg, progress in messages:
            text_buffer.append(msg)  # 이미 포맷팅된 메시지 사용
        
        # 한 번에 텍스트 추가
        if text_buffer:
            cursor.insertText('\n'.join(text_buffer) + '\n')
        
        # 스크롤을 최신 내용으로
        log_widget.setTextCursor(cursor)
        log_widget.ensureCursorVisible()

    def cleanup_old_logs(self, max_age_hours=1):  # 24시간에서 1시간으로 변경
        """오래된 로그 항목 정리"""
        try:
            current_time = time.time()
            log_widget = self.findChild(QTextEdit, "logWidget")
            if not log_widget:
                return
                
            lines = log_widget.toPlainText().split('\n')
            if len(lines) <= 500:  # 500줄 이하면 정리하지 않음
                return
                
            recent_logs = []
            for line in lines:
                try:
                    if line.startswith('['):
                        timestamp_str = line[1:20]  # [YYYY-MM-DD HH:MM:SS] 형식
                        log_time = time.mktime(time.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S'))
                        
                        # max_age_hours 이내의 로그만 유지
                        if current_time - log_time < max_age_hours * 3600:
                            recent_logs.append(line)
                except:
                    recent_logs.append(line)
            
            # 최근 400줄만 유지
            if len(recent_logs) > 400:
                recent_logs = recent_logs[-400:]
                
            log_widget.setText('\n'.join(recent_logs))
        except Exception as e:
            print(f"로그 정리 중 오류: {e}")

    def setup_backup_worker(self, job_id, source_path, dest_path, backup_mode="mirror"):
        """백업 워커를 설정하고 스레드 풀에 추가합니다."""
        try:
            worker = BackupWorker(
                job_id=job_id,
                source_path=source_path,
                dest_path=dest_path,
                backup_mode=backup_mode,
                log_queue=self.log_queue
            )
            
            # 워커 참조 저장
            self.active_workers[job_id] = worker
            
            # 스레드 풀에 작업 제출
            future = self.thread_pool.submit(worker.run)
            future.add_done_callback(lambda f: self.handle_worker_finished(job_id))
            
            return worker
            
        except Exception as e:
            logging.error(f"백업 워커 설정 중 오류 발생: {e}")
            return None
            
    def cleanup_before_exit(self):
        """앱 종료 전 정리 작업을 수행합니다."""
        try:
            # 로그 타이머 중지
            self.log_timer.stop()
            # 모든 활성 작업 중지
            for job_id in list(self.active_workers.keys()):
                try:
                    worker = self.active_workers[job_id]
                    worker.stop()
                except Exception as e:
                    print(f"작업 {job_id} 중지 중 오류: {str(e)}")
            # 스레드 풀 종료
            self.thread_pool.shutdown(wait=True)
            # 기존 정리 작업 수행
            self._cleanup_on_exit()
        except Exception as e:
            logging.error(f"종료 정리 작업 중 오류 발생: {e}")

    def stop_job(self, job_id):
        """작업을 안전하게 중지합니다."""
        try:
            if job_id in self.active_workers:
                worker = self.active_workers[job_id]
                # 반드시 stop() 호출로 스레드/타이머/감시자 정리
                try:
                    worker.stop()
                except Exception as e:
                    print(f"BackupWorker stop() 중 오류: {e}")
                del self.active_workers[job_id]
            self.stop_job_resources(job_id)
            # 활성 작업 목록에서 제거
            if hasattr(self, 'active_jobs_widget'):
                self.active_jobs_widget.remove_job_widget(job_id)
            # active_jobs 딕셔너리에서 제거
            if job_id in self.active_jobs:
                del self.active_jobs[job_id]
            # 작업 상태 저장
            self.save_active_jobs()
        except Exception as e:
            logging.error(f"작업 중지 중 오류 발생: {e}")

    def connect_worker_signals(self, worker):
        # 중복 연결 방지: 이미 연결된 경우 disconnect 후 connect
        try:
            worker.update_signal.disconnect(self.handle_worker_update)
        except Exception:
            pass
        worker.update_signal.connect(self.handle_worker_update)
        try:
            worker.batch_update_signal.disconnect(self.handle_batch_updates)
        except Exception:
            pass
        worker.batch_update_signal.connect(self.handle_batch_updates)
        # 기타 필요한 시그널도 동일하게 처리

    def get_backup_exclusions(self):
        return self.backup_exclusions.copy()

    def add_backup_exclusion(self, path, type_):
        self.backup_exclusions[path] = type_
        self.save_active_jobs()

    def remove_backup_exclusion(self, path):
        if path in self.backup_exclusions:
            del self.backup_exclusions[path]
            self.save_active_jobs()

class SystemResourceWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__(title="시스템 리소스 상태", parent=parent)
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_resource_status)
        self.setup_ui()
        self.start_monitoring()

    def setup_ui(self):
        # CPU 사용량
        self.cpu_label = QLabel("CPU 사용량:")
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setTextVisible(True)

        # 메모리 사용량
        self.memory_label = QLabel("메모리 사용량:")
        self.memory_progress = QProgressBar()
        self.memory_progress.setTextVisible(True)

        # 디스크 사용량
        self.disk_label = QLabel("디스크 사용량:")
        self.disk_progress = QProgressBar()
        self.disk_progress.setTextVisible(True)

        # 시스템 로드
        self.load_label = QLabel("시스템 로드:")
        
        # 전체 상태
        self.status_label = QLabel()
        self.status_label.setWordWrap(True)
        
        # 마지막 업데이트 시간
        self.last_update_label = QLabel()

        # 위젯 배치 - CardWidget의 layout 사용
        self.layout.addWidget(self.cpu_label)
        self.layout.addWidget(self.cpu_progress)
        self.layout.addWidget(self.memory_label)
        self.layout.addWidget(self.memory_progress)
        self.layout.addWidget(self.disk_label)
        self.layout.addWidget(self.disk_progress)
        if platform.system() != "Windows":  # Windows가 아닌 경우에만 시스템 로드 표시
            self.layout.addWidget(self.load_label)
        self.layout.addWidget(self.status_label)
        self.layout.addWidget(self.last_update_label)

        # 프로그레스 바 스타일 설정
        progress_style = """
            QProgressBar {
                border: 1px solid #CCCCCC;
                border-radius: 3px;
                text-align: center;
                height: 20px;
            }
            QProgressBar::chunk {
                border-radius: 2px;
            }
        """
        self.cpu_progress.setStyleSheet(progress_style)
        self.memory_progress.setStyleSheet(progress_style)
        self.disk_progress.setStyleSheet(progress_style)

    def start_monitoring(self):
        self.timer.start(2000)  # 2초마다 업데이트

    def stop_monitoring(self):
        self.timer.stop()

    def update_resource_status(self):
        try:
            # CPU 사용량
            cpu_percent = psutil.cpu_percent()
            self.cpu_progress.setValue(int(cpu_percent))
            self.cpu_label.setText(f"CPU 사용량: {cpu_percent:.1f}%")
            self.update_progress_color(self.cpu_progress, cpu_percent)

            # 메모리 사용량
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.memory_progress.setValue(int(memory_percent))
            available_gb = memory.available / (1024 ** 3)
            self.memory_label.setText(f"메모리 사용량: {memory_percent:.1f}% (가용: {available_gb:.1f}GB)")
            self.update_progress_color(self.memory_progress, memory_percent)

            # 디스크 사용량
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            self.disk_progress.setValue(int(disk_percent))
            free_gb = disk.free / (1024 ** 3)
            self.disk_label.setText(f"디스크 사용량: {disk_percent:.1f}% (여유: {free_gb:.1f}GB)")
            self.update_progress_color(self.disk_progress, disk_percent)

            # 시스템 로드 (Windows가 아닌 경우)
            if platform.system() != "Windows":
                load1, load5, load15 = psutil.getloadavg()
                self.load_label.setText(f"시스템 로드: 1분: {load1:.2f}, 5분: {load5:.2f}, 15분: {load15:.2f}")

            # 전체 상태 평가
            self.evaluate_system_status()

            # 마지막 업데이트 시간
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.last_update_label.setText(f"마지막 업데이트: {current_time}")

        except Exception as e:
            self.status_label.setText(f"리소스 상태 업데이트 중 오류 발생: {str(e)}")

    def update_progress_color(self, progress_bar, value):
        if value < 60:
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #CCCCCC;
                    border-radius: 3px;
                    text-align: center;
                }
                QProgressBar::chunk {
                    background-color: #4CAF50;
                    border-radius: 2px;
                }
            """)  # 녹색
        elif value < 80:
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #CCCCCC;
                    border-radius: 3px;
                    text-align: center;
                }
                QProgressBar::chunk {
                    background-color: #FFA500;
                    border-radius: 2px;
                }
            """)  # 주황색
        else:
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #CCCCCC;
                    border-radius: 3px;
                    text-align: center;
                }
                QProgressBar::chunk {
                    background-color: #F44336;
                    border-radius: 2px;
                }
            """)  # 빨간색

    def evaluate_system_status(self):
        warnings = []
        
        if psutil.cpu_percent() > 80:
            warnings.append("CPU 사용량이 높습니다")
        
        memory = psutil.virtual_memory()
        if memory.available < 2 * (1024 ** 3):  # 2GB 미만
            warnings.append("가용 메모리가 부족합니다")
        
        disk = psutil.disk_usage('/')
        if disk.free < 10 * (1024 ** 3):  # 10GB 미만
            warnings.append("디스크 여유 공간이 부족합니다")

        if warnings:
            status_text = "주의: " + ", ".join(warnings)
            self.status_label.setStyleSheet("color: #F44336;")  # 빨간색
        else:
            status_text = "시스템 상태가 정상입니다"
            self.status_label.setStyleSheet("color: #4CAF50;")  # 녹색
        
        self.status_label.setText(status_text)

    def closeEvent(self, event):
        self.stop_monitoring()
        super().closeEvent(event)

class MetadataMonitorWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_metrics)
        self.update_timer.start(5000)  # 5초마다 업데이트
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 탭 위젯 추가
        tab_widget = QTabWidget()
        
        # 메트릭 탭
        metrics_tab = QWidget()
        metrics_layout = QVBoxLayout(metrics_tab)
        
        # 메트릭 카드 프레임
        metrics_frame = QFrame()
        metrics_frame.setFrameStyle(QFrame.StyledPanel)
        metrics_grid = QGridLayout(metrics_frame)
        
        # 메트릭 카드 추가
        self.pending_events_card = self.create_metric_card("대기 이벤트", "0")
        self.processing_rate_card = self.create_metric_card("처리 속도", "0/s")
        self.avg_processing_card = self.create_metric_card("평균 처리 시간", "0ms")
        self.memory_usage_card = self.create_metric_card("메모리 사용량", "0MB")
        self.db_size_card = self.create_metric_card("DB 크기", "0MB")
        
        metrics_grid.addWidget(self.pending_events_card, 0, 0)
        metrics_grid.addWidget(self.processing_rate_card, 0, 1)
        metrics_grid.addWidget(self.avg_processing_card, 1, 0)
        metrics_grid.addWidget(self.memory_usage_card, 1, 1)
        metrics_grid.addWidget(self.db_size_card, 2, 0, 1, 2)
        
        metrics_layout.addWidget(metrics_frame)
        
        # 차트 영역
        charts_frame = QFrame()
        charts_frame.setFrameStyle(QFrame.StyledPanel)
        charts_layout = QHBoxLayout(charts_frame)
        
        self.processing_chart = self.create_chart("처리 속도")
        self.processing_time_chart = self.create_chart("평균 처리 시간")
        
        charts_layout.addWidget(self.processing_chart)
        charts_layout.addWidget(self.processing_time_chart)
        
        metrics_layout.addWidget(charts_frame)
        
        # 이벤트 테이블
        table_frame = QFrame()
        table_frame.setFrameStyle(QFrame.StyledPanel)
        table_layout = QVBoxLayout(table_frame)
        
        self.events_table = QTableWidget()
        self.events_table.setColumnCount(6)
        self.events_table.setHorizontalHeaderLabels([
            "ID", "경로", "이벤트 유형", "시간", "상태", "우선순위"
        ])
        
        # 필터 영역
        filter_layout = QHBoxLayout()
        
        self.event_type_filter = QComboBox()
        self.event_type_filter.addItems(["모든 유형", "created", "modified", "deleted", "moved"])
        self.event_type_filter.currentTextChanged.connect(self.update_events_table)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems(["모든 상태", "대기", "처리됨", "실패"])
        self.status_filter.currentTextChanged.connect(self.update_events_table)
        

class BackupExclusionWidget(QWidget):
    """백업 제외 파일/폴더를 관리하는 위젯"""
    exclusion_changed = pyqtSignal()  # 제외 목록이 변경되었을 때 발생하는 시그널

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_exclusions()

    def setup_ui(self):
        """UI 초기화"""
        layout = QVBoxLayout(self)
        
        # 설명 레이블
        description = QLabel("백업에서 제외할 파일, 폴더 또는 패턴을 관리합니다.\n"
                           "제외된 항목은 백업 대상에서 제외되며, 실시간으로 적용됩니다.\n"
                           "패턴은 와일드카드(*)를 사용할 수 있습니다. 예: *.tmp, temp/*")
        description.setWordWrap(True)
        layout.addWidget(description)

        # 패턴 입력 영역
        pattern_layout = QHBoxLayout()
        self.pattern_input = QLineEdit()
        self.pattern_input.setPlaceholderText("제외할 패턴 입력 (예: *.tmp, temp/*)")
        pattern_layout.addWidget(self.pattern_input)
        
        self.pattern_type_combo = QComboBox()
        self.pattern_type_combo.addItems(["파일", "폴더"])
        pattern_layout.addWidget(self.pattern_type_combo)
        
        add_pattern_btn = QPushButton("패턴 추가")
        add_pattern_btn.clicked.connect(self.add_pattern)
        pattern_layout.addWidget(add_pattern_btn)
        
        layout.addLayout(pattern_layout)

        # 구분선
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line)

        # 제외 목록 테이블
        self.exclusion_table = QTableWidget()
        self.exclusion_table.setColumnCount(3)
        self.exclusion_table.setHorizontalHeaderLabels(["경로/패턴", "유형", "작업"])
        self.exclusion_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.exclusion_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.exclusion_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.exclusion_table.setAlternatingRowColors(True)
        layout.addWidget(self.exclusion_table)

        # 버튼 레이아웃
        button_layout = QHBoxLayout()
        
        # 파일 추가 버튼
        add_file_btn = QPushButton("파일 추가")
        add_file_btn.clicked.connect(self.add_file)
        button_layout.addWidget(add_file_btn)
        
        # 폴더 추가 버튼
        add_folder_btn = QPushButton("폴더 추가")
        add_folder_btn.clicked.connect(self.add_folder)
        button_layout.addWidget(add_folder_btn)
        
        # 선택 항목 삭제 버튼
        remove_btn = QPushButton("선택 항목 삭제")
        remove_btn.clicked.connect(self.remove_selected)
        button_layout.addWidget(remove_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)

    def add_pattern(self):
        """사용자가 입력한 패턴 추가"""
        pattern = self.pattern_input.text().strip()
        if not pattern:
            QMessageBox.warning(self, "경고", "패턴을 입력해주세요.")
            return
            
        type_ = self.pattern_type_combo.currentText()
        self.add_exclusion(pattern, type_)
        self.pattern_input.clear()  # 입력 필드 초기화

    def load_exclusions(self):
        """저장된 제외 목록을 로드"""
        try:
            # ValidationApp 인스턴스에서 active_jobs 가져오기
            app = self.window()
            if isinstance(app, ValidationApp):
                exclusions = app.get_backup_exclusions()
                self.exclusion_table.setRowCount(0)  # 테이블 초기화
                
                for path, type_ in exclusions.items():
                    self.add_exclusion_to_table(path, type_)
        except Exception as e:
            print(f"제외 목록 로드 중 오류: {e}")

    def add_exclusion_to_table(self, path, type_):
        """테이블에 제외 항목 추가"""
        row = self.exclusion_table.rowCount()
        self.exclusion_table.insertRow(row)
        
        # 경로
        path_item = QTableWidgetItem(path)
        path_item.setFlags(path_item.flags() & ~Qt.ItemIsEditable)  # 읽기 전용
        self.exclusion_table.setItem(row, 0, path_item)
        
        # 유형
        type_item = QTableWidgetItem(type_)
        type_item.setFlags(type_item.flags() & ~Qt.ItemIsEditable)  # 읽기 전용
        self.exclusion_table.setItem(row, 1, type_item)
        
        # 삭제 버튼
        delete_btn = QPushButton("삭제")
        delete_btn.clicked.connect(lambda: self.remove_exclusion(row))
        self.exclusion_table.setCellWidget(row, 2, delete_btn)

    def add_file(self):
        """파일 추가 다이얼로그"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "제외할 파일 선택", "",
            "모든 파일 (*.*)"
        )
        if file_path:
            self.add_exclusion(file_path, "파일")

    def add_folder(self):
        """폴더 추가 다이얼로그"""
        folder_path = QFileDialog.getExistingDirectory(
            self, "제외할 폴더 선택", ""
        )
        if folder_path:
            self.add_exclusion(folder_path, "폴더")

    def add_exclusion(self, path, type_):
        """제외 목록에 항목 추가"""
        try:
            # ValidationApp 인스턴스에서 active_jobs 가져오기
            app = self.window()
            if isinstance(app, ValidationApp):
                # 이미 존재하는지 확인
                exclusions = app.get_backup_exclusions()
                if path in exclusions:
                    QMessageBox.warning(self, "경고", "이미 제외 목록에 있는 경로입니다.")
                    return
                
                # 제외 목록에 추가
                app.add_backup_exclusion(path, type_)
                
                # 테이블에 추가
                self.add_exclusion_to_table(path, type_)
                
                # 변경 시그널 발생
                self.exclusion_changed.emit()
        except Exception as e:
            QMessageBox.critical(self, "오류", f"제외 항목 추가 중 오류 발생: {e}")

    def remove_exclusion(self, row):
        """제외 목록에서 항목 제거"""
        try:
            path = self.exclusion_table.item(row, 0).text()
            
            # ValidationApp 인스턴스에서 active_jobs 가져오기
            app = self.window()
            if isinstance(app, ValidationApp):
                # 제외 목록에서 제거
                app.remove_backup_exclusion(path)
                
                # 테이블에서 제거
                self.exclusion_table.removeRow(row)
                
                # 변경 시그널 발생
                self.exclusion_changed.emit()
        except Exception as e:
            QMessageBox.critical(self, "오류", f"제외 항목 제거 중 오류 발생: {e}")

    def remove_selected(self):
        """선택된 항목들 제거"""
        selected_rows = set(item.row() for item in self.exclusion_table.selectedItems())
        if not selected_rows:
            QMessageBox.warning(self, "경고", "삭제할 항목을 선택해주세요.")
            return
        
        # 선택된 행들을 역순으로 제거 (인덱스가 변경되지 않도록)
        for row in sorted(selected_rows, reverse=True):
            self.remove_exclusion(row)

