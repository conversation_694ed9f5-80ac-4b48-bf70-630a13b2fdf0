from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QListWidget, QListWidgetItem, QInputDialog, QMessageBox,
                            QDialog, QLabel, QLineEdit, QFileDialog, QDialogButtonBox,
                            QSplitter, QFrame)
from PyQt5.QtCore import pyqtSignal, Qt
import json
import os
from backup_app.config.default_projects import DEFAULT_PROJECTS  # 기본 작품 목록

class AddProjectDialog(QDialog):
    """작품 추가를 위한 다이얼로그"""
    def __init__(self, parent=None, existing_projects=None, project_manager=None):
        super().__init__(parent)
        self.existing_projects = existing_projects or []
        self.project_manager = project_manager
        self.setWindowTitle("작품 관리")
        self.setMinimumWidth(600)
        self.setMinimumHeight(400)
        self.setup_ui()

    def setup_ui(self):
        main_layout = QVBoxLayout()
        
        # 스플리터 생성
        splitter = QSplitter(Qt.Horizontal)
        
        # 왼쪽 패널 (기존 작품 목록)
        left_panel = QFrame()
        left_layout = QVBoxLayout()
        left_panel.setLayout(left_layout)
        
        existing_label = QLabel("기존 작품 목록")
        left_layout.addWidget(existing_label)
        
        self.existing_list = QListWidget()
        self.existing_list.addItems(sorted(self.existing_projects))  # DEFAULT_PROJECTS 제거
        left_layout.addWidget(self.existing_list)
        
        delete_btn = QPushButton("선택 작품 삭제")
        delete_btn.clicked.connect(self.delete_selected_project)
        left_layout.addWidget(delete_btn)
        
        # 오른쪽 패널 (새 작품 추가)
        right_panel = QFrame()
        right_layout = QVBoxLayout()
        right_panel.setLayout(right_layout)
        
        right_layout.addWidget(QLabel("새 작품 추가"))
        
        # 작품명 입력
        name_layout = QHBoxLayout()
        name_label = QLabel("작품명:")
        self.name_edit = QLineEdit()
        name_layout.addWidget(name_label)
        name_layout.addWidget(self.name_edit)
        right_layout.addLayout(name_layout)

        # 경로 선택
        path_layout = QHBoxLayout()
        path_label = QLabel("경로:")
        self.path_edit = QLineEdit()
        self.path_button = QPushButton("찾아보기")
        self.path_button.clicked.connect(self.browse_path)
        path_layout.addWidget(path_label)
        path_layout.addWidget(self.path_edit)
        path_layout.addWidget(self.path_button)
        right_layout.addLayout(path_layout)
        
        # 추가 버튼
        add_btn = QPushButton("새 작품 추가")
        add_btn.clicked.connect(self.add_new_project)
        right_layout.addWidget(add_btn)
        
        right_layout.addStretch()
        
        # 스플리터에 패널 추가
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        
        main_layout.addWidget(splitter)

        # 확인/취소 버튼
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            parent=self)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        main_layout.addWidget(buttons)

        self.setLayout(main_layout)

    def browse_path(self):
        path = QFileDialog.getExistingDirectory(self, "작품 경로 선택")
        if path:
            self.path_edit.setText(path)

    def add_new_project(self):
        name = self.name_edit.text().strip()
        path = self.path_edit.text().strip()
        
        if not name:
            QMessageBox.warning(self, '경고', '작품명을 입력해주세요.')
            return
            
        if not path:
            QMessageBox.warning(self, '경고', '경로를 선택해주세요.')
            return
            
        if name in self.existing_projects:
            QMessageBox.warning(self, '경고', '이미 존재하는 작품 이름입니다.')
            return
            
        # 작품 목록에 추가
        self.existing_projects.append(name)
        self.existing_list.clear()
        self.existing_list.addItems(sorted(self.existing_projects))
        
        # ProjectManager에도 추가
        if self.project_manager:
            item = QListWidgetItem(name)
            item.setData(Qt.UserRole, path)
            self.project_manager.project_list.addItem(item)
            self.project_manager.save_projects()  # 변경사항 저장
        
        # 입력 필드 초기화
        self.name_edit.clear()
        self.path_edit.clear()

    def delete_selected_project(self):
        selected_items = self.existing_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, '경고', '삭제할 작품을 선택해주세요.')
            return
            
        selected_name = selected_items[0].text()
        reply = QMessageBox.question(self, '확인', 
                                   f'"{selected_name}" 작품을 삭제하시겠습니까?',
                                   QMessageBox.Yes | QMessageBox.No)
                                   
        if reply == QMessageBox.Yes:
            # 선택된 아이템의 행 번호를 찾아서 삭제
            row = self.existing_list.row(selected_items[0])
            self.existing_list.takeItem(row)
            
            # existing_projects 리스트에서도 제거
            if selected_name in self.existing_projects:
                self.existing_projects.remove(selected_name)
            
            # ProjectManager의 project_list에서도 제거하고 변경사항 저장
            if self.project_manager:
                self.project_manager.remove_project(selected_name)

    def get_data(self):
        """현재 작품 목록과 새로 추가된 작품 정보를 반환"""
        current_projects = []
        for i in range(self.existing_list.count()):
            current_projects.append(self.existing_list.item(i).text())
            
        return {
            'name': self.name_edit.text().strip(),
            'path': self.path_edit.text().strip(),
            'existing_projects': current_projects
        }

class ProjectManager(QWidget):
    """작품 선택 관리를 위한 위젯"""
    
    projectsChanged = pyqtSignal()  # 작품 목록이 변경될 때 발생하는 시그널

    def __init__(self, parent=None):
        super().__init__(parent)
        self.projects_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'projects.json')
        self.init_ui()
        self.load_projects()

    def init_ui(self):
        """UI 초기화"""
        layout = QVBoxLayout()
        
        # 작품 목록 위젯
        self.project_list = QListWidget()
        layout.addWidget(self.project_list)
        
        self.setLayout(layout)

    def load_projects(self):
        """저장된 작품 목록 불러오기"""
        try:
            if os.path.exists(self.projects_file):
                with open(self.projects_file, 'r', encoding='utf-8') as f:
                    projects = json.load(f)
                    for project in projects:
                        if isinstance(project, dict):
                            item = QListWidgetItem(project['name'])
                            item.setData(Qt.UserRole, project['path'])
                        else:
                            item = QListWidgetItem(project)
                        self.project_list.addItem(item)
        except Exception as e:
            QMessageBox.warning(self, '오류', f'작품 목록을 불러오는 중 오류가 발생했습니다: {str(e)}')

    def save_projects(self):
        """작품 목록 저장"""
        try:
            projects = []
            for i in range(self.project_list.count()):
                item = self.project_list.item(i)
                path = item.data(Qt.UserRole)
                if path:
                    projects.append({
                        'name': item.text(),
                        'path': path
                    })
                else:
                    projects.append(item.text())
            
            os.makedirs(os.path.dirname(self.projects_file), exist_ok=True)
            with open(self.projects_file, 'w', encoding='utf-8') as f:
                json.dump(projects, f, ensure_ascii=False, indent=2)
            
            # 작품 목록이 변경되었음을 알림
            self.projectsChanged.emit()
        except Exception as e:
            QMessageBox.warning(self, '오류', f'작품 목록을 저장하는 중 오류가 발생했습니다: {str(e)}')

    def add_project(self, project_name):
        """새 작품 추가"""
        if not project_name:
            return
            
        # 이미 존재하는지 확인
        for i in range(self.project_list.count()):
            if self.project_list.item(i).text() == project_name:
                return
                
        # 새 작품 추가
        item = QListWidgetItem(project_name)
        self.project_list.addItem(item)
        self.save_projects()
        self.projectsChanged.emit()

    def remove_project(self, project_name):
        """작품을 제거하고 변경사항을 저장"""
        # project_list에서 제거
        for i in range(self.project_list.count()):
            item = self.project_list.item(i)
            if item.text() == project_name:
                self.project_list.takeItem(i)
                break
        
        # 변경사항 저장 및 알림
        self.save_projects()

    def get_projects(self):
        """현재 프로젝트 목록을 반환합니다."""
        projects = []
        for i in range(self.project_list.count()):
            projects.append(self.project_list.item(i).text())
        return projects

    def get_project_path(self, project_name):
        """작품의 경로를 반환합니다."""
        # 기본 경로 설정
        default_paths = {
            "BB": "/Volumes/projects/BB",
            "BM": "/Volumes/projects/BM",
            "GN": "/Volumes/projects/GN",
            "KOTH": "/Volumes/projects/KOTH",
            "MatingSeason": "/Volumes/projects/MatingSeason",
            "Test": "/Volumes/projects/Test",
            "WB": "/Volumes/projects/WB",
            "Yeson_Test": "/Volumes/projects/Yeson_Test",
            "Yeson_Test_4K": "/Volumes/projects/Yeson_Test_4K",
            "Curious_George": "/Volumes/projects/Curious_George"
        }
        
        # 프로젝트 리스트에서 경로 찾기
        for i in range(self.project_list.count()):
            item = self.project_list.item(i)
            if item.text() == project_name:
                path = item.data(Qt.UserRole)
                if path:  # 사용자가 설정한 경로가 있으면 그 경로 사용
                    return path
        
        # 기본 경로 반환
        return default_paths.get(project_name)

    def load_from_settings(self, projects):
        """설정 파일에서 프로젝트 목록을 불러옵니다."""
        try:
            # 기존 프로젝트 목록 초기화
            self.project_list.clear()
            
            # 새로운 프로젝트 목록 추가
            for project in projects:
                item = QListWidgetItem(project)
                # 기존에 저장된 경로가 있다면 그 경로를 사용
                if os.path.exists(self.projects_file):
                    try:
                        with open(self.projects_file, 'r', encoding='utf-8') as f:
                            saved_projects = json.load(f)
                            for saved_project in saved_projects:
                                if isinstance(saved_project, dict) and saved_project['name'] == project:
                                    item.setData(Qt.UserRole, saved_project['path'])
                                    break
                    except:
                        pass
                self.project_list.addItem(item)
            
            # 변경 알림
            self.projectsChanged.emit()
            
            print(f"프로젝트 목록 불러오기 완료: {self.project_list.count()}개")
            
            # 프로젝트 경로 정보 출력
            for i in range(self.project_list.count()):
                item = self.project_list.item(i)
                path = item.data(Qt.UserRole)
                print(f"프로젝트: {item.text()}, 경로: {path}")
                
            return True
        except Exception as e:
            print(f"프로젝트 목록 불러오기 실패: {str(e)}")
            return False 