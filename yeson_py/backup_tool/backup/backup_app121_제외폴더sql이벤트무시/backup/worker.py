import os
import sys
import time
import threading
import queue
import shutil
import psutil
import subprocess
import traceback
import gc
import logging
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QMetaObject, Q_ARG, pyqtSlot, QTimer
from PyQt5.QtWidgets import QApplication
from watchdog.observers import Observer
import platform
import select
from ..utils.rsync_config import get_rsync_command, get_rsync_options_string
from ..utils.logger import BackupLogger
from ..utils.memory_manager import MemoryManager
from ..utils.admin_auth import AdminAuthManager
from ..utils.metadata_db import MetadataDB  # MetadataDB import 추가
from watchdog.events import FileSystemEventHandler, FileSystemEvent  # FileSystemEvent 추가

# 전역 rsync 프로세스 카운터를 모듈 레벨에서 초기화
GLOBAL_RSYNC_COUNTER = None

class GlobalRsyncCounter:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(GlobalRsyncCounter, cls).__new__(cls)
                cls._instance.count = 0
                cls._instance.count_lock = threading.Lock()
            return cls._instance
    
    def increment(self):
        with self.count_lock:
            self.count += 1
            return self.count
            
    def decrement(self):
        with self.count_lock:
            self.count = max(0, self.count - 1)
            return self.count
            
    def get_count(self):
        with self.count_lock:
            return self.count
            
    def reset(self):
        with self.count_lock:
            self.count = 0

# 전역 카운터 초기화
GLOBAL_RSYNC_COUNTER = GlobalRsyncCounter()

class BackupWorker(QThread):
    update_signal = pyqtSignal(str, str, int)
    scene_backup_status_signal = pyqtSignal(str, str, str)
    restart_observer_signal = pyqtSignal(str, str)
    job_finished_signal = pyqtSignal(str)
    batch_update_signal = pyqtSignal(list)  # 메시지 배치 전송용
    schedule_timer_signal = pyqtSignal()
    cancel_timer_signal = pyqtSignal()
    events_updated_signal = pyqtSignal()  # ★ 추가: DB 갱신 시 UI 갱신용 시그널

    # 로깅 최적화를 위한 상수 추가
    LOG_INTERVAL = 1000  # 로그 업데이트 최소 간격 (밀리초)
    MEMORY_CHECK_INTERVAL = 30000  # 메모리 체크 간격 (30초)
    MEMORY_THRESHOLD_MB = 1000  # 메모리 정리 임계값 (1GB)
    MAX_CONCURRENT_THREADS = 3  # 동시 실행 스레드 수 제한
    MIN_CONCURRENT_THREADS = 2  # 최소 스레드 수
    MAX_ALLOWED_THREADS = 3  # 최대 허용 스레드 수
    BATCH_SIZE = 3  # 한 번에 큐에서 가져올 작업 수
    MAX_TOTAL_RSYNC = 10  # 시스템 전체 rsync 프로세스 최대 개수
    QUEUE_SIZE_LIMIT = 10  # 작업 큐 최대 크기
    THREAD_WAIT_TIME = 0.2  # 스레드 대기 시간 (초)
    QUEUE_PROCESS_DELAY = 0.1  # 큐 처리 간 지연 시간 (초)
    TASK_PROCESS_DELAY = 0.05  # 작업 처리 간 지연 시간 (초)
    MAX_QUEUE_SIZE = 30  # 최대 큐 크기

    # 시스템 리소스 관련 상수 추가
    MIN_MEMORY_AVAILABLE_MB = 1000  # 최소 필요 가용 메모리 (1GB로 증가)
    MAX_CPU_USAGE = 90  # 최대 허용 CPU 사용률 (UI와 일치)
    MIN_DISK_SPACE_GB = 5  # 최소 필요 디스크 공간 (5GB)
    RESOURCE_CHECK_INTERVAL = 30  # 리소스 체크 주기 (30초로 감소)
    SCALE_UP_THRESHOLD = 60  # CPU 사용률이 이 값보다 낮을 때 스케일 업 (60%)
    SCALE_DOWN_THRESHOLD = 70  # CPU 사용률이 이 값보다 높을 때 스케일 다운 (70%)
    SCALE_STEP = 1  # 한 번에 조정할 스레드 수
    CPU_CHECK_INTERVAL = 5  # CPU 체크 간격 (초)

    def __init__(self, job_id, backup_queue, source_handler, dest_handler, scene_tree_ref, source_path, dest_path, max_workers=4, periodic_sync_interval=120, backup_mode="mirror", log_queue=None):
        super().__init__()
        self.job_id = job_id
        self.running = True
        self.backup_queue = queue.Queue(maxsize=self.QUEUE_SIZE_LIMIT)
        self.source_handler = source_handler
        self.dest_handler = dest_handler
        self.source_handler.set_backup_mode(backup_mode)
        self.dest_handler.set_backup_mode(backup_mode)
        self.scene_tree_ref = scene_tree_ref
        self.source_path = source_path
        self.dest_path = dest_path
        self.backed_up_scenes = set()
        self.retry_limit = 3
        self.executor = None
        self.active_futures = set()
        self.task_lock = threading.Lock()
        self._reset_task_counters()
        self.last_progress_emit_time = 0
        self.last_log_time = 0
        self.active_threads_count = 0
        self.threads_lock = threading.Lock()
        
        # 로거 초기화
        self.logger = BackupLogger()
        
        # 메타데이터 DB 초기화 추가
        self.metadata_db = MetadataDB()
        
        # 전역 rsync 프로세스 카운터 사용
        self.rsync_counter = GLOBAL_RSYNC_COUNTER

        # 주기적 동기화 관련 속성
        self._periodic_sync_interval = int(periodic_sync_interval)
        self.periodic_sync_timer = None
        self.is_periodic_sync_running = False
        self.periodic_sync_lock = threading.Lock()
        self.interval_change_lock = threading.Lock()

        # 백업 모드 설정
        self._backup_mode = backup_mode
        
        # 로그 레벨 설정
        self.log_level = 2  # 기본 로그 레벨
        
        # 종료 플래그 추가
        self.is_shutting_down = False

        # 메모리 모니터링 타이머 추가
        self.memory_monitor_timer = None
        self.last_memory_check = 0
        self.memory_check_count = 0

        # 시스템 리소스 모니터링 관련 속성 추가
        self.last_resource_check = 0
        self.resource_warning_count = 0
        self.initial_resource_check_done = False

        # 감시자 초기화 (하지만 아직 시작하지 않음)
        self.source_observer = None
        self.dest_observer = None

        # 초기 상태 메시지 출력
        QMetaObject.invokeMethod(self, "_emit_initial_status",
                               Qt.QueuedConnection)

        self.current_rsync_process = None  # 현재 실행 중인 rsync 프로세스 추적

        # 타이머 관련 시그널 연결
        self.schedule_timer_signal.connect(self._schedule_periodic_sync_slot, Qt.QueuedConnection)
        self.cancel_timer_signal.connect(self._safe_cancel_timer_slot, Qt.QueuedConnection)

        self._interval_lock = threading.Lock()
        self.log_queue = log_queue  # 추가: UI 로그 큐

        self.admin_auth = AdminAuthManager()

        # 추가: 동기화 검사 관련 속성
        self.sync_check_interval = 300  # 5분마다 동기화 검사
        self.last_sync_check = 0
        self.sync_check_timer = None
        self.sync_check_lock = threading.Lock()
        
        # 동기화 검사 타이머 시작
        self._start_sync_check_timer()

        # 성능 메트릭 수집 관련 속성 추가
        self.metrics_timer = None
        self.metrics_interval = 60  # 1분마다 메트릭 수집
        self.last_metrics_update = 0
        self.batch_id = None
        self.batch_start_time = None
        self.batch_events = 0
        self.batch_processed = 0
        self.batch_errors = 0

        # 성능 메트릭 타이머 시작
        self._start_metrics_timer()

    @property
    def backup_mode(self):
        return self._backup_mode

    @backup_mode.setter
    def backup_mode(self, mode):
        # 허용된 값만 적용
        allowed_modes = {"mirror", "incremental"}
        if mode not in allowed_modes:
            self.emit_progress(f"[경고] 잘못된 백업 모드 값 무시: {mode}", -1)
            return
        self._backup_mode = mode
        if hasattr(self, 'source_handler'):
            self.source_handler.set_backup_mode(mode)
        if hasattr(self, 'dest_handler'):
            self.dest_handler.set_backup_mode(mode)
        self.terminate_all_rsync_processes()
        self.cancel_all_sync_timers_and_tasks()
        self.emit_progress(f"백업 모드가 변경됨: {mode}", -1)
        self._schedule_periodic_sync()
        import threading
        threading.Thread(target=self._perform_periodic_sync, daemon=True).start()

    def emit_progress(self, message, progress):
        """진행 상황을 UI에 전달하고 로그에 기록합니다."""
        should_update, formatted_message, progress = self.logger.log_progress(self.job_id, message, progress)
        if should_update:
            if isinstance(formatted_message, list):
                self.batch_update_signal.emit(formatted_message)
            else:
                self.update_signal.emit(self.job_id, formatted_message, progress)
        # UI 로그 큐에도 전달
        if self.log_queue:
            try:
                queue_size = self.log_queue.qsize()
                if queue_size > 900:  # 큐가 거의 찼을 때 경고
                    print(f"Warning: Log queue is almost full when emitting progress ({queue_size}/1000)")
                self.log_queue.put((self.job_id, message))
                if queue_size > 900:  # 큐가 거의 찼을 때 경고
                    print(f"After emit: Log queue size is {self.log_queue.qsize()}/1000")
            except Exception as e:
                print(f"Error putting message in log queue: {e}")
                import traceback
                print(f"Log queue error traceback:\n{traceback.format_exc()}")

    def log_debug(self, message):
        """디버그 메시지를 로깅합니다."""
        self.logger.debug(f"[{self.job_id}] {message}")
        if self.log_queue:
            try:
                self.log_queue.put((self.job_id, message))
            except Exception:
                pass

    def log_info(self, message):
        """정보 메시지를 로깅합니다."""
        self.logger.info(f"[{self.job_id}] {message}")
        if self.log_queue:
            try:
                self.log_queue.put((self.job_id, message))
            except Exception:
                pass

    def log_warning(self, message):
        """경고 메시지를 로깅합니다."""
        self.logger.warning(f"[{self.job_id}] {message}")
        if self.log_queue:
            try:
                self.log_queue.put((self.job_id, message))
            except Exception:
                pass

    def log_error(self, message):
        """에러 메시지를 로깅합니다."""
        self.logger.error(f"[{self.job_id}] {message}")
        if self.log_queue:
            try:
                self.log_queue.put((self.job_id, message))
            except Exception:
                pass

    def log_to_file(self, message):
        """로그 파일에만 기록하는 메서드"""
        try:
            log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
            os.makedirs(log_dir, exist_ok=True)
            log_file = os.path.join(log_dir, f'backup_{time.strftime("%Y%m%d")}.log')
            timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] {message}\n")
        except Exception:
            pass  # 로그 파일 기록 실패는 무시

    def _reset_task_counters(self):
        """작업 카운터를 초기화합니다."""
        with self.task_lock:
            self.total_queued_tasks = 0
            self.completed_tasks = 0

    def _process_task(self, task_data):
        """작업을 처리하고 결과를 반환합니다."""
        import time
        import traceback

        event_id = None
        task_start_time = time.time()
        try:
            # task_data 형식 검증
            if not isinstance(task_data, (list, tuple)) or len(task_data) < 6:
                self.logger.error(f"잘못된 task_data 형식: {task_data}")
                return False

            event_id = task_data[5]
            if not event_id:
                self.logger.error("이벤트 ID가 없습니다")
                return False

            # 작업 시작 시 상태를 '처리 중'으로 업데이트
            if not self.metadata_db.update_event_status(event_id, "처리 중", task_start_time):
                self.logger.error(f"이벤트 상태를 '처리 중'으로 업데이트 실패 (ID: {event_id})")
                # 실패한 상태 업데이트를 error_logs에 기록
                try:
                    with self.metadata_db._get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute("""
                            INSERT INTO error_logs 
                            (event_id, error_type, error_message, timestamp, retry_count)
                            VALUES (?, ?, ?, ?, ?)
                        """, (event_id, 'status_update_failed', '처리 중 상태 업데이트 실패', time.time(), 0))
                        conn.commit()
                except Exception as e:
                    self.logger.error(f"오류 로그 기록 실패: {str(e)}")
                return False

            # 작업 유형에 따른 처리
            task_type = task_data[0]
            success = False
            error_message = None

            try:
                if task_type == "backup":
                    success = self._process_backup_task(task_data)
                elif task_type == "delete":
                    success = self._process_delete_task(task_data)
                else:
                    error_message = f"알 수 없는 작업 유형: {task_type}"
                    self.logger.error(error_message)
                    success = False
            except Exception as task_error:
                error_message = f"작업 처리 중 예외 발생: {str(task_error)}"
                self.logger.error(error_message)
                self.logger.error(f"상세 오류: {traceback.format_exc()}")
                success = False

            # 작업 결과에 따른 상태 업데이트
            status = "처리됨" if success else "실패"
            update_success = False

            # 상태 업데이트 시도 (최대 3번 재시도)
            for retry in range(3):
                if self.metadata_db.update_event_status(event_id, status, time.time()):
                    update_success = True
                    break
                time.sleep(0.5 * (2 ** retry))  # 지수 백오프

            # ★★★ 핵심: 처리 성공 시 processed=1로 업데이트 ★★★
            if status == "처리됨" and update_success:
                try:
                    with self.metadata_db._get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute("UPDATE change_events SET processed=1 WHERE id=?", (event_id,))
                        conn.commit()
                    self.logger.info(f"[처리됨] processed=1로 업데이트 완료: {event_id}")
                    self.events_updated_signal.emit()  # ★ UI 갱신 시그널 emit
                except Exception as e:
                    self.logger.error(f"processed=1 업데이트 실패 (ID: {event_id}): {str(e)}")

            if not update_success:
                error_msg = f"이벤트 상태 업데이트 실패 (ID: {event_id}, 상태: {status})"
                self.logger.error(error_msg)
                # 실패한 상태 업데이트를 error_logs에 기록
                try:
                    with self.metadata_db._get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute("""
                            INSERT INTO error_logs 
                            (event_id, error_type, error_message, timestamp, retry_count)
                            VALUES (?, ?, ?, ?, ?)
                        """, (event_id, 'status_update_failed', error_msg, time.time(), 3))
                        conn.commit()
                    self.events_updated_signal.emit()  # ★ 실패 시에도 emit
                except Exception as e:
                    self.logger.error(f"오류 로그 기록 실패: {str(e)}")

            # 작업 처리 시간 기록
            processing_time = time.time() - task_start_time
            try:
                with self.metadata_db._get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        UPDATE change_events 
                        SET processing_time = ?
                        WHERE id = ?
                    """, (processing_time, event_id))
                    conn.commit()
            except Exception as e:
                self.logger.error(f"처리 시간 업데이트 실패 (ID: {event_id}): {str(e)}")

            # 작업 처리 전 배치 이벤트 카운트 증가
            if self.batch_id is not None:
                self.batch_events += 1
                self.log_debug(f"배치 이벤트 증가: {self.batch_events}")

            return success

        except Exception as e:
            error_message = f"작업 처리 중 예외 발생: {str(e)}"
            self.logger.error(error_message)
            self.logger.error(f"상세 오류: {traceback.format_exc()}")

            # 예외 발생 시에도 이벤트 상태를 '실패'로 업데이트 시도
            if event_id:
                try:
                    for retry in range(3):
                        if self.metadata_db.update_event_status(event_id, "실패", time.time()):
                            break
                        time.sleep(0.5 * (2 ** retry))
                except Exception as update_error:
                    self.logger.error(f"예외 발생 후 상태 업데이트 실패 (ID: {event_id}): {str(update_error)}")
                    # 실패한 상태 업데이트를 error_logs에 기록
                    try:
                        with self.metadata_db._get_connection() as conn:
                            cursor = conn.cursor()
                            cursor.execute("""
                                INSERT INTO error_logs 
                                (event_id, error_type, error_message, timestamp, retry_count)
                                VALUES (?, ?, ?, ?, ?)
                            """, (event_id, 'status_update_failed', str(update_error), time.time(), 3))
                            conn.commit()
                    except Exception as e:
                        self.logger.error(f"오류 로그 기록 실패: {str(e)}")
                    self.events_updated_signal.emit()  # ★ 실패 시에도 emit

            return False



    def process_rsync_output(self, process, cmd):
        """rsync 프로세스의 출력을 처리하고 결과를 반환합니다."""
        try:
            stdout, stderr = process.communicate()
            if process.returncode == 0:
                return True, "동기화 성공"
            
            # 권한 관련 에러 처리
            if "Permission denied" in stderr:
                # 에러 메시지에서 파일 경로 추출
                import re
                error_files = []
                for line in stderr.split('\n'):
                    if "Permission denied" in line:
                        match = re.search(r'open "([^"]+)" failed', line)
                        if match:
                            error_files.append(match.group(1))
                
                if error_files:
                    error_msg = "다음 파일들에 대한 권한 에러가 발생했습니다:\n"
                    for file_path in error_files:
                        error_msg += f"- {file_path}\n"
                    error_msg += "\n이 파일들에 대해 권한을 임시로 변경하여 재시도합니다."
                    self.emit_progress(error_msg, -1)
                    return False, ("permission_denied", error_files)
            
            # 기타 rsync 에러 처리
            error_msg = f"rsync 실행 실패 (코드: {process.returncode})"
            if stderr:
                error_msg += f"\n{stderr}"
            self.emit_progress(error_msg, -1)
            return False, error_msg
            
        except Exception as e:
            error_msg = f"rsync 출력 처리 중 오류: {str(e)}"
            self.emit_progress(error_msg, -1)
            return False, error_msg

    @pyqtSlot()
    def start_progressive_watching(self):
        try:
            self.watched_dirs = set()
            self.dest_watched_dirs = set()
            self.watch_queue = queue.Queue()
            
            # 소스 루트 감시
            self.emit_progress("실시간 감시 초기화 중...", -1)
            self._add_watch(self.source_path)
            # 목적지 루트 감시
            if hasattr(self, 'dest_observer') and self.dest_observer:
                self._add_dest_watch(self.dest_path)
            
            # 소스 하위 디렉토리 감시
            dirs_to_watch = []
            with os.scandir(self.source_path) as it:
                for entry in it:
                    if entry.is_dir():
                        dirs_to_watch.append(entry.path)
            if dirs_to_watch:
                self.emit_progress(f"총 {len(dirs_to_watch)}개 디렉토리 감시 예정", -1)
                batch_size = 3
                with ThreadPoolExecutor(max_workers=3) as executor:
                    futures = []
                    for i in range(0, len(dirs_to_watch), batch_size):
                        batch = dirs_to_watch[i:i + batch_size]
                        futures.extend([executor.submit(self._add_watch, path) for path in batch])
                        progress = min(95, int((i + batch_size) / len(dirs_to_watch) * 100))
                        self.emit_progress(f"디렉토리 감시 초기화 중... ({i + len(batch)}/{len(dirs_to_watch)})", progress)
                        time.sleep(0.05)
            # 목적지 하위 디렉토리 감시
            dest_dirs_to_watch = []
            with os.scandir(self.dest_path) as it:
                for entry in it:
                    if entry.is_dir():
                        dest_dirs_to_watch.append(entry.path)
            if dest_dirs_to_watch:
                batch_size = 3
                with ThreadPoolExecutor(max_workers=3) as executor:
                    for i in range(0, len(dest_dirs_to_watch), batch_size):
                        batch = dest_dirs_to_watch[i:i + batch_size]
                        for path in batch:
                            self._add_dest_watch(path)
                        time.sleep(0.05)
            self.emit_progress("실시간 감시 초기화 완료", 100)
        except Exception as e:
            self.emit_progress(f"감시 초기화 중 오류 발생: {e}", -1)
            import traceback
            self.emit_progress(f"상세 오류: {traceback.format_exc()}", -1)

    def _add_watch(self, path):
        """개별 디렉토리에 대한 감시 추가"""
        try:
            if path not in self.watched_dirs:
                # source_observer가 None이면 재초기화
                if not self.source_observer:
                    self.source_observer = Observer()
                    self.source_observer.start()
                    self.emit_progress("소스 감시자 재초기화됨", -1)
                
                self.source_observer.schedule(self.source_handler, path, recursive=False)
                self.watched_dirs.add(path)
                dir_name = os.path.basename(path) or path
                self.emit_progress(f"디렉토리 감시 시작: {dir_name}", -1)
                
                # 하위 디렉토리도 감시 대상에 추가
                try:
                    with os.scandir(path) as it:
                        for entry in it:
                            if entry.is_dir():
                                self.watch_queue.put(entry.path)
                except Exception as e:
                    self.emit_progress(f"하위 디렉토리 스캔 중 오류 ({dir_name}): {e}", -1)
        except Exception as e:
            self.emit_progress(f"디렉토리 감시 추가 중 오류 ({path}): {e}", -1)

    def _add_dest_watch(self, path):
        try:
            if not hasattr(self, 'dest_watched_dirs'):
                self.dest_watched_dirs = set()
            if path not in self.dest_watched_dirs:
                self.dest_observer.schedule(self.dest_handler, path, recursive=False)
                self.dest_watched_dirs.add(path)
                dir_name = os.path.basename(path) or path
                self.emit_progress(f"대상 디렉토리 감시 시작: {dir_name}", -1)
                try:
                    with os.scandir(path) as it:
                        for entry in it:
                            if entry.is_dir():
                                self.watch_queue.put(entry.path)
                except Exception as e:
                    self.emit_progress(f"대상 하위 디렉토리 스캔 중 오류 ({dir_name}): {e}", -1)
        except Exception as e:
            self.emit_progress(f"대상 디렉토리 감시 추가 중 오류 ({path}): {e}", -1)

    @pyqtSlot()
    def _emit_initial_status(self):
        """초기 상태 메시지를 출력합니다."""
        self.emit_progress("=== 백업 작업 시작 ===", 0)
        if self.periodic_sync_interval <= 0:
            self.emit_progress("동기화가 비활성화된 상태입니다. 백업 작업을 진행하지 않습니다.", -1)
            self.job_finished_signal.emit(self.job_id)
        else:
            self.emit_progress(f"소스 경로: {self.source_path}", -1)
            self.emit_progress(f"대상 경로: {self.dest_path}", -1)
            self.emit_progress(f"백업 모드: {'미러링' if self.backup_mode == 'mirror' else '증분'}", -1)
            self.emit_progress(f"주기적 동기화: {self.periodic_sync_interval}초 간격", -1)

    def run(self):
        """메인 워커 루프 최적화"""
        try:
            # 목적지 폴더가 없으면 자동 생성
            if not os.path.exists(self.dest_path):
                try:
                    os.makedirs(self.dest_path, exist_ok=True)
                    self.emit_progress(f"[복구] 목적지 최상위 폴더가 없어서 자동 생성: {self.dest_path}", -1)
                except Exception as e:
                    self.emit_progress(f"[복구] 목적지 폴더 생성 실패: {e}", -1)
                    self.job_finished_signal.emit(self.job_id)
                    return

            # 동기화 무설정(0) 체크를 가장 먼저 수행
            if self.periodic_sync_interval <= 0:
                return

            # 초기 리소스 체크
            initial_status = self.check_system_resources()
            if initial_status and not initial_status['is_healthy']:
                self.emit_progress("!!! 경고: 시스템 리소스가 부족한 상태입니다 !!!", -1)
                for warning in initial_status['warnings']:
                    self.emit_progress(f"- {warning}", -1)
                self.emit_progress("시스템 리소스가 충분해질 때까지 잠시 대기합니다...", -1)
                time.sleep(5)  # 5초 대기 후 다시 시도
                
                # 재시도
                initial_status = self.check_system_resources()
                if not initial_status['is_healthy']:
                    self.emit_progress("시스템 리소스가 여전히 부족합니다. 백업을 시작할 수 없습니다.", -1)
                    self.job_finished_signal.emit(self.job_id)
                    return
                
            # 리소스 체크가 성공한 경우에만 백업 시작
            self.emit_progress("시스템 리소스 확인 완료. 백업을 시작합니다.", -1)
            
            self.executor = ThreadPoolExecutor(max_workers=self.MAX_CONCURRENT_THREADS)
            self.start_memory_monitoring()
            
            # 감시자 초기화 및 시작
            if not self.initialize_watchers():
                self.emit_progress("감시자 초기화 실패로 백업을 시작할 수 없습니다.", -1)
                self.job_finished_signal.emit(self.job_id)
                return
            
            # 주기적 동기화 타이머 등록 (직접 호출)
            self._schedule_periodic_sync()

            self.current_rsync_process = None  # 워커 시작 시 초기화
        except Exception as e:
            self.emit_progress(f"초기화 실패: {e}", -1)
            self.job_finished_signal.emit(self.job_id)
            self.current_rsync_process = None
            return

        # 초기 동기화 수행
        self.emit_progress("초기 동기화를 시작합니다...", 5)
        try:
            success = self.run_segmented_sync()
            if not success:
                self.emit_progress("초기 동기화가 실패했습니다.", -1)
                self.job_finished_signal.emit(self.job_id)
                return
            self.emit_progress("초기 동기화가 완료되었습니다.", 100)
        except Exception as e:
            self.emit_progress(f"초기 동기화 중 오류 발생: {e}", -1)
            self.job_finished_signal.emit(self.job_id)
            self.current_rsync_process = None
            return

        self.emit_progress("실시간 감시를 시작합니다...", -1)
        last_status_time = time.time()
        status_interval = 60  # 60초마다 상태 메시지 출력

        while self.running:
            try:
                current_time = time.time()
                
                # 주기적으로 상태 메시지 출력
                if current_time - last_status_time >= status_interval:
                    queue_size = self.backup_queue.qsize()
                    active_count = self.active_threads_count
                    self.emit_progress(
                        f"실시간 감시 중... (대기 작업: {queue_size}, 활성 스레드: {active_count}/{self.MAX_CONCURRENT_THREADS})",
                        -1
                    )
                    last_status_time = current_time

                # 활성 스레드 수 확인
                with self.threads_lock:
                    if self.active_threads_count >= self.MAX_CONCURRENT_THREADS:
                        time.sleep(self.THREAD_WAIT_TIME)
                        continue

                    available_slots = self.MAX_CONCURRENT_THREADS - self.active_threads_count
                    tasks_to_process = min(available_slots, self.BATCH_SIZE)

                # 작업 수집
                tasks_to_submit = []
                for _ in range(tasks_to_process):
                    try:
                        task = self.backup_queue.get_nowait()
                        tasks_to_submit.append(task)
                    except queue.Empty:
                        break

                if not tasks_to_submit:
                    time.sleep(self.THREAD_WAIT_TIME)
                    continue

                # 작업 제출
                with self.task_lock:
                    for task in tasks_to_submit:
                        if not self.running:
                            break
                        if self.executor and not self.executor._shutdown:
                            with self.threads_lock:
                                self.active_threads_count += 1
                            future = self.executor.submit(self._process_task, task)
                            future.add_done_callback(self._task_completed_callback)
                            self.active_futures.add(future)

                # 메모리 관리
                if self.total_queued_tasks % self.BATCH_SIZE == 0:
                    self.perform_memory_cleanup()

            except Exception as e:
                self.emit_progress(f"작업 처리 중 오류: {e}", -1)
                if not self.running:
                    break

        self.emit_progress("백업 작업을 종료합니다...", -1)
        self.stop_memory_monitoring()
        self.job_cleanup()
        self.emit_progress("백업 작업이 종료되었습니다.", 100)
        self.job_finished_signal.emit(self.job_id)

        # 워커 종료 시점에 rsync 프로세스가 남아있으면 종료
        if self.current_rsync_process is not None:
            try:
                self.current_rsync_process.terminate()
                self.current_rsync_process.wait(timeout=5)
            except Exception:
                pass
            self.current_rsync_process = None

    def _task_completed_callback(self, future):
        """작업 완료 콜백 - 스레드 카운트 관리 추가"""
        with self.threads_lock:
            self.active_threads_count = max(0, self.active_threads_count - 1)
        self._task_callback(future)

    def _task_callback(self, future):
        # Existing method unchanged
        if not self.running: return

        try: src_path, dest_path, event_type, success, task_key_for_handler = future.result()
        except Exception as e: print(f"!!! Worker Error: Exception retrieving future result: {e}"); success = False; event_type = "unknown_error"; task_key_for_handler = None

        with self.task_lock:
            self.completed_tasks += 1
            self.active_futures.discard(future)

            current_time = time.time()
            is_last_task = self.backup_queue.empty() and not self.active_futures
            should_update_progress = (current_time - self.last_progress_emit_time > 0.5) or is_last_task

            if should_update_progress:
                remaining_tasks = self.backup_queue.qsize() + len(self.active_futures)
                estimated_total = max(1, self.completed_tasks + remaining_tasks)  # 0으로 나누기 방지
                progress = min(99, int(self.completed_tasks / estimated_total * 100))
                final_progress = 100 if is_last_task else progress
                status_msg = "모든 대기 작업 처리 완료" if is_last_task and progress >= 99 else f"실시간 처리 중... (완료: {self.completed_tasks}, 대기: {remaining_tasks})"
                # print(f" -> Progress Update: {status_msg} ({final_progress}%)")
                self.emit_progress(status_msg, final_progress)
                self.last_progress_emit_time = current_time

            if task_key_for_handler:
                 handler_notified = False
                 try:
                     task_path_or_flag = task_key_for_handler[0]
                     task_type = task_key_for_handler[1]

                     if event_type in ["created", "modified"] and self.source_handler and isinstance(task_path_or_flag, str) and task_path_or_flag.startswith(self.source_path):
                          self.source_handler.task_completed(task_key_for_handler); handler_notified = True
                     elif src_path == "delete_target" and self.dest_handler:
                          if task_path_or_flag == dest_path and task_type == 'deleted':
                              self.dest_handler.task_completed(task_key_for_handler); handler_notified = True
                     elif event_type == "restore" and self.dest_handler:
                          if isinstance(task_path_or_flag, str) and task_path_or_flag.startswith(self.source_path) and task_type == 'restore':
                              self.dest_handler.task_completed(task_key_for_handler); handler_notified = True
                     elif event_type == "recreate_dir" and self.dest_handler:
                          if isinstance(task_path_or_flag, str) and task_path_or_flag.startswith(self.source_path) and task_type == 'recreate_root':
                              self.dest_handler.task_completed(task_key_for_handler); handler_notified = True

                 except Exception as e: print(f"!!! Worker Error: Exception while notifying handler for key {task_key_for_handler}: {e}")
                 # if not handler_notified: print(f" -> Warning: No handler notified for key: {task_key_for_handler}, Event: {event_type}")

            if success and event_type != "deleted" and src_path != "delete_target":
                path_to_check = src_path if event_type != "restore" else dest_path
                if path_to_check:
                    scene_name = self.extract_scene_name(path_to_check)
                    if scene_name and scene_name not in self.backed_up_scenes:
                        if self.is_scene_fully_backed_up(scene_name):
                            self.backed_up_scenes.add(scene_name)
                            self.scene_backup_status_signal.emit(self.job_id, scene_name, "백업완료")
                            # print(f" -> Scene '{scene_name}' marked as backed up for Job {self.job_id}")

        # print(f"--- Task Callback Finished ---")

    def _safe_cancel_timer(self):
        try:
            if self.periodic_sync_timer:
                self.periodic_sync_timer.cancel()
                return True
        except Exception as e:
            print(f"Error canceling timer: {e}")
        return False

    @pyqtSlot()
    def _perform_initial_sync(self):
        """초기 동기화를 수행하는 메서드"""
        try:
            self.emit_progress("초기 동기화를 시작합니다...", 5)
            
            # ThreadPoolExecutor를 사용하여 비동기로 실행
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(self.run_segmented_sync)
                success = future.result()
                
            if success:
                self.emit_progress("초기 동기화가 완료되었습니다.", 100)
            else:
                self.emit_progress("초기 동기화가 실패했습니다.", -1)
                
            self.current_rsync_process = None  # 초기 동기화 완료 후 초기화
        except Exception as e:
            self.emit_progress(f"초기 동기화 중 오류 발생: {e}", -1)
            self.current_rsync_process = None

    @pyqtSlot(int)
    def _async_restart_backup(self, new_interval):
        """비동기로 백업을 재시작하는 메서드 (UI 프리징 방지, 무거운 작업 워커 스레드에서 직접 실행)"""
        def restart_logic():
            try:
                self._safe_cancel_timer()
                if hasattr(self, 'source_observer') and self.source_observer:
                    try:
                        self.source_observer.stop()
                        self.source_observer.join(timeout=1)
                    except Exception as e:
                        self.emit_progress(f"source_observer 정리 중 오류: {e}", -1)
                    self.source_observer = None
                if self.executor:
                    try:
                        self.executor.shutdown(wait=True)
                    except Exception as e:
                        self.emit_progress(f"executor 종료 중 오류: {e}", -1)
                    self.executor = None
                self.running = True
                self.executor = ThreadPoolExecutor(max_workers=self.MAX_CONCURRENT_THREADS)
                self.start_memory_monitoring()
                # 감시자 재시작
                self.source_observer = Observer()
                self.source_observer.start()
                # 점진적 감시 및 초기 동기화, 타이머 재설정 모두 워커 스레드에서 직접 실행
                self.start_progressive_watching()
                self._perform_initial_sync()
                self._reschedule_sync_timer_safely()
            except Exception as e:
                self.emit_progress(f"백업 재시작 중 오류 발생: {e}", -1)
        threading.Thread(target=restart_logic, daemon=True).start()

    def update_sync_interval(self, new_interval):
        """동기화 간격 업데이트"""
        try:
            new_interval = int(new_interval)
            if new_interval < 0:
                self.emit_progress("동기화 간격은 0 이상이어야 합니다.", -1)
                return False

            if new_interval > 86400:
                self.emit_progress("동기화 간격이 24시간을 초과할 수 없습니다.", -1)
                return False

            with self.interval_change_lock:
                if self.periodic_sync_interval == new_interval:
                    return True

                old_interval = self.periodic_sync_interval
                was_disabled = old_interval <= 0

                # 값 설정
                self.periodic_sync_interval = new_interval
                old_text = self.format_time_text(old_interval)
                new_text = self.format_time_text(new_interval)

                if new_interval == 0:
                    self.emit_progress("=== 동기화 비활성화 시작 ===", -1)
                    self.running = False
                    self.cancel_timer_signal.emit()  # 메인 스레드에서 타이머 취소
                    self.emit_progress("동기화가 비활성화되었습니다.", -1)
                else:
                    if was_disabled:
                        self.emit_progress(f"동기화가 활성화되었습니다. 백업 작업을 시작합니다... (간격: {new_text})", -1)
                        self.running = True
                        QMetaObject.invokeMethod(self, "_async_restart_backup",
                                            Qt.QueuedConnection,
                                            Q_ARG(int, new_interval))
                    else:
                        self.emit_progress(f"동기화 간격을 {old_text}에서 {new_text}로 변경합니다...", -1)
                        self.schedule_timer_signal.emit()  # 메인 스레드에서 타이머 재설정
                        self.emit_progress(f"주기적 동기화 간격이 {old_text}에서 {new_text}로 변경되었습니다.", -1)

                return True

        except Exception as e:
            self.emit_progress(f"동기화 간격 변경 중 오류: {str(e)}", -1)
            return False

    @pyqtSlot()
    def _reschedule_sync_timer(self):
        try:
            # 기존 코드를 신규 안전 메서드 호출로 대체
            self._reschedule_sync_timer_safely()
            
            # 나머지 로직은 그대로 유지
            interval_text = ""
            sync_seconds = int(self.periodic_sync_interval)
            if sync_seconds < 60:
                interval_text = f"{sync_seconds}초"
            elif sync_seconds < 3600:
                minutes = sync_seconds // 60
                interval_text = f"{minutes}분"
            else:
                hours = sync_seconds // 3600
                interval_text = f"{hours}시간"
                
            self.emit_progress(f"동기화 일정이 재설정되었습니다. 다음 동기화까지 {interval_text}", -1)
        except Exception as e:
            self.emit_progress(f"동기화 일정 재설정 중 오류: {e}", -1)

    # periodic_sync_interval 프로퍼티 추가 (getter/setter)
    @property
    def periodic_sync_interval(self):
        with self._interval_lock:
            return int(self._periodic_sync_interval)

    @periodic_sync_interval.setter
    def periodic_sync_interval(self, value):
        with self._interval_lock:
            try:
                parsed_value = int(float(value))
                if parsed_value <= 0:
                    parsed_value = 0
                elif parsed_value > 86400 * 10:
                    parsed_value = 86400
                self._periodic_sync_interval = parsed_value
                self.emit_progress(f"동기화 간격 설정됨: 입력값={value}, 저장값={self._periodic_sync_interval}", -1)
            except (ValueError, TypeError):
                self._periodic_sync_interval = 0
                self.emit_progress(f"잘못된 동기화 간격 값({value}), 무설정(0초)으로 설정", -1)

    @pyqtSlot()
    def _schedule_periodic_sync_slot(self):
        """메인 스레드에서 실행되는 타이머 스케줄링 슬롯"""
        try:
            if not self.running:
                return

            if self.periodic_sync_interval <= 0:
                self.emit_progress("[디버그] 주기적 동기화 비활성화됨", -1)
                return

            sync_seconds = min(int(self.periodic_sync_interval), 86400)

            # 기존 타이머 정리
            if self.periodic_sync_timer:
                try:
                    self.periodic_sync_timer.cancel()
                except:
                    pass
                self.periodic_sync_timer = None

            # 새 타이머 설정
            self.periodic_sync_timer = threading.Timer(sync_seconds, self._trigger_periodic_sync)
            self.periodic_sync_timer.daemon = True
            self.periodic_sync_timer.start()

            time_text = self.format_time_text(sync_seconds)
            self.emit_progress(f"[디버그] scheduleperiodic_sync 타이머 시작, 다음 동기화 {time_text} 후", -1)

        except Exception as e:
            self.emit_progress(f"타이머 스케줄링 중 오류: {str(e)}", -1)

    @pyqtSlot()
    def _safe_cancel_timer_slot(self):
        """메인 스레드에서 실행되는 타이머 취소 슬롯"""
        try:
            if self.periodic_sync_timer:
                self.periodic_sync_timer.cancel()
                self.periodic_sync_timer = None
                self.emit_progress("타이머가 취소되었습니다.", -1)
        except Exception as e:
            self.emit_progress(f"타이머 취소 중 오류: {str(e)}", -1)

    def format_time_text(self, seconds):
        if seconds <= 0:
            return "무설정"
        elif seconds >= 86400:
            days = seconds // 86400
            hours = (seconds % 86400) // 3600
            if hours > 0:
                return f"{days}일 {hours}시간"
            else:
                return f"{days}일"
        elif seconds >= 3600:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            if minutes > 0:
                return f"{hours}시간 {minutes}분"
            else:
                return f"{hours}시간"
        elif seconds >= 60:
            minutes = seconds // 60
            seconds_remain = seconds % 60
            if seconds_remain > 0:
                return f"{minutes}분 {seconds_remain}초"
            else:
                return f"{minutes}분"
        else:
            return f"{seconds}초"

    def _trigger_periodic_sync(self):
        """주기적 동기화를 트리거합니다."""
        self.emit_progress(f"주기적 동기화 트리거 (running={self.running})", -1)
        if not self.running:
            with self.periodic_sync_lock:
                self.is_periodic_sync_running = False
            return

        with self.periodic_sync_lock:
            if self.is_periodic_sync_running:
                self.emit_progress("이미 동기화가 실행 중입니다", -1)
                return
            self.is_periodic_sync_running = True

        try:
            self.emit_progress("주기적 동기화를 시작합니다...", -1)
            self._perform_periodic_sync()
        except Exception as e:
            self.emit_progress(f"주기적 동기화 중 오류 발생: {e}", -1)
            import traceback
            self.emit_progress(f"상세 오류: {traceback.format_exc()}", -1)
        finally:
            with self.periodic_sync_lock:
                self.is_periodic_sync_running = False
            self.emit_progress("주기적 동기화 완료", -1)
            if self.running:
                self._schedule_periodic_sync()

    def _reschedule_sync_timer_safely(self):
        """타이머를 안전하게 재설정하는 헬퍼 메서드"""
        try:
            self._safe_cancel_timer()

            if not self.running or self.periodic_sync_interval <= 0:
                return

            # 1. 명시적으로 값을 정수로 변환하여 로컬 변수에 저장
            sync_seconds = int(self.periodic_sync_interval)
            
            # 2. 타이머 생성 및 시작
            self.periodic_sync_timer = threading.Timer(sync_seconds, self._trigger_periodic_sync)
            self.periodic_sync_timer.daemon = True
            self.periodic_sync_timer.start()
            
            # 3. 직접 시간 형식 변환 (중간 변수 없이)
            time_text = ""
            if sync_seconds < 60:
                time_text = f"{sync_seconds}초"
            elif sync_seconds < 3600:
                minutes = sync_seconds // 60
                time_text = f"{minutes}분"
            elif sync_seconds < 86400:
                hours = sync_seconds // 3600
                minutes = (sync_seconds % 3600) // 60
                if minutes > 0:
                    time_text = f"{hours}시간 {minutes}분"
                else:
                    time_text = f"{hours}시간"
            else:
                days = sync_seconds // 86400
                hours = (sync_seconds % 86400) // 3600
                if hours > 0:
                    time_text = f"{days}일 {hours}시간"
                else:
                    time_text = f"{days}일"
            
            # 4. 문자열만 사용하는 명확한 로그 메시지
            debug_message = f"[디버그] _schedule_periodic_sync 타이머 시작, 다음 동기화 {time_text} 후"
            self.emit_progress(debug_message, -1)
        except Exception as e:
            self.emit_progress(f"타이머 재설정 중 오류: {e}", -1)

    def _schedule_periodic_sync(self):
        """주기적 동기화 타이머를 설정하는 메서드"""
        try:
            with self._interval_lock:
                interval = self._periodic_sync_interval
            if not self.running:
                return
            if interval <= 0:
                self.emit_progress("[디버그] _schedule_periodic_sync 타이머 시작, 다음 동기화: 무설정", -1)
                return
            sync_seconds = int(interval)
            # 기존 타이머 정리
            if self.periodic_sync_timer:
                try:
                    self.periodic_sync_timer.cancel()
                except:
                    pass
                self.periodic_sync_timer = None
            # 새 타이머 설정
            self.periodic_sync_timer = threading.Timer(sync_seconds, self._trigger_periodic_sync)
            self.periodic_sync_timer.daemon = True
            self.periodic_sync_timer.start()
            time_text = self.format_time_text(sync_seconds)
            self.emit_progress(f"[디버그] _schedule_periodic_sync 타이머 시작, 다음 동기화 {time_text} 후 (저장값: {interval})", -1)
        except Exception as e:
            self.emit_progress(f"타이머 스케줄링 중 오류: {str(e)}", -1)

    def _perform_periodic_sync(self):
        """주기적 동기화를 수행합니다."""
        if not self.running:
            with self.periodic_sync_lock:
                self.is_periodic_sync_running = False
            return

        # 목적지 최상위 폴더 보장
        ensure_dest_root_exists(self.dest_path)
        # rsync_config.py의 설정을 사용하여 명령어 생성
        cmd = get_rsync_command(self.source_path, self.dest_path, self.backup_mode == "mirror", job_id=self.job_id)
        
        # 명령어 실행
        command_str = ' '.join(cmd)
        mode_text = "미러링" if self.backup_mode == "mirror" else "인크리멘탈"
        self.emit_progress(f"{mode_text} 동기화 시작...", -1)
        self.emit_progress(f"소스 경로: {self.source_path}", -1)
        self.emit_progress(f"대상 경로: {self.dest_path}", -1)
        self.emit_progress(f"실행 명령: {command_str}", -1)

        success = False
        processed_files = 0
        error_output = []

        max_retries = 3
        retry_delay = 5  # 5초
        for retry in range(max_retries):
            try:
                if not self.running:
                    with self.periodic_sync_lock:
                        self.is_periodic_sync_running = False
                    self.log_debug("동기화 중단 (running=False)")
                    return

                # rsync 명령 구성
                rsync_cmd = get_rsync_command(
                    self.source_path,
                    self.dest_path,
                    is_mirror=(self.backup_mode == "mirror")
                )

                mode_text = "미러링" if self.backup_mode == "mirror" else "인크리멘탈"
                self.emit_progress(f"{mode_text} 동기화 시작...", -1)
                self.emit_progress(f"소스 경로: {self.source_path}", -1)
                self.emit_progress(f"대상 경로: {self.dest_path}", -1)
                self.emit_progress(f"실행 명령: {' '.join(rsync_cmd)}", -1)

                my_env = os.environ.copy()
                my_env["LANG"] = "C.UTF-8"
                my_env["LC_ALL"] = "C.UTF-8"

                process = subprocess.Popen(
                    rsync_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1,
                    universal_newlines=True,
                    env=my_env,
                    errors='ignore',
                    preexec_fn=os.setsid  # 프로세스 그룹 생성
                )

                stdout, stderr = process.communicate()
                
                if process.returncode == 0:
                    self.emit_progress(f"{mode_text} 동기화 완료", 100)
                    return  # 성공시 종료
                elif process.returncode == 20:  # rsync was killed
                    self.emit_progress("동기화가 중단되었습니다.", -1)
                    return
                else:
                    error_msg = f"rsync 종료 코드: {process.returncode}"
                    if stderr:
                        error_msg += f"\n{stderr}"
                    raise Exception(error_msg)

            except Exception as e:
                if retry < max_retries - 1:
                    self.emit_progress(f"동기화 중 오류, 재시도 중 ({retry + 1}/{max_retries}): {e}", -1)
                    time.sleep(retry_delay)
                else:
                    self.emit_progress(f"동기화 실패: {e}", -1)
                    return

        self.log_debug("동기화 작업 종료")

    def sync_directory_timestamp(self, source_dir, dest_dir):
        """소스 디렉토리의 타임스탬프를 대상 디렉토리에 복사합니다."""
        try:
            # 소스 디렉토리의 타임스탬프 가져오기
            source_stat = os.stat(source_dir)
            source_atime = source_stat.st_atime
            source_mtime = source_stat.st_mtime

            # 대상 디렉토리에 타임스탬프 적용
            os.utime(dest_dir, (source_atime, source_mtime))
        except Exception as e:
            self.emit_progress(f"디렉토리 타임스탬프 복사 실패: {os.path.basename(source_dir)} - {e}", -1)

    def perform_memory_cleanup(self):
        """메모리 정리 작업 수행"""
        import gc
        gc.collect()

        # 활성 스레드 수 로깅
        with self.threads_lock:
            current_threads = self.active_threads_count
        
        # 작업 큐 관리
        if hasattr(self, 'backup_queue') and self.backup_queue:
            try:
                queue_size = self.backup_queue.qsize()
                if queue_size > self.QUEUE_SIZE_LIMIT:
                    self.emit_progress(f"메모리 관리: 큐 크기 제한 중 ({queue_size}개 → {self.QUEUE_SIZE_LIMIT}개)", -1)
                    items_to_remove = queue_size - self.QUEUE_SIZE_LIMIT
                    for _ in range(items_to_remove):
                        try:
                            self.backup_queue.get_nowait()
                        except queue.Empty:
                            break
            except Exception as e:
                self.emit_progress(f"큐 정리 중 오류: {e}", -1)

        # 메모리 사용량 확인
        process = psutil.Process()
        memory_mb = process.memory_info().rss / (1024 * 1024)
        self.emit_progress(f"시스템 상태 - 메모리: {memory_mb:.1f}MB, 활성 스레드: {current_threads}", -1)

    def perform_thorough_memory_cleanup(self):
        """더 철저한 메모리 정리를 수행합니다."""
        # 기존 메모리 정리 수행
        self.perform_memory_cleanup()

        # 모든 완료된 future 객체 정리
        if hasattr(self, 'active_futures'):
            completed_futures = [f for f in self.active_futures if f.done()]
            for future in completed_futures:
                try:
                    self.active_futures.discard(future)
                    del future
                except:
                    pass

        # 백업된 씬 세트 초기화
        if hasattr(self, 'backed_up_scenes'):
            self.backed_up_scenes.clear()

        # 로그 버퍼 초기화
        if hasattr(self, 'log_buffer'):
            self.log_buffer.clear()

        # 강제 가비지 컬렉션 수행
        import gc
        gc.collect(generation=2)  # 모든 세대의 가비지 컬렉션 수행
        gc.collect(generation=1)
        gc.collect(generation=0)

        # 메모리 상태 확인 및 로깅
        import psutil
        process = psutil.Process()
        memory_before = process.memory_info().rss / (1024 * 1024)
        
        # 추가 메모리 해제 시도
        import ctypes
        try:
            ctypes.CDLL('libc.so.6').malloc_trim(0)
        except:
            try:
                # macOS나 다른 시스템에서의 대체 방법
                import sys
                if sys.platform == 'darwin':
                    import subprocess
                    subprocess.run(['purge'], capture_output=True)
            except:
                pass

        # 메모리 정리 결과 확인
        memory_after = process.memory_info().rss / (1024 * 1024)
        self.emit_progress(f"메모리 정리 완료: {memory_before:.1f}MB → {memory_after:.1f}MB", -1)

    def start_memory_monitoring(self):
        """메모리 모니터링 타이머 시작"""
        if not self.memory_monitor_timer:
            self.memory_monitor_timer = QTimer()
            self.memory_monitor_timer.timeout.connect(self.check_memory_usage)
            self.memory_monitor_timer.start(self.MEMORY_CHECK_INTERVAL)
            self.emit_progress("메모리 모니터링 시작됨", -1)

    def stop_memory_monitoring(self):
        """메모리 모니터링 타이머 중지"""
        if self.memory_monitor_timer:
            self.memory_monitor_timer.stop()
            self.memory_monitor_timer = None

    def check_memory_usage(self):
        """메모리 사용량 체크 및 자동 정리"""
        if not self.running:
            return

        try:
            current_time = time.time()
            if current_time - self.last_memory_check < (self.MEMORY_CHECK_INTERVAL / 1000):
                return

            self.last_memory_check = current_time
            self.memory_check_count += 1

            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024

            # 메모리 사용량이 임계값을 초과하면 정리 수행
            if memory_mb > self.MEMORY_THRESHOLD_MB:
                self.emit_progress(f"메모리 사용량 임계값 초과 ({memory_mb:.1f}MB > {self.MEMORY_THRESHOLD_MB}MB), 정리 시작", -1)
                self.perform_thorough_memory_cleanup()
            
            # 10번째 체크마다 가벼운 정리 수행 (약 5분마다)
            elif self.memory_check_count % 10 == 0:
                self.emit_progress(f"주기적 메모리 정리 수행 중 (현재 사용량: {memory_mb:.1f}MB)", -1)
                self.perform_memory_cleanup()

        except Exception as e:
            self.emit_progress(f"메모리 체크 중 오류: {e}", -1)

    def check_system_resources(self):
        """시스템 리소스 상태를 확인하고 결과를 반환합니다."""
        try:
            # CPU 사용률 확인 (더 정확한 측정을 위해 평균값 사용)
            cpu_percent = 0
            samples = 3
            for _ in range(samples):
                cpu_percent += psutil.cpu_percent(interval=0.5)
                time.sleep(0.1)
            cpu_percent /= samples

            # 메모리 사용량 확인
            memory = psutil.virtual_memory()
            available_memory_mb = memory.available / (1024 * 1024)

            # 디스크 공간 확인
            disk_usage = shutil.disk_usage(self.dest_path)
            free_space_gb = disk_usage.free / (1024 * 1024 * 1024)

            # 시스템 부하 확인 (Unix 시스템의 경우)
            load_avg = psutil.getloadavg()[0] if platform.system() != 'Windows' else 0

            status = {
                'cpu_usage': cpu_percent,
                'memory_available': available_memory_mb,
                'disk_space': free_space_gb,
                'load_average': load_avg,
                'is_healthy': True,
                'warnings': []
            }

            # CPU 사용률이 임계값을 초과하는 경우
            if cpu_percent > self.MAX_CPU_USAGE:
                status['warnings'].append(f"CPU 사용률이 높습니다 ({cpu_percent:.1f}% > {self.MAX_CPU_USAGE}%)")
                status['is_healthy'] = False
                self._reduce_cpu_load()

            # 메모리가 부족한 경우
            from ..utils.memory_manager import MemoryManager
            if MemoryManager().is_system_memory_low():
                status['warnings'].append(f"가용 메모리가 부족합니다 ({available_memory_mb:.0f}MB < 1000MB)")
                status['is_healthy'] = False

            # 디스크 공간이 부족한 경우
            if free_space_gb < self.MIN_DISK_SPACE_GB:
                status['warnings'].append(f"디스크 공간이 부족합니다 ({free_space_gb:.1f}GB < {self.MIN_DISK_SPACE_GB}GB)")
                status['is_healthy'] = False

            # 시스템 부하가 높은 경우 (Unix 시스템만)
            if platform.system() != 'Windows' and load_avg > psutil.cpu_count():
                status['warnings'].append(f"시스템 부하가 높습니다 (Load Avg: {load_avg:.1f})")
                status['is_healthy'] = False

            return status

        except Exception as e:
            self.emit_progress(f"시스템 리소스 확인 중 오류: {str(e)}", -1)
            return {
                'cpu_usage': 0,
                'memory_available': 0,
                'disk_space': 0,
                'load_average': 0,
                'is_healthy': False,
                'warnings': ['리소스 확인 실패']
            }

    def _reduce_cpu_load(self):
        """CPU 사용률이 높을 때 작업량을 줄입니다."""
        try:
            # 현재 실행 중인 rsync 프로세스 수 확인
            current_rsync_count = self.rsync_counter.get_count()
            
            # CPU 사용률이 높으면 동시 실행 프로세스 수를 줄임
            if current_rsync_count > 1:
                new_limit = max(1, current_rsync_count - self.SCALE_STEP)
                self.emit_progress(f"CPU 부하 감소를 위해 동시 실행 프로세스 수를 {new_limit}개로 조정합니다.", -1)
                
                # 처리 중인 작업 일시 중지
                time.sleep(2)  # 현재 작업들이 진행되도록 잠시 대기
                
        except Exception as e:
            self.emit_progress(f"CPU 부하 감소 처리 중 오류: {str(e)}", -1)

    def log_resource_status(self, status):
        """리소스 상태를 로그에 기록합니다."""
        self.emit_progress("=== 시스템 리소스 상태 ===", -1)
        self.emit_progress(f"CPU 사용률: {status['cpu_usage']:.1f}%", -1)
        self.emit_progress(f"가용 메모리: {status['memory_available']:.1f}MB", -1)
        self.emit_progress(f"여유 디스크 공간: {status['disk_space']:.1f}GB", -1)
        if platform.system() != 'Windows':
            self.emit_progress(f"시스템 부하(1분): {status['load_average']:.2f}", -1)
            
        if status['warnings']:
            for warning in status['warnings']:
                self.emit_progress(f"경고: {warning}", -1)
                
    def adjust_worker_count(self, status):
        """시스템 상태에 따라 작업자 수를 점진적으로 조정합니다."""
        current_workers = self.MAX_CONCURRENT_THREADS
        cpu_usage = status['cpu_usage']
        memory_available = status['memory_available']
        
        # 리소스 부족 상태 체크
        is_cpu_high = cpu_usage > self.SCALE_DOWN_THRESHOLD
        is_memory_low = memory_available < self.MIN_MEMORY_AVAILABLE_MB
        is_resource_warning = not status['is_healthy']
        
        if is_cpu_high or is_memory_low or is_resource_warning:
            # 리소스 부족 시 점진적으로 감소
            if current_workers > self.MIN_CONCURRENT_THREADS:
                new_worker_count = max(
                    self.MIN_CONCURRENT_THREADS,
                    current_workers - self.SCALE_STEP
                )
                if new_worker_count != current_workers:
                    self.MAX_CONCURRENT_THREADS = new_worker_count
                    self.emit_progress(
                        f"리소스 부족으로 작업자 수 감소: {current_workers}개 → {new_worker_count}개 "
                        f"(CPU: {cpu_usage:.1f}%, 메모리: {memory_available:.0f}MB)",
                        -1
                    )
        elif cpu_usage < self.SCALE_UP_THRESHOLD and memory_available > self.MIN_MEMORY_AVAILABLE_MB * 1.5:
            # 리소스 여유 시 점진적으로 증가
            if current_workers < self.MAX_ALLOWED_THREADS:
                new_worker_count = min(
                    self.MAX_ALLOWED_THREADS,
                    current_workers + self.SCALE_STEP
                )
                if new_worker_count != current_workers:
                    self.MAX_CONCURRENT_THREADS = new_worker_count
                    self.emit_progress(
                        f"리소스 여유로 작업자 수 증가: {current_workers}개 → {new_worker_count}개 "
                        f"(CPU: {cpu_usage:.1f}%, 메모리: {memory_available:.0f}MB)",
                        -1
                    )
                
    def _copy_file_with_retry(self, src_path, dest_path, retry_count=0):
        """파일을 복사하는 메서드 (재시도 로직 포함)"""
        if retry_count >= self.retry_limit:
            self.emit_progress(f"파일 복사 최대 재시도 횟수 초과: {os.path.basename(src_path)}", -1)
            return False

        try:
            # rsync_config.py의 설정을 사용하여 명령어 생성
            rsync_cmd = get_rsync_command(src_path, dest_path, job_id=self.job_id)
            
            with RsyncProcessManager(self) as rsync:
                if rsync is None:  # 프로세스 수 초과
                    if retry_count < self.retry_limit:
                        time.sleep(1)
                        return self._copy_file_with_retry(src_path, dest_path, retry_count + 1)
                    return False
                    
                process = rsync.run_rsync(
                    rsync_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                success, result = self.process_rsync_output(process, rsync_cmd)
                
                # 권한 에러가 발생한 경우에만 권한 변경 후 재시도
                if not success and isinstance(result, tuple) and result[0] == "permission_denied":
                    error_files = result[1]
                    for file_path in error_files:
                        try:
                            # 원본 파일의 권한 저장
                            original_mode = os.stat(file_path).st_mode
                            
                            # 관리자 권한으로 권한 변경
                            success, msg = self.admin_auth.execute_with_sudo(['chmod', '777', file_path])
                            if not success:
                                self.emit_progress(f"권한 변경 실패 ({file_path}): {msg}", -1)
                                continue
                                
                            self.emit_progress(f"파일 권한 변경: {os.path.basename(file_path)}", -1)
                            
                            # rsync 재시도
                            process = rsync.run_rsync(
                                rsync_cmd,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                text=True
                            )
                            success, _ = self.process_rsync_output(process, rsync_cmd)
                            
                            # 원래 권한으로 복구
                            success, msg = self.admin_auth.execute_with_sudo(['chmod', str(oct(original_mode)[-3:]), file_path])
                            if not success:
                                self.emit_progress(f"권한 복구 실패 ({file_path}): {msg}", -1)
                            else:
                                self.emit_progress(f"파일 권한 복구: {os.path.basename(file_path)}", -1)
                                
                        except Exception as e:
                            self.emit_progress(f"권한 변경 처리 중 오류: {e}", -1)
                            if original_mode is not None:
                                try:
                                    self.admin_auth.execute_with_sudo(['chmod', str(oct(original_mode)[-3:]), file_path])
                                except:
                                    pass
                
                if not success and retry_count < self.retry_limit:
                    self.emit_progress(f"파일 복사 재시도 중 ({retry_count + 1}/{self.retry_limit}): {os.path.basename(src_path)}", -1)
                    time.sleep(1)
                    return self._copy_file_with_retry(src_path, dest_path, retry_count + 1)
                
                return success
                
        except Exception as e:
            self.emit_progress(f"파일 복사 중 오류 발생: {e}", -1)
            if retry_count < self.retry_limit:
                time.sleep(1)
                return self._copy_file_with_retry(src_path, dest_path, retry_count + 1)
            return False

    def _copy_directory_with_retry(self, src_path, dest_path, retry_count=0):
        """디렉토리를 복사하는 메서드 (재시도 로직 포함)"""
        if retry_count >= self.retry_limit:
            self.emit_progress(f"디렉토리 복사 최대 재시도 횟수 초과: {os.path.basename(src_path)}", -1)
            return False

        try:
            # 목적지 최상위 폴더 보장
            ensure_dest_root_exists(dest_path)
            # rsync_config.py의 설정을 사용하여 명령어 생성
            rsync_cmd = get_rsync_command(f"{src_path}/", dest_path, is_mirror=False, job_id=self.job_id)
            
            with RsyncProcessManager(self) as rsync:
                if rsync is None:  # 프로세스 수 초과
                    if retry_count < self.retry_limit:
                        time.sleep(1)
                        return self._copy_directory_with_retry(src_path, dest_path, retry_count + 1)
                    return False
                    
                process = rsync.run_rsync(
                    rsync_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                success, result = self.process_rsync_output(process, rsync_cmd)
                
                # 권한 에러가 발생한 경우에만 권한 변경 후 재시도
                if not success and isinstance(result, tuple) and result[0] == "permission_denied":
                    error_files = result[1]
                    for file_path in error_files:
                        try:
                            # 원본 디렉토리의 권한 저장
                            original_mode = os.stat(file_path).st_mode
                            
                            # 관리자 권한으로 권한 변경
                            success, msg = self.admin_auth.execute_with_sudo(['chmod', '777', file_path])
                            if not success:
                                self.emit_progress(f"권한 변경 실패 ({file_path}): {msg}", -1)
                                continue
                                
                            self.emit_progress(f"디렉토리 권한 변경: {os.path.basename(file_path)}", -1)
                            
                            # rsync 재시도
                            process = rsync.run_rsync(
                                rsync_cmd,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                text=True
                            )
                            success, _ = self.process_rsync_output(process, rsync_cmd)
                            
                            # 원래 권한으로 복구
                            success, msg = self.admin_auth.execute_with_sudo(['chmod', str(oct(original_mode)[-3:]), file_path])
                            if not success:
                                self.emit_progress(f"권한 복구 실패 ({file_path}): {msg}", -1)
                            else:
                                self.emit_progress(f"디렉토리 권한 복구: {os.path.basename(file_path)}", -1)
                                
                        except Exception as e:
                            self.emit_progress(f"권한 변경 처리 중 오류: {e}", -1)
                            if original_mode is not None:
                                try:
                                    self.admin_auth.execute_with_sudo(['chmod', str(oct(original_mode)[-3:]), file_path])
                                except:
                                    pass
                
                if not success and retry_count < self.retry_limit:
                    self.emit_progress(f"디렉토리 복사 재시도 중 ({retry_count + 1}/{self.retry_limit}): {os.path.basename(src_path)}", -1)
                    time.sleep(1)
                    return self._copy_directory_with_retry(src_path, dest_path, retry_count + 1)
                
                return success
                
        except Exception as e:
            self.emit_progress(f"디렉토리 복사 중 오류 발생: {e}", -1)
            if retry_count < self.retry_limit:
                time.sleep(1)
                return self._copy_directory_with_retry(src_path, dest_path, retry_count + 1)
            return False

    def job_cleanup(self):
        """작업 정리 메서드 개선 (스레드/감시자/타이머 안전 정리)"""
        # 명시적으로 모든 스레드 정리
        if hasattr(self, 'executor') and self.executor:
            try:
                self.executor.shutdown(wait=True)
            except Exception as e:
                self.emit_progress(f"스레드 풀 종료 중 오류: {e}", -1)
            self.executor = None
        # 소스 감시자 정리
        if hasattr(self, 'source_observer') and self.source_observer:
            try:
                self.source_observer.stop()
                self.source_observer.join(3)
            except Exception as e:
                self.emit_progress(f"소스 감시자 정리 중 오류: {e}", -1)
            self.source_observer = None
        # 대상 감시자 정리
        if hasattr(self, 'dest_observer') and self.dest_observer:
            try:
                self.dest_observer.stop()
                self.dest_observer.join(3)
            except Exception as e:
                self.emit_progress(f"대상 감시자 정리 중 오류: {e}", -1)
            self.dest_observer = None
        # 타이머 취소
        if hasattr(self, 'periodic_sync_timer') and self.periodic_sync_timer:
            try:
                self.periodic_sync_timer.cancel()
            except Exception as e:
                self.emit_progress(f"타이머 정리 중 오류: {e}", -1)
            self.periodic_sync_timer = None
        # 백업 큐 비우기
        if hasattr(self, 'backup_queue') and self.backup_queue:
            try:
                while not self.backup_queue.empty():
                    try:
                        self.backup_queue.get_nowait()
                    except queue.Empty:
                        break
                self.emit_progress("작업 대기열 정리 완료", -1)
            except Exception as e:
                self.emit_progress(f"작업 대기열 정리 중 오류: {e}", -1)
        # 활성 작업(futures) 정리
        if hasattr(self, 'active_futures'):
            try:
                for future in list(self.active_futures):
                    future.cancel()
                self.active_futures.clear()
                self.emit_progress("활성 작업 정리 완료", -1)
            except Exception as e:
                self.emit_progress(f"활성 작업 정리 중 오류: {e}", -1)
        
        # 동기화 검사 타이머 정리 추가
        self._stop_sync_check_timer()

        # 성능 메트릭 타이머 정리
        if self.metrics_timer:
            try:
                self.metrics_timer.stop()
                self.metrics_timer = None
            except Exception as e:
                self.log_error(f"성능 메트릭 타이머 정리 중 오류: {e}")

        # 현재 배치 완료 처리
        if self.batch_id is not None:
            try:
                self.metadata_db.complete_batch(
                    self.batch_id,
                    'completed',  # 'processing'에서 'completed'로 변경
                    self.batch_events,
                    self.batch_processed,
                    self.batch_errors
                )
                self.log_info(f"최종 배치 완료: 이벤트={self.batch_events}, 처리됨={self.batch_processed}, 오류={self.batch_errors}")
            except Exception as e:
                self.log_error(f"배치 완료 처리 중 오류: {e}")

    def run_segmented_sync(self):
        """세그먼트 단위로 동기화를 수행합니다."""
        interval_value = int(self.periodic_sync_interval)
        self.emit_progress(f"[디버그] run_segmented_sync 진입 시 backup_mode: {self.backup_mode}, interval={interval_value}초", -1)
        import threading
        max_retries = 3
        retry_delay = 5  # 5초
        for retry in range(max_retries):
            try:
                # 목적지 최상위 폴더 보장
                ensure_dest_root_exists(self.dest_path)
                # rsync_config.py의 설정을 사용하여 명령어 생성
                rsync_cmd = get_rsync_command(
                    self.source_path,
                    self.dest_path,
                    is_mirror=(self.backup_mode == "mirror"),
                    job_id=self.job_id
                )
                
                mode_text = "미러링" if self.backup_mode == "mirror" else "인크리멘탈"
                self.emit_progress(f"{mode_text} 동기화 시작...", -1)
                self.emit_progress(f"소스 경로: {self.source_path}", -1)
                self.emit_progress(f"대상 경로: {self.dest_path}", -1)
                self.emit_progress(f"실행 명령: {' '.join(rsync_cmd)}", -1)

                with RsyncProcessManager(self) as rsync:
                    if rsync is None:  # 프로세스 수 초과
                        if retry < max_retries - 1:
                            self.emit_progress(f"프로세스 수 초과로 대기 중... ({retry + 1}/{max_retries}, {retry_delay}초 후 재시도)", -1)
                            time.sleep(retry_delay)
                            continue
                        else:
                            self.emit_progress("초기 동기화 실패: 최대 재시도 횟수 초과", -1)
                            return False

                    self.current_rsync_process = rsync.run_rsync(
                        rsync_cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        universal_newlines=True,
                        bufsize=1,
                        errors='ignore'
                    )
                    process = self.current_rsync_process
                    result = {}
                    def comm_thread():
                        try:
                            out, err = process.communicate()
                            result['stdout'] = out
                            result['stderr'] = err
                        except Exception as e:
                            result['exc'] = e
                    t = threading.Thread(target=comm_thread)
                    t.start()
                    try:
                        while t.is_alive():
                            if not self.running:
                                process.terminate()
                                try:
                                    process.wait(timeout=0.5)
                                except Exception:
                                    process.kill()
                                    try:
                                        process.wait(timeout=0.5)
                                    except Exception:
                                        pass
                                self.emit_progress("동기화가 중단되었습니다.", -1)
                                self.current_rsync_process = None
                                return False
                            t.join(timeout=0.1)
                    finally:
                        t.join(timeout=1)
                    # communicate 결과 처리
                    if 'exc' in result:
                        raise Exception(result['exc'])
                    stdout = result.get('stdout', '')
                    stderr = result.get('stderr', '')
                    if not self.running:
                        process.terminate()
                        try:
                            process.wait(timeout=0.5)
                        except Exception:
                            process.kill()
                            try:
                                process.wait(timeout=0.5)
                            except Exception:
                                pass
                        self.emit_progress("동기화가 중단되었습니다.", -1)
                        self.current_rsync_process = None
                        return False
                    if process.returncode == 0:
                        if self.backup_mode == "mirror":
                            self.emit_progress("미러링 동기화 완료", -1)
                        else:
                            self.emit_progress("인크리멘탈 동기화 완료", -1)
                        self.current_rsync_process = None
                        # === 여기서 미처리 이벤트 일괄 처리 ===
                        self.mark_all_events_as_processed()
                        return True
                    elif process.returncode == 20:  # rsync was killed
                        self.emit_progress("동기화가 중단되었습니다.", -1)
                        self.current_rsync_process = None
                        return False
                    else:
                        error_msg = f"동기화 실패 (종료 코드: {process.returncode})"
                        if stderr:
                            error_msg += f"\n{stderr}"
                        self.current_rsync_process = None
                        raise Exception(error_msg)
                
            except Exception as e:
                if not self.running:
                    self.emit_progress("동기화가 중단되었습니다.", -1)
                    return False
                if retry < max_retries - 1:
                    self.emit_progress(f"초기 동기화 중 오류, 재시도 중 ({retry + 1}/{max_retries}): {e}", -1)
                    time.sleep(retry_delay)
                else:
                    self.emit_progress(f"초기 동기화 실패: {e}", -1)
                    return False
        self.emit_progress(f"[디버그] run_segmented_sync 종료, interval={interval_value}초", -1)
        return True

    def initialize_watchers(self):
        """감시자 초기화 및 시작"""
        if self.periodic_sync_interval <= 0:
            return False

        try:
            self.emit_progress(f"실시간 감시 설정 시작 - 소스: {self.source_path}, 대상: {self.dest_path}", -1)
            
            # 소스 감시자 초기화 및 시작
            if not self.source_observer:
                self.source_observer = Observer()
                self.source_observer.start()
                self.emit_progress(f"소스 감시자 시작됨: {self.source_path}", -1)
                
                # 소스 경로에 대한 감시 즉시 시작
                self.source_observer.schedule(self.source_handler, self.source_path, recursive=True)
                self.emit_progress(f"소스 경로 감시 시작됨 (재귀적): {self.source_path}", -1)
            
            # 대상 감시자 초기화 및 시작
            if not self.dest_observer:
                self.dest_observer = Observer()
                self.dest_observer.start()
                self.emit_progress(f"대상 감시자 시작됨: {self.dest_path}", -1)
                
                # 대상 경로에 대한 감시 즉시 시작
                self.dest_observer.schedule(self.dest_handler, self.dest_path, recursive=True)
                self.emit_progress(f"대상 경로 감시 시작됨 (재귀적): {self.dest_path}", -1)

            # 점진적 감시 설정 시작
            self.watched_dirs = set()
            self.dest_watched_dirs = set()
            self.watch_queue = queue.Queue()
            
            # 소스 루트 감시 (이미 위에서 설정했으므로 중복 설정 방지)
            self.watched_dirs.add(self.source_path)
            if hasattr(self, 'dest_observer') and self.dest_observer:
                self.dest_watched_dirs.add(self.dest_path)
            
            # 소스 하위 디렉토리 감시
            dirs_to_watch = []
            with os.scandir(self.source_path) as it:
                for entry in it:
                    if entry.is_dir():
                        dirs_to_watch.append(entry.path)
            
            # 하위 디렉토리 감시 시작
            for dir_path in dirs_to_watch:
                self.watch_queue.put(dir_path)
            
            self.emit_progress("실시간 감시 설정이 완료되었습니다.", -1)
            return True
            
        except Exception as e:
            self.emit_progress(f"감시자 초기화 중 오류 발생: {e}", -1)
            import traceback
            self.emit_progress(f"상세 오류: {traceback.format_exc()}", -1)
            return False

    def terminate(self):
        """QThread 종료 시그널 + rsync 프로세스 강제 종료"""
        self.running = False
        if self.current_rsync_process is not None:
            try:
                self.current_rsync_process.terminate()
                self.current_rsync_process.wait(timeout=5)
            except Exception:
                pass
            self.current_rsync_process = None
        super().terminate()

    def terminate_all_rsync_processes(self):
        # 현재 워커가 관리하는 프로세스 종료
        if self.current_rsync_process is not None:
            try:
                self.current_rsync_process.terminate()
                self.current_rsync_process.wait(timeout=5)
            except Exception:
                pass
            self.current_rsync_process = None
        # 시스템 전체에서 rsync 프로세스도 종료 (안전하게)
        import psutil
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] == 'rsync':
                    proc.terminate()
            except Exception:
                continue

    def cancel_all_sync_timers_and_tasks(self):
        # 타이머 취소
        if hasattr(self, 'periodic_sync_timer') and self.periodic_sync_timer:
            try:
                self.periodic_sync_timer.cancel()
            except Exception:
                pass
            self.periodic_sync_timer = None
        # 플래그 해제
        with self.periodic_sync_lock:
            self.is_periodic_sync_running = False
        # 스레드풀/큐 작업 취소
        if hasattr(self, 'executor') and self.executor:
            try:
                self.executor.shutdown(wait=False)
            except Exception:
                pass
            self.executor = None
        if hasattr(self, 'active_futures'):
            try:
                for future in list(self.active_futures):
                    future.cancel()
                self.active_futures.clear()
            except Exception:
                pass
        # backup_queue 비우기
        if hasattr(self, 'backup_queue') and self.backup_queue:
            try:
                while not self.backup_queue.empty():
                    try:
                        self.backup_queue.get_nowait()
                    except Exception:
                        break
            except Exception:
                pass

    def log_debug(self, message):
        """디버그 메시지를 로깅합니다."""
        if hasattr(self, 'debug_mode') and self.debug_mode:
            self.emit_progress(f"[디버그] {message}", -1)

    def _execute_single_rsync(self, task_data):
        """단일 rsync 작업 실행"""
        try:
            # 목적지 폴더가 없으면 자동 생성
            dest_dir = os.path.dirname(task_data.dst_path)
            if not os.path.exists(dest_dir):
                try:
                    os.makedirs(dest_dir, exist_ok=True)
                    self.logger.info(f"목적지 폴더 생성: {dest_dir}")
                except Exception as e:
                    self.logger.error(f"목적지 폴더 생성 실패: {e}")
                    return False

            # 소스 파일/디렉토리 권한 확인 및 설정
            if os.path.exists(task_data.src_path):
                try:
                    # 관리자 권한으로 소스 파일/디렉토리 권한을 777로 변경
                    success, output = self.admin_auth.execute_with_sudo(['chmod', '-R', '777', task_data.src_path])
                    if not success:
                        self.logger.warning(f"소스 권한 변경 실패: {output}")
                except Exception as e:
                    self.logger.warning(f"소스 권한 변경 중 오류: {e}")

            # rsync 명령어 생성
            cmd = get_rsync_command(task_data.src_path, task_data.dst_path, self.backup_mode == self.MODE_MIRROR, job_id=self.job_id)
            
            # rsync 명령 실행
            command_str = ' '.join(cmd)
            self.logger.debug(f"실행할 rsync 명령: {command_str}")
            
            process = self.rsync_manager.start_rsync(command_str)
            
            if process:
                return_code = process.wait()  # 프로세스 완료 대기
                if return_code == 0:
                    self.logger.info(f"동기화 완료: {task_data.src_path} -> {task_data.dst_path}")
                    return True
                else:
                    self.logger.warning(f"동기화 실패 (코드: {return_code}): {task_data.src_path}")
                    if return_code == 23:  # rsync의 부분 전송 성공 코드
                        self.logger.info("일부 파일이 전송됨 (코드 23) - 다음 동기화에서 나머지 처리")
                        return True
                    elif return_code == 13:  # 권한 오류
                        # 권한 오류 발생 시 대상 파일/디렉토리 권한도 변경 시도
                        try:
                            success, output = self.admin_auth.execute_with_sudo(['chmod', '-R', '777', task_data.dst_path])
                            if success:
                                # 권한 변경 후 재시도
                                process = self.rsync_manager.start_rsync(command_str)
                                if process:
                                    return_code = process.wait()
                                    if return_code == 0:
                                        self.logger.info(f"권한 변경 후 동기화 성공: {task_data.src_path}")
                                        return True
                        except Exception as e:
                            self.logger.error(f"대상 권한 변경 중 오류: {e}")
            return False
            
        except Exception as e:
            self.logger.error(f"rsync 실행 중 오류: {e}")
            return False

    def _process_backup_task(self, task_data):
        """백업 작업을 처리하고 이벤트 상태를 업데이트합니다."""
        try:
            # task_data가 튜플인 경우 처리
            if isinstance(task_data, (list, tuple)):
                source_path, dest_path, event_type, is_directory, _, event_id = task_data
            else:
                # 딕셔너리 형태로 전달된 경우 (이전 버전과의 호환성)
                event_id = task_data.get('event_id')
                source_path = task_data.get('source_path')
                dest_path = task_data.get('dest_path')
                is_directory = task_data.get('is_directory', False)
            
            if not all([event_id, source_path, dest_path]):
                self.log_error(f"필수 작업 데이터 누락: {task_data}")
                return False
                
            self.log_info(f"백업 작업 시작 (이벤트 ID: {event_id}, 소스: {source_path}, 대상: {dest_path})")
            
            # 백업 실행
            success = False
            error_message = None
            
            try:
                if is_directory:
                    success = self._copy_directory_with_retry(source_path, dest_path)
                else:
                    success = self._copy_file_with_retry(source_path, dest_path)
                    
                if success:
                    # 백업 성공 시 상태 업데이트
                    processed_at = time.time()
                    update_success = False
                    retry_count = 0
                    max_retries = 3
                    
                    while not update_success and retry_count < max_retries:
                        try:
                            # 직접 processed 값을 1로 업데이트
                            with self.metadata_db._get_connection() as conn:
                                cursor = conn.cursor()
                                cursor.execute("""
                                    UPDATE change_events 
                                    SET processed = 1,
                                        processing_time = ?,
                                        last_update_attempt = ?,
                                        update_attempt_count = update_attempt_count + 1,
                                        status = '처리됨',
                                        error_message = NULL
                                    WHERE id = ?
                                """, (processed_at, time.time(), event_id))
                                
                                if cursor.rowcount == 0:
                                    raise Exception("업데이트된 행이 없음")
                                    
                                conn.commit()
                                update_success = True
                                self.log_info(f"이벤트 상태 업데이트 성공 (ID: {event_id}, processed: 1)")
                                self.events_updated_signal.emit()  # ★ 성공 시 emit
                                
                        except Exception as e:
                            retry_count += 1
                            self.log_error(f"상태 업데이트 실패 (시도 {retry_count}/{max_retries}): {str(e)}")
                            if retry_count < max_retries:
                                time.sleep(0.5 * (2 ** retry_count))
                            else:
                                error_message = f"상태 업데이트 실패: {str(e)}"
                                
                    if not update_success:
                        self.log_error(f"이벤트 상태 업데이트 최종 실패 (ID: {event_id}): {error_message}")
                        return False
                        
                else:
                    error_message = "파일/디렉토리 복사 실패"
                    self.log_error(f"백업 실패 (이벤트 ID: {event_id}): {error_message}")
                    self.events_updated_signal.emit()  # ★ 실패 시 emit
                    
            except Exception as e:
                error_message = str(e)
                self.log_error(f"백업 중 예외 발생 (이벤트 ID: {event_id}): {error_message}")
                success = False
                
            # 실패 시 상태 업데이트
            if not success:
                try:
                    with self.metadata_db._get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute("""
                            UPDATE change_events 
                            SET processed = 0,
                                processing_time = ?,
                                last_update_attempt = ?,
                                update_attempt_count = update_attempt_count + 1,
                                status = '실패',
                                error_message = ?
                            WHERE id = ?
                        """, (time.time(), time.time(), error_message, event_id))
                        conn.commit()
                        self.log_info(f"실패 상태 업데이트 완료 (ID: {event_id})")
                except Exception as e:
                    self.log_error(f"실패 상태 업데이트 중 오류: {str(e)}")
                    
            # 배치 처리 통계 업데이트
            if self.batch_id is not None:
                self.batch_events += 1
                if success:
                    self.batch_processed += 1
                    self.log_debug(f"배치 처리 성공 증가: {self.batch_processed}")
                else:
                    self.batch_errors += 1
                    self.log_debug(f"배치 오류 증가: {self.batch_errors}")

            return success
            
        except Exception as e:
            if self.batch_id is not None:
                self.batch_errors += 1
                self.log_debug(f"예외 발생으로 배치 오류 증가: {self.batch_errors}")
            self.log_error(f"작업 처리 중 예외 발생: {str(e)}")
            return False

    def mark_all_events_as_processed(self):
        """모든 미처리 이벤트를 처리됨으로 일괄 업데이트"""
        try:
            with self.metadata_db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE change_events
                    SET processed = 1, status = '처리됨'
                    WHERE processed = 0
                """)
                conn.commit()
            self.logger.info("모든 미처리 이벤트를 처리됨으로 일괄 업데이트 완료")
            self.events_updated_signal.emit()  # ★ 일괄 처리 후 emit
        except Exception as e:
            self.logger.error(f"미처리 이벤트 일괄 업데이트 실패: {e}")

    def _start_sync_check_timer(self):
        """동기화 검사 타이머를 시작합니다."""
        if self.sync_check_timer is None:
            self.sync_check_timer = QTimer()
            self.sync_check_timer.timeout.connect(self._check_dest_sync)
            self.sync_check_timer.start(self.sync_check_interval * 1000)  # 밀리초 단위

    def _stop_sync_check_timer(self):
        """동기화 검사 타이머를 중지합니다."""
        if self.sync_check_timer is not None:
            self.sync_check_timer.stop()
            self.sync_check_timer = None

    def _check_dest_sync(self):
        """대상 디렉토리의 동기화 상태를 주기적으로 검사합니다."""
        if not self.running or not self.dest_handler or self.backup_mode != "mirror":
            return

        try:
            current_time = time.time()
            with self.sync_check_lock:
                if current_time - self.last_sync_check < self.sync_check_interval:
                    return
                self.last_sync_check = current_time

            self.logger.info("대상 디렉토리 동기화 상태 검사 시작")
            
            # 대상 디렉토리 순회
            for root, dirs, files in os.walk(self.dest_path):
                # 디렉토리 검사
                for dir_name in dirs:
                    dest_dir_path = os.path.join(root, dir_name)
                    rel_path = os.path.relpath(dest_dir_path, self.dest_path)
                    source_dir_path = os.path.join(self.source_path, rel_path)
                    
                    if not os.path.exists(dest_dir_path) and os.path.exists(source_dir_path):
                        # 디렉토리가 삭제되었지만 소스에 있는 경우
                        self.logger.info(f"삭제된 디렉토리 발견: {dest_dir_path}")
                        self.dest_handler._handle_dest_deleted(
                            FileSystemEvent(dest_dir_path, is_directory=True)
                        )
                
                # 파일 검사
                for file_name in files:
                    dest_file_path = os.path.join(root, file_name)
                    rel_path = os.path.relpath(dest_file_path, self.dest_path)
                    source_file_path = os.path.join(self.source_path, rel_path)
                    
                    if not os.path.exists(dest_file_path) and os.path.exists(source_file_path):
                        # 파일이 삭제되었지만 소스에 있는 경우
                        self.logger.info(f"삭제된 파일 발견: {dest_file_path}")
                        self.dest_handler._handle_dest_deleted(
                            FileSystemEvent(dest_file_path, is_directory=False)
                        )
            
            self.logger.info("대상 디렉토리 동기화 상태 검사 완료")
            
        except Exception as e:
            self.logger.error(f"동기화 상태 검사 중 오류 발생: {str(e)}")
            import traceback
            self.logger.error(f"상세 오류: {traceback.format_exc()}")

    def _start_metrics_timer(self):
        """성능 메트릭 수집 타이머를 시작합니다."""
        try:
            if self.metrics_timer:
                self.metrics_timer.cancel()
            self.metrics_timer = QTimer()
            self.metrics_timer.timeout.connect(self._update_performance_metrics)
            self.metrics_timer.start(self.metrics_interval * 1000)  # 밀리초 단위
            self.log_info("성능 메트릭 수집 타이머 시작")
        except Exception as e:
            self.log_error(f"성능 메트릭 타이머 시작 실패: {e}")

    def _update_performance_metrics(self):
        """성능 메트릭을 수집하고 데이터베이스에 저장합니다."""
        try:
            if not self.running:
                return

            current_time = time.time()
            if current_time - self.last_metrics_update < self.metrics_interval:
                return

            # 현재 배치 상태 확인 및 완료 처리
            if self.batch_id is not None:
                # 현재 배치를 완료 상태로 업데이트
                self.metadata_db.complete_batch(
                    self.batch_id,
                    'completed',  # 'processing'에서 'completed'로 변경
                    self.batch_events,
                    self.batch_processed,
                    self.batch_errors
                )
                self.log_debug(f"배치 완료: 이벤트={self.batch_events}, 처리됨={self.batch_processed}, 오류={self.batch_errors}")

            # 새로운 배치 시작
            self.batch_id = self.metadata_db.create_batch()
            self.batch_start_time = current_time
            self.batch_events = 0
            self.batch_processed = 0
            self.batch_errors = 0
            self.log_debug("새로운 배치 시작")

            # 성능 메트릭 업데이트
            self.metadata_db.update_performance_metrics()

            self.last_metrics_update = current_time
            self.log_debug("성능 메트릭 업데이트 완료")

        except Exception as e:
            self.log_error(f"성능 메트릭 업데이트 실패: {e}")

def ensure_dest_root_exists(dest_path):
    """목적지 최상위 폴더가 없으면 생성"""
    root_dir = dest_path
    # 파일이면 상위 폴더, 디렉토리면 자기 자신
    if not os.path.isdir(dest_path):
        root_dir = os.path.dirname(dest_path)
    if root_dir and not os.path.exists(root_dir):
        os.makedirs(root_dir, exist_ok=True)

class RsyncProcessManager:
    """rsync 프로세스 관리를 위한 컨텍스트 매니저"""
    def __init__(self, worker):
        self.worker = worker
        self.process = None
        
    def __enter__(self):
        current_count = GLOBAL_RSYNC_COUNTER.get_count()
        if current_count >= self.worker.MAX_TOTAL_RSYNC:
            self.worker.emit_progress(
                f"현재 실행 중인 rsync 프로세스가 너무 많습니다 ({current_count}/{self.worker.MAX_TOTAL_RSYNC}). "
                "프로세스 수가 감소할 때까지 대기합니다.", -1
            )
            return None
        
        GLOBAL_RSYNC_COUNTER.increment()
        self.worker.emit_progress(f"현재 rsync 프로세스 수: {GLOBAL_RSYNC_COUNTER.get_count()}", -1)
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.process:
            try:
                self.process.terminate()
                self.process.wait()
            except:
                pass
        GLOBAL_RSYNC_COUNTER.decrement()
        self.worker.emit_progress(f"현재 rsync 프로세스 수: {GLOBAL_RSYNC_COUNTER.get_count()}", -1)
        return False  # Re-raise any exceptions

    def run_rsync(self, cmd, **kwargs):
        """rsync 프로세스를 실행하고 반환합니다."""
        self.process = subprocess.Popen(cmd, preexec_fn=os.setsid, **kwargs)
        return self.process
