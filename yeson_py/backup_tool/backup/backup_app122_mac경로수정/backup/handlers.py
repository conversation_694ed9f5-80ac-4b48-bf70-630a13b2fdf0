import os
import time
import threading
import queue
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from backup_app.utils.logger import BackupLogger
import shutil
from backup_app.utils.metadata_db import MetadataDB

class BackupEventHandler(FileSystemEventHandler):
    def __init__(self, backup_queue, source_path, dest_path, is_source=True, backup_mode="incremental"):
        super().__init__()
        self.backup_queue = backup_queue
        self.source_path = source_path
        self.dest_path = dest_path
        self.is_source = is_source
        self.backup_mode = backup_mode
        self.pending_tasks = set()
        self.debounce_timer = {}
        self.debounce_lock = threading.Lock()
        self.debounce_duration = 1.5  # 디바운스 시간
        # 추가: 이벤트 레이트 제한
        self.event_count = 0
        self.event_limit = 100  # 최대 이벤트 수 (조정 가능)
        self.event_window = 10   # 시간 윈도우(초)
        self.last_reset = time.time()
        self.event_lock = threading.Lock()
        self.logger = BackupLogger()
        # 메타데이터 DB 초기화 추가
        self.metadata_db = MetadataDB()
        # 추가: 디렉토리 modified 이벤트 디바운싱
        self.dir_modified_timers = {}
        self.dir_modified_lock = threading.Lock()
        # 추가: 배치 처리용 큐
        self.batch_queue = queue.Queue()
        self.batch_processing = False
        self.batch_thread = None
        # 추가: 복구 작업 추적
        self.restore_tasks = set()
        self.restore_lock = threading.Lock()
        self.start_batch_processor()
        # 제외된 폴더 목록 캐시
        self._excluded_folders = None
        self._excluded_folders_lock = threading.Lock()
        self._last_exclusions_update = 0
        self.EXCLUSIONS_CACHE_TTL = 60  # 60초 동안 캐시 유지

    def start_batch_processor(self):
        """배치 처리를 위한 스레드를 시작합니다."""
        if self.batch_thread is None or not self.batch_thread.is_alive():
            self.batch_thread = threading.Thread(target=self._process_batch_queue, daemon=True)
            self.batch_thread.start()

    def _process_batch_queue(self):
        """배치 큐의 이벤트를 처리합니다."""
        while True:
            try:
                batch = []
                # 최대 100ms 동안 이벤트 수집
                start_time = time.time()
                while time.time() - start_time < 0.1 and len(batch) < 50:
                    try:
                        event = self.batch_queue.get_nowait()
                        batch.append(event)
                    except queue.Empty:
                        time.sleep(0.01)
                        break

                if batch:
                    self._process_batch(batch)
            except Exception as e:
                self.logger.error(f"배치 처리 중 오류: {e}")

    def _process_batch(self, batch):
        """이벤트 배치를 처리합니다."""
        # 중복 제거 및 정렬
        unique_events = {}
        for event in batch:
            key = (event.src_path, event.event_type, event.is_directory)
            if key not in unique_events:
                unique_events[key] = event

        # 이벤트 처리
        for event in unique_events.values():
            if event.event_type == "created":
                self._handle_created(event)
            elif event.event_type == "modified":
                self._handle_modified(event)
            elif event.event_type == "deleted":
                self._handle_deleted(event)
            elif event.event_type == "moved":
                self._handle_moved(event)

    def _get_excluded_folders(self):
        """제외된 폴더 목록을 가져옵니다. 캐시된 결과를 사용합니다."""
        current_time = time.time()
        with self._excluded_folders_lock:
            if (self._excluded_folders is None or 
                current_time - self._last_exclusions_update > self.EXCLUSIONS_CACHE_TTL):
                from ..utils.rsync_config import get_exclusions
                exclusions = get_exclusions()
                self._excluded_folders = set()
                
                for path, type_ in exclusions.items():
                    if type_ == "폴더":
                        try:
                            # 소스 경로 기준으로 상대 경로 확인
                            rel_path = os.path.relpath(path, self.source_path)
                            if not rel_path.startswith('..'):  # 소스 경로 내부의 폴더만
                                self._excluded_folders.add(rel_path)
                        except ValueError:
                            # 상대 경로 변환 실패 시 무시
                            continue
                
                self._last_exclusions_update = current_time
            
            return self._excluded_folders

    def _is_in_excluded_folder(self, path):
        """주어진 경로가 제외된 폴더 내에 있는지 확인합니다."""
        try:
            rel_path = os.path.relpath(path, self.source_path)
            if rel_path.startswith('..'):  # 소스 경로 외부
                return False
            
            # 제외된 폴더 목록 확인
            excluded_folders = self._get_excluded_folders()
            path_parts = rel_path.split(os.sep)
            
            # 경로의 각 부분을 확인하여 제외된 폴더 내부인지 검사
            current_path = ""
            for part in path_parts:
                if current_path:
                    current_path = os.path.join(current_path, part)
                else:
                    current_path = part
                if current_path in excluded_folders:
                    return True
            
            return False
        except ValueError:
            return False

    def _should_ignore_event(self, event):
        """이벤트를 무시해야 하는지 확인합니다."""
        # 제외된 폴더 내의 이벤트 무시
        if self._is_in_excluded_folder(event.src_path):
            return True
        
        # journal 파일 이벤트 무시
        if event.src_path.endswith('-journal'):
            return True
        
        # 디렉토리 modified 이벤트 디바운싱
        if event.event_type == "modified" and event.is_directory:
            with self.dir_modified_lock:
                current_time = time.time()
                if event.src_path in self.dir_modified_timers:
                    last_time = self.dir_modified_timers[event.src_path]
                    if current_time - last_time < 1.0:  # 1초 이내 중복 무시
                        return True
                self.dir_modified_timers[event.src_path] = current_time
                # 오래된 타이머 정리
                self.dir_modified_timers = {k: v for k, v in self.dir_modified_timers.items() 
                                         if current_time - v < 5.0}
        
        return False

    def on_created(self, event):
        """파일이나 디렉토리가 생성되었을 때 호출되는 메서드"""
        if self._should_ignore_event(event):
            return
        self.batch_queue.put(event)

    def on_modified(self, event):
        """파일이나 디렉토리가 수정되었을 때 호출되는 메서드"""
        if self._should_ignore_event(event):
            return
        self.batch_queue.put(event)

    def on_deleted(self, event):
        """파일이나 디렉토리가 삭제되었을 때 호출되는 메서드"""
        if self._should_ignore_event(event):
            return
            
        if not self.is_source:
            # 대상 디렉토리에서의 삭제 이벤트 처리
            self._handle_dest_deleted(event)
        else:
            # 소스 디렉토리에서의 삭제 이벤트 처리 (기존 코드)
            self.batch_queue.put(event)

    def on_moved(self, event):
        """파일이나 디렉토리가 이동되었을 때 호출되는 메서드"""
        if self._should_ignore_event(event):
            return
        self.batch_queue.put(event)

    def _handle_created(self, event):
        """생성 이벤트 처리"""
        self.logger.info(f"[이벤트] 생성됨: {event.src_path} (디렉토리: {event.is_directory})")
        try:
            self.metadata_db.add_change(event.src_path, "created", event.is_directory)
            if not self.is_source and self.backup_mode == "mirror":
                self._handle_mirror_mode_created(event)
            else:
                self._debounce_event(event.src_path, "created", event.is_directory)
        except Exception as e:
            self.logger.error(f"생성 이벤트 처리 중 오류: {e}")

    def _handle_modified(self, event):
        """수정 이벤트 처리"""
        self.logger.info(f"[이벤트] 수정됨: {event.src_path} (디렉토리: {event.is_directory})")
        try:
            self.metadata_db.add_change(event.src_path, "modified", event.is_directory)
            if not self.is_source and self.backup_mode == "mirror":
                self._handle_mirror_mode_modified(event)
            elif not event.is_directory:
                self._debounce_event(event.src_path, "modified", False)
        except Exception as e:
            self.logger.error(f"수정 이벤트 처리 중 오류: {e}")

    def _handle_dest_deleted(self, event):
        """대상 디렉토리에서 파일이 삭제되었을 때의 처리"""
        if not self.is_source and self.backup_mode == "mirror":
            try:
                rel_path = os.path.relpath(event.src_path, self.dest_path)
                source_path = os.path.join(self.source_path, rel_path)
                
                if os.path.exists(source_path):
                    # 복구 작업이 이미 진행 중인지 확인
                    with self.restore_lock:
                        if event.src_path in self.restore_tasks:
                            self.logger.debug(f"이미 복구 중인 파일: {event.src_path}")
                            return
                        self.restore_tasks.add(event.src_path)
                    
                    try:
                        # 디렉토리인 경우 부모 디렉토리 생성 확인
                        if event.is_directory:
                            os.makedirs(os.path.dirname(event.src_path), exist_ok=True)
                            shutil.copytree(source_path, event.src_path)
                        else:
                            os.makedirs(os.path.dirname(event.src_path), exist_ok=True)
                            shutil.copy2(source_path, event.src_path)
                        
                        self.logger.info(f"삭제된 파일 복구 완료: {event.src_path}")
                        event_id = self.metadata_db.add_change(event.src_path, "restored", event.is_directory)
                        
                        # 복구 성공 시 백업 큐에 작업 추가
                        if event_id is not None:
                            task_data = (source_path, event.src_path, "restored", event.is_directory, None, event_id)
                            self.backup_queue.put(task_data)
                            
                    except Exception as e:
                        self.logger.error(f"파일 복구 실패: {event.src_path} - {str(e)}")
                        # 복구 실패 시 메타데이터에 기록
                        self.metadata_db.add_change(event.src_path, "restore_failed", event.is_directory, error_message=str(e))
                    
                    finally:
                        # 복구 작업 완료 후 추적 목록에서 제거
                        with self.restore_lock:
                            self.restore_tasks.discard(event.src_path)
                            
            except Exception as e:
                self.logger.error(f"복구 처리 중 예외 발생: {str(e)}")
                import traceback
                self.logger.error(f"상세 오류: {traceback.format_exc()}")

    def _handle_moved(self, event):
        """이동 이벤트 처리"""
        if not self.is_source:
            return
        self.logger.info(f"[이벤트] 이동됨: {event.src_path} -> {event.dest_path} (디렉토리: {event.is_directory})")
        try:
            src_path = os.path.normpath(event.src_path)
            dest_path = os.path.normpath(event.dest_path)
            if not src_path.startswith(self.source_path) or not dest_path.startswith(self.source_path):
                return
            event_id = self.metadata_db.add_change(src_path, "moved", event.is_directory, new_path=dest_path)
            if event_id is not None:
                task_data = (src_path, self.get_dest_path(src_path), "moved", event.is_directory, 
                           self.get_dest_path(dest_path), event_id)
                self.backup_queue.put(task_data)
        except Exception as e:
            self.logger.error(f"이동 이벤트 처리 중 오류: {e}")

    def _handle_mirror_mode_created(self, event):
        """미러 모드에서의 생성 이벤트 처리"""
        rel_path = os.path.relpath(event.src_path, self.dest_path)
        source_equiv = os.path.join(self.source_path, rel_path)
        if not os.path.exists(source_equiv):
            if event.is_directory:
                shutil.rmtree(event.src_path, ignore_errors=True)
            else:
                try:
                    os.remove(event.src_path)
                except Exception:
                    pass
            self.backup_queue.put(("delete_target", event.src_path, "deleted", event.is_directory))

    def _handle_mirror_mode_modified(self, event):
        """미러 모드에서의 수정 이벤트 처리"""
        rel_path = os.path.relpath(event.src_path, self.dest_path)
        source_equiv = os.path.join(self.source_path, rel_path)
        if not os.path.exists(source_equiv):
            if event.is_directory:
                shutil.rmtree(event.src_path, ignore_errors=True)
            else:
                try:
                    os.remove(event.src_path)
                except Exception:
                    pass
            self.backup_queue.put(("delete_target", event.src_path, "deleted", event.is_directory))

    def get_dest_path(self, source_path):
        """소스 경로에 해당하는 목적지 경로를 반환합니다."""
        try:
            rel_path = os.path.relpath(source_path, self.source_path)
            return os.path.join(self.dest_path, rel_path)
        except ValueError:
            self.logger.error(f"경로 변환 실패: {source_path}")
            return None

    def set_backup_mode(self, mode):
        """백업 모드를 설정합니다."""
        self.backup_mode = mode

    # 모든 이벤트 핸들러에서 호출할 메서드 추가
    def check_event_limit(self):
        with self.event_lock:
            current_time = time.time()
            # 시간 윈도우가 지났으면 카운터 리셋
            if current_time - self.last_reset > self.event_window:
                self.event_count = 0
                self.last_reset = current_time

            self.event_count += 1
            if self.event_count > self.event_limit:
                # 이벤트 한도 초과 - 이벤트 드롭
                return False

            return True

    def _debounce_event(self, path, event_type, is_directory):
        """이벤트 디바운스 처리"""
        try:
            with self.debounce_lock:
                current_time = time.time()
                task_key = (path, event_type, is_directory)
                
                # 이전 타이머가 있으면 취소
                if task_key in self.debounce_timer:
                    self.logger.debug(f"이전 타이머 취소: {path}")
                    self.debounce_timer[task_key].cancel()
                
                # 새 타이머 설정
                timer = threading.Timer(self.debounce_duration, 
                                      lambda: self._process_debounced_event(task_key))
                self.debounce_timer[task_key] = timer
                timer.start()
                self.logger.debug(f"디바운스 타이머 시작: {path} (지연: {self.debounce_duration}초)")
        except Exception as e:
            self.logger.error(f"디바운스 처리 중 예외 발생: {e}")
            import traceback
            self.logger.error(f"상세 오류: {traceback.format_exc()}")

    def _process_debounced_event(self, task_key):
        """디바운스된 이벤트 처리"""
        try:
            path, event_type, is_directory = task_key
            self.logger.debug(f"디바운스된 이벤트 처리 시작: {path}")
            
            with self.debounce_lock:
                if task_key in self.debounce_timer:
                    del self.debounce_timer[task_key]
            
            # 백업 큐에 작업 추가
            self.queue_backup(path, event_type, is_directory)
            self.logger.debug(f"디바운스된 이벤트 처리 완료: {path}")
        except Exception as e:
            self.logger.error(f"디바운스된 이벤트 처리 중 예외 발생: {e}")
            import traceback
            self.logger.error(f"상세 오류: {traceback.format_exc()}")

    def queue_backup(self, event_path, event_type, is_directory=False):
        """백업 작업을 큐에 추가"""
        try:
            self.logger.debug(f"백업 작업 큐 추가 시작: {event_path}")
            
            # 이벤트 경로가 소스 경로 내에 있는지 확인
            normalized_watch_path = os.path.normpath(self.source_path)
            normalized_event_path = os.path.normpath(event_path)
            
            self.logger.debug(f"정규화된 경로 - 감시: {normalized_watch_path}, 이벤트: {normalized_event_path}")
            
            if not normalized_event_path.startswith(normalized_watch_path):
                self.logger.debug(f"이벤트 경로가 소스 경로 밖에 있어 무시: {event_path}")
                return
            
            rel_path = os.path.relpath(normalized_event_path, normalized_watch_path)
            dest_path = os.path.join(self.dest_path, rel_path)
            
            # 작업 키 생성
            task_key = (event_path, dest_path, event_type, is_directory)
            
            # 이미 대기 중인 작업이 있는지 확인
            if task_key in self.pending_tasks:
                self.logger.debug(f"이미 대기 중인 작업이 있어 무시: {event_path}")
                return
            
            # 작업을 대기 목록에 추가
            self.pending_tasks.add(task_key)
            self.logger.debug(f"작업을 대기 목록에 추가: {event_path}")
            
            # 백업 큐에 작업 추가
            self.backup_queue.put(task_key)
            self.logger.info(f"백업 작업 큐에 추가됨: {event_path} -> {dest_path}")
            
        except Exception as e:
            self.logger.error(f"백업 작업 큐 추가 중 오류: {e}")
            import traceback
            self.logger.error(f"상세 오류: {traceback.format_exc()}")

    def task_completed(self, task_key_tuple):
        self.pending_tasks.discard(task_key_tuple)
        # print(f"Debug: Task completed, removed from pending: {task_key_tuple}")