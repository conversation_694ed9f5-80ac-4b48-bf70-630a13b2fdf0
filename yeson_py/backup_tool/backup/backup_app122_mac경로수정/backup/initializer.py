"""
백업 초기화를 수행하는 클래스입니다.
"""
import os
import subprocess
from PyQt5.QtCore import QThread, pyqtSignal
from ..utils.rsync_config import get_rsync_command, get_rsync_options_string

class BackupInitializer(QThread):
    progress_signal = pyqtSignal(str, str, int)
    initialization_finished = pyqtSignal(str, bool)

    def __init__(self, job_id, source_path, dest_path, max_workers=4):
        super().__init__()
        self.job_id = job_id
        self.source_path = source_path
        self.dest_path = dest_path
        self.max_workers = max_workers
        self._stop_flag = False

    def emit_progress(self, message, progress):
        self.progress_signal.emit(self.job_id, message, progress)

    def get_subdirs(self, path):
        try:
            return [d for d in os.listdir(path) if os.path.isdir(os.path.join(path, d))]
        except Exception as e:
            self.emit_progress(f"Error reading directory {path}: {str(e)}", -1)
            return []

    def run(self):
        try:
            # rsync_config.py의 설정을 사용하여 명령어 생성
            cmd = get_rsync_command(self.source_path, self.dest_path, is_mirror=True, job_id=self.job_id)
            command_str = ' '.join(cmd)
            
            # 명령어 실행
            process = subprocess.Popen(command_str, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            stdout, stderr = process.communicate()
            
            if process.returncode != 0:
                self.emit_progress(f"초기화 실패: {stderr.decode()}", -1)
                self.initialization_finished.emit(self.job_id, False)
            else:
                self.emit_progress("초기화 완료", 100)
                self.initialization_finished.emit(self.job_id, True)
                
        except Exception as e:
            self.emit_progress(f"초기화 실패: {str(e)}", -1)
            self.initialization_finished.emit(self.job_id, False)

    def stop(self):
        self._stop_flag = True
