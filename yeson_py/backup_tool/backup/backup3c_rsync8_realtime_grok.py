import sys
import os
import subprocess
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Border, Side, Alignment
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QPushButton, QLineEdit,
                            QHBoxLayout, QComboBox, QLabel, QTreeWidget, QTreeWidgetItem, QStyle,
                            QFrame, QSplitter, QStatusBar, QGraphicsDropShadowEffect, QHeaderView,
                            QStyleFactory, QSizePolicy, QSpacerItem, QScrollArea, QFileDialog,
                            QTextEdit, QProgressBar)
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect, QSize, QThread, pyqtSignal
from PyQt5.QtGui import QColor, QPalette, QFont, QIcon, QLinearGradient, QBrush, QPixmap, QKeySequence
from PyQt5.QtWidgets import Q<PERSON>hortcut
import shutil
import time
import threading
import queue
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class StyleManager:
    DARK_THEME = {
        "BG": "#1E2526",
        "LIGHT_BG": "#2A3439",
        "ACCENT": "#D4AF37",
        "TEXT": "#E8ECEF",
        "MEDIUM_GRAY": "#A9B1B3",
        "DARK_ACCENT": "#3A4A50"
    }
    
    LIGHT_THEME = {
        "BG": "#F5F6F5",
        "LIGHT_BG": "#FFFFFF",
        "ACCENT": "#F4A261",
        "TEXT": "#2A3439",
        "MEDIUM_GRAY": "#6B7280",
        "DARK_ACCENT": "#E5E7EB"
    }
    
    @staticmethod
    def apply_style(app, theme="dark"):
        colors = StyleManager.DARK_THEME if theme == "dark" else StyleManager.LIGHT_THEME
        app.setStyle(QStyleFactory.create("Fusion"))
        font = QFont("Helvetica", 10)
        app.setFont(font)
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(colors["BG"]))
        palette.setColor(QPalette.WindowText, QColor(colors["TEXT"]))
        palette.setColor(QPalette.Base, QColor(colors["LIGHT_BG"]))
        palette.setColor(QPalette.AlternateBase, QColor(colors["DARK_ACCENT"]))
        palette.setColor(QPalette.ToolTipBase, QColor(colors["LIGHT_BG"]))
        palette.setColor(QPalette.ToolTipText, QColor(colors["TEXT"]))
        palette.setColor(QPalette.Text, QColor(colors["TEXT"]))
        palette.setColor(QPalette.Button, QColor(colors["DARK_ACCENT"]))
        palette.setColor(QPalette.ButtonText, QColor(colors["TEXT"]))
        palette.setColor(QPalette.BrightText, QColor(colors["ACCENT"]))
        palette.setColor(QPalette.Link, QColor(colors["ACCENT"]))
        palette.setColor(QPalette.Highlight, QColor(colors["ACCENT"]))
        palette.setColor(QPalette.HighlightedText, QColor(colors["BG"]))
        app.setPalette(palette)
        
        app.setStyleSheet(f"""
            QMainWindow {{ background: {colors["BG"]}; }}
            QWidget {{ background: {colors["BG"]}; }}
            QLabel {{ color: {colors["TEXT"]}; font-size: 12px; font-weight: 400; padding: 4px; }}
            QLabel#headerLabel {{ font-size: 14px; font-weight: bold; color: {colors["ACCENT"]}; padding: 8px; }}
            QComboBox {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; padding: 4px 8px; font-size: 12px; background: {colors["LIGHT_BG"]}; color: {colors["TEXT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; }}
            QComboBox::drop-down {{ subcontrol-origin: padding; subcontrol-position: right center; width: 20px; border-left: 1px solid {colors["DARK_ACCENT"]}; border-top-right-radius: 4px; border-bottom-right-radius: 4px; }}
            QComboBox QAbstractItemView {{ font-size: 12px; background: {colors["LIGHT_BG"]}; border: 1px solid {colors["DARK_ACCENT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; padding: 4px; }}
            QLineEdit {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; padding: 4px 8px; font-size: 12px; background: {colors["LIGHT_BG"]}; color: {colors["TEXT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; }}
            QLineEdit:read-only {{ background: {colors["DARK_ACCENT"]}; border: 1px solid {colors["DARK_ACCENT"]}; }}
            QPushButton {{ background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {colors["ACCENT"]}, stop:1 {StyleManager.adjust_brightness(colors["ACCENT"], -20)}); color: {colors["BG"]}; border: none; border-radius: 4px; padding: 6px 12px; font-size: 12px; font-weight: bold; }}
            QPushButton:hover {{ background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {StyleManager.adjust_brightness(colors["ACCENT"], 20)}, stop:1 {colors["ACCENT"]}); }}
            QPushButton:pressed {{ background-color: {StyleManager.adjust_brightness(colors["ACCENT"], -40)}; }}
            QPushButton:disabled {{ background-color: {colors["MEDIUM_GRAY"]}; color: {colors["DARK_ACCENT"]}; }}
            QTreeWidget {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; font-size: 12px; background: {colors["LIGHT_BG"]}; alternate-background-color: {colors["DARK_ACCENT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; padding: 4px; }}
            QTreeWidget::item {{ padding: 4px; border-bottom: 1px solid {colors["DARK_ACCENT"]}; }}
            QTreeWidget::item:selected {{ background-color: {colors["ACCENT"]}; color: {colors["BG"]}; }}
            QHeaderView::section {{ background-color: {colors["DARK_ACCENT"]}; color: {colors["TEXT"]}; padding: 4px; border: none; border-right: 1px solid {colors["MEDIUM_GRAY"]}; border-bottom: 1px solid {colors["MEDIUM_GRAY"]}; font-size: 12px; font-weight: bold; }}
            QStatusBar {{ background-color: {colors["DARK_ACCENT"]}; color: {colors["TEXT"]}; font-size: 12px; padding: 4px 8px; }}
            QFrame#separator {{ background-color: {colors["MEDIUM_GRAY"]}; max-height: 1px; margin: 6px 0px; }}
            QToolTip {{ background-color: {colors["LIGHT_BG"]}; color: {colors["TEXT"]}; font-size: 12px; border: 1px solid {colors["DARK_ACCENT"]}; padding: 4px; }}
            CardWidget {{ background: {colors["LIGHT_BG"]}; border-radius: 4px; border: 1px solid {colors["DARK_ACCENT"]}; }}
            QTextEdit {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; background: {colors["LIGHT_BG"]}; color: {colors["TEXT"]}; padding: 4px; }}
            QProgressBar {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; background: {colors["LIGHT_BG"]}; text-align: center; }}
            QProgressBar::chunk {{ background-color: {colors["ACCENT"]}; border-radius: 2px; }}
        """)
    
    @staticmethod
    def adjust_brightness(color_hex, amount):
        color = QColor(color_hex)
        r, g, b = color.red(), color.green(), color.blue()
        r = max(0, min(255, r + amount))
        g = max(0, min(255, g + amount))
        b = max(0, min(255, b + amount))
        return f"#{r:02x}{g:02x}{b:02x}"

class AnimationHelper:
    @staticmethod
    def fade_in(widget, duration=300):
        widget.show()
    
    @staticmethod
    def add_drop_shadow(widget, radius=10, x_offset=2, y_offset=2, color=QColor(0, 0, 0, 80)):
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(radius)
        shadow.setXOffset(x_offset)
        shadow.setYOffset(y_offset)
        shadow.setColor(color)
        widget.setGraphicsEffect(shadow)

class ChartTreeWidget(QTreeWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAlternatingRowColors(True)
        self.setAnimated(True)
        self.setHeaderLabels(['씬 이름', 'Frames', 'FEET', '백업상태'])
        self.setSelectionMode(QTreeWidget.ExtendedSelection)
        header = self.header()
        header.setSectionResizeMode(QHeaderView.Interactive)
        header.setSectionsMovable(False)
        header.setStretchLastSection(False)
        self.setColumnWidth(0, 250)
        self.setColumnWidth(1, 80)
        self.setColumnWidth(2, 80)
        self.setColumnWidth(3, 80)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        AnimationHelper.add_drop_shadow(self)
        self.backup_status = {}

    def set_scene_backup_status(self, scene_name, status):
        """특정 씬의 백업 상태를 설정하고 UI를 업데이트합니다."""
        self.backup_status[scene_name] = status
        found = False
        for i in range(self.topLevelItemCount()):
            item = self.topLevelItem(i)
            if item.text(0) == scene_name:
                item.setText(3, status)  # 백업상태 컬럼에 상태 설정
                if status == "백업완료":
                    # 배경 색상은 설정하지 않고 텍스트만 표시
                    # 필요에 따라 텍스트 색상을 변경하려면 아래 주석을 해제
                    # item.setForeground(3, QColor("#FFFFFF"))  # 흰색 글씨 (선택 사항)
                    for col in range(4):
                        item.setBackground(col, QColor(StyleManager.DARK_THEME["LIGHT_BG"]))
                        item.setForeground(col, QColor(StyleManager.DARK_THEME["TEXT"]))
                else:
                    # 기본 스타일로 복원
                    for col in range(4):
                        item.setBackground(col, QColor(StyleManager.DARK_THEME["LIGHT_BG"]))
                        item.setForeground(col, QColor(StyleManager.DARK_THEME["TEXT"]))
                found = True
                break
        if not found:
            print(f"디버깅: 트리에서 {scene_name}을 찾을 수 없습니다.")
        self.viewport().update()  # 트리 뷰 강제 갱신
        QApplication.processEvents()  # UI 즉시 업데이트

    def get_scene_backup_status(self, scene_name):
        return self.backup_status.get(scene_name, "")

    def reset_backup_status(self):
        self.backup_status.clear()
        for i in range(self.topLevelItemCount()):
            item = self.topLevelItem(i)
            item.setText(3, "")
            for col in range(4):
                item.setBackground(col, QColor(StyleManager.DARK_THEME["LIGHT_BG"]))
                item.setForeground(col, QColor(StyleManager.DARK_THEME["TEXT"]))

class CardWidget(QFrame):
    def __init__(self, title=None, parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.StyledPanel)
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(10, 10, 10, 10)
        self.layout.setSpacing(8)
        if title:
            self.title = QLabel(title)
            self.title.setObjectName("headerLabel")
            self.layout.addWidget(self.title)
            separator = QFrame()
            separator.setFrameShape(QFrame.HLine)
            separator.setFrameShadow(QFrame.Sunken)
            separator.setObjectName("separator")
            self.layout.addWidget(separator)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        AnimationHelper.add_drop_shadow(self)

class ShowSelectionWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("작품 선택", parent)
        
        self.show_layout = QHBoxLayout()
        self.show_layout.setSpacing(8)
        show_label = QLabel("작품:")
        show_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.show_layout.addWidget(show_label)
        self.show_combo = QComboBox()
        self.show_combo.addItem("작품 선택")
        self.show_combo.addItems(["BB", "GN", "BM", "KOTH", "Test", "Yeson_DANG", "Yeson_Test", "Yeson_Test_4K"])
        self.show_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.show_layout.addWidget(self.show_combo)
        self.show_backup_btn = QPushButton("백업")
        self.show_backup_btn.setCursor(Qt.PointingHandCursor)
        self.show_backup_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.show_layout.addWidget(self.show_backup_btn)
        self.show_layout.addStretch()
        self.layout.addLayout(self.show_layout)
        
        self.season_layout = QHBoxLayout()
        self.season_layout.setSpacing(8)
        season_label = QLabel("시즌:")
        season_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.season_layout.addWidget(season_label)
        self.season_combo = QComboBox()
        self.season_combo.addItem("시즌 선택")
        self.season_combo.setEnabled(False)
        self.season_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.season_layout.addWidget(self.season_combo)
        self.season_backup_btn = QPushButton("백업")
        self.season_backup_btn.setCursor(Qt.PointingHandCursor)
        self.season_backup_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.season_layout.addWidget(self.season_backup_btn)
        self.season_layout.addStretch()
        self.layout.addLayout(self.season_layout)
        
        self.episode_layout = QHBoxLayout()
        self.episode_layout.setSpacing(8)
        episode_label = QLabel("화수:")
        episode_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.episode_layout.addWidget(episode_label)
        self.episode_combo = QComboBox()
        self.episode_combo.addItem("화수 선택")
        self.episode_combo.setEnabled(False)
        self.episode_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.episode_layout.addWidget(self.episode_combo)
        self.episode_backup_btn = QPushButton("백업")
        self.episode_backup_btn.setCursor(Qt.PointingHandCursor)
        self.episode_backup_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.episode_layout.addWidget(self.episode_backup_btn)
        self.episode_layout.addStretch()
        self.layout.addLayout(self.episode_layout)

class SummaryWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("에피소드 정보", parent)
        summary_layout = QHBoxLayout()
        summary_layout.setSpacing(10)
        frame_layout = QVBoxLayout()
        frame_title = QLabel("총 프레임 수:")
        frame_title.setAlignment(Qt.AlignCenter)
        frame_layout.addWidget(frame_title)
        self.total_frames_display = QLineEdit()
        self.total_frames_display.setAlignment(Qt.AlignCenter)
        self.total_frames_display.setReadOnly(True)
        self.total_frames_display.setMinimumWidth(80)
        self.total_frames_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        frame_layout.addWidget(self.total_frames_display)
        summary_layout.addLayout(frame_layout)
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("separator")
        summary_layout.addWidget(separator)
        feet_layout = QVBoxLayout()
        feet_title = QLabel("총 FEET:")
        feet_title.setAlignment(Qt.AlignCenter)
        feet_layout.addWidget(feet_title)
        self.episode_feet_display = QLineEdit()
        self.episode_feet_display.setAlignment(Qt.AlignCenter)
        self.episode_feet_display.setReadOnly(True)
        self.episode_feet_display.setMinimumWidth(80)
        self.episode_feet_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        feet_layout.addWidget(self.episode_feet_display)
        summary_layout.addLayout(feet_layout)
        self.layout.addLayout(summary_layout)

class CalculationWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("선택 씬 계산", parent)
        calc_layout = QHBoxLayout()
        calc_layout.setSpacing(8)
        self.calc_button = QPushButton("선택 씬 계산")
        self.calc_button.setCursor(Qt.PointingHandCursor)
        self.calc_button.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        calc_layout.addWidget(self.calc_button)
        calc_layout.addSpacing(10)
        feet_label = QLabel("선택된 씬 총 FEET:")
        feet_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        calc_layout.addWidget(feet_label)
        self.total_feet_display = QLineEdit()
        self.total_feet_display.setAlignment(Qt.AlignCenter)
        self.total_feet_display.setReadOnly(True)
        self.total_feet_display.setMinimumWidth(100)
        self.total_feet_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        calc_layout.addWidget(self.total_feet_display)
        calc_layout.addStretch()
        self.layout.addLayout(calc_layout)

class SceneListWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("씬 목록", parent)
        self.scene_tree = ChartTreeWidget()
        self.scene_tree.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.layout.addWidget(self.scene_tree)
        button_layout = QHBoxLayout()
        self.export_button = QPushButton("엑셀로 내보내기")
        self.export_button.setCursor(Qt.PointingHandCursor)
        self.export_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        button_layout.addWidget(self.export_button)
        button_layout.addStretch()
        selection_label = QLabel("Tip: 여러 씬을 선택하려면 Ctrl 또는 Shift 키를 사용하세요.")
        selection_label.setAlignment(Qt.AlignRight)
        selection_label.setStyleSheet("font-style: italic;")
        button_layout.addWidget(selection_label)
        self.layout.addLayout(button_layout)

class BackupStatusWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("백업 진행 상황", parent)
        self.setVisible(False)
        
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setMinimumHeight(100)
        self.layout.addWidget(self.log_display)

        # 로그 폰트 크기 조정
        font = QFont("Helvetica", 16)  # 원하는 폰트와 크기로 설정
        self.log_display.setFont(font)

        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.layout.addWidget(self.progress_bar)
    
    def append_log(self, message):
        """
        안전하게 로그를 추가합니다.
        메인 스레드에서만 호출되어야 합니다.
        """
        if message and message.strip():
            self.log_display.append(message)
            self.log_display.ensureCursorVisible()
            QApplication.processEvents()  # 로그 메시지 즉시 표시

    def set_progress(self, value):
        """
        안전하게 진행률을 설정합니다.
        메인 스레드에서만 호출되어야 합니다.
        """
        if 0 <= value <= 100:
            self.progress_bar.setValue(value)
            QApplication.processEvents()  # 진행 표시줄 즉시 업데이트
    
    def reset(self):
        """백업 상태 위젯을 초기화합니다."""
        self.log_display.clear()
        self.progress_bar.setValue(0)
        self.setVisible(False)

class BackupEventHandler(FileSystemEventHandler):
    def __init__(self, backup_queue, source_path, dest_path, status_widget, is_source=True):
        self.backup_queue = backup_queue
        self.source_path = source_path
        self.dest_path = dest_path
        self.status_widget = status_widget
        self.is_source = is_source
        self.pending_tasks = set()
        # 이벤트 축소를 위한 타이머
        self.debounce_timer = {}
        self.debounce_lock = threading.Lock()

    def on_created(self, event):
        if not event.is_directory:
            self._debounce_event(event.src_path, "created", False)

    def on_modified(self, event):
        if not event.is_directory:
            self._debounce_event(event.src_path, "modified", False)

    def on_deleted(self, event):
        self._debounce_event(event.src_path, "deleted", event.is_directory)

    def _debounce_event(self, src_path, event_type, is_directory):
        """
        이벤트를 축소하여 짧은 시간 내 동일 파일에 대한 중복 이벤트를 방지합니다.
        """
        with self.debounce_lock:
            key = (src_path, event_type)
            
            # 이전 타이머가 있으면 취소
            if key in self.debounce_timer and self.debounce_timer[key].is_alive():
                self.debounce_timer[key].cancel()
            
            # 새 타이머 설정 (0.5초 후 실행)
            timer = threading.Timer(0.5, self.queue_backup, args=[src_path, event_type, is_directory])
            timer.daemon = True  # 데몬 스레드로 설정
            self.debounce_timer[key] = timer
            timer.start()

    def queue_backup(self, src_path, event_type, is_directory=False):
        """
        백업 작업을 큐에 추가합니다.
        """
        try:
            rel_path = os.path.relpath(src_path, self.source_path if self.is_source else self.dest_path)
            task_key = (src_path, event_type)
            
            if self.is_source:
                dest_path = os.path.join(self.dest_path, rel_path)
                if os.path.exists(src_path):
                    if task_key not in self.pending_tasks:
                        self.backup_queue.put((src_path, dest_path, event_type, is_directory))
                        self.pending_tasks.add(task_key)
                        # 로그 메시지는 백업 워커에서 처리
            else:
                source_file = os.path.join(self.source_path, rel_path)
                dest_path = os.path.join(self.dest_path, rel_path)
                if event_type == "deleted" and os.path.exists(source_file):
                    self.backup_queue.put((source_file, dest_path, "created", is_directory))
                    self.pending_tasks.add((source_file, "created"))
                    # 로그 메시지는 백업 워커에서 처리
                elif event_type == "deleted":
                    # 소스 파일이 없는 경우는 처리하지 않음
                    pass
        except Exception as e:
            # 오류가 발생해도 백업 프로세스가 계속 실행되도록 함
            print(f"이벤트 처리 중 오류: {e}")

    def task_completed(self, src_path, event_type):
        """
        작업이 완료되었음을 표시합니다.
        """
        self.pending_tasks.discard((src_path, event_type))

class BackupWorker(QThread):
    update_signal = pyqtSignal(str, int)
    scene_backup_status_signal = pyqtSignal(str, str)

    def __init__(self, backup_queue, source_handler, dest_handler, status_widget, scene_tree=None, source_path=None):
        super().__init__()
        self.running = True
        self.current_set_active = False
        self.backup_queue = backup_queue
        self.source_handler = source_handler
        self.dest_handler = dest_handler
        self.status_widget = status_widget
        self.scene_tree = scene_tree
        self.source_path = source_path
        self.backed_up_scenes = set()
        self.last_progress = -1

    def extract_scene_name(self, path):
        if not path or not self.source_path:
            print(f"디버깅: 경로 또는 소스 경로가 없습니다 - path: {path}, source_path: {self.source_path}")
            return None
        try:
            rel_path = os.path.relpath(path, self.source_path)
            parts = rel_path.split(os.sep)
            for part in parts:
                if part.startswith('scene-'):
                    print(f"디버깅: 추출된 씬 이름 - {part}")
                    return part
            print(f"디버깅: 씬 이름 추출 실패 - rel_path: {rel_path}")
            return None
        except Exception as e:
            print(f"디버깅: 씬 이름 추출 중 오류 - {str(e)}")
            return None

    def is_scene_fully_backed_up(self, scene_name, dest_path):
        """씬 폴더가 완전히 백업되었는지 확인합니다."""
        scene_source_path = os.path.join(self.source_path, scene_name)
        scene_dest_path = os.path.join(dest_path, scene_name)
        if not os.path.exists(scene_source_path):
            print(f"디버깅: 소스 경로 존재하지 않음 - {scene_source_path}")
            return False
        if not os.path.exists(scene_dest_path):
            print(f"디버깅: 대상 경로 존재하지 않음 - {scene_dest_path}")
            return False
        source_files = set(os.listdir(scene_source_path))
        dest_files = set(os.listdir(scene_dest_path))
        if source_files == dest_files:
            print(f"디버깅: {scene_name} 백업 완료 - 파일 일치")
            return True
        else:
            print(f"디버깅: {scene_name} 백업 미완료 - 파일 불일치 (소스: {source_files}, 대상: {dest_files})")
            return False

    def run(self):
        while self.running:
            try:
                if self.backup_queue.empty() and self.current_set_active:
                    self.emit_progress("작업 세트 완료", 100)
                    self.total_tasks = 0
                    self.completed_tasks = 0
                    self.current_set_active = False
                    self.last_progress = -1
                    time.sleep(1)
                    continue
                
                try:
                    src_path, dest_path, event_type, is_directory = self.backup_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                if not self.current_set_active:
                    self.total_tasks = self.backup_queue.qsize() + 1
                    self.completed_tasks = 0
                    self.current_set_active = True
                    self.emit_progress(f"새 작업 세트 시작: 총 {self.total_tasks}개 작업", 0)
                
                if event_type == "deleted":
                    if os.path.exists(dest_path):
                        if is_directory:
                            shutil.rmtree(dest_path)
                            self.emit_progress(f"삭제됨: 폴더 {dest_path}", -1)
                        else:
                            os.remove(dest_path)
                            self.emit_progress(f"삭제됨: {dest_path}", -1)
                elif os.path.exists(src_path):
                    os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                    if is_directory:
                        if os.path.exists(dest_path):
                            shutil.rmtree(dest_path)
                        shutil.copytree(src_path, dest_path)
                        self.emit_progress(f"백업됨: 폴더 {src_path} -> {dest_path}", -1)
                    else:
                        shutil.copy2(src_path, dest_path)
                        self.emit_progress(f"백업됨: {src_path} -> {dest_path}", -1)
                    
                    scene_name = self.extract_scene_name(src_path)
                    if scene_name and scene_name not in self.backed_up_scenes:
                        dest_base = os.path.dirname(dest_path)
                        if self.is_scene_fully_backed_up(scene_name, dest_base):
                            self.backed_up_scenes.add(scene_name)
                            self.scene_backup_status_signal.emit(scene_name, "백업완료")
                            self.emit_progress(f"씬 [{scene_name}] 백업 완료", -1)
                
                self.completed_tasks += 1
                current_queue_size = self.backup_queue.qsize()
                if self.completed_tasks + current_queue_size > self.total_tasks:
                    self.total_tasks = self.completed_tasks + current_queue_size
                    self.emit_progress(f"작업이 추가됨: 총 {self.total_tasks}개로 업데이트", -1)
                
                progress = min(99, int(100 * self.completed_tasks / max(1, self.total_tasks)))
                if progress != self.last_progress:
                    self.emit_progress("", progress)
                    self.last_progress = progress
                
                self.backup_queue.task_done()
                self.source_handler.task_completed(src_path, event_type)
                self.dest_handler.task_completed(src_path, event_type)
                
            except Exception as e:
                self.emit_progress(f"백업 작업자 오류: {str(e)}", -1)
                time.sleep(0.5)
        
        if self.current_set_active:
            self.emit_progress("실시간 백업 완료", 100)

    def emit_progress(self, message, progress):
        if message:
            self.update_signal.emit(message, -1)
        if progress >= 0:
            self.update_signal.emit("", progress)

    def stop(self):
        self.running = False

class ValidationApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Yeson Entertainment 씬 관리 시스템")
        self.setGeometry(100, 100, 900, 700)
        self.setMinimumSize(600, 400)
        
        self.current_theme = "dark"
        app = QApplication.instance()
        StyleManager.apply_style(app, self.current_theme)
        
        self.path_manager = PathManager()
        self.calculator = FrameCalculator()
        self.db_manager = SceneDbManager(self.path_manager)
        
        self.backup_queue = queue.Queue()
        self.source_observer = None
        self.dest_observer = None
        self.backup_worker = None
        
        self.setup_ui()
        self.connect_signals()
        
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("애플리케이션이 준비되었습니다")
        
        self.theme_shortcut = QShortcut(QKeySequence("1"), self)
        self.theme_shortcut.activated.connect(self.toggle_theme)

    def setup_ui(self):
        self.main_widget = QWidget()
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidget(self.main_widget)
        self.scroll_area.setWidgetResizable(True)
        self.setCentralWidget(self.scroll_area)
        
        self.main_layout = QVBoxLayout()
        self.main_layout.setSpacing(10)
        self.main_layout.setContentsMargins(15, 15, 15, 15)
        
        header_layout = QHBoxLayout()
        self.app_title = QLabel("Yeson Entertainment 씬 피트 계산")
        self.app_title.setStyleSheet("font-size: 16px; font-weight: bold; padding: 8px;")
        header_layout.addWidget(self.app_title)
        header_layout.addStretch()
        
        self.theme_button = QPushButton("밝은 테마로 전환" if self.current_theme == "dark" else "다크 테마로 전환")
        self.theme_button.setCursor(Qt.PointingHandCursor)
        self.theme_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.theme_button.clicked.connect(self.toggle_theme)
        header_layout.addWidget(self.theme_button)
        
        self.main_layout.addLayout(header_layout)
        
        self.show_selection = ShowSelectionWidget()
        info_layout = QHBoxLayout()
        info_layout.setSpacing(10)
        self.summary = SummaryWidget()
        self.calculation = CalculationWidget()
        info_layout.addWidget(self.summary, 1)
        info_layout.addWidget(self.calculation, 1)

        self.scene_list_widget = SceneListWidget()
        
        # Add QSplitter between SceneListWidget and BackupStatusWidget
        self.splitter = QSplitter(Qt.Vertical)
        self.splitter.addWidget(self.scene_list_widget)
        self.backup_status = BackupStatusWidget()
        self.splitter.addWidget(self.backup_status)
        
        self.main_layout.addWidget(self.show_selection)
        self.main_layout.addLayout(info_layout)
        self.main_layout.addWidget(self.splitter)  # Use the splitter here
        
        self.main_widget.setLayout(self.main_layout)
    
    def toggle_theme(self):
        self.current_theme = "light" if self.current_theme == "dark" else "dark"
        self.theme_button.setText("밝은 테마로 전환" if self.current_theme == "dark" else "다크 테마로 전환")
        StyleManager.apply_style(QApplication.instance(), self.current_theme)
        self.statusBar.showMessage(f"{self.current_theme.capitalize()} 테마로 전환되었습니다.")
    
    def connect_signals(self):
        self.show_selection.show_combo.currentTextChanged.connect(self.on_show_changed)
        self.show_selection.season_combo.currentTextChanged.connect(self.on_season_changed)
        self.show_selection.episode_combo.currentTextChanged.connect(self.on_episode_changed)
        self.calculation.calc_button.clicked.connect(self.calculate_selected_scenes)
        self.scene_list_widget.export_button.clicked.connect(self.export_to_excel)
        
        self.show_selection.show_backup_btn.clicked.connect(self.backup_show)
        self.show_selection.season_backup_btn.clicked.connect(self.backup_season)
        self.show_selection.episode_backup_btn.clicked.connect(self.backup_episode)
    
    def on_show_changed(self, show):
        self.scene_list_widget.scene_tree.clear()
        self.show_selection.season_combo.clear()
        self.show_selection.episode_combo.clear()
        self.show_selection.season_combo.addItem("시즌 선택")
        self.show_selection.episode_combo.addItem("화수 선택")
        if show == "작품 선택":
            self.show_selection.season_combo.setEnabled(False)
            self.show_selection.episode_combo.setEnabled(False)
            self.statusBar.showMessage("작품을 선택해주세요")
            return
        project_path = self.path_manager.get_show_path(show)
        if not project_path:
            self.statusBar.showMessage(f"{show} 작품의 경로를 찾을 수 없습니다")
            return
        if show == "Yeson_DANG":
            self.show_selection.season_combo.setEnabled(False)
            self.show_selection.episode_combo.setEnabled(False)
            self.load_scene_folders(project_path)
            self.statusBar.showMessage(f"{show} 작품이 선택되었습니다")
            return
        self.show_selection.season_combo.setEnabled(True)
        if show in ["BB", "GN", "BM", "KOTH", "Test"]:
            seasons = self.path_manager.show_paths[show]["seasons"]
        elif show in ["Yeson_Test", "Yeson_Test_4K"]:
            try:
                seasons = [item for item in os.listdir(project_path) 
                        if os.path.isdir(os.path.join(project_path, item)) 
                        and not item.startswith('.') and item != "DANG"]
                seasons.sort()
            except Exception as e:
                print(f"시즌 폴더 검색 중 오류: {e}")
                seasons = []
        self.show_selection.season_combo.addItems(seasons)
        self.show_selection.episode_combo.setEnabled(False)
        self.statusBar.showMessage(f"{show} 작품이 선택되었습니다. 시즌을 선택해주세요.")
    
    def on_season_changed(self, season):
        self.scene_list_widget.scene_tree.clear()
        if season == "시즌 선택":
            self.show_selection.episode_combo.clear()
            self.show_selection.episode_combo.addItem("화수 선택")
            self.show_selection.episode_combo.setEnabled(False)
            return
        show = self.show_selection.show_combo.currentText()
        project_path = self.path_manager.get_show_path(show)
        if not project_path:
            return
        if show in ["Test", "Yeson_Test", "Yeson_Test_4K"]:
            season_path = os.path.join(project_path, season)
            self.load_scene_folders(season_path)
            self.show_selection.episode_combo.setEnabled(False)
            self.statusBar.showMessage(f"{show} - {season} 시즌이 선택되었습니다.")
            return
        self.show_selection.episode_combo.clear()
        self.show_selection.episode_combo.addItem("화수 선택")
        self.show_selection.episode_combo.setEnabled(True)
        self.update_episode_list(show, season, project_path)
        self.statusBar.showMessage(f"{show} - {season} 시즌이 선택되었습니다. 화수를 선택해주세요.")
    
    def on_episode_changed(self, episode):
        self.scene_list_widget.scene_tree.clear()
        if episode == "화수 선택":
            return
        show = self.show_selection.show_combo.currentText()
        season = self.show_selection.season_combo.currentText()
        project_path = self.path_manager.get_show_path(show)
        if not project_path:
            return
        episode_path = os.path.join(project_path, season, episode)
        self.load_scene_folders(episode_path)
        self.update_scene_list()
        self.show_selection.show_combo.repaint()
        self.show_selection.season_combo.repaint()
        self.show_selection.episode_combo.repaint()
        self.statusBar.showMessage(f"{show} - {season} - {episode} 화수가 로드되었습니다.")
    
    def load_scene_folders(self, path):
        if not os.path.exists(path):
            self.statusBar.showMessage(f"경로를 찾을 수 없습니다: {path}")
            return
        scene_folders = []
        for item in os.listdir(path):
            if os.path.isdir(os.path.join(path, item)) and item.startswith('scene-'):
                scene_folders.append(item)
        for scene in sorted(scene_folders):
            item = QTreeWidgetItem([scene])
            self.scene_list_widget.scene_tree.addTopLevelItem(item)
        if scene_folders:
            self.statusBar.showMessage(f"{len(scene_folders)}개의 씬 폴더를 찾았습니다.")
        else:
            self.statusBar.showMessage("씬 폴더를 찾을 수 없습니다.")
    
    def update_episode_list(self, show, season, project_path):
        season_path = os.path.join(project_path, season)
        if not os.path.exists(season_path):
            self.statusBar.showMessage(f"시즌 경로를 찾을 수 없습니다: {season_path}")
            return
        episodes = []
        if show == "BB":
            for item in os.listdir(season_path):
                if "Season13" in season and "DASA" in item:
                    episodes.append(item)
                elif "Season14" in season and "EASA" in item:
                    episodes.append(item)
        elif show == "GN":
            if "GN_Season" in season:
                season_num = season.replace("GN_Season", "")
                for item in os.listdir(season_path):
                    if item.startswith(f"{season_num}LBW"):
                        episodes.append(item)
        elif show == "BM":
            for item in os.listdir(season_path):
                if item.startswith("BM_8"):
                    episodes.append(item)
        elif show == "KOTH":
            for item in os.listdir(season_path):
                if "Season14" in season and item.startswith("EABE"):
                    episodes.append(item)
                elif "Season15" in season and item.startswith("15"):
                    episodes.append(item)
        self.show_selection.episode_combo.addItems(sorted(episodes))
        if episodes:
            self.statusBar.showMessage(f"{len(episodes)}개의 에피소드를 찾았습니다.")
        else:
            self.statusBar.showMessage("에피소드를 찾을 수 없습니다.")
    
    def update_scene_list(self):
        show = self.show_selection.show_combo.currentText()
        episode = self.show_selection.episode_combo.currentText()
        season = self.show_selection.season_combo.currentText()
        if show in ["Test", "Yeson_DANG", "Yeson_Test", "Yeson_Test_4K"]:
            identifier = season if show in ["Test", "Yeson_Test", "Yeson_Test_4K"] else show
            frames_info = self.db_manager.get_frames_info(identifier)
        else:
            frames_info = self.db_manager.get_frames_info(episode)
        if not frames_info:
            self.statusBar.showMessage("프레임 정보를 가져올 수 없습니다.")
            return
        latest_scenes = self.db_manager.filter_scene_folders(frames_info)
        total_frames = sum(int(frames_info[scene]) for scene in latest_scenes.values())
        total_feet = self.calculator.calculate_sheet_length(total_frames)
        if show in ["Test", "Yeson_DANG", "Yeson_Test", "Yeson_Test_4K"]:
            self.update_simple_scene_list(latest_scenes, frames_info)
        else:
            self.update_sequence_scene_list(latest_scenes, frames_info, show)
        self.summary.total_frames_display.setText(str(total_frames))
        self.summary.episode_feet_display.setText(self.calculator.format_feet_display(total_feet))
        self.statusBar.showMessage(f"씬 목록이 업데이트되었습니다. 총 프레임: {total_frames}, 총 FEET: {self.calculator.format_feet_display(total_feet)}")
    
    def update_simple_scene_list(self, latest_scenes, frames_info):
        items = []
        for scene_name in sorted(latest_scenes.values()):
            frames = frames_info[scene_name]
            sheet_length = self.calculator.calculate_sheet_length(frames)
            feet_display = self.calculator.format_feet_display(sheet_length)
            item = QTreeWidgetItem([scene_name, str(frames), feet_display, ""])  # 백업상태 컬럼 추가
            items.append(item)
        self.scene_list_widget.scene_tree.clear()
        self.scene_list_widget.scene_tree.addTopLevelItems(items)
        # 백업 상태 초기화
        self.scene_list_widget.scene_tree.reset_backup_status()

    def update_sequence_scene_list(self, latest_scenes, frames_info, show):
        items = []
        for scene_name in sorted(latest_scenes.values()):
            if show == "BM":
                scene_num = scene_name.replace('scene-', '')
                seq = scene_num[0]
            elif show == "KOTH":
                scene_num = scene_name.replace('scene-', '')
                try:
                    scene_number = int(scene_num[:3])
                    if scene_number < 300:
                        seq = "Act1"
                    elif scene_number < 600:
                        seq = "Act2"
                    else:
                        seq = "Act3"
                except ValueError:
                    seq = "-"
            else:
                seq = scene_name.split('_')[0]
            frames = frames_info[scene_name]
            sheet_length = self.calculator.calculate_sheet_length(frames)
            feet_display = self.calculator.format_feet_display(sheet_length)
            item = QTreeWidgetItem([scene_name, str(frames), feet_display, ""])  # 백업상태 컬럼 추가
            items.append(item)
        self.scene_list_widget.scene_tree.clear()
        self.scene_list_widget.scene_tree.addTopLevelItems(items)
        # 백업 상태 초기화
        self.scene_list_widget.scene_tree.reset_backup_status()
    
    def calculate_selected_scenes(self):
        selected_items = self.scene_list_widget.scene_tree.selectedItems()
        if not selected_items:
            self.calculation.total_feet_display.setText("0F 00f")
            self.statusBar.showMessage("선택된 씬이 없습니다.")
            return
        total_frames = sum(int(item.text(1)) for item in selected_items)
        total_feet = self.calculator.calculate_sheet_length(total_frames)
        feet_display = self.calculator.format_feet_display(total_feet)
        self.calculation.total_feet_display.setText(feet_display)
        self.statusBar.showMessage(f"{len(selected_items)}개 씬이 선택되었습니다. 총 프레임: {total_frames}, 총 FEET: {feet_display}")
    
    def export_to_excel(self):
        tree = self.scene_list_widget.scene_tree
        if tree.topLevelItemCount() == 0:
            self.statusBar.showMessage("내보낼 씬 목록이 없습니다.")
            return
        
        show = self.show_selection.show_combo.currentText()
        season = self.show_selection.season_combo.currentText()
        episode = self.show_selection.episode_combo.currentText()
        total_frames = self.summary.total_frames_display.text()
        total_feet = self.summary.episode_feet_display.text()
        
        scene_data = []
        for index in range(tree.topLevelItemCount()):
            item = tree.topLevelItem(index)
            scene_data.append([item.text(0), item.text(1), item.text(2)])
        
        default_filename = f"{show}_{season}_{episode}_scenes.xlsx" if show != "작품 선택" else "scenes.xlsx"
        file_path, _ = QFileDialog.getSaveFileName(self, "엑셀 파일로 저장", default_filename, "Excel Files (*.xlsx)")
        if not file_path:
            self.statusBar.showMessage("파일 저장이 취소되었습니다.")
            return
        
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "SceneList"
            
            colors = StyleManager.DARK_THEME if self.current_theme == "dark" else StyleManager.LIGHT_THEME
            
            header_font = Font(name="Helvetica", size=14, bold=True, color=colors["ACCENT"][1:])
            subheader_font = Font(name="Helvetica", size=12, bold=True, color=colors["TEXT"][1:])
            text_font = Font(name="Helvetica", size=12, color=colors["TEXT"][1:])
            border = Border(left=Side(style="thin", color=colors["DARK_ACCENT"][1:]),
                            right=Side(style="thin", color=colors["DARK_ACCENT"][1:]),
                            top=Side(style="thin", color=colors["DARK_ACCENT"][1:]),
                            bottom=Side(style="thin", color=colors["DARK_ACCENT"][1:]))
            fill_bg = PatternFill(start_color=colors["BG"][1:], end_color=colors["BG"][1:], fill_type="solid")
            fill_light_bg = PatternFill(start_color=colors["LIGHT_BG"][1:], end_color=colors["LIGHT_BG"][1:], fill_type="solid")
            fill_header = PatternFill(start_color=colors["DARK_ACCENT"][1:], end_color=colors["DARK_ACCENT"][1:], fill_type="solid")
            fill_accent = PatternFill(start_color=colors["ACCENT"][1:], end_color=colors["ACCENT"][1:], fill_type="solid")
            center_align = Alignment(horizontal="center", vertical="center")
            
            for row in ws.iter_rows(min_row=1, max_row=100, min_col=1, max_col=10):
                for cell in row:
                    cell.fill = fill_bg
            
            ws["A1"] = "작품 선택"
            ws["A1"].font = header_font
            ws["A1"].fill = fill_light_bg
            ws["A1"].border = border
            ws["A1"].alignment = center_align
            ws.merge_cells("A1:C1")
            
            ws["A2"] = "작품"
            ws["B2"] = show
            ws["A3"] = "시즌"
            ws["B3"] = season
            ws["A4"] = "화수"
            ws["B4"] = episode
            for row in ws["A2:C4"]:
                for cell in row:
                    cell.font = text_font
                    cell.fill = fill_light_bg
                    cell.border = border
                    cell.alignment = center_align
            ws["C2"] = "선택 정보"
            ws["C2"].fill = fill_header
            
            ws["A6"] = "에피소드 정보"
            ws["A6"].font = header_font
            ws["A6"].fill = fill_light_bg
            ws["A6"].border = border
            ws["A6"].alignment = center_align
            ws.merge_cells("A6:C6")
            
            ws["A7"] = "총 프레임 수"
            ws["B7"] = total_frames
            ws["A8"] = "총 FEET"
            ws["B8"] = total_feet
            for row in ws["A7:B8"]:
                for cell in row:
                    cell.font = text_font
                    cell.fill = fill_light_bg
                    cell.border = border
                    cell.alignment = center_align
            ws["C7"] = "요약"
            ws["C7"].fill = fill_header
            ws["C8"] = ""
            ws["C8"].fill = fill_header
            
            ws["A10"] = "씬 목록"
            ws["A10"].font = header_font
            ws["A10"].fill = fill_light_bg
            ws["A10"].border = border
            ws["A10"].alignment = center_align
            ws.merge_cells("A10:C10")
            
            headers = ["씬 이름", "Frames", "FEET"]
            for col, header in enumerate(headers, start=1):
                cell = ws.cell(row=11, column=col, value=header)
                cell.font = subheader_font
                cell.fill = fill_header
                cell.border = border
                cell.alignment = center_align
            
            for row_idx, scene in enumerate(scene_data, start=12):
                for col_idx, value in enumerate(scene, start=1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    cell.font = text_font
                    cell.fill = fill_light_bg
                    cell.border = border
                    cell.alignment = center_align
            
            ws.column_dimensions["A"].width = 40
            ws.column_dimensions["B"].width = 15
            ws.column_dimensions["C"].width = 15
            
            ws["A5"].fill = fill_accent
            ws["B5"].fill = fill_accent
            ws["C5"].fill = fill_accent
            ws["A9"].fill = fill_accent
            ws["B9"].fill = fill_accent
            ws["C9"].fill = fill_accent
            
            wb.save(file_path)
            self.statusBar.showMessage(f"씬 목록이 {file_path}에 저장되었습니다.")
        except Exception as e:
            self.statusBar.showMessage(f"엑셀 파일 저장 중 오류 발생: {e}")

    def backup_show(self):
        show = self.show_selection.show_combo.currentText()
        if show == "작품 선택":
            self.statusBar.showMessage("백업할 작품을 선택해주세요.")
            return
        source_path = self.path_manager.get_show_path(show)
        if not source_path or not os.path.exists(source_path):
            self.statusBar.showMessage(f"{show} 작품의 경로를 찾을 수 없습니다.")
            return
        dest_dir = QFileDialog.getExistingDirectory(self, "백업 경로 선택", os.path.expanduser("~"))
        if not dest_dir:
            self.statusBar.showMessage("백업 경로 선택이 취소되었습니다.")
            return
        dest_path = os.path.join(dest_dir, show)
        os.makedirs(dest_path, exist_ok=True)
        self.start_realtime_backup(source_path, dest_path)

    def backup_season(self):
        show = self.show_selection.show_combo.currentText()
        season = self.show_selection.season_combo.currentText()
        if show == "작품 선택" or season == "시즌 선택":
            self.statusBar.showMessage("백업할 시즌을 선택해주세요.")
            return
        source_path = os.path.join(self.path_manager.get_show_path(show), season)
        if not os.path.exists(source_path):
            self.statusBar.showMessage(f"{show} - {season} 시즌의 경로를 찾을 수 없습니다.")
            return
        dest_dir = QFileDialog.getExistingDirectory(self, "백업 경로 선택", os.path.expanduser("~"))
        if not dest_dir:
            self.statusBar.showMessage("백업 경로 선택이 취소되었습니다.")
            return
        show_path = os.path.join(dest_dir, show)
        dest_path = os.path.join(show_path, season)
        os.makedirs(dest_path, exist_ok=True)
        self.start_realtime_backup(source_path, dest_path)

    def backup_episode(self):
        show = self.show_selection.show_combo.currentText()
        season = self.show_selection.season_combo.currentText()
        episode = self.show_selection.episode_combo.currentText()
        if show == "작품 선택" or season == "시즌 선택" or episode == "화수 선택":
            self.statusBar.showMessage("백업할 화수를 선택해주세요.")
            return
        source_path = os.path.join(self.path_manager.get_show_path(show), season, episode)
        if not os.path.exists(source_path):
            self.statusBar.showMessage(f"{show} - {season} - {episode} 화수의 경로를 찾을 수 없습니다.")
            return
        dest_dir = QFileDialog.getExistingDirectory(self, "백업 경로 선택", os.path.expanduser("~"))
        if not dest_dir:
            self.statusBar.showMessage("백업 경로 선택이 취소되었습니다.")
            return
        show_path = os.path.join(dest_dir, show)
        season_path = os.path.join(show_path, season)
        dest_path = os.path.join(season_path, episode)
        os.makedirs(dest_path, exist_ok=True)
        self.start_realtime_backup(source_path, dest_path)

    def perform_backup(self, source_path, dest_path):
        """
        초기 백업을 수행하며 실시간으로 로그를 업데이트합니다.
        각 씬의 백업 완료 여부를 개별적으로 확인하고 표시합니다.
        """
        try:
            self.backup_status.setVisible(True)
            QApplication.processEvents()

            try:
                # 환경 변수 설정 추가 (rsync 인코딩 문제 해결 시도)
                my_env = os.environ.copy()
                my_env["LANG"] = "ko_KR.UTF-8"
                my_env["LC_ALL"] = "ko_KR.UTF-8"

                result = subprocess.run(['rsync', '--version'], check=True,
                                        stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                        text=True, encoding='utf-8', env=my_env)
                version_line = result.stdout.split('\n')[0]
                self.backup_status.append_log(f"rsync 버전: {version_line}")
                QApplication.processEvents()
            except (subprocess.SubprocessError, FileNotFoundError):
                self.backup_status.append_log("오류: rsync가 설치되어 있지 않습니다.")
                QApplication.processEvents()
                self.statusBar.showMessage("rsync가 설치되어 있지 않습니다.")
                return False
            
            # rsync 명령 구성
            rsync_cmd = [
                'rsync', 
                '-avhi', 
                '--progress',
                source_path + '/', 
                dest_path
            ]
            
            self.backup_status.append_log("rsync를 사용하여 초기 동기화 중...")
            self.backup_status.set_progress(0)
            QApplication.processEvents()  # 로그 업데이트 즉시 표시
            
            # rsync 프로세스 실행
            process = subprocess.Popen(rsync_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                                    text=True, bufsize=1, universal_newlines=True)
            
            files_processed = 0
            total_files_est = 100  # 초기 추정값
            
            # 씬 폴더 목록 및 완료 추적
            scene_folders = set()
            completed_scenes = set()
            scene_files_map = {}  # 각 씬별 파일 목록 추적
            
            # 씬 폴더 목록 초기화
            for i in range(self.scene_list_widget.scene_tree.topLevelItemCount()):
                item = self.scene_list_widget.scene_tree.topLevelItem(i)
                scene_name = item.text(0)
                scene_folders.add(scene_name)
                scene_files_map[scene_name] = set()
                
                # 소스 폴더의 파일 목록 가져오기
                scene_source_path = os.path.join(source_path, scene_name)
                if os.path.exists(scene_source_path) and os.path.isdir(scene_source_path):
                    scene_files_map[scene_name] = set(os.listdir(scene_source_path))
            
            # 실시간으로 출력 읽기
            while process.poll() is None:
                line = process.stdout.readline().strip()
                if line:
                    # 진행률 정보 추출
                    if "to-check=" in line:
                        try:
                            check_part = line.split("to-check=")[1].split(")")[0]
                            remaining, total = map(int, check_part.split("/"))
                            files_processed = total - remaining
                            total_files_est = total
                            progress = min(99, int(100 * files_processed / total_files_est))
                            self.backup_status.set_progress(progress)
                            QApplication.processEvents()  # 진행 표시줄 업데이트 즉시 표시
                        except (IndexError, ValueError):
                            pass
                    # 파일 전송 로그 표시
                    elif line.startswith(">") and not any(x in line for x in ["to-check=", "speedup"]):
                        # 파일 경로에서 씬 폴더 이름 추출
                        try:
                            file_path = line.split(" ")[1]
                            rel_path = os.path.relpath(file_path, source_path) if source_path in file_path else file_path
                            path_parts = rel_path.split(os.sep)
                            
                            # 찾은 파일이 씬 폴더에 속하는지 확인
                            for scene_name in scene_folders:
                                if path_parts[0] == scene_name:
                                    # 해당 씬에 파일 복사 기록
                                    scene_dest_path = os.path.join(dest_path, scene_name)
                                    
                                    # 씬 폴더가 완전히 백업되었는지 주기적으로 확인
                                    if os.path.exists(scene_dest_path) and scene_name not in completed_scenes:
                                        dest_files = set(os.listdir(scene_dest_path))
                                        source_files = scene_files_map.get(scene_name, set())
                                        
                                        # 모든 파일이 복사되었는지 확인
                                        if source_files and source_files.issubset(dest_files):
                                            completed_scenes.add(scene_name)
                                            self.scene_list_widget.scene_tree.set_scene_backup_status(scene_name, "백업완료")
                                            self.backup_status.append_log(f"씬 [{scene_name}] 백업 완료")
                                            QApplication.processEvents()
                                    break
                        except Exception as e:
                            print(f"파일 경로 처리 중 오류: {str(e)}")
                        
                        self.backup_status.append_log(f"동기화 중: {line}")
                        QApplication.processEvents()  # 로그 업데이트 즉시 표시
                        files_processed += 1
                    
                # 주기적으로 모든 씬의 백업 상태 확인 (100개 파일마다)
                if files_processed % 100 == 0:
                    self.check_scenes_backup_status(source_path, dest_path, scene_folders, completed_scenes, scene_files_map)
            
            # 남은 출력 처리
            stdout, stderr = process.communicate()
            if stderr:
                self.backup_status.append_log(f"초기 동기화 중 오류: {stderr.strip()}")
                QApplication.processEvents()
            
            # rsync 완료 후 마지막으로 모든 씬의 백업 상태 확인
            self.check_scenes_backup_status(source_path, dest_path, scene_folders, completed_scenes, scene_files_map)
            
            if process.returncode == 0:
                self.backup_status.set_progress(100)
                self.backup_status.append_log("초기 동기화 완료")
                QApplication.processEvents()
                self.statusBar.showMessage("초기 동기화가 완료되었습니다.")
                return True
            else:
                self.backup_status.append_log(f"초기 동기화 실패: rsync 종료 코드 {process.returncode}")
                QApplication.processEvents()
                self.statusBar.showMessage("초기 동기화에 실패했습니다.")
                return False
                
        except Exception as e:
            self.backup_status.append_log(f"초기 동기화 중 예외 발생: {str(e)}")
            QApplication.processEvents()
            self.statusBar.showMessage("초기 동기화 중 오류가 발생했습니다.")
            return False

    def check_scenes_backup_status(self, source_path, dest_path, scene_folders, completed_scenes, scene_files_map):
        """각 씬 폴더의 백업 완료 상태를 확인하고 업데이트합니다."""
        for scene_name in scene_folders:
            if scene_name in completed_scenes:
                continue
                
            scene_source_path = os.path.join(source_path, scene_name)
            scene_dest_path = os.path.join(dest_path, scene_name)
            
            if os.path.exists(scene_source_path) and os.path.exists(scene_dest_path):
                source_files = scene_files_map.get(scene_name, set())
                if not source_files:  # 필요한 경우 소스 파일 목록 업데이트
                    source_files = set(os.listdir(scene_source_path))
                    scene_files_map[scene_name] = source_files
                    
                dest_files = set(os.listdir(scene_dest_path))
                
                # 소스의 모든 파일이 대상에 있는지 확인
                if source_files and source_files.issubset(dest_files):
                    completed_scenes.add(scene_name)
                    self.scene_list_widget.scene_tree.set_scene_backup_status(scene_name, "백업완료")
                    self.backup_status.append_log(f"씬 [{scene_name}] 백업 완료")
                    QApplication.processEvents()

    def start_realtime_backup(self, source_path, dest_path):
        if self.source_observer and self.source_observer.is_alive():
            self.stop_realtime_backup()

        self.backup_status.reset()
        self.backup_status.setVisible(True)
        self.backup_status.append_log(f"백업 프로세스 시작: {source_path} -> {dest_path}")
        self.statusBar.showMessage("초기 파일 복사를 시작합니다...")
        QApplication.processEvents()

        if not self.perform_backup(source_path, dest_path):
            self.backup_status.append_log("초기 파일 복사 실패로 백업을 중단합니다.")
            self.statusBar.showMessage("초기 파일 복사에 실패했습니다.")
            return

        if not os.listdir(dest_path):
            self.backup_status.append_log("초기 복사 후 대상 디렉토리가 비어 있습니다.")
            self.statusBar.showMessage("초기 복사 실패: 대상 디렉토리가 비어 있습니다.")
            return

        self.scene_list_widget.scene_tree.reset_backup_status()

        # 초기 동기화 후 모든 씬의 백업 상태 확인
        self.check_all_scenes_backup_status(source_path, dest_path)

        try:
            source_handler = BackupEventHandler(self.backup_queue, source_path, dest_path, self.backup_status, is_source=True)
            self.source_observer = Observer()
            self.source_observer.schedule(source_handler, source_path, recursive=True)
            self.source_observer.start()
            self.backup_status.append_log(f"소스 디렉토리 감시 시작: {source_path}")

            dest_handler = BackupEventHandler(self.backup_queue, source_path, dest_path, self.backup_status, is_source=False)
            self.dest_observer = Observer()
            self.dest_observer.schedule(dest_handler, dest_path, recursive=True)
            self.dest_observer.start()
            self.backup_status.append_log(f"대상 디렉토리 감시 시작: {dest_path}")

            self.backup_worker = BackupWorker(
                self.backup_queue, 
                source_handler, 
                dest_handler, 
                self.backup_status,
                self.scene_list_widget.scene_tree,
                source_path
            )
            self.backup_worker.update_signal.connect(self.update_backup_status)
            self.backup_worker.scene_backup_status_signal.connect(self.update_scene_backup_status)
            self.backup_worker.start()

            self.statusBar.showMessage("실시간 백업이 시작되었습니다.")
            QApplication.processEvents()
        except Exception as e:
            self.backup_status.append_log(f"실시간 백업 시작 중 오류: {str(e)}")
            self.statusBar.showMessage("실시간 백업 시작 중 오류가 발생했습니다.")
            self.stop_realtime_backup()

    def update_scene_backup_status(self, scene_name, status):
        """BackupWorker로부터 받은 씬 백업 상태를 업데이트합니다."""
        if self.scene_list_widget and self.scene_list_widget.scene_tree:
            print(f"디버깅: {scene_name}의 백업 상태 업데이트 - {status}")
            self.scene_list_widget.scene_tree.set_scene_backup_status(scene_name, status)
            self.statusBar.showMessage(f"씬 [{scene_name}]의 백업 상태가 '{status}'(으)로 업데이트되었습니다.")
        else:
            print("디버깅: 씬 목록 위젯 또는 트리가 초기화되지 않았습니다.")

    def check_all_scenes_backup_status(self, source_path, dest_path):
        """모든 씬의 백업 상태를 확인하고 업데이트합니다."""
        if not self.scene_list_widget or not self.scene_list_widget.scene_tree:
            print("디버깅: 씬 목록 위젯 또는 트리가 초기화되지 않았습니다.")
            return

        tree = self.scene_list_widget.scene_tree
        for i in range(tree.topLevelItemCount()):
            item = tree.topLevelItem(i)
            scene_name = item.text(0)
            scene_source_path = os.path.join(source_path, scene_name)
            scene_dest_path = os.path.join(dest_path, scene_name)
            if os.path.exists(scene_source_path) and os.path.exists(scene_dest_path):
                source_files = set(os.listdir(scene_source_path))
                dest_files = set(os.listdir(scene_dest_path))
                if source_files == dest_files:
                    print(f"디버깅: 초기 동기화 후 {scene_name} 백업 완료 확인")
                    self.scene_list_widget.scene_tree.set_scene_backup_status(scene_name, "백업완료")

    def stop_realtime_backup(self):
        """실시간 백업을 중지합니다."""
        try:
            # 감시자 중지
            if self.source_observer and self.source_observer.is_alive():
                self.source_observer.stop()
                self.source_observer.join(timeout=3)  # 최대 3초 대기
                self.backup_status.append_log("소스 디렉토리 감시가 중지되었습니다.")
            
            if self.dest_observer and self.dest_observer.is_alive():
                self.dest_observer.stop()
                self.dest_observer.join(timeout=3)  # 최대 3초 대기
                self.backup_status.append_log("대상 디렉토리 감시가 중지되었습니다.")
            
            # 백업 작업자 중지
            if self.backup_worker and self.backup_worker.isRunning():
                self.backup_worker.stop()
                self.backup_worker.wait(3000)  # 최대 3초 대기
                self.backup_status.append_log("백업 작업자가 중지되었습니다.")
            
            self.statusBar.showMessage("실시간 백업이 중지되었습니다.")
        except Exception as e:
            self.backup_status.append_log(f"백업 중지 중 오류: {str(e)}")
            self.statusBar.showMessage("백업 중지 중 오류가 발생했습니다.")

    def update_backup_status(self, message, progress):
        if message and message.strip():
            self.backup_status.log_display.append(message)
            self.backup_status.log_display.ensureCursorVisible()
            QApplication.processEvents()  # 로그 메시지 즉시 표시
        
        if progress >= 0:
            self.backup_status.progress_bar.setValue(progress)
            QApplication.processEvents()  # 진행 표시줄 즉시 업데이트
            
            if progress == 100 and "완료" in message:
                self.backup_status.log_display.append("모든 백업 작업이 완료되었습니다.")
                self.backup_status.log_display.ensureCursorVisible()
                QApplication.processEvents()  # 완료 메시지 즉시 표시

    def closeEvent(self, event):
        self.stop_realtime_backup()
        event.accept()

class PathManager:
    def __init__(self):
        self.show_paths = {
            "BB": {"base": "Bento_Project", "seasons": ["BB_Season13", "BB_Season14"]},
            "GN": {"base": "Bento_Project2/Great_North", "seasons": ["GN_Season4", "GN_Season5"]},
            "BM": {"base": "Titmouse/Big_Mouth", "seasons": ["BM_Season8"]},
            "KOTH": {"base": "Disney/KOTH", "seasons": ["KOTH_Season14", "KOTH_Season15"]},
            "Test": {"base": "Test", "seasons": ["TEST_SYSTEM"]},
            "Yeson_DANG": {"base": "Yeson_Test/DANG", "seasons": []},
            "Yeson_Test": {"base": "Yeson_Test", "seasons": []},
            "Yeson_Test_4K": {"base": "Yeson_Test_4K", "seasons": []}
        }
        self.possible_paths = [
            "/usadata2", "/usadata3", "/System/Volumes/Data/mnt/usadata2", 
            "/System/Volumes/Data/mnt/usadata3", "/System/Volumes/data/mnt/usadata2", 
            "/System/Volumes/data/mnt/usadata3", "/System/Volumes/Data/System/Volumes/Data/mnt/usadata2", 
            "/System/Volumes/Data/System/Volumes/Data/mnt/usadata3", 
            "/System/Volumes/data/System/Volumes/data/mnt/usadata2", 
            "/System/Volumes/data/System/Volumes/data/mnt/usadata3"
        ]
        self.db_paths = [
            "/USA_DB/db_jobs", "/System/Volumes/Data/mnt/USA_DB/db_jobs", 
            "/System/Volumes/data/mnt/USA_DB/db_jobs", 
            "/System/Volumes/Data/System/Volumes/Data/mnt/USA_DB/db_jobs", 
            "/System/Volumes/data/System/Volumes/data/mnt/USA_DB/db_jobs"
        ]
    
    def get_show_path(self, show):
        if show == "작품 선택":
            return None
        for base_path in self.possible_paths:
            if not os.path.exists(base_path):
                continue
            if show == "BB":
                project_path = os.path.join(base_path, "Bento_Project")
            elif show == "GN":
                project_path = os.path.join(base_path, "Bento_Project2", "Great_North")
            elif show == "BM":
                project_path = os.path.join(base_path, "Titmouse", "Big_Mouth")
            elif show == "KOTH":
                project_path = os.path.join(base_path, "Disney", "KOTH")
            elif show == "Test":
                project_path = os.path.join(base_path, "Test")
            elif show == "Yeson_DANG":
                if "usadata3" not in base_path:
                    continue
                project_path = os.path.join(base_path, "Yeson_Test", "DANG")
            elif show in ["Yeson_Test", "Yeson_Test_4K"]:
                if "usadata3" not in base_path:
                    continue
                project_path = os.path.join(base_path, show)
            if os.path.exists(project_path):
                return project_path
        return None
    
    def get_possible_db_paths(self, episode):
        return [os.path.join(base_path, episode, "scene.db") for base_path in self.db_paths]

class FrameCalculator:
    @staticmethod
    def calculate_sheet_length(frames):
        try:
            frames = int(frames)
            integer_part = frames // 16
            decimal_part = frames % 16
            return f"{integer_part}.{decimal_part:02d}"
        except:
            return "0.00"
    
    @staticmethod
    def format_feet_display(sheet_length):
        try:
            feet, frames = str(sheet_length).split('.')
            return f"{feet}F {frames}f"
        except:
            return "0F 00f"

class SceneDbManager:
    def __init__(self, path_manager):
        self.path_manager = path_manager
        self.cache = {}
    
    def get_frames_info(self, episode):
        if episode in self.cache:
            return self.cache[episode]
        frames_info = {}
        if not episode or episode in ["작품 선택", "시즌 선택", "화수 선택"]:
            return frames_info
        is_valid_episode = (
            episode.startswith(('DASA', 'EASA')) or
            episode.startswith(('4LBW', '5LBW')) or
            episode.startswith('BM_8') or
            episode.startswith(('EABE', '15')) or
            episode in ["Test", "Yeson_DANG", "Yeson_Test", "Yeson_Test_4K"]
        )
        if not is_valid_episode:
            return frames_info
        possible_db_paths = self.path_manager.get_possible_db_paths(episode)
        for db_path in possible_db_paths:
            if not os.path.exists(db_path):
                continue
            try:
                dbu_command = [
                    "/Applications/Toon Boom Harmony 21.1 Premium/Harmony 21.1 Premium.app/Contents/tba/macosx/bin/dbu",
                    "-l", "-r", db_path
                ]
                result = subprocess.run(dbu_command, capture_output=True, text=True)
                if result.returncode != 0:
                    continue
                current_scene = None
                for line in result.stdout.splitlines():
                    if 'Path:' in line:
                        path = line.split('Path:')[1].strip()
                        current_scene = os.path.basename(path)
                    elif 'Frames:' in line and current_scene:
                        frames = line.split('Frames:')[1].strip()
                        frames_info[current_scene] = frames
                if frames_info:
                    self.cache[episode] = frames_info
                    break
            except Exception as e:
                print(f"프레임 정보 추출 중 오류 발생: {e}")
                continue
        return frames_info
    
    @staticmethod
    def filter_scene_folders(frames_info):
        excluded_terms = ['CHARACTERS', 'PROPS', 'Sub_Model', 'STOCK', 'Crowd', '_old', '_batch', 'batch']
        return {
            scene_name: scene_name for scene_name in frames_info.keys()
            if not any(term in scene_name for term in excluded_terms)
        }

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = ValidationApp()
    window.show()
    sys.exit(app.exec_())