# utils.py
from PyQt5.QtWidgets import QGraphicsDropShadowEffect
from PyQt5.QtGui import QColor

class AnimationHelper:
    @staticmethod
    def fade_in(widget, duration=300):
        widget.show()

    @staticmethod
    def add_drop_shadow(widget, radius=10, x_offset=2, y_offset=2, color=QColor(0, 0, 0, 80)):
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(radius)
        shadow.setXOffset(x_offset)
        shadow.setYOffset(y_offset)
        shadow.setColor(color)
        widget.setGraphicsEffect(shadow)

class FrameCalculator:
    @staticmethod
    def calculate_sheet_length(frames):
        try:
            frames = int(frames)
            integer_part = frames // 16
            decimal_part = frames % 16
            return f"{integer_part}.{decimal_part:02d}"
        except:
            return "0.00"

    @staticmethod
    def format_feet_display(sheet_length):
        try:
            feet, frames = str(sheet_length).split('.')
            return f"{feet}F {frames}f"
        except:
            return "0F 00f"