# styles.py
from PyQt5.QtGui import QColor, QPalette, QFont
from PyQt5.QtWidgets import QStyleFactory

class StyleManager:
    DARK_THEME = {
        "BG": "#1E2526",
        "LIGHT_BG": "#2A3439",
        "ACCENT": "#D4AF37",
        "TEXT": "#E8ECEF",
        "MEDIUM_GRAY": "#A9B1B3",
        "DARK_ACCENT": "#3A4A50"
    }
    
    LIGHT_THEME = {
        "BG": "#F5F6F5",
        "LIGHT_BG": "#FFFFFF",
        "ACCENT": "#F4A261",
        "TEXT": "#2A3439",
        "MEDIUM_GRAY": "#6B7280",
        "DARK_ACCENT": "#E5E7EB"
    }
    
    @staticmethod
    def apply_style(app, theme="dark"):
        colors = StyleManager.DARK_THEME if theme == "dark" else StyleManager.LIGHT_THEME
        app.setStyle(QStyleFactory.create("Fusion"))
        font = QFont("Helvetica", 10)
        app.setFont(font)
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(colors["BG"]))
        palette.setColor(QPalette.WindowText, QColor(colors["TEXT"]))
        palette.setColor(QPalette.Base, QColor(colors["LIGHT_BG"]))
        palette.setColor(QPalette.AlternateBase, QColor(colors["DARK_ACCENT"]))
        palette.setColor(QPalette.ToolTipBase, QColor(colors["LIGHT_BG"]))
        palette.setColor(QPalette.ToolTipText, QColor(colors["TEXT"]))
        palette.setColor(QPalette.Text, QColor(colors["TEXT"]))
        palette.setColor(QPalette.Button, QColor(colors["DARK_ACCENT"]))
        palette.setColor(QPalette.ButtonText, QColor(colors["TEXT"]))
        palette.setColor(QPalette.BrightText, QColor(colors["ACCENT"]))
        palette.setColor(QPalette.Link, QColor(colors["ACCENT"]))
        palette.setColor(QPalette.Highlight, QColor(colors["ACCENT"]))
        palette.setColor(QPalette.HighlightedText, QColor(colors["BG"]))
        app.setPalette(palette)

        app.setStyleSheet(f"""
            QMainWindow {{ background: {colors["BG"]}; }}
            QWidget {{ background: {colors["BG"]}; }}
            QLabel {{ color: {colors["TEXT"]}; font-size: 12px; font-weight: 400; padding: 4px; }}
            QLabel#headerLabel {{ font-size: 14px; font-weight: bold; color: {colors["ACCENT"]}; padding: 8px; }}
            QComboBox {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; padding: 4px 8px; font-size: 12px; background: {colors["LIGHT_BG"]}; color: {colors["TEXT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; }}
            QComboBox::drop-down {{ subcontrol-origin: padding; subcontrol-position: right center; width: 20px; border-left: 1px solid {colors["DARK_ACCENT"]}; border-top-right-radius: 4px; border-bottom-right-radius: 4px; }}
            QComboBox QAbstractItemView {{ font-size: 12px; background: {colors["LIGHT_BG"]}; border: 1px solid {colors["DARK_ACCENT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; padding: 4px; }}
            QLineEdit {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; padding: 4px 8px; font-size: 12px; background: {colors["LIGHT_BG"]}; color: {colors["TEXT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; }}
            QLineEdit:read-only {{ background: {colors["DARK_ACCENT"]}; border: 1px solid {colors["DARK_ACCENT"]}; }}
            QPushButton {{ background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {colors["ACCENT"]}, stop:1 {StyleManager.adjust_brightness(colors["ACCENT"], -20)}); color: {colors["BG"]}; border: none; border-radius: 4px; padding: 6px 12px; font-size: 12px; font-weight: bold; }}
            QPushButton:hover {{ background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {StyleManager.adjust_brightness(colors["ACCENT"], 20)}, stop:1 {colors["ACCENT"]}); }}
            QPushButton:pressed {{ background-color: {StyleManager.adjust_brightness(colors["ACCENT"], -40)}; }}
            QPushButton:disabled {{ background-color: {colors["MEDIUM_GRAY"]}; color: {colors["DARK_ACCENT"]}; }}
            QTreeWidget {{ border: 1px solid {colors["DARK_ACCENT"]}; border-radius: 4px; font-size: 12px; background: {colors["LIGHT_BG"]}; alternate-background-color: {colors["DARK_ACCENT"]}; selection-background-color: {colors["ACCENT"]}; selection-color: {colors["BG"]}; padding: 4px; }}
            QTreeWidget::item {{ padding: 4px; border-bottom: 1px solid {colors["DARK_ACCENT"]}; }}
            QTreeWidget::item:selected {{ background-color: {colors["ACCENT"]}; color: {colors["BG"]}; }}
            QHeaderView::section {{ background-color: {colors["DARK_ACCENT"]}; color: {colors["TEXT"]}; padding: 4px; border: none; border-right: 1px solid {colors["MEDIUM_GRAY"]}; border-bottom: 1px solid {colors["MEDIUM_GRAY"]}; font-size: 12px; font-weight: bold; }}
            QStatusBar {{ background-color: {colors["DARK_ACCENT"]}; color: {colors["TEXT"]}; font-size: 12px; padding: 4px 8px; }}
            QFrame#separator {{ background-color: {colors["MEDIUM_GRAY"]}; max-height: 1px; margin: 6px 0px; }}
            QToolTip {{ background-color: {colors["LIGHT_BG"]}; color: {colors["TEXT"]}; font-size: 12px; border: 1px solid {colors["DARK_ACCENT"]}; padding: 4px; }}
            CardWidget {{ background: {colors["LIGHT_BG"]}; border-radius: 4px; border: 1px solid {colors["DARK_ACCENT"]}; }}
        """)

    @staticmethod
    def adjust_brightness(color_hex, amount):
        color = QColor(color_hex)
        r, g, b = color.red(), color.green(), color.blue()
        r = max(0, min(255, r + amount))
        g = max(0, min(255, g + amount))
        b = max(0, min(255, b + amount))
        return f"#{r:02x}{g:02x}{b:02x}"