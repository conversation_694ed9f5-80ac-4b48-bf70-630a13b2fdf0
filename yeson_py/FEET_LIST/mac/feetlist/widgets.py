# widgets.py
from PyQt5.QtWidgets import (QTreeWidget, QTreeWidgetItem, QFrame, QVBoxLayout, QHBoxLayout, QWidget,
                             QLabel, QComboBox, QLineEdit, QPushButton, QHeaderView, QSizePolicy, QTreeWidget)
from PyQt5.QtCore import Qt
from utils import AnimationHelper

class ChartTreeWidget(QTreeWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAlternatingRowColors(True)
        self.setAnimated(True)
        self.setHeaderLabels(['씬 이름', 'Frames', 'FEET'])
        self.setSelectionMode(QTreeWidget.ExtendedSelection)
        header = self.header()
        header.setSectionResizeMode(QHeaderView.Interactive)
        header.setSectionsMovable(False)
        header.setStretchLastSection(False)
        self.setColumnWidth(0, 220)  # 이미지에 맞춘 너비
        self.setColumnWidth(1, 70)
        self.setColumnWidth(2, 70)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        AnimationHelper.add_drop_shadow(self)


class CardWidget(QWidget):
    def __init__(self, title=None):
        super().__init__()
        self.layout = QVBoxLayout(self)
        self.setLayout(self.layout)
        if title:
            title_label = QLabel(title)
            self.layout.addWidget(title_label)

class ShowSelectionWidget(QWidget):
    def __init__(self):
        super().__init__()
        # 여기에 ShowSelectionWidget의 UI 요소들을 추가합니다.

class SummaryWidget(QWidget):
    def __init__(self):
        super().__init__()
        # 여기에 SummaryWidget의 UI 요소들을 추가합니다.

class CalculationWidget(QWidget):
    def __init__(self):
        super().__init__()
        # 여기에 CalculationWidget의 UI 요소들을 추가합니다.

class SceneListWidget(QWidget):
    def __init__(self):
        super().__init__()
        # 여기에 SceneListWidget의 UI 요소들을 추가합니다.

class CardWidget(QFrame):
    def __init__(self, title=None, parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.StyledPanel)
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(10, 10, 10, 10)
        self.layout.setSpacing(8)
        if title:
            self.title = QLabel(title)
            self.title.setObjectName("headerLabel")
            self.layout.addWidget(self.title)
            separator = QFrame()
            separator.setFrameShape(QFrame.HLine)
            separator.setFrameShadow(QFrame.Sunken)
            separator.setObjectName("separator")
            self.layout.addWidget(separator)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        AnimationHelper.add_drop_shadow(self)

class ShowSelectionWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("작품 선택", parent)
        self.show_layout = QHBoxLayout()
        self.show_layout.setSpacing(8)
        show_label = QLabel("작품:")
        show_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.show_layout.addWidget(show_label)
        self.show_combo = QComboBox()
        self.show_combo.addItem("작품 선택")
        self.show_combo.addItems(["BB", "GN", "BM", "KOTH", "Test", "Yeson_DANG", "Yeson_Test", "Yeson_Test_4K"])
        self.show_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.show_layout.addWidget(self.show_combo)
        self.show_layout.addStretch()
        self.layout.addLayout(self.show_layout)
        self.season_layout = QHBoxLayout()
        self.season_layout.setSpacing(8)
        season_label = QLabel("시즌:")
        season_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.season_layout.addWidget(season_label)
        self.season_combo = QComboBox()
        self.season_combo.addItem("시즌 선택")
        self.season_combo.setEnabled(False)
        self.season_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.season_layout.addWidget(self.season_combo)
        self.season_layout.addStretch()
        self.layout.addLayout(self.season_layout)
        self.episode_layout = QHBoxLayout()
        self.episode_layout.setSpacing(8)
        episode_label = QLabel("화수:")
        episode_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.episode_layout.addWidget(episode_label)
        self.episode_combo = QComboBox()
        self.episode_combo.addItem("화수 선택")
        self.episode_combo.setEnabled(False)
        self.episode_combo.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.episode_layout.addWidget(self.episode_combo)
        self.episode_layout.addStretch()
        self.layout.addLayout(self.episode_layout)

class SummaryWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("에피소드 정보", parent)
        summary_layout = QHBoxLayout()
        summary_layout.setSpacing(10)
        frame_layout = QVBoxLayout()
        frame_title = QLabel("총 프레임 수:")
        frame_title.setAlignment(Qt.AlignCenter)
        frame_layout.addWidget(frame_title)
        self.total_frames_display = QLineEdit()
        self.total_frames_display.setAlignment(Qt.AlignCenter)
        self.total_frames_display.setReadOnly(True)
        self.total_frames_display.setMinimumWidth(80)
        self.total_frames_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        frame_layout.addWidget(self.total_frames_display)
        summary_layout.addLayout(frame_layout)
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("separator")
        summary_layout.addWidget(separator)
        feet_layout = QVBoxLayout()
        feet_title = QLabel("총 FEET:")
        feet_title.setAlignment(Qt.AlignCenter)
        feet_layout.addWidget(feet_title)
        self.episode_feet_display = QLineEdit()
        self.episode_feet_display.setAlignment(Qt.AlignCenter)
        self.episode_feet_display.setReadOnly(True)
        self.episode_feet_display.setMinimumWidth(80)
        self.episode_feet_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        feet_layout.addWidget(self.episode_feet_display)
        summary_layout.addLayout(feet_layout)
        self.layout.addLayout(summary_layout)

class CalculationWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("선택 씬 계산", parent)
        calc_layout = QHBoxLayout()
        calc_layout.setSpacing(8)
        self.calc_button = QPushButton("선택 씬 계산")
        self.calc_button.setCursor(Qt.PointingHandCursor)
        self.calc_button.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        calc_layout.addWidget(self.calc_button)
        calc_layout.addSpacing(10)
        feet_label = QLabel("선택된 씬 총 FEET:")
        feet_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        calc_layout.addWidget(feet_label)
        self.total_feet_display = QLineEdit()
        self.total_feet_display.setAlignment(Qt.AlignCenter)
        self.total_feet_display.setReadOnly(True)
        self.total_feet_display.setMinimumWidth(100)
        self.total_feet_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        calc_layout.addWidget(self.total_feet_display)
        calc_layout.addStretch()
        self.layout.addLayout(calc_layout)

class SceneListWidget(CardWidget):
    def __init__(self, parent=None):
        super().__init__("씬 목록", parent)
        self.scene_tree = ChartTreeWidget()
        self.scene_tree.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.layout.addWidget(self.scene_tree)
        button_layout = QHBoxLayout()
        self.export_button = QPushButton("엑셀로 내보내기")
        self.export_button.setCursor(Qt.PointingHandCursor)
        self.export_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        button_layout.addWidget(self.export_button)
        button_layout.addStretch()
        selection_label = QLabel("Tip: 여러 씬을 선택하려면 Ctrl 또는 Shift 키를 사용하세요.")
        selection_label.setAlignment(Qt.AlignRight)
        selection_label.setStyleSheet("font-style: italic;")
        button_layout.addWidget(selection_label)
        self.layout.addLayout(button_layout)