import sys
import os
import re
import glob
import shutil
import subprocess
import time
import json
from datetime import datetime
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QListWidget,
    QMenu, QAction, QFileDialog, QProgressBar, QDialog, QMessageBox, QTextEdit,
    QPushButton, QTableWidget, QTableWidgetItem, QInputDialog, QMainWindow, QLineEdit,
    QColorDialog, QFontDialog, QListWidgetItem, QGraphicsOpacityEffect, QMenuBar,
    QComboBox, QFrame, QListView, QTreeView, QAbstractItemView, QGridLayout, QCheckBox,
    QDialogButtonBox, QProgressDialog, QGroupBox, QTreeWidget, QTreeWidgetItem
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QPropertyAnimation, QUrl, QSettings
from PyQt5.QtGui import QColor, QPixmap, QFont, QDesktopServices


class LoginWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Yeson Control Center")
        self.setGeometry(100, 100, 600, 400)

        self.settings = QSettings("YourCompany", "YourApp")

        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout()

        # Add logo image
        image_label = QLabel()
        pixmap = QPixmap("/USA_DB/test_jn/yeson.png")  # Replace with your image path
        image_label.setPixmap(pixmap)
        image_label.setAlignment(Qt.AlignCenter)  # Center align the image
        layout.addWidget(image_label)

        # Add title label
        title_label = QLabel("Yeson ControlCenter")
        title_label.setStyleSheet("font-size: 40px; font-weight: bold; color: green;")
        title_label.setAlignment(Qt.AlignCenter) # Center align
        layout.addWidget(title_label)

        # Horizontal layout for user name label and input field
        user_layout = QHBoxLayout()

        user_label = QLabel("User Name:")
        self.username_field = QLineEdit()
        self.username_field.setFixedWidth(450)  # Set the width of the input field

        saved_username = self.settings.value("username", "")
        self.username_field.setText(saved_username)

        self.username_field.setSelection(0, len(saved_username))

        user_layout.addWidget(user_label)
        user_layout.addWidget(self.username_field)

        # Add user layout to the main layout
        layout.addLayout(user_layout)
        # Password layout (initially hidden)
        self.password_layout = QHBoxLayout()
        password_label = QLabel("Password:")
        self.password_field = QLineEdit()
        self.password_field.setFixedWidth(450)
        self.password_field.setEchoMode(QLineEdit.Password)  # Hide password
        self.password_layout.addWidget(password_label)
        self.password_layout.addWidget(self.password_field)
        layout.addLayout(self.password_layout)

        # Initially hide password field
        password_label.hide()
        self.password_field.hide()

        # Connect username field's textChanged signal
        self.username_field.textChanged.connect(self.on_username_changed)

        # Connect Enter key to login for both fields
        self.username_field.returnPressed.connect(self.handle_return_pressed)
        self.password_field.returnPressed.connect(self.open_new_window)

        # Login button
        login_button = QPushButton("Login")
        login_button.clicked.connect(self.open_new_window)

        # Cancel button
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.close)  # Close the window on cancel

        # Horizontal layout for buttons
        button_layout = QHBoxLayout()
        button_layout.addWidget(login_button)
        button_layout.addWidget(cancel_button)

        # Add button layout to the main layout
        layout.addLayout(button_layout)

        central_widget.setLayout(layout)

        self.create_animations(image_label, title_label)

    def on_username_changed(self, text):
        # Show/hide password field based on username
        is_admin = (text.strip() == "admin")
        self.password_layout.itemAt(0).widget().setVisible(is_admin)  # Show/hide label
        self.password_layout.itemAt(1).widget().setVisible(is_admin)  # Show/hide field
        if not is_admin:
            self.password_field.clear()

    def handle_return_pressed(self):
        if self.username_field.text().strip() == "admin":
            self.password_field.setFocus()
        else:
            self.open_new_window()

    def create_animations(self, image_label, title_label):
        # Apply opacity effect to the logo image
        logo_opacity_effect = QGraphicsOpacityEffect()
        image_label.setGraphicsEffect(logo_opacity_effect)

        # Fade-in animation for the logo image
        self.logo_animation = QPropertyAnimation(logo_opacity_effect, b"opacity")
        self.logo_animation.setDuration(2000)  # Duration in milliseconds
        self.logo_animation.setStartValue(0)
        self.logo_animation.setEndValue(1)

        # Apply opacity effect to the title label
        title_opacity_effect = QGraphicsOpacityEffect()
        title_label.setGraphicsEffect(title_opacity_effect)

        # Fade-in animation for the title label
        self.title_animation = QPropertyAnimation(title_opacity_effect, b"opacity")
        self.title_animation.setDuration(7000)  # Duration in milliseconds
        self.title_animation.setStartValue(0)
        self.title_animation.setEndValue(1)

    def showEvent(self, event):
        super().showEvent(event)

        # Start animations when window is shown
        self.logo_animation.start()
        self.title_animation.start()

    def open_new_window(self):
        username = self.username_field.text().strip()

        if username:
            self.settings.setValue("username", username)

        if username == "admin":
            # Check admin password
            admin_password = "zmflsj"  # 실제 환경에서는 암호화된 비밀번호를 사용해야 합니다
            if self.password_field.text() == admin_password:
                self.new_window = MyWindow(full_access=True, username=username, is_admin=True)
                self.new_window.users = self.load_users()  # Load users data
                self.new_window.show()
                self.close()
            else:
                QMessageBox.warning(self, "Login Failed", "잘못된 비밀번호입니다.")
                self.password_field.clear()
                self.password_field.setFocus()
        else:
            # Load users within the try block, and create/handle if not exist
            try:
                with open('/USA_DB/test_jn/users.json', 'r') as f:
                    users = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                users = self.create_default_users()

            if username in users:
                self.new_window = MyWindow(
                    full_access=True,  # You can modify this based on user permissions
                    username=username,
                    is_admin=False
                )
                self.new_window.users = users
                self.new_window.show()
                self.close()
            elif username in ["scan", "yeson"]:
                self.new_window = MyWindow(full_access=True, username=username, is_admin=False)
                self.new_window.show()
                self.close()
            elif username == "color":
                self.new_window = MyWindow(full_access=False, username=username, is_admin=False)
                self.new_window.show()
                self.close()
            else:
                QMessageBox.warning(self, "Invalid Username", "잘못된 이름입니다.")


    def load_users(self):
      try:
          with open('/USA_DB/test_jn/users.json', 'r') as f:
              return json.load(f)
      except (FileNotFoundError, json.JSONDecodeError):
          return self.create_default_users()

    def create_default_users(self):
        # Create default users.json with admin user
        default_users = {
            "admin": {
                "permissions": {
                    "jobs": {"Job 만들기": True},
                    "scenes": {
                        "Scene 만들기": True,
                        "Scene 삭제": True,
                        "Scene 복사": True,
                        "Export": True,
                        "다중 jobs Export": True,
                        "Import Single": True,
                        "Import Multiple": True,
                        "외주정리 작업": True,
                        "Rename": True,
                        "Checkout": True,
                        "Checkin": True,
                        "Reorder": True
                    },
                   "menus": {}  # Add menus context
                }
            }
        }
        with open('/USA_DB/test_jn/users.json', 'w') as f:
            json.dump(default_users, f, indent=4)
        return default_users


class UserManagementDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("User Management")
        self.setModal(True)
        self.users_file_path = '/USA_DB/test_jn/users.json'
        self.resize(1000, 600)  # 초기 크기를 800x600으로 설정

        # 최소 창 크기 설정
        self.setMinimumSize(600, 400)  # 최소 크기를 600x400으로 설정
        self.setup_ui()
        self.load_users()

    def setup_ui(self):
        layout = QVBoxLayout()

        # Left side - User list
        list_layout = QHBoxLayout()

        # User list
        user_group = QGroupBox("Users")
        user_layout = QVBoxLayout()
        self.user_list = QListWidget()
        self.user_list.itemClicked.connect(self.load_user_permissions)
        user_layout.addWidget(self.user_list)
        user_group.setLayout(user_layout)
        list_layout.addWidget(user_group)

        # Right side - Menu permissions
        permission_group = QGroupBox("Menu Permissions")
        permission_layout = QVBoxLayout()

        # Username input for new users
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("새로운 username 입력")
        permission_layout.addWidget(self.username_input)

        # Menu permissions tree
        self.permission_tree = QTreeWidget()
        self.permission_tree.setHeaderLabels(["Menu", "Enabled"])
        self.setup_permission_tree()
        permission_layout.addWidget(self.permission_tree)

        permission_group.setLayout(permission_layout)
        list_layout.addWidget(permission_group)

        layout.addLayout(list_layout)

        # Buttons
        button_layout = QHBoxLayout()
        add_button = QPushButton("Add/Update User")
        delete_button = QPushButton("Delete User")
        close_button = QPushButton("Close")

        add_button.clicked.connect(self.add_user)
        delete_button.clicked.connect(self.delete_user)
        close_button.clicked.connect(self.close)

        button_layout.addWidget(add_button)
        button_layout.addWidget(delete_button)
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def setup_permission_tree(self):
        self.permission_tree.clear()
        self.permission_tree.setColumnWidth(0, 300)

        # Jobs context
        jobs_item = QTreeWidgetItem(self.permission_tree)
        jobs_item.setText(0, "jobs")
        jobs_item.setFlags(jobs_item.flags() | Qt.ItemIsTristate | Qt.ItemIsUserCheckable)

        job_create = QTreeWidgetItem(jobs_item)
        job_create.setText(0, "Job 만들기")
        job_create.setFlags(job_create.flags() | Qt.ItemIsUserCheckable)
        job_create.setCheckState(1, Qt.Unchecked)

        # Scenes context
        scenes_item = QTreeWidgetItem(self.permission_tree)
        scenes_item.setText(0, "scenes")
        scenes_item.setFlags(scenes_item.flags() | Qt.ItemIsTristate | Qt.ItemIsUserCheckable)

        scene_menus = [
            "Scene 만들기",
            "Scene 만들기color",
            "Scene 삭제",
            "Scene 복사",
            "Export",
            "다중 jobs Export",
            "Import Single",
            "Import Multiple",
            "외주정리 작업",
            "Rename",
            "Checkout",
            "Checkin",
            "Reorder"
        ]

        # Menu permissions
        menus_item = QTreeWidgetItem(self.permission_tree)
        menus_item.setText(0, "menus")
        menus_item.setFlags(menus_item.flags() | Qt.ItemIsTristate | Qt.ItemIsUserCheckable)

        menu_items = [
            "Layout2",
            "Script",
            "LockManager"
        ]

        # Add scene menus
        for menu in scene_menus:
            menu_item = QTreeWidgetItem(scenes_item)
            menu_item.setText(0, menu)
            menu_item.setFlags(menu_item.flags() | Qt.ItemIsUserCheckable)
            menu_item.setCheckState(1, Qt.Unchecked)

        # Add main menu items
        for menu in menu_items:
            menu_item = QTreeWidgetItem(menus_item)
            menu_item.setText(0, menu)
            menu_item.setFlags(menu_item.flags() | Qt.ItemIsUserCheckable)
            menu_item.setCheckState(1, Qt.Unchecked)

        self.permission_tree.expandAll()

    def load_users(self):
        self.user_list.clear()
        try:
            with open(self.users_file_path, 'r') as f:
                self.users = json.load(f)
                for username in self.users:
                    self.user_list.addItem(username)
        except FileNotFoundError:
            self.users = {}

    def get_permissions_from_tree(self):
        permissions = {
            "jobs": {},
            "scenes": {},
            "menus": {}  # menus 컨텍스트 추가
        }

        for context_index in range(self.permission_tree.topLevelItemCount()):
            context_item = self.permission_tree.topLevelItem(context_index)
            context = context_item.text(0)

            # 각 컨텍스트에 대한 권한 처리
            for menu_index in range(context_item.childCount()):
                menu_item = context_item.child(menu_index)
                menu_name = menu_item.text(0)
                enabled = menu_item.checkState(1) == Qt.Checked
                permissions[context][menu_name] = enabled

        return permissions

    def set_permissions_to_tree(self, permissions):
        for context_index in range(self.permission_tree.topLevelItemCount()):
            context_item = self.permission_tree.topLevelItem(context_index)
            context = context_item.text(0)

            for menu_index in range(context_item.childCount()):
                menu_item = context_item.child(menu_index)
                menu_name = menu_item.text(0)
                enabled = permissions.get(context, {}).get(menu_name, False)
                menu_item.setCheckState(1, Qt.Checked if enabled else Qt.Unchecked)

    def load_user_permissions(self, item):
        username = item.text()
        self.username_input.setText(username)
        if username in self.users:
            permissions = self.users[username].get("permissions", {
                "jobs": {},
                "scenes": {},
                "menus": {}  # menus 컨텍스트 추가
            })
            self.set_permissions_to_tree(permissions)

    def add_user(self):
        username = self.username_input.text().strip()
        if not username:
            QMessageBox.warning(self, "Error", "Please enter a username.")
            return

        permissions = self.get_permissions_from_tree()
        self.users[username] = {
            "permissions": permissions
        }

        self.save_users()
        self.load_users()
        self.username_input.clear()
        self.setup_permission_tree()
        QMessageBox.information(self, "Success", f"User '{username}' 추가/업데이트.")

    def delete_user(self):
        current_item = self.user_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Error", "Please select a user to delete.")
            return

        username = current_item.text()

        if username == "admin":
            QMessageBox.warning(self, "Error", "Cannot delete admin user.")
            return

        confirm = QMessageBox.question(self, "Confirm Delete",
                                     f"User {username} 삭제 합니까?",
                                     QMessageBox.Yes | QMessageBox.No)

        if confirm == QMessageBox.Yes:
            del self.users[username]
            self.save_users()
            self.load_users()
            self.username_input.clear()
            self.setup_permission_tree()

    def save_users(self):
        with open(self.users_file_path, 'w') as f:
            json.dump(self.users, f, indent=4)


class TplData:
    def __init__(self, folder_path):
        self.folder_path = folder_path
        self.tvg_file = None
        self.thumb_folder = folder_path + '/.thumbnails'
        self.thumb_file = self.thumb_folder + '/t-0001.png'
        self.is_exist_thumb = os.path.exists(self.thumb_file)

# 개선된 find_tpl 함수: glob을 사용하여 재귀적으로 .tpl 폴더 검색
def find_tpl(start_path):
    tpl_list = []
    if not os.path.isdir(start_path):
        return tpl_list

    for tpl_path in glob.glob(os.path.join(start_path, '**/*.tpl'), recursive=True):
        tdata = TplData(os.path.dirname(tpl_path))  # .tpl 폴더의 상위 폴더를 전달
        tpl_list.append(tdata)
    return tpl_list



def find_tvg(tpl_list):
    for tpl in tpl_list:
        if not tpl.is_exist_thumb:
            for root, dirs, files in os.walk(tpl.folder_path):
                for f in files:
                    if '.tvg' == f[-4:]:
                        tpl.tvg_file = root + '/' + f

def make_thumb(tpl_list):
    for tpl in tpl_list:
        if not tpl.is_exist_thumb and tpl.tvg_file:
            comm = [
                '/Applications/Toon Boom Harmony 21.1 Premium/Harmony 21.1 Premium.app/Contents/tba/macosx/bin/utransform',
                '-resolution', '320', '240',
                '-outformat', 'PNG',
                '-outfile', tpl.thumb_file,
                tpl.tvg_file
            ]
            print(" ".join(comm))  # 커맨드 로깅
            subprocess.run(comm)  # subprocess.run 사용, shell=False (기본값)

class ThumbnailWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("스크립트 실행")
        self.setGeometry(100, 100, 400, 300)

        layout = QVBoxLayout()

        # Create a button to run the script
        run_button = QPushButton("Thumbnail 생성", self)
        run_button.setFixedSize(400, 50)
        run_button.clicked.connect(self.run_script)

        label = QLabel("작품 선택", self)

        self.folder_selector = QComboBox(self)
        self.folder_selector.setFixedSize(400, 60)

        base_folder_path = "/Users/<USER>/pallate"
        if os.path.exists(base_folder_path) and os.path.isdir(base_folder_path):
            folders = [f for f in os.listdir(base_folder_path) if os.path.isdir(os.path.join(base_folder_path, f))]
            self.folder_selector.addItems(folders)

        copy_button = QPushButton("Pallate 복사 할 폴더 선택", self)
        copy_button.setFixedSize(400, 80)
        copy_button.clicked.connect(self.copy_files)

        new_button = QPushButton("dot 스크립트", self)
        new_button.setFixedSize(400, 50)
        new_button.clicked.connect(self.open_list_window)

        layout.addWidget(run_button)

        separator1 = QFrame()
        separator1.setFrameShape(QFrame.HLine)
        separator1.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator1)

        layout.addWidget(new_button)

        separator2 = QFrame()
        separator2.setFrameShape(QFrame.HLine)
        separator2.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator2)

        layout.addWidget(label)
        layout.addWidget(self.folder_selector)
        layout.addWidget(copy_button)


        separator3 = QFrame()
        separator3.setFrameShape(QFrame.HLine)
        separator3.setFrameShadow(QFrame.Sunken)

        layout.addWidget(separator3)

        p2_button = QPushButton("p2 스크립트", self)
        p2_button.setFixedSize(400, 50)
        p2_button.clicked.connect(self.run_p2_script)
        layout.addWidget(p2_button)

        # separator 추가
        separator4 = QFrame()
        separator4.setFrameShape(QFrame.HLine)
        separator4.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator4)

        self.setLayout(layout)

    def run_p2_script(self):
        source_dir = "/Users/<USER>/Desktop/split"

        # 실행 확인 대화상자 표시
        reply = QMessageBox.question(self, 'Confirm',
                                'p2 스크립트를 실행하시겠습니까?',
                                QMessageBox.Yes | QMessageBox.No,
                                QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                # .tpl 파일들을 찾아서 처리
                for tpl_file in os.listdir(source_dir):
                    if tpl_file.endswith('.tpl'):
                        tpl_name = os.path.splitext(tpl_file)[0]
                        tpl_path = os.path.join(source_dir, tpl_file)

                        # scene.xstage 파일 찾기 및 이름 변경
                        for root, dirs, files in os.walk(tpl_path):
                            for file in files:
                                if file == 'scene.xstage':
                                    old_path = os.path.join(root, file)
                                    new_path = os.path.join(root, f"{tpl_name}.xstage")
                                    os.rename(old_path, new_path)

                        # annotation/scene 폴더 이름 변경
                        annotation_path = os.path.join(tpl_path, 'annotation')
                        if os.path.exists(annotation_path):
                            scene_path = os.path.join(annotation_path, 'scene')
                            if os.path.exists(scene_path):
                                new_scene_path = os.path.join(annotation_path, tpl_name)
                                os.rename(scene_path, new_scene_path)

                        # 새 폴더 생성 및 파일 복사
                        new_folder = os.path.join(source_dir, tpl_name)
                        if not os.path.exists(new_folder):
                            os.makedirs(new_folder)

                        frames_dir = os.path.join(new_folder, 'frames')
                        environments_dir = os.path.join(new_folder, 'environments', 'Digital')
                        jobs_dir = os.path.join(new_folder, 'jobs', 'Digital')

                        # 필요한 디렉토리들 생성
                        os.makedirs(frames_dir, exist_ok=True)
                        os.makedirs(environments_dir, exist_ok=True)
                        os.makedirs(jobs_dir, exist_ok=True)

                        # tpl 파일 내용 복사
                        for root, dirs, files in os.walk(tpl_path):
                            for file in files:
                                src_file = os.path.join(root, file)
                                rel_path = os.path.relpath(root, tpl_path)
                                dst_dir = os.path.join(new_folder, rel_path)

                                if not os.path.exists(dst_dir):
                                    os.makedirs(dst_dir)

                                dst_file = os.path.join(dst_dir, file)
                                shutil.copy2(src_file, dst_file)

                QMessageBox.information(self, "Success", "p2 스크립트가 성공적으로 실행되었습니다.")

            except Exception as e:
                QMessageBox.critical(self, "Error", f"오류가 발생했습니다: {str(e)}")

    def open_list_window(self):
        self.list_window = ListWindow()
        self.list_window.show()


    def run_script(self):
        start_path = QFileDialog.getExistingDirectory(self, "Select Folder")
        if start_path:
            tpl_list = find_tpl(start_path)
            find_tvg(tpl_list)
            make_thumb(tpl_list)
            QMessageBox.information(self, "Complete", "Thumbnail 생성이 완료되었습니다!")
            for tpl in tpl_list:
                print(tpl.folder_path, tpl.is_exist_thumb, tpl.tvg_file)  # 수정된 부분
        else:
            QMessageBox.warning(self, "No Selection", "폴더가 선택되지 않았습니다.")

    def copy_files(self):
        selected_folder_name = self.folder_selector.currentText()
        base_folder_path = "/Users/<USER>/pallate"

        # Construct the path to the source folder containing "PALETTE_LIST"
        source_folder_path = os.path.join(base_folder_path, selected_folder_name)
        source_file = os.path.join(source_folder_path, "PALETTE_LIST")

        if not os.path.isfile(source_file):
            QMessageBox.warning(self, "Error", f"The file 'PALETTE_LIST' does not exist in {source_folder_path}.")
            return

        # Let the user select the destination folder
        destination_root = QFileDialog.getExistingDirectory(self, "Select Destination Folder")

        if destination_root:
            # Iterate over each subdirectory in the selected destination root
            for subfolder in os.listdir(destination_root):
                subfolder_path = os.path.join(destination_root, subfolder)
                if os.path.isdir(subfolder_path):
                    destination_file = os.path.join(subfolder_path, "PALETTE_LIST")
                    try:
                        copy2(source_file, destination_file)  # Copy "PALETTE_LIST" to each subfolder
                    except Exception as e:
                        QMessageBox.warning(self, "Error", f"Failed to copy to {subfolder}: {str(e)}")
                        return

            QMessageBox.information(self, "Complete", f"'PALETTE_LIST' 복사가 완료 되었습니다.")
        else:
            QMessageBox.warning(self, "No Selection", "폴더가 선택되지 않았습니다.")

class ListWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Folder List")
        self.setGeometry(150, 150, 500, 300)

        layout = QVBoxLayout()

        self.folder_list = QListWidget(self)

        # Button to add folders to the list
        add_button = QPushButton("폴더 추가", self)
        add_button.clicked.connect(self.add_folder)

        # Button to process selected folders
        process_button = QPushButton("실행", self)
        process_button.clicked.connect(self.process_folders)

        # Button to clear the list
        clear_button = QPushButton("리스트 초기화", self)
        clear_button.clicked.connect(self.clear_list)

        button_layout = QHBoxLayout()
        button_layout.addWidget(add_button)
        button_layout.addWidget(process_button)
        button_layout.addWidget(clear_button)

        layout.addWidget(self.folder_list)
        layout.addLayout(button_layout)

        self.setLayout(layout)

        self.folder_paths = {}

    def add_folder(self):
            # Create a QFileDialog instance
        dialog = QFileDialog(self)
        dialog.setFileMode(QFileDialog.Directory)  # Set mode to select directories
        dialog.setOption(QFileDialog.DontUseNativeDialog, True)  # Use non-native dialog

        # Access the list and tree views within the dialog to enable multi-selection
        list_view = dialog.findChild(QListView, "listView")
        if list_view:
            list_view.setSelectionMode(QAbstractItemView.ExtendedSelection)

        tree_view = dialog.findChild(QTreeView)
        if tree_view:
            tree_view.setSelectionMode(QAbstractItemView.ExtendedSelection)

        # Execute the dialog and retrieve selected directories
        if dialog.exec_() == QFileDialog.Accepted:
            selected_folders = dialog.selectedFiles()

            # Filter out parent directories using os.path.commonpath
            filtered_folders = []
            for folder in selected_folders:
                is_parent = False
                for other_folder in selected_folders:
                    if folder != other_folder and os.path.commonpath([folder, other_folder]) == folder:
                        is_parent = True
                        break
                if not is_parent:
                    filtered_folders.append(folder)

            # Add filtered folders to the list
            for folder in filtered_folders:
                folder_name = os.path.basename(folder)
                if folder_name not in self.folder_paths:
                    self.folder_list.addItem(folder_name)
                    self.folder_paths[folder_name] = folder
                    print(f"Added folder: {folder_name}")  # Debugging: Confirm addition

    def process_folders(self):
        try:
            for index in range(self.folder_list.count()):
                base_folder_name = self.folder_list.item(index).text()
                full_path = self.folder_paths.get(base_folder_name)

                # Debugging: Print the full path being checked
                print(f"Checking path: {full_path}")

                if not os.path.exists(full_path):
                    print(f"Path does not exist: {full_path}")
                    continue

                # List all items in the directory
                all_items = os.listdir(full_path)

                # Filter to find valid date folders in the format YYYY_MMDD
                date_folders = [
                    item for item in all_items
                    if os.path.isdir(os.path.join(full_path, item)) and  # Ensure it's a directory
                    len(item) == 9 and item[4] == '_' and  # Check for underscore at correct position
                    item[:4].isdigit() and item[5:].isdigit()  # Ensure both parts are digits
                ]

                # Debugging: Output filtered date folders
                print(f"Filtered date folders in {base_folder_name}: {date_folders}")

                if not date_folders:
                    print("No valid date folders found.")
                    continue

                # Find and return the latest date folder
                latest_date_folder = max(date_folders, key=lambda d: (d[:4], d[5:]))
                latest_date_folder_path = os.path.join(full_path, latest_date_folder)
                print(f"Latest date folder: {latest_date_folder}")

                # Find folders ending with _FNL or _FINAL inside the latest date folder
                fnl_folders = [
                    os.path.join(latest_date_folder_path, item) for item in os.listdir(latest_date_folder_path)
                    if os.path.isdir(os.path.join(latest_date_folder_path, item)) and
                    (item.endswith('_FNL') or item.endswith('_FINAL'))
                ]

                if not fnl_folders:
                    print(f"No _FNL or _FINAL folders found in {latest_date_folder}")
                    continue

                fnl_folder = fnl_folders[0]
                print(f"Processing FNL folder: {fnl_folder}")

                annotation_folder = os.path.join(fnl_folder, "annotation")
                if not os.path.exists(annotation_folder):
                    print(f"Annotation folder does not exist: {annotation_folder}")
                    continue

                for root, dirs, files in os.walk(annotation_folder):
                    for file in files:
                        if file.startswith("._") and file.endswith(".png"):
                            file_to_delete = os.path.join(root, file)
                            try:
                                os.remove(file_to_delete)
                                print(f"Deleted hidden file: {file_to_delete}")
                            except Exception as e:
                                print(f"Error deleting {file_to_delete}: {e}")

                # Wait for file system to update
                time.sleep(1)

                # Refresh directory listing
                os.listdir(annotation_folder)

                # Find FNL folders with explicit loop
                inner_fnl_folders = []
                for item in os.listdir(annotation_folder):
                    full_item_path = os.path.join(annotation_folder, item)
                    if (os.path.isdir(full_item_path) and
                        (item.endswith('_FNL') or item.endswith('_FINAL'))):
                        inner_fnl_folders.append(full_item_path)

                if not inner_fnl_folders:
                    print(f"No inner _FNL or _FINAL folders found in {annotation_folder}")
                    continue

                inner_fnl_folder = inner_fnl_folders[0]
                print(f"Processing inner FNL folder: {inner_fnl_folder}")

                dotpngs_folder = os.path.join(inner_fnl_folder, "dotpngs")
                os.makedirs(dotpngs_folder, exist_ok=True)

                # Move PNG files smaller than 500 bytes to dotpngs
                for file_name in os.listdir(inner_fnl_folder):
                    file_path = os.path.join(inner_fnl_folder, file_name)
                    if file_name.endswith(".png") and os.path.getsize(file_path) <= 500:
                        shutil.move(file_path, dotpngs_folder)
                        print(f"Moved {file_name} to dotpngs")

                # Compress the dotpngs folder into a .tar archive first
                tar_path = os.path.join(annotation_folder, "dotpngs.tar")
                with tarfile.open(tar_path, "w") as tar:
                    tar.add(dotpngs_folder, arcname="dotpngs")
                    print(f"Created tar archive at {tar_path}")

                # Now compress the .tar file into a .tar.gz archive
                tar_gz_path = os.path.join(annotation_folder, "dotpngs.tar.gz")
                with open(tar_path, "rb") as f_in:
                    with open(tar_gz_path, "wb") as f_out:
                        shutil.copyfileobj(f_in, f_out)
                        print(f"Created tar.gz archive at {tar_gz_path}")

                shutil.rmtree(dotpngs_folder)
                os.remove(tar_path)

                old_file_pattern = f"{os.path.basename(fnl_folder)}_OLD.xstage"
                old_file_pattern2 = f"{os.path.basename(fnl_folder)}.xstage_OLD"
                old_file_pattern3 = f"{os.path.basename(fnl_folder)}.xstage_OLD.xstage"
                old_file_pattern4 = f"._{os.path.basename(fnl_folder)}.xstage_OLD.xstage"

                # Delete @eaDir directories in the original folder before copying
                for root, dirs, files in os.walk(full_path):
                    for dir in dirs:
                        if dir == "@eaDir":
                            dir_to_delete = os.path.join(root, dir)
                            print(f"Deleting directory {dir_to_delete}")
                            shutil.rmtree(dir_to_delete)

                # Copy the cleaned base folder to /Users/<USER>/Desktop/import excluding dotpngs.tar.gz
                import_destination = "/Users/<USER>/Desktop/import"
                destination_path = os.path.join(import_destination, os.path.basename(fnl_folder))

                shutil.copytree(fnl_folder, destination_path, dirs_exist_ok=True,
                                ignore=shutil.ignore_patterns('dotpngs.tar.gz'))

                # Delete _OLD.xstage files and @eaDir directories in the copied folder
                for root, dirs, files in os.walk(destination_path):
                    for file in files:
                        if file == old_file_pattern or file == old_file_pattern2 or file == old_file_pattern3 or file == old_file_pattern4:
                            file_to_delete = os.path.join(root, file)
                            print(f"Deleting {file_to_delete}")
                            os.remove(file_to_delete)

                    for dir in dirs:
                        if dir == "@eaDir":
                            dir_to_delete = os.path.join(root, dir)
                            print(f"Deleting directory {dir_to_delete}")
                            shutil.rmtree(dir_to_delete)

            QMessageBox.information(self, "Success", "Processed: Harmony file copy complete")

        except Exception as e:
            print(f"An error occurred: {e}")
            QMessageBox.warning(self, "Processing Error", f"An error occurred: {e}")


    def clear_list(self):
        self.folder_list.clear()
        self.folder_paths.clear() # folder_paths도 초기화


class LockManagerWindow(QDialog):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Lock Manager")
        self.setGeometry(100, 100, 1050, 900)

        main_layout = QVBoxLayout(self)
        top_layout = QHBoxLayout()

        # Environments section, 간소화
        self.envListBox = self.create_list_widget(label_text='Environments',
                                                  selection_callback=self.updateJobs)
        env_path = '/USA_DB/environments'
        if os.path.exists(env_path):
            environments = sorted([d for d in os.listdir(env_path) if os.path.isdir(os.path.join(env_path, d))])
            self.envListBox.addItems(environments)

        # Jobs section, 간소화
        self.jobsListBox = self.create_list_widget(label_text='Jobs',
                                                   selection_callback=self.updateLockList) # scene list 도 같이 업데이트 되게 수정.
        self.jobsListBox.itemSelectionChanged.connect(self.updateScenes)

        # Scene section, 간소화
        self.sceneListBox = self.create_list_widget(label_text='Scene',
                                                    selection_callback=self.updateLockList)


        # Lock List section, 간소화
        self.lockListBox = self.create_list_widget(label_text='Lock List', selection_mode=QListWidget.ExtendedSelection)

        # Environments, Jobs, Scene 레이아웃 추가
        for list_box in [self.envListBox, self.jobsListBox, self.sceneListBox]:
            layout = QVBoxLayout()
            layout.addWidget(list_box.label)  # QLabel 위젯 추가
            layout.addWidget(list_box)
            top_layout.addLayout(layout)


        # Unlock 버튼, 간소화
        unlockButton = QPushButton('선택해서 lock 풀기')
        unlockButton.clicked.connect(self.unlockLocks)

        # Add top layout and lock layout to the main layout
        main_layout.addLayout(top_layout)

        lock_layout = QVBoxLayout()  # 레이아웃 생성
        lock_layout.addWidget(self.lockListBox.label)  # 라벨 추가
        lock_layout.addWidget(self.lockListBox)

        main_layout.addLayout(lock_layout)
        main_layout.addWidget(unlockButton)

    def create_list_widget(self, label_text, selection_callback=None, selection_mode=QListWidget.SingleSelection):
        """ListWidget과 연결된 QLabel을 생성하고 초기화하는 헬퍼 함수"""
        list_widget = QListWidget()
        list_widget.label = QLabel(label_text)  # 연결된 QLabel 생성
        list_widget.setSelectionMode(selection_mode)
        if selection_callback:
            list_widget.itemSelectionChanged.connect(selection_callback)
        return list_widget


    def updateJobs(self):
        self.jobsListBox.clear()
        selected_env = self.envListBox.currentItem()
        if not selected_env:
            return

        env_text = selected_env.text()

        env_settings = {
            'Bigmouth': ['/usadata2/Titmouse/Big_Mouth/BM_Season8', '/usadata2/Titmouse/Big_Mouth'],
            'Bobs_Burgers': ['/usadata3/Bento_Project/BB_Season13', '/usadata3/Bento_Project/BB_Season14', '/usadata3/Bento_Project'],
            'Bobs_Burgers_old': ['/usadata3/Bento_Project/BB_Season'],
            'King_Of_The_Hill': ['/usadata2/Disney/KOTH/KOTH_Season14', '/usadata2/Disney/KOTH/KOTH_Season15', '/usadata2/Disney/KOTH'],
            'The_Great_North': ['/usadata3/Bento_Project2/Great_North/GN_Season4', '/usadata3/Bento_Project2/Great_North/GN_Season5', '/usadata3/Bento_Project2/Great_North'],
            'Test': ['/usadata3/Test'],
            'Yeson_Test': ['/usadata3/Yeson_Test'],
            'Yeson_Test_4K': ['/usadata3/Yeson_Test_4K']
        }

        if env_text not in env_settings:
            return

        # Get the list of paths for the selected environment
        base_paths = env_settings[env_text]

        # Iterate over each base path (e.g., BM_Season8 and BM_Library)
        for base_path in base_paths:
            if os.path.exists(base_path):
                for job in sorted(os.listdir(base_path)):
                    job_path = os.path.join(base_path, job)
                    # Exclude "BM_Season8" and "EXPORT" from being displayed
                    if os.path.isdir(job_path) and "_Season" not in job and job != "EXPORT":
                        self.jobsListBox.addItem(job)

        self.jobsListBox.sortItems()

    def updateScenes(self):
        self.sceneListBox.clear()

        selected_job = self.jobsListBox.currentItem()
        selected_env = self.envListBox.currentItem()

        if not selected_job or not selected_env:
            return

        env_text = selected_env.text()

        env_settings = {
            'Bigmouth': ['/usadata2/Titmouse/Big_Mouth/BM_Season8', '/usadata2/Titmouse/Big_Mouth'],
            'Bobs_Burgers': ['/usadata3/Bento_Project/BB_Season13', '/usadata3/Bento_Project/BB_Season14', '/usadata3/Bento_Project'],
            'Bobs_Burgers_old': ['/usadata3/Bento_Project/BB_Season'],
            'King_Of_The_Hill': ['/usadata2/Disney/KOTH/KOTH_Season14', '/usadata2/Disney/KOTH/KOTH_Season15', '/usadata2/Disney/KOTH'],
            'The_Great_North': ['/usadata3/Bento_Project2/Great_North/GN_Season4', '/usadata3/Bento_Project2/Great_North/GN_Season5', '/usadata3/Bento_Project2/Great_North'],
            'Test': ['/usadata3/Test'],
            'Yeson_Test': ['/usadata3/Yeson_Test'],
            'Yeson_Test_4K': ['/usadata3/Yeson_Test_4K']
        }

        if env_text in env_settings:
            base_paths = env_settings[env_text]

        job_paths = []

        for base_path in base_paths:
            job_path = os.path.join(base_path, selected_job.text())

            if os.path.exists(job_path):
                job_paths.append(job_path)


        if not job_paths and env_text in ['Test', 'Yeson_Test', 'Yeson_Test_4K']:
            job_path = os.path.join(base_path, selected_job.text())
            if os.path.exists(job_path):
                job_paths.append(job_path)

        scene_paths = []  # Initialize scene_paths list

        for job_path in job_paths:
            for item in sorted(os.listdir(job_path)):
                item_path = os.path.join(job_path, item)
                # Only add items that start with "scene"
                if os.path.isdir(item_path) and item.startswith('scene'):
                    scene_paths.append(item_path)
        for scene_path in scene_paths:
            list_item_text = os.path.basename(scene_path)

            list_item = QListWidgetItem(list_item_text)

            list_item.setData(Qt.UserRole, scene_path)
            self.sceneListBox.addItem(list_item)
        self.sceneListBox.sortItems()
    def updateLockList(self):
        self.lockListBox.clear()
        selected_job_item = self.jobsListBox.currentItem()
        selected_scene_item = self.sceneListBox.currentItem()
        selected_env_item = self.envListBox.currentItem()

        # Check if job and environment are selected
        if not (selected_job_item and selected_env_item):
            return

        selected_env = selected_env_item.text()
        selected_job1 = selected_job_item.text()
        selected_job2 = selected_job_item.text().upper()

        # Check if a scene is selected
        selected_scene1 = selected_scene_item.text() if selected_scene_item else None
        selected_scene2 = selected_scene_item.text().upper() if selected_scene_item else None

        env_settings = {
            'Bigmouth': ['/usadata2/Titmouse/Big_Mouth/BM_Season8', '/usadata2/Titmouse/Big_Mouth'],
            'Bobs_Burgers': ['/usadata3/Bento_Project/BB_Season13', '/usadata3/Bento_Project/BB_Season14', '/usadata3/Bento_Project'],
            'Bobs_Burgers_old': ['/usadata3/Bento_Project/BB_Season'],
            'King_Of_The_Hill': ['/usadata2/Disney/KOTH/KOTH_Season14', '/usadata2/Disney/KOTH/KOTH_Season15', '/usadata2/Disney/KOTH'],
            'The_Great_North': ['/usadata3/Bento_Project2/Great_North/GN_Season4', '/usadata3/Bento_Project2/Great_North/GN_Season5', '/usadata3/Bento_Project2/Great_North'],
            'Test': ['/usadata3/Test'],
            'Yeson_Test': ['/usadata3/Yeson_Test'],
            'Yeson_Test_4K': ['/usadata3/Yeson_Test_4K']
        }

        base_paths = env_settings.get(selected_env, [])


        for base_path in base_paths:
            try:
                result = subprocess.run(
                    "/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/dblock -l",
                    shell=True, capture_output=True, text=True, check=True
                )

                if result.returncode != 0:
                    QMessageBox.critical(self, "Error", f"Lock check failed: {result.stderr}")
                    return

                locks = result.stdout.splitlines()

                formatted_locks = []
                if not selected_scene2:
                    for lock in locks:
                        if f"/USA_DB/environments/{selected_env}/palette-library/" in lock or \
                        f"/USA_DB/jobs/{selected_job1}/palette-library/" in lock:
                            key_part = lock.split('key:')[1].split(' ')[0]
                            user_part = lock.split('(user:')[1].split(')')[0]
                            formatted_lock = f"{key_part} (user:{user_part})"
                            formatted_locks.append(formatted_lock)

                # If job, environment, and scene are all selected
                else:
                    for lock in locks:
                        if f"/{selected_job2}/{selected_scene2}" in lock or \
                            f"/{selected_job1}/{selected_scene1}" in lock:
                            key_part = lock.split('key:')[1].split(' ')[0]
                            user_part = lock.split('(user:')[1].split(')')[0]
                            formatted_lock = f"{key_part} (user:{user_part})"
                            formatted_locks.append(formatted_lock)

                # Debug: Print formatted locks
                print("Formatted Locks:", formatted_locks)

                self.lockListBox.setStyleSheet("font-size: 20pt;")  # Adjust the font size as needed

                self.lockListBox.setSelectionMode(QListWidget.ExtendedSelection)

                self.lockListBox.addItems(formatted_locks)

            except subprocess.CalledProcessError as e:
                QMessageBox.critical(self, "Error", f"Failed to retrieve lock list: {e}")

    def unlockLocks(self):
        selected_job_items = self.jobsListBox.selectedItems()
        selected_env_item = self.envListBox.currentItem()
        selected_lock_items = self.lockListBox.selectedItems()

        if not (selected_job_items and selected_env_item):
            QMessageBox.warning(self, "Warning", "Please select an environment and job(s).")
            return

        selected_env = selected_env_item.text()

        try:
            for lock_item in selected_lock_items:
                lock_text = lock_item.text()
                # Extract the key path from the lock text
                key_path = lock_text.split(' ')[0]

                # Execute unlock command for each selected lock
                subprocess.run(
                    f"/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/dblock -e {key_path}",
                    shell=True, check=True
                )

            QMessageBox.information(self, "Success", "Locks have been successfully removed.")

            self.updateLockList()

        except subprocess.CalledProcessError as e:
            QMessageBox.critical(self, "Error", f"Failed to unlock: {e}")

class SceneCopyWindow(QDialog):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Scene Copy")
        self.setGeometry(100, 100, 1050, 900)

        main_layout = QVBoxLayout(self)

        # Top Layout for Source Lists
        top_layout = QHBoxLayout()

        # Source Environments section
        srcEnvLabel = QLabel('Source Environments')
        self.srcEnvListBox = QListWidget()
        self.srcEnvListBox.itemSelectionChanged.connect(self.updateSrcJobs)
        env_path = '/USA_DB/environments'
        if os.path.exists(env_path):
            environments = sorted([d for d in os.listdir(env_path) if os.path.isdir(os.path.join(env_path, d))])
            self.srcEnvListBox.addItems(environments)
        srcEnvLayout = QVBoxLayout()
        srcEnvLayout.addWidget(srcEnvLabel)
        srcEnvLayout.addWidget(self.srcEnvListBox)

        # Source Jobs section
        srcJobsLabel = QLabel('Source Jobs')
        self.srcJobsListBox = QListWidget()
        self.srcJobsListBox.itemSelectionChanged.connect(self.updateSrcScenes)
        srcJobsLayout = QVBoxLayout()
        srcJobsLayout.addWidget(srcJobsLabel)
        srcJobsLayout.addWidget(self.srcJobsListBox)

        # Source Scenes section
        srcSceneLabel = QLabel('Source Scenes')
        self.srcSceneListBox = QListWidget()
        srcSceneLayout = QVBoxLayout()
        srcSceneLayout.addWidget(srcSceneLabel)
        srcSceneLayout.addWidget(self.srcSceneListBox)

        # Add Source sections to the top layout
        top_layout.addLayout(srcEnvLayout)
        top_layout.addLayout(srcJobsLayout)
        top_layout.addLayout(srcSceneLayout)

        # Bottom Layout for Target Lists
        bottom_layout = QHBoxLayout()

        # Target Environments section
        tgtEnvLabel = QLabel('Target Environments')
        self.tgtEnvListBox = QListWidget()
        self.tgtEnvListBox.itemSelectionChanged.connect(self.updateTgtJobs)
        env_path = '/USA_DB/environments'
        if os.path.exists(env_path):
            environments = sorted([d for d in os.listdir(env_path) if os.path.isdir(os.path.join(env_path, d))])
            self.tgtEnvListBox.addItems(environments)
        tgtEnvLayout = QVBoxLayout()
        tgtEnvLayout.addWidget(tgtEnvLabel)
        tgtEnvLayout.addWidget(self.tgtEnvListBox)

        # Target Jobs section
        tgtJobsLabel = QLabel('Target Jobs')
        self.tgtJobsListBox = QListWidget()
        self.tgtJobsListBox.itemSelectionChanged.connect(self.updateTgtScenes)
        tgtJobsLayout = QVBoxLayout()
        tgtJobsLayout.addWidget(tgtJobsLabel)
        tgtJobsLayout.addWidget(self.tgtJobsListBox)

        # Target Scenes section
        tgtSceneLabel = QLabel('Target Scenes')
        self.tgtSceneListBox = QListWidget()
        tgtSceneLayout = QVBoxLayout()
        tgtSceneLayout.addWidget(tgtSceneLabel)
        tgtSceneLayout.addWidget(self.tgtSceneListBox)

        # Add Target sections to the bottom layout
        bottom_layout.addLayout(tgtEnvLayout)
        bottom_layout.addLayout(tgtJobsLayout)
        bottom_layout.addLayout(tgtSceneLayout)

        # Copy Button
        copyButton = QPushButton('Scene 복사 시작')
        copyButton.clicked.connect(self.copyScene)

        # Add layouts to main layout
        main_layout.addLayout(top_layout)

        main_layout.addWidget(copyButton)

        main_layout.addLayout(bottom_layout)

    def updateSrcJobs(self):
        self.srcJobsListBox.clear()
        selected_env = self.srcEnvListBox.currentItem()
        if not selected_env:
            return

        env_text = selected_env.text()
        filter_old = False

        env_settings = {
            'Bigmouth': ('/usadata2/Titmouse/Big_Mouth', 'BM_Season'),
            'Bobs_Burgers': ('/usadata3/Bento_Project', 'BB_Season'),
            'Bobs_Burgers_old': ('/usadata3/Bento_Project', 'BB_Season', True),
            'King_Of_The_Hill': ('/usadata2/Disney/KOTH', 'KOTH_Season'),
            'The_Great_North': ('/usadata3/Bento_Project2/Great_North', 'GN_Season'),
            'The_Great_North_old': ('/usadata3/Bento_Project2/Great_North', 'GN_Season', True),
            'Test': ('/usadata3/Test', ''),
            'Yeson_Test': ('/usadata3/Yeson_Test', '')
        }

        if env_text in env_settings:
            base_path, season_prefix, *filter_old_flag = env_settings[env_text]
            if filter_old_flag:
                filter_old = filter_old_flag[0]
        else:
            return

        if os.path.exists(base_path):
            if env_text in ['Test', 'Yeson_Test']:
                for job in sorted(os.listdir(base_path)):
                    job_path = os.path.join(base_path, job)
                    if os.path.isdir(job_path):
                        self.srcJobsListBox.addItem(job)
            else:
                for folder in sorted(os.listdir(base_path)):
                    folder_path = os.path.join(base_path, folder)
                    is_old = '_old' in folder
                    if filter_old and not is_old:
                        continue
                    if not filter_old and is_old:
                        continue
                    if os.path.isdir(folder_path) and (season_prefix in folder or not season_prefix):
                        for job in sorted(os.listdir(folder_path)):
                            job_path = os.path.join(folder_path, job)
                            if os.path.isdir(job_path):
                                self.srcJobsListBox.addItem(job)
            self.srcJobsListBox.sortItems()

    def updateSrcScenes(self):
        self.srcSceneListBox.clear()
        selected_job = self.srcJobsListBox.currentItem()
        selected_env = self.srcEnvListBox.currentItem()
        if not selected_job or not selected_env:
            return

        env_text = selected_env.text()
        base_path = ''

        env_settings = {
            'Bigmouth': '/usadata2/Titmouse/Big_Mouth',
            'Bobs_Burgers': '/usadata3/Bento_Project',
            'Bobs_Burgers_old': '/usadata3/Bento_Project',
            'King_Of_The_Hill': '/usadata2/Disney/KOTH',
            'The_Great_North': '/usadata3/Bento_Project2/Great_North',
            'The_Great_North_old': '/usadata3/Bento_Project2/Great_North',
            'Test': '/usadata3/Test',
            'Yeson_Test': '/usadata3/Yeson_Test'
        }

        if env_text in env_settings:
            base_path = env_settings[env_text]

        try:
            season_dirs = [folder for folder in os.listdir(base_path) if 'Season' in folder]
        except FileNotFoundError as e:
            print(f"Error accessing base path: {e}")
            return

        job_paths = []

        for season_dir in season_dirs:
            job_path = os.path.join(base_path, season_dir, selected_job.text())
            if os.path.exists(job_path):
                job_paths.append(job_path)

        if not job_paths and env_text in ['Test', 'Yeson_Test']:
            job_path = os.path.join(base_path, selected_job.text())
            if os.path.exists(job_path):
                job_paths.append(job_path)

        for job_path in job_paths:
            for item in sorted(os.listdir(job_path)):
                item_path = os.path.join(job_path, item)
                if not item.startswith('.') and (os.path.isdir(item_path) or os.path.isfile(item_path)):
                    list_item = QListWidgetItem(item)
                    list_item.setData(Qt.UserRole, item_path)
                    self.srcSceneListBox.addItem(list_item)

        self.srcSceneListBox.sortItems()

    def updateTgtJobs(self):
        self.tgtJobsListBox.clear()
        selected_env = self.tgtEnvListBox.currentItem()
        if not selected_env:
            return

        env_text = selected_env.text()
        filter_old = False

        env_settings = {
            'Bigmouth': ('/usadata2/Titmouse/Big_Mouth', 'BM_Season'),
            'Bobs_Burgers': ('/usadata3/Bento_Project', 'BB_Season'),
            'Bobs_Burgers_old': ('/usadata3/Bento_Project', 'BB_Season', True),
            'King_Of_The_Hill': ('/usadata2/Disney/KOTH', 'KOTH_Season'),
            'The_Great_North': ('/usadata3/Bento_Project2/Great_North', 'GN_Season'),
            'The_Great_North_old': ('/usadata3/Bento_Project2/Great_North', 'GN_Season', True),
            'Test': ('/usadata3/Test', ''),
            'Yeson_Test': ('/usadata3/Yeson_Test', '')
        }

        if env_text in env_settings:
            base_path, season_prefix, *filter_old_flag = env_settings[env_text]
            if filter_old_flag:
                filter_old = filter_old_flag[0]
        else:
            return

        if os.path.exists(base_path):
            if env_text in ['Test', 'Yeson_Test']:
                for job in sorted(os.listdir(base_path)):
                    job_path = os.path.join(base_path, job)
                    if os.path.isdir(job_path):
                        self.tgtJobsListBox.addItem(job)
            else:
                for folder in sorted(os.listdir(base_path)):
                    folder_path = os.path.join(base_path, folder)
                    is_old = '_old' in folder
                    if filter_old and not is_old:
                        continue
                    if not filter_old and is_old:
                        continue
                    if os.path.isdir(folder_path) and (season_prefix in folder or not season_prefix):
                        for job in sorted(os.listdir(folder_path)):
                            job_path = os.path.join(folder_path, job)
                            if os.path.isdir(job_path):
                                self.tgtJobsListBox.addItem(job)
            self.tgtJobsListBox.sortItems()


    def updateTgtScenes(self):
        self.tgtSceneListBox.clear()
        selected_job = self.tgtJobsListBox.currentItem()
        selected_env = self.tgtEnvListBox.currentItem()
        if not selected_job or not selected_env:
            return

        env_text = selected_env.text()
        base_path = ''

        env_settings = {
            'Bigmouth': '/usadata2/Titmouse/Big_Mouth',
            'Bobs_Burgers': '/usadata3/Bento_Project',
            'Bobs_Burgers_old': '/usadata3/Bento_Project',
            'King_Of_The_Hill': '/usadata2/Disney/KOTH',
            'The_Great_North': '/usadata3/Bento_Project2/Great_North',
            'The_Great_North_old': '/usadata3/Bento_Project2/Great_North',
            'Test': '/usadata3/Test',
            'Yeson_Test': '/usadata3/Yeson_Test'
        }

        if env_text in env_settings:
            base_path = env_settings[env_text]

        try:
            season_dirs = [folder for folder in os.listdir(base_path) if 'Season' in folder]
        except FileNotFoundError as e:
            print(f"Error accessing base path: {e}")
            return

        job_paths = []

        for season_dir in season_dirs:
            job_path = os.path.join(base_path, season_dir, selected_job.text())
            if os.path.exists(job_path):
                job_paths.append(job_path)

        if not job_paths and env_text in ['Test', 'Yeson_Test']:
            job_path = os.path.join(base_path, selected_job.text())
            if os.path.exists(job_path):
                job_paths.append(job_path)

        for job_path in job_paths:
            for item in sorted(os.listdir(job_path)):
                item_path = os.path.join(job_path, item)
                if not item.startswith('.') and (os.path.isdir(item_path) or os.path.isfile(item_path)):
                    list_item = QListWidgetItem(item)
                    list_item.setData(Qt.UserRole, item_path)
                    self.tgtSceneListBox.addItem(list_item)

        self.tgtSceneListBox.sortItems()

    def copyScene(self):
        selected_src_env_item = self.srcEnvListBox.currentItem()
        selected_src_job_item = self.srcJobsListBox.currentItem()
        selected_src_scene_item = self.srcSceneListBox.currentItem()

        selected_tgt_env_item = self.tgtEnvListBox.currentItem()
        selected_tgt_job_item = self.tgtJobsListBox.currentItem()
        selected_tgt_scene_item = self.tgtSceneListBox.currentItem()

        if not (selected_src_env_item and selected_src_job_item and selected_src_scene_item and
                selected_tgt_env_item and selected_tgt_job_item and selected_tgt_scene_item):
            QMessageBox.warning(self, "Warning", "Please select all source and target fields.")
            return

        env_settings = {
            'Bigmouth': '/usadata2/Titmouse/Big_Mouth',
            'Bobs_Burgers': '/usadata3/Bento_Project',
            'King_Of_The_Hill': '/usadata2/Disney/KOTH',
            'The_Great_North': '/usadata3/Bento_Project2/Great_North',
            'Test': '/usadata3/Test',
            'Yeson_Test': '/usadata3/Yeson_Test'
        }

        def get_season_folder(env_text, job_text):
            if env_text == 'Bobs_Burgers':
                if job_text.startswith('DASA'):
                    return 'BB_Season13'
                elif job_text.startswith('EASA'):
                    return 'BB_Season14'
                elif job_text.startswith('FASA'):
                    return 'BB_Season15'
            elif env_text == 'Bigmouth':
                return 'BM_Season8'
            elif env_text == 'The_Great_North':
                if job_text.startswith('4LBW'):
                    return 'GN_Season4'
                elif job_text.startswith('5LBW'):
                    return 'GN_Season5'
            elif env_text == 'King_Of_The_Hill':
                if job_text.startswith('EABE'):
                    return 'KOTH_Season14'
                elif job_text.startswith('15'):
                    return 'KOTH_Season15'
            return ''

        src_env_text = selected_src_env_item.text()
        tgt_env_text = selected_tgt_env_item.text()

        if src_env_text not in env_settings or tgt_env_text not in env_settings:
            QMessageBox.warning(self, "Warning", "Invalid environment selection.")
            return

        src_base_path = env_settings[src_env_text]
        tgt_base_path = env_settings[tgt_env_text]

        src_job = selected_src_job_item.text()
        src_scene = selected_src_scene_item.text()

        tgt_job = selected_tgt_job_item.text()
        tgt_scene = selected_tgt_scene_item.text()

        # Construct paths
        src_season_folder = get_season_folder(src_env_text, src_job)
        tgt_season_folder = get_season_folder(tgt_env_text, tgt_job)

        src_path1 = os.path.join(src_base_path, src_season_folder, src_job, src_scene)
        tgt_path1 = os.path.join(tgt_base_path, tgt_season_folder, tgt_job, tgt_scene)

        src_path2 = f"/USA_DB/db_jobs/{src_job}/{src_scene}"
        tgt_path2 = f"/USA_DB/db_jobs/{tgt_job}/{tgt_scene}"

        # Debug: Print constructed paths
        print(f"Source Path: {src_path1}")
        print(f"Target Path: {tgt_path1}")

        try:
            if os.path.exists(tgt_path1):
                shutil.rmtree(tgt_path1)
            shutil.copytree(src_path1, tgt_path1)

            # Copy from /USA_DB/db_jobs paths
            if os.path.exists(tgt_path2):
                shutil.rmtree(tgt_path2)
            shutil.copytree(src_path2, tgt_path2)
            QMessageBox.information(self, "Success", "Scene copied successfully.")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to copy scene: {e}")


class LogTransferDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("로그 파일 전송")
        self.setGeometry(100, 100, 800, 500)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # 로그 파일 목록
        self.log_list = QListWidget()
        self.log_list.setSelectionMode(QListWidget.ExtendedSelection)
        layout.addWidget(QLabel("전송할 로그 파일 선택:"))
        layout.addWidget(self.log_list)

        # 버튼들
        button_layout = QHBoxLayout()

        refresh_button = QPushButton("새로고침")
        refresh_button.clicked.connect(self.load_log_files)

        transfer_button = QPushButton("전송")
        transfer_button.clicked.connect(self.transfer_logs)

        close_button = QPushButton("닫기")
        close_button.clicked.connect(self.close)

        button_layout.addWidget(refresh_button)
        button_layout.addWidget(transfer_button)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)
        self.setLayout(layout)

        # 초기 파일 목록 로드
        self.load_log_files()

    def load_log_files(self):
        self.log_list.clear()
        log_dir = "/Volumes/data/bak"
        try:
            for file in os.listdir(log_dir):
                if file.endswith('.txt'):
                    self.log_list.addItem(file)
        except Exception as e:
            QMessageBox.warning(self, "Error", f"로그 파일 목록을 불러오는 중 오류가 발생했습니다: {str(e)}")

    def transfer_logs(self):
        selected_items = self.log_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "Warning", "전송할 로그 파일을 선택해주세요.")
            return

        progress = QProgressDialog("로그 파일 전송 중...", "취소", 0, len(selected_items), self)
        progress.setWindowModality(Qt.WindowModal)

        success_count = 0
        failed_files = []

        for i, item in enumerate(selected_items):
            if progress.wasCanceled():
                break

            source_file = os.path.join("/Volumes/data/bak", item.text())
            try:
                # rsync를 사용하여 파일 전송
                command = f"rsync -av {source_file} /USA_DB/test_jn/log/"
                result = subprocess.run(command, shell=True, capture_output=True, text=True)

                if result.returncode == 0:
                    success_count += 1
                else:
                    failed_files.append(item.text())

            except Exception as e:
                failed_files.append(item.text())

            progress.setValue(i + 1)

        progress.close()

        # 결과 메시지 표시
        if failed_files:
            QMessageBox.warning(self, "전송 완료",
                              f"{success_count}개 파일 전송 성공\n"
                              f"{len(failed_files)}개 파일 전송 실패:\n"
                              f"{', '.join(failed_files)}")
        else:
            QMessageBox.information(self, "전송 완료",
                                  f"모든 파일({success_count}개)이 성공적으로 전송되었습니다.")


class MyWindow(QWidget):
    def __init__(self, full_access, username, is_admin=True):
        super().__init__()

        self.initUI()
        self.initLogFile()  # 로그 파일 초기화


        self.existing_items = set()

        self.json_file_path = '/USA_DB/test_jn/scene_status.json'
        self.users_file_path = '/USA_DB/test_jn/users.json'
        self.ensure_directory_exists()
        self.scene_status = self.load_scene_status()

        self.full_access = full_access
        self.username = username
        self.is_admin = is_admin

        try:
            with open(self.users_file_path, 'r') as f:
                users = json.load(f)
                self.user_permissions = users.get(username, {}).get("permissions", {})
        except FileNotFoundError:
            self.user_permissions = {}

        # Create layout
        layout = QVBoxLayout(self)

        # Create menu bar
        menu_bar = QMenuBar(self)

        # Add "File" menu
        file_menu = menu_bar.addMenu(f"File    - User: {username}")

        # Define menu actions with their permissions
        menu_actions = {
            "Layout2 연결": {
                'action': QAction("Layout2 연결", self),
                'callback': self.open_layout2,
                'permission': 'Layout2',
                'admin_only': False
            },
            "스크립트 실행": {
                'action': QAction("스크립트 실행", self),
                'callback': self.open_thumbnail_window,
                'permission': 'Script',
                'admin_only': False
            },
            "Lock Manager": {
                'action': QAction("Lock Manager", self),
                'callback': self.open_lock_manager,
                'permission': 'LockManager',
                'admin_only': False
            },
            "User Management": {
                'action': QAction("User Management", self),
                'callback': self.open_user_management,
                'permission': 'UserManagement',
                'admin_only': True
            },
            "Log Transfer": {  # 새로 추가된 로그 전송 메뉴
                'action': QAction("관리자에게 로그 전송", self),
                'callback': self.show_log_transfer_dialog,
                'permission': 'LogTransfer',
                'admin_only': True
            },
            "Exit": {
                'action': QAction("Exit", self),
                'callback': self.close,
                'permission': None,  # Exit is always available
                'admin_only': False
            }
        }

        # Add actions based on permissions
        for menu_name, menu_config in menu_actions.items():
            action = menu_config['action']

            # Check if action should be added
            should_add = False

            if menu_config['admin_only']:
                # Admin-only menus
                should_add = self.is_admin
            elif menu_config['permission'] is None:
                # Always available menus (like Exit)
                should_add = True
            else:
                # Check user permissions
                should_add = (self.user_permissions.get('menus', {}).get(menu_config['permission'], False)
                            or self.is_admin)

            if should_add:
                action.triggered.connect(menu_config['callback'])
                file_menu.addAction(action)

        # Add menu bar to layout
        layout.setMenuBar(menu_bar)

        # Set the layout for the widget
        self.setLayout(layout)

    def show_log_transfer_dialog(self):
        dialog = LogTransferDialog(self)
        dialog.exec_()

    def open_user_management(self):
        self.user_management = UserManagementDialog(self)
        self.user_management.show()

    def open_lock_manager(self):
        dialog = LockManagerWindow()
        dialog.exec_()

    def open_layout2(self):
        url = QUrl("smb://192.168.0.18/layout2")
        QDesktopServices.openUrl(url)

    def open_thumbnail_window(self):
        self.thumbnail_window = ThumbnailWindow()
        self.thumbnail_window.show()

    def ensure_directory_exists(self):
        # JSON 파일을 저장할 디렉터리가 존재하는지 확인하고 없으면 생성
        json_dir = os.path.dirname(self.json_file_path)
        if not os.path.exists(json_dir):
            os.makedirs(json_dir)

    def load_scene_status(self):
        try:
            with open(self.json_file_path, 'r') as file:
                return json.load(file)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}

    def save_scene_status(self):
        with open(self.json_file_path, 'w') as file:
            json.dump(self.scene_status, file)

    def set_checkout_status(self, scene_name, status):
        self.scene_status[scene_name] = status
        self.save_scene_status()

    def is_checked_out(self, scene_name):
        return self.scene_status.get(scene_name, False)

    def refresh_scene_list(self):
        """현재 작업과 환경에 따라 장면 목록을 새로 고칩니다."""
        self.sceneListBox.clear()
        selected_job = self.jobsListBox.currentItem()
        selected_env = self.envListBox.currentItem()

        def initUI(self):
            mainLayout = QVBoxLayout()
            topLayout = QHBoxLayout()
    
            # 로그 창
            self.logTextEdit = QTextEdit()
            self.logTextEdit.setReadOnly(True)
    
    
             #로그와 환경 목록을 수직으로 정렬
            logAndEnvLayout = QVBoxLayout()
            logAndEnvLayout.addWidget(QLabel('Logs'))
            logAndEnvLayout.addWidget(self.logTextEdit)
    
            self.logTextEdit.setStyleSheet("background-color: #FAF4C0; color: black;")
            # Set fixed size for the log text edit
            self.logTextEdit.setFixedSize(450, 900)  # Example size, adjust as needed
    
    
            # Environments section
            envLabel = QLabel('Environments')
            self.envListBox = QListWidget()
            self.envListBox.itemSelectionChanged.connect(self.updateJobs)
            envLayout = QVBoxLayout()
            envLayout.addWidget(envLabel)
            envLayout.addWidget(self.envListBox)
    
            # Jobs section
            jobsLabel = QLabel('Jobs')
            self.jobsListBox = QListWidget()
            self.jobsListBox.setContextMenuPolicy(Qt.CustomContextMenu)
            self.jobsListBox.itemSelectionChanged.connect(self.updateScenes)
            self.jobsListBox.customContextMenuRequested.connect(lambda pos: self.showContextMenu(pos, 'jobs'))
            jobsLayout = QVBoxLayout()
            jobsLayout.addWidget(jobsLabel)
            jobsLayout.addWidget(self.jobsListBox)
    
            # Add Environments and Jobs sections to the top layout
            topLayout.addLayout(envLayout)
            topLayout.addLayout(jobsLayout)
    
            # Scene section
            sceneLabel = QLabel('Scene')
            self.sceneListBox = QListWidget()
            self.sceneListBox.setSelectionMode(QListWidget.ExtendedSelection)  # Enable multi-selection
            self.sceneListBox.setContextMenuPolicy(Qt.CustomContextMenu)
            self.sceneListBox.customContextMenuRequested.connect(lambda pos: self.showContextMenu(pos, 'scenes'))
            sceneLayout = QVBoxLayout()
            sceneLayout.addWidget(sceneLabel)
            sceneLayout.addWidget(self.sceneListBox)
    
            # Add top layout and Scene section to the main layout
            mainLayout.addLayout(topLayout)
            mainLayout.addLayout(sceneLayout)
    
    
            # Add notes section to the main layout
            mainHorizontalLayout = QHBoxLayout()
    
            # Set stretch factors for layouts
            mainHorizontalLayout.addLayout(mainLayout, 2)  # Existing UI components with stretch factor of 2
            mainHorizontalLayout.addStretch(0)  # Optional: Add stretch between sections if needed
    
    
            #메인 수평 레이아웃에 로그 및 환경 레이아웃과 노트 레이아웃 추가
    
            mainHorizontalLayout.addLayout(logAndEnvLayout)
            # Add mainHorizontalLayout to mainLayout
            mainLayout.addLayout(mainHorizontalLayout)
    
            self.setLayout(mainLayout) # Add this line
            self.setWindowTitle('controlcenter -ver2.81')
            self.setGeometry(100, 100, 1250, 900)
    
            self.populateEnvironments()
    
            self.sceneListBox.itemDoubleClicked.connect(self.handle_double_click_scene_list)
    def initUI(self):
        mainLayout = QVBoxLayout()
        topLayout = QHBoxLayout()

        # 로그 창
        self.logTextEdit = QTextEdit()
        self.logTextEdit.setReadOnly(True)


         #로그와 환경 목록을 수직으로 정렬
        logAndEnvLayout = QVBoxLayout()
        logAndEnvLayout.addWidget(QLabel('Logs'))
        logAndEnvLayout.addWidget(self.logTextEdit)

        self.logTextEdit.setStyleSheet("background-color: #FAF4C0; color: black;")
        # Set fixed size for the log text edit
        self.logTextEdit.setFixedSize(450, 900)  # Example size, adjust as needed


        # Environments section
        envLabel = QLabel('Environments')
        self.envListBox = QListWidget()
        self.envListBox.itemSelectionChanged.connect(self.updateJobs)
        envLayout = QVBoxLayout()
        envLayout.addWidget(envLabel)
        envLayout.addWidget(self.envListBox)

        # Jobs section
        jobsLabel = QLabel('Jobs')
        self.jobsListBox = QListWidget()
        self.jobsListBox.setContextMenuPolicy(Qt.CustomContextMenu)
        self.jobsListBox.itemSelectionChanged.connect(self.updateScenes)
        self.jobsListBox.customContextMenuRequested.connect(lambda pos: self.showContextMenu(pos, 'jobs'))
        jobsLayout = QVBoxLayout()
        jobsLayout.addWidget(jobsLabel)
        jobsLayout.addWidget(self.jobsListBox)

        # Add Environments and Jobs sections to the top layout
        topLayout.addLayout(envLayout)
        topLayout.addLayout(jobsLayout)

        # Scene section
        sceneLabel = QLabel('Scene')
        self.sceneListBox = QListWidget()
        self.sceneListBox.setSelectionMode(QListWidget.ExtendedSelection)  # Enable multi-selection
        self.sceneListBox.setContextMenuPolicy(Qt.CustomContextMenu)
        self.sceneListBox.customContextMenuRequested.connect(lambda pos: self.showContextMenu(pos, 'scenes'))
        sceneLayout = QVBoxLayout()
        sceneLayout.addWidget(sceneLabel)
        sceneLayout.addWidget(self.sceneListBox)

        # Add top layout and Scene section to the main layout
        mainLayout.addLayout(topLayout)
        mainLayout.addLayout(jobsLayout)
        mainLayout.addLayout(sceneLayout)


        # Add notes section to the main layout
        mainHorizontalLayout = QHBoxLayout()

        # Set stretch factors for layouts
        mainHorizontalLayout.addLayout(mainLayout, 2)  # Existing UI components with stretch factor of 2
        mainHorizontalLayout.addStretch(0)  # Optional: Add stretch between sections if needed


        self.setLayout(mainLayout)

         #메인 수평 레이아웃에 로그 및 환경 레이아웃과 노트 레이아웃 추가

        mainHorizontalLayout.addLayout(logAndEnvLayout)


        self.setLayout(mainLayout)
        self.setWindowTitle('controlcenter -ver2.81')
        self.setGeometry(100, 100, 1250, 900)

        self.populateEnvironments()

        self.sceneListBox.itemDoubleClicked.connect(self.handle_double_click_scene_list)

    def populateEnvironments(self):
        env_path = '/USA_DB/environments'
        if os.path.exists(env_path):
            for folder in sorted(os.listdir(env_path)):
                folder_path = os.path.join(env_path, folder)
                if os.path.isdir(folder_path):
                    self.envListBox.addItem(folder)
            self.envListBox.sortItems()

    def initLogFile(self):
        # Create backup directory if it doesn't exist
        backup_dir = "/Volumes/data/bak"
        os.makedirs(backup_dir, exist_ok=True)

        # Create a log file with today's date
        today_date = datetime.now().strftime('%Y-%m-%d')
        self.log_file_path = os.path.join(backup_dir, f"log_{today_date}.txt")

        # Open log file in append mode
        self.log_file = open(self.log_file_path, "a")

    def writeLog(self, message):
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        full_message = f"[{timestamp}] {message}\n"

        # Write to file
        self.log_file.write(full_message)
        self.log_file.flush()

        # Display in UI
        self.logTextEdit.append(full_message)

    def closeEvent(self, event):
        # Close the log file when the window is closed
        if hasattr(self, 'log_file'):
            self.log_file.close()
        event.accept()

    def updateJobs(self):
        self.jobsListBox.clear()
        selected_env = self.envListBox.currentItem()
        if not selected_env:
            return

        env_text = selected_env.text()

        # Define paths for BM_Season and BM_Library
        env_settings = {
            'Bigmouth': ['/usadata2/Titmouse/Big_Mouth/BM_Season8', '/usadata2/Titmouse/Big_Mouth'],
            'Bobs_Burgers': ['/usadata3/Bento_Project/BB_Season13', '/usadata3/Bento_Project/BB_Season14', '/usadata3/Bento_Project'],
            'Bobs_Burgers_old': ['/usadata3/Bento_Project/BB_Season'],
            'King_Of_The_Hill': ['/usadata2/Disney/KOTH/KOTH_Season14', '/usadata2/Disney/KOTH/KOTH_Season15', '/usadata2/Disney/KOTH'],
            'The_Great_North': ['/usadata3/Bento_Project2/Great_North/GN_Season4', '/usadata3/Bento_Project2/Great_North/GN_Season5', '/usadata3/Bento_Project2/Great_North'],
            'Test': ['/usadata3/Test'],
            'Yeson_Test': ['/usadata3/Yeson_Test'],
            'Yeson_Test_4K': ['/usadata3/Yeson_Test_4K']
        }

        if env_text not in env_settings:
            return

        # Get the list of paths for the selected environment
        base_paths = env_settings[env_text]

        # Iterate over each base path (e.g., BM_Season8 and BM_Library)
        for base_path in base_paths:
            if os.path.exists(base_path):
                for job in sorted(os.listdir(base_path)):
                    job_path = os.path.join(base_path, job)
                    # Exclude "BM_Season8" and "EXPORT" from being displayed
                    if os.path.isdir(job_path) and "_Season" not in job and job != "EXPORT":
                        self.jobsListBox.addItem(job)

        self.jobsListBox.sortItems()

    def updateScenes(self):
        self.sceneListBox.clear()
        selected_job = self.jobsListBox.currentItem()
        selected_env = self.envListBox.currentItem()
        if not selected_job or not selected_env:
            return

        env_text = selected_env.text()
        #base_path = ''

        env_settings = {
            'Bigmouth': ['/usadata2/Titmouse/Big_Mouth/BM_Season8', '/usadata2/Titmouse/Big_Mouth'],
            'Bobs_Burgers': ['/usadata3/Bento_Project/BB_Season13', '/usadata3/Bento_Project/BB_Season14', '/usadata3/Bento_Project'],
            'Bobs_Burgers_old': ['/usadata3/Bento_Project/BB_Season'],
            'King_Of_The_Hill': ['/usadata2/Disney/KOTH/KOTH_Season14', '/usadata2/Disney/KOTH/KOTH_Season15', '/usadata2/Disney/KOTH'],
            'The_Great_North': ['/usadata3/Bento_Project2/Great_North/GN_Season4', '/usadata3/Bento_Project2/Great_North/GN_Season5', '/usadata3/Bento_Project2/Great_North'],
            'Test': ['/usadata3/Test'],
            'Yeson_Test': ['/usadata3/Yeson_Test'],
            'Yeson_Test_4K': ['/usadata3/Yeson_Test_4K']
        }

        if env_text in env_settings:
            base_paths = env_settings[env_text]

        job_paths = []

        for base_path in base_paths:
            job_path = os.path.join(base_path, selected_job.text())

            if os.path.exists(job_path):
                job_paths.append(job_path)


        if not job_paths and env_text in ['Test', 'Yeson_Test', 'Yeson_Test_4K']:
            job_path = os.path.join(base_path, selected_job.text())
            if os.path.exists(job_path):
                job_paths.append(job_path)

        scene_paths = []  # Initialize scene_paths list

        for job_path in job_paths:
            for item in sorted(os.listdir(job_path)):
                item_path = os.path.join(job_path, item)
                # Only add items that start with "scene"
                if os.path.isdir(item_path) and item.startswith('scene'):
                    scene_paths.append(item_path)

        for scene_path in scene_paths:
            list_item_text = os.path.basename(scene_path)

            list_item = QListWidgetItem(list_item_text)

            # Check if the exact scene path is checked out
            if self.is_checked_out(scene_path):
                list_item.setText(f"{list_item_text} (Checkout)")
                list_item.setForeground(QColor('red'))
                print(f"Marked as checked out: {list_item_text}")  # Debugging

            list_item.setData(Qt.UserRole, scene_path)
            self.sceneListBox.addItem(list_item)

        self.sceneListBox.sortItems()
        dbu_command = f"/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/dbu -l -r /USA_DB/db_jobs/{selected_job.text()}/scene.db"
        frames_info, path_info = {}, {}

        try:
            result = subprocess.run(dbu_command, shell=True, capture_output=True, text=True, check=True)

            # Parse the output to map scene paths to frame counts
            lines = result.stdout.splitlines()

            current_path = None
            for line in lines:
                line = line.strip()
                if line.startswith("Path:"):
                    current_path = line.split("Path:")[-1].strip()
                    # Remove job and scene names from the path for display
                    display_path_value = current_path.replace(selected_job.text(), '').replace(os.path.basename(current_path), '').strip('/')
                    path_info[current_path] = display_path_value
                elif current_path and line.startswith("Frames:"):
                    frames_count = line.split("Frames:")[-1].strip()
                    frames_info[current_path] = frames_count
        except subprocess.CalledProcessError as e:
            print(f"Error executing dbu command: {e}")
            return

        if self.sceneListBox.count() > 0:
            monospace_font = QFont("Courier New", 16)
            #monospace_font.setBold(True)  # Set the font to bold
            max_length = max(len(self.sceneListBox.item(index).text()) for index in range(self.sceneListBox.count()))

        # Update list items with frame count information
        for index in range(self.sceneListBox.count()):
            list_item = self.sceneListBox.item(index)
            item_path = list_item.data(Qt.UserRole)

            # Get frames count from parsed output using full path matching
            frames_count = frames_info.get(item_path, "Unknown")
            display_value = path_info.get(item_path, "Unknown")

            formatted_text = f"{list_item.text():<{max_length}}  (Frames: {frames_count})    /{display_value}"

            # 포맷된 텍스트와 글꼴 설정
            list_item.setText(formatted_text)
            list_item.setFont(monospace_font)

        self.sceneListBox.sortItems()

    def handle_double_click_scene_list(self):
        # Get the currently selected item in the scene list
        selected_item = self.sceneListBox.currentItem()
        if selected_item:

            selected_item_text = selected_item.text()
            # Use regex to clean up the scene name
            scene_name = re.sub(r'scene-|\s+\(Frames:.*|\s+/Path:.*', '', selected_item_text).strip()

            # Get the selected environment and job
            selected_env = self.envListBox.currentItem()
            selected_job = self.jobsListBox.currentItem()

            if not selected_env or not selected_job:
                print('No environment or job selected. Cannot open scene.')
                return

            # Build the command to open the scene
            command_to_open_scene = (
                f"/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/MacOS/Harmony\ Premium"
                f" -env {selected_env.text()} -job {selected_job.text()} -scene {scene_name}"
            )

            print(f"Executing command: {command_to_open_scene}")

            # Execute the command
            try:
                subprocess.run(command_to_open_scene, shell=True, check=True)
            except subprocess.CalledProcessError as e:
                print(f'Error executing command: {e}')


    def update_selection_behavior(self):
        # Allow the selection behavior to remain after a mouse click
        self.sceneListBox.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.sceneListBox.itemClicked.connect(self.handle_item_clicked)

    def handle_item_clicked(self, item):
        # This method allows the selected item to remain highlighted
        item.setSelected(not item.isSelected())

    def showContextMenu(self, position, context):
        menu = QMenu()

        # Get user permissions
        try:
            with open('/USA_DB/test_jn/users.json', 'r') as f:
                users = json.load(f)
                user_permissions = users.get(self.username, {}).get("permissions", {})
                context_permissions = user_permissions.get(context, {})
        except FileNotFoundError:
            context_permissions = {}

        if context == 'jobs':
            if context_permissions.get('Job 만들기', False) or self.username == "admin":
                create_job_action = QAction('Job 만들기', self)
                create_job_action.triggered.connect(self.createJob)
                menu.addAction(create_job_action)

        elif context == 'scenes':
            actions = {
                'Scene 만들기': {
                    'callback': self.createScene1,
                    'enabled': context_permissions.get('Scene 만들기', False) or self.username in ["scan", "yeson", "admin"]
                },
                'Scene 만들기color': {
                    'callback': self.createScene2,
                    'enabled': context_permissions.get('Scene 만들기color', False) or self.username == "color" or self.username == "admin"
                },
                'Scene 삭제': {
                    'callback': self.backupAndDelete,
                    'enabled': context_permissions.get('Scene 삭제', False) or self.username == "admin"
                },
                'Scene 복사': {
                    'callback': self.copyScene,
                    'enabled': context_permissions.get('Scene 복사', False) or self.username == "admin"
                },
                'Export': {
                    'callback': self.export_item,
                    'enabled': context_permissions.get('Export', False) or self.username == "admin"
                },
                '다중 jobs Export': {
                    'callback': self.openExportDialog,
                    'enabled': context_permissions.get('다중 jobs Export', False) or self.username == "admin"
                },
                'Import Single': {
                    'callback': self.importScene_S,
                    'enabled': context_permissions.get('Import Single', False) or self.username == "admin"
                },
                'Import Multiple': {
                    'callback': self.importScene,
                    'enabled': context_permissions.get('Import Multiple', False) or self.username == "admin"
                },
                '외주정리 작업': {
                    'callback': self.importOutsourcing,
                    'enabled': context_permissions.get('외주정리 작업', False) or self.username == "admin"
                },
                'Rename': {
                    'callback': self.renameScene,
                    'enabled': context_permissions.get('Rename', False) or self.username == "admin"
                },
                'Checkout': {
                    'callback': self.checkout_scene,
                    'enabled': context_permissions.get('Checkout', False) or self.username == "admin"
                },
                'Checkin': {
                    'callback': self.checkin_scene,
                    'enabled': context_permissions.get('Checkin', False) or self.username == "admin"
                },
                'Reorder': {
                    'callback': self.reorder_scenes,
                    'enabled': context_permissions.get('Reorder', False) or self.username == "admin"
                }
            }

            # Add actions to menu
            for action_name, config in actions.items():
                if config['enabled']:
                    action = QAction(action_name, self)
                    action.triggered.connect(config['callback'])
                    menu.addAction(action)

        # Add permission management for admin users
        if self.username == "admin":
            menu.addSeparator()
            manage_permissions_action = QAction('User Management', self)
            manage_permissions_action.triggered.connect(self.open_user_management)
            menu.addAction(manage_permissions_action)

        menu.exec_(self.jobsListBox.mapToGlobal(position) if context == 'jobs'
                else self.sceneListBox.mapToGlobal(position))

    def copyScene(self):
        selected_scene_item = self.sceneListBox.currentItem()

        if not selected_scene_item:
            QMessageBox.warning(self, "Warning", "Please select a scene to copy.")
            return

        selected_scene = selected_scene_item.text()

        # Open the SceneCopyWindow
        copy_window = SceneCopyWindow()
        copy_window.exec_()


    def backupAndDelete(self):
        selected_items = self.sceneListBox.selectedItems()
        if not selected_items:
            print("선택된 장면이 없습니다.")
            return

        selected_env = self.envListBox.currentItem()
        selected_job = self.jobsListBox.currentItem()

        if not selected_env or not selected_job:
            print("선택된 작업이나 환경이 없습니다.")
            return

        # Confirmation dialog
        reply = QMessageBox.question(
            self, '삭제 확인', "정말로 삭제 하시겠습니까?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.No:
            return

        # Use the backup directory as the export destination
        dest_dir = os.path.join('/Volumes/data/bak', datetime.now().strftime('%Y-%m-%d'))
        os.makedirs(dest_dir, exist_ok=True)

        # Initialize error_occurred
        error_occurred = False

        # Create a job folder
        job_folder = os.path.join(dest_dir, selected_job.text())
        # Check if the job folder already exists and remove it
        if os.path.exists(job_folder):
            for selected_item in selected_items:
                selected_item_text = selected_item.text()
                scene_name = re.sub(r'scene-|\s+\(Frames:.*|\s+/Path:.*', '', selected_item_text).strip()
                scene_folder = os.path.join(job_folder, scene_name)
                # If the scene folder exists, remove it
                if os.path.exists(scene_folder):
                    shutil.rmtree(scene_folder)
        os.makedirs(job_folder, exist_ok=True)

        # Create a QDialog for log output
        log_dialog = QDialog(self)
        log_dialog.setWindowTitle("Export Log")
        log_dialog.setStyleSheet("background-color: white;")
        layout = QVBoxLayout(log_dialog)
        log_text_edit = QTextEdit()
        log_text_edit.setReadOnly(True)
        layout.addWidget(log_text_edit)
        ok_button = QPushButton("OK")
        ok_button.setEnabled(False)
        ok_button.setStyleSheet("background-color: black; color: white;")
        ok_button.clicked.connect(log_dialog.accept)
        layout.addWidget(ok_button)
        log_dialog.setLayout(layout)
        log_dialog.setMinimumSize(600, 300)
        log_dialog.setWindowFlags(log_dialog.windowFlags() | Qt.WindowStaysOnTopHint)
        log_dialog.show()

        # Append the current date and time in green
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_text_edit.setTextColor(QColor('green'))
        log_text_edit.append(f"Control Center (Controlcenter) {current_time}")

        for selected_item in selected_items:
            selected_item_text = selected_item.text()
            scene_name = re.sub(r'scene-|\s+\(Frames:.*|\s+/Path:.*', '', selected_item_text).strip()
            scene_folder = os.path.join(job_folder, scene_name)
            os.makedirs(scene_folder, exist_ok=True)

            # Check for locks using dblock -l command
            lock_check_command = "/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/dblock -l"
            lock_result = subprocess.run(lock_check_command, shell=True, capture_output=True, text=True)

            if lock_result.returncode != 0:
                error_message = f"Lock check failed: {lock_result.stderr}"
                print(error_message)
                self.writeLog(error_message)
                continue

            # Parse lock result to check for matching job and scene names
            locked_items = lock_result.stdout.splitlines()

            lock_info = None
            for line in locked_items:
                if f"/USA_DB/db_jobs/{selected_job.text()}/scene-{scene_name}/" in line:
                    lock_info = line
                    break

            if lock_info:
                # Extract user and peer information from the lock_info line
                user_info_start = lock_info.find("user:")
                peer_info_start = lock_info.find("peer")

                user_info_end = lock_info.find(")", user_info_start) if user_info_start != -1 else -1
                peer_info_end = lock_info.find(")", peer_info_start) if peer_info_start != -1 else -1

                user_info = lock_info[user_info_start:user_info_end+1].strip() if user_info_start != -1 else "Unknown"
                peer_info = lock_info[peer_info_start:peer_info_end+1].strip() if peer_info_start != -1 else "Unknown"

                error_message = f"Scene '{scene_name}' is locked by {user_info}, {peer_info} and cannot be deleted."
                print(error_message)
                self.writeLog(error_message)
                log_text_edit.setTextColor(QColor('red'))
                log_text_edit.append(error_message)
                error_occurred = True
                continue


            command = f"/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/Controlcenter -batch -export -offline -export_env {selected_env.text()} -export_job {selected_job.text()} -export_scene {scene_name} -env_palette -path {scene_folder}"

            try:
                # Execute command and capture logs
                result = subprocess.run(command, shell=True, capture_output=True, text=True)

                # Process standard output and error for detailed logs
                for line in result.stdout.splitlines() + result.stderr.splitlines():
                    if "Export: Errors(s) occured, check the log file:" in line:
                        log_text_edit.setTextColor(QColor('red'))
                        error_occurred = True
                    else:
                        log_text_edit.setTextColor(QColor('black'))
                    log_text_edit.append(line)


                nested_scene_folder = os.path.join(scene_folder, selected_job.text(), scene_name)
                if os.path.exists(nested_scene_folder):
                    for item in os.listdir(nested_scene_folder):
                        shutil.move(os.path.join(nested_scene_folder, item), scene_folder)
                    shutil.rmtree(nested_scene_folder)

                # Check and remove empty job folder if it exists
                job_nested_folder = os.path.join(scene_folder, selected_job.text())
                if os.path.exists(job_nested_folder) and not os.listdir(job_nested_folder):
                    os.rmdir(job_nested_folder)

                # Delete the scene after export
                delete_command = f"/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/Controlcenter -delete -env {selected_env.text()} -job {selected_job.text()} -scene {scene_name}"
                delete_result = subprocess.run(delete_command, shell=True, capture_output=True, text=True)

                if delete_result.returncode != 0:
                    delete_error_message = f"삭제 명령어 실행 중 오류 발생: {delete_result.stderr}"
                    print(delete_error_message)
                    self.writeLog(delete_error_message)
                    self.logTextEdit.setTextColor(QColor('red'))
                    self.logTextEdit.append(delete_error_message)
                else:
                    success_message = f"{selected_job.text()}의 Scene '{scene_name}'이 성공적으로 삭제되었습니다."
                    self.writeLog(success_message)

            except subprocess.CalledProcessError as e:
                error_message = f"[{current_time}] Error exporting scene {scene_name}: {e}"
                log_text_edit.setTextColor(QColor('red'))
                log_text_edit.append(error_message)
                self.writeLog(error_message)
                error_occurred = True

            # Reset text color to black for next iteration
            log_text_edit.setTextColor(QColor('black'))

        # Enable OK button when all commands are complete
        ok_button.setEnabled(True)

        # Execute log dialog and refresh scene list
        log_dialog.exec_()
        self.updateScenes()

        # Show completion message
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowTitle("작업 완료")

        if error_occurred:
            msg_box.setText("<font color='red'>삭제 작업 중 오류가 발생했습니다.</font>")
            self.writeLog("Export 작업 중 오류가 발생했습니다.")

        else:
            msg_box.setText("삭제 작업이 완료되었습니다.")


        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()

    def export_item(self):
        selected_items = self.sceneListBox.selectedItems()
        if not selected_items:
            print("선택된 장면이 없습니다.")
            return

        selected_env = self.envListBox.currentItem()
        selected_job = self.jobsListBox.currentItem()

        if not selected_env or not selected_job:
            print("선택된 작업이나 환경이 없습니다.")
            return

        dest_dir = QFileDialog.getExistingDirectory(self, "Select Export Directory")
        if not dest_dir:
            print("No directory selected.")
            return

        self.writeLog(f"Export 폴더: {dest_dir}")
        error_occurred = False
        job_folder = os.path.join(dest_dir, selected_job.text())
        os.makedirs(job_folder, exist_ok=True)

        selected_texts = [item.text() for item in selected_items]

        # Initialize the progress dialog
        progress_dialog = QProgressDialog("Exporting scenes...", "Cancel", 0, len(selected_items), self)
        progress_dialog.setWindowTitle("Export Progress")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)
        progress_dialog.show()

        log_entries = []  # To store log entries for later display

        for i, selected_item in enumerate(selected_items):
            if progress_dialog.wasCanceled():
                break

            selected_item_text = selected_item.text()
            scene_name = re.sub(r'scene-|\s+\(Frames:.*|\s+/Path:.*', '', selected_item_text).strip()
            scene_folder = os.path.join(job_folder, scene_name)
            os.makedirs(scene_folder, exist_ok=True)

            command = f"/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/Controlcenter -batch -export -final -offline -export_env {selected_env.text()} -export_job {selected_job.text()} -export_scene {scene_name} -env_palette -path {scene_folder}"

            try:
                result = subprocess.run(command, shell=True, capture_output=True, text=True)

                for line in result.stdout.splitlines() + result.stderr.splitlines():
                    log_entries.append(line)

                if result.returncode == 0:
                    simple_message = f"{selected_job.text()}에서 {scene_name}을 export했습니다."
                    log_entries.append(simple_message)
                    self.writeLog(simple_message)
                else:
                    error_message = f"Error exporting {scene_name}: {result.stderr}"
                    log_entries.append(error_message)
                    self.writeLog(error_message)
                    error_occurred = True

                nested_scene_folder = os.path.join(scene_folder, selected_job.text(), scene_name)
                if os.path.exists(nested_scene_folder):
                    for item in os.listdir(nested_scene_folder):
                        shutil.move(os.path.join(nested_scene_folder, item), scene_folder)
                    shutil.rmtree(nested_scene_folder)

                job_nested_folder = os.path.join(scene_folder, selected_job.text())
                if os.path.exists(job_nested_folder) and not os.listdir(job_nested_folder):
                    os.rmdir(job_nested_folder)

            except subprocess.CalledProcessError as e:
                error_message = f"Error exporting scene {scene_name}: {e}"
                log_entries.append(error_message)
                self.writeLog(error_message)
                error_occurred = True

            # Update progress
            progress_dialog.setValue(i + 1)

        # Close the progress dialog
        progress_dialog.close()

        # Create a QDialog for log output
        log_dialog = QDialog(self)
        log_dialog.setWindowTitle("Export Log")
        log_dialog.setStyleSheet("background-color: white;")

        layout = QVBoxLayout(log_dialog)

        log_text_edit = QTextEdit()
        log_text_edit.setReadOnly(True)

        layout.addWidget(log_text_edit)

        ok_button = QPushButton("OK")
        ok_button.setEnabled(True)
        ok_button.setStyleSheet("background-color: black; color: white;")
        ok_button.clicked.connect(log_dialog.accept)

        layout.addWidget(ok_button)

        log_dialog.setLayout(layout)
        log_dialog.setMinimumSize(600, 300)
        log_dialog.setWindowFlags(log_dialog.windowFlags() | Qt.WindowStaysOnTopHint)
        log_dialog.show()

        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_text_edit.setTextColor(QColor('green'))
        log_text_edit.append(f"Control Center (Controlcenter) {current_time}")

        # Append collected logs
        for entry in log_entries:
            if "Error" in entry:
                log_text_edit.setTextColor(QColor('red'))
            else:
                log_text_edit.setTextColor(QColor('black'))
            log_text_edit.append(entry)

        # Show the log dialog after processing
        log_dialog.exec_()

        # Refresh scene list and show completion message
        self.updateScenes()

        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowTitle("작업 완료")

        if error_occurred:
            msg_box.setText("<font color='red'>Export 작업 중 오류가 발생했습니다.</font>")
            self.writeLog("Export 작업 중 오류가 발생했습니다.")

        else:
            msg_box.setText("Export 작업이 완료되었습니다.")


        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()

        # Re-select the previously selected items after updating scenes
        for index in range(self.sceneListBox.count()):
            item = self.sceneListBox.item(index)
            if item.text() in selected_texts:
                item.setSelected(True)

    def renameScene(self):
        selected_items = self.sceneListBox.selectedItems()
        if not selected_items:
            print("선택된 장면이 없습니다.")
            return

        selected_item = selected_items[0]
        selected_item_text = selected_item.text()
        current_scene_name = re.sub(r'scene-|\s+\(Frames:.*|\s+/Path:.*', '', selected_item_text).strip()


        # Pre-fill the input dialog with the current scene name
        new_scene_name, ok = QInputDialog.getText(self, 'Rename Scene', 'Enter new scene name:', text=current_scene_name)
        if not ok or not new_scene_name:
            return

        selected_env = self.envListBox.currentItem()
        selected_job = self.jobsListBox.currentItem()

        if not selected_env or not selected_job:
            print("선택된 작업이나 환경이 없습니다.")
            return

        command = f"/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/Controlcenter -rename -env {selected_env.text()} -job {selected_job.text()} -scene {current_scene_name} -name {new_scene_name}"

        try:
            result = subprocess.run(command, shell=True, capture_output=True, text=True)

            if result.returncode != 0:
                error_message = f"이름 변경 명령어 실행 중 오류 발생: {result.stderr}"
                QMessageBox.critical(self, "Error", error_message)
                self.writeLog(error_message)
            else:
                # Log a concise success message
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                success_message = f"Scene '{current_scene_name}'가 '{new_scene_name}'으로 변경되었습니다."

                QMessageBox.information(self, "Success", success_message)
                self.writeLog(success_message)

                self.updateScenes()

        except Exception as e:
            error_message = f"명령어 실행 중 예외 발생: {e}"
            QMessageBox.critical(self, "Error", error_message)
            self.writeLog(error_message)


    def createScene1(self):
        selected_env = self.envListBox.currentItem()
        selected_job = self.jobsListBox.currentItem()

        if not selected_env or not selected_job:
            QMessageBox.warning(self, "Warning", "환경 또는 작업이 선택되지 않았습니다.")
            return

        dialog = QDialog(self)
        dialog.setWindowTitle('Create Scene')

        layout = QVBoxLayout(dialog)

        input_label = QLabel('여러개의 Scene 생성 시 ","로 구분:')
        scene_input = QLineEdit(dialog)

        checkbox = QCheckBox("순서대로 여러개 만들기(예 001-010)", dialog)

        checkbox_prefix = QCheckBox("알파벳 추가", dialog)
        prefix_input = QLineEdit(dialog)
        prefix_input.setPlaceholderText("앞에 추가할 알파벳을 입력하세요(예:A)")

        layout.addWidget(input_label)
        layout.addWidget(scene_input)
        layout.addWidget(checkbox)
        layout.addWidget(checkbox_prefix)
        layout.addWidget(prefix_input)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, dialog)
        layout.addWidget(buttons)

        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)

        if dialog.exec_() != QDialog.Accepted:
            return

        scene_names = scene_input.text()

        if not scene_names:
            return

        scene_list = []

        if checkbox.isChecked():
            try:
                start, end = map(int, scene_names.split('-'))
                num_digits = 3
                scene_list = [f"{i:0{num_digits}}" for i in range(start, end + 1)]
            except ValueError:
                QMessageBox.warning(self, "Warning", "잘못된 범위 형식입니다. 예: 001-010")
                return
        else:
            scene_list = [name.strip() for name in scene_names.split(',') if name.strip()]

        if checkbox_prefix.isChecked():
            prefix = prefix_input.text().strip()
            if prefix:
                scene_list = [f"{prefix}{name}" for name in scene_list]

        file_systems_mapping = {
            'Bigmouth': ['/usadata2/Titmouse/Big_Mouth/BM_Season8'],
            'Bobs_Burgers': ['/usadata3/Bento_Project/BB_Season13', '/usadata3/Bento_Project/BB_Season14'],
            'Bobs_Burgers_old': ['/usadata3/Bento_Project/BB_Season'],
            'King_Of_The_Hill': ['/usadata2/Disney/KOTH/KOTH_Season14', '/usadata2/Disney/KOTH/KOTH_Season15'],
            'The_Great_North': ['/usadata3/Bento_Project2/Great_North/GN_Season4', '/usadata3/Bento_Project2/Great_North/GN_Season5'],
            'The_Great_North_old': ['/usadata3/Bento_Project2/Great_North'],
            'Test': ['/usadata3/Test'],
            'Yeson_Test': ['/usadata3/Yeson_Test'],
            'Yeson_Test_4K': ['/usadata3/Yeson_Test_4K']
        }

        file_systems = file_systems_mapping.get(selected_env.text(), [])

        if not file_systems:
            QMessageBox.warning(self, "Warning", "선택한 환경에 대한 파일 시스템이 없습니다.")
            return

        file_system, ok = QInputDialog.getItem(self, 'Create Scene', 'Select file system:', file_systems, 0, False)
        if not ok or not file_system:
            return

        # 파일 시스템 선택 확인 메시지
        confirm_msg = f"선택한 {file_system} 맞습니까?\n\nEnvironments: {selected_env.text()}\njobs: {selected_job.text()}\n파일시스템: {file_system}"
        reply = QMessageBox.question(self, '확인', confirm_msg, QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.No:
            return

        success_scenes = []
        error_messages = []

        for scene_name in scene_list:
            command = f"/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/Controlcenter -create -env {selected_env.text()} -job {selected_job.text()} -scene {scene_name} -filesys {file_system} -sorted"

            try:
                result = subprocess.run(command, shell=True, capture_output=True, text=True)

                if result.returncode != 0:
                    error_message = f"장면 '{scene_name}' 생성 명령어 실행 중 오류 발생: {result.stderr}"
                    error_messages.append(error_message)
                    self.writeLog(error_message)
                else:
                    success_scenes.append(scene_name)
                    self.writeLog(f"{selected_job.text()}에서 새로운 Scene '{scene_name}'가 생성되었습니다.")

                    self.updateScenes()

                    for index in range(self.sceneListBox.count()):
                        list_item = self.sceneListBox.item(index)
                        item_text = re.sub(r'scene-|\s+\(Frames:.*|\s+/Path:.*', '', list_item.text()).strip()

                        if item_text == scene_name and item_text not in self.existing_items:
                            list_item.setBackground(QColor('#E7E7E7'))
                            self.existing_items.add(item_text)

            except Exception as e:
                error_message = f"명령어 실행 중 예외 발생: {e}"
                error_messages.append(error_message)
                self.writeLog(error_message)

        if success_scenes:
            success_message = "다음 Scene들이 생성되었습니다:\n" + "\n".join(success_scenes)
            QMessageBox.information(self, "Success", success_message)

        if error_messages:
            QMessageBox.critical(self, "Error", "\n".join(error_messages))
    def createScene2(self):
        selected_env = self.envListBox.currentItem()
        selected_job = self.jobsListBox.currentItem()

        if not selected_env or not selected_job:
            QMessageBox.warning(self, "Warning", "환경 또는 작업이 선택되지 않았습니다.")
            return

        dialog = QDialog(self)
        dialog.setWindowTitle("Select Scene Types")

        layout = QVBoxLayout(dialog)

        checkbox_characters = QCheckBox("CHARACTERS")
        checkbox_props = QCheckBox("PROPS")

        layout.addWidget(checkbox_characters)
        layout.addWidget(checkbox_props)

        ok_button = QPushButton("OK")
        ok_button.clicked.connect(dialog.accept)

        layout.addWidget(ok_button)

        dialog.setLayout(layout)

        if dialog.exec_() == QDialog.Accepted:
            selected_types = []
            if checkbox_characters.isChecked():
                selected_types.append("CHARACTERS")
            if checkbox_props.isChecked():
                selected_types.append("PROPS")
            if not selected_types:
                QMessageBox.warning(self, "Warning", "하나 이상의 Scene을 선택해야 합니다.")
                return

        # Predefined list of file systems
        file_systems = [
            '/usadata3/Bento_Project/BB_Season13',
            '/usadata3/Bento_Project/BB_Season14',
            '/usadata3/Bento_Project2/Great_North/GN_Season4',
            '/usadata2/Titmouse/Big_Mouth/BM_Season8',
            '/usadata3/Bento_Project',
            '/usadata3/Bento_Project2',
            '/usadata3/Test',
            '/usadata3/Yeson_Test',
            '/usadata3/Yeson_Test_4K',
            '/usadata2/Disney/KOTH/KOTH_Season14',
            '/usadata2/Disney/KOTH',
            '/usadata3/Bento_Project2/Great_North/GN_Season5',
            '/usadata2/Disney/KOTH/KOTH_Season15'

        ]  # Replace with actual file systems
        file_system, ok = QInputDialog.getItem(self, 'Create Scene', 'Select file system:', file_systems, 0, False)
        if not ok or not file_system:
            return

        success_scenes = []  # Initialize the list to store successfully created scenes
        error_messages = []

        for scene_type in selected_types:
            command = f"/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/Controlcenter -create -env {selected_env.text()} -job {selected_job.text()} -scene {scene_type} -filesys {file_system} -sorted"


            try:
                result = subprocess.run(command, shell=True, capture_output=True, text=True)

                if result.returncode != 0:
                    error_message = f"장면 '{scene_type}' 생성 명령어 실행 중 오류 발생: {result.stderr}"
                    error_messages.append(error_message)
                    self.writeLog(error_message)
                else:
                    success_scenes.append(scene_type)  # Add to success list
                    self.writeLog(f"{selected_job.text()}에서 새로운 Scene '{scene_type}'가 생성되었습니다.")

                    self.updateScenes()

                    for index in range(self.sceneListBox.count()):
                        list_item = self.sceneListBox.item(index)
                        item_text = re.sub(r'scene-|\s+\(Frames:.*', '', list_item.text()).strip()

                        if item_text == scene_type and item_text not in self.existing_items:
                            print(f"Highlighting scene: {item_text}")  # Debugging output
                            list_item.setBackground(QColor('#E7E7E7'))  # Highlight new item
                            self.existing_items.add(item_text)  # Add to existing items

            except Exception as e:
                error_message = f"명령어 실행 중 예외 발생: {e}"
                error_messages.append(error_message)
                self.writeLog(error_message)

        # Display accumulated messages
        if success_scenes:
            success_message = "Scene이 생성되었습니다:\n" + "\n".join(success_scenes)
            QMessageBox.information(self, "Success", success_message)

        if error_messages:
            QMessageBox.critical(self, "Error", "\n".join(error_messages))


    def importScene(self):
        selected_env = self.envListBox.currentItem()
        selected_job = self.jobsListBox.currentItem()

        if not selected_env or not selected_job:
            QMessageBox.warning(self, "Warning", "환경 또는 작업이 선택되지 않았습니다.")
            return

        # Select a directory containing .xstage files first
        folder_path = QFileDialog.getExistingDirectory(self, 'Select Folder Containing XStage Files')
        if not folder_path:
            QMessageBox.warning(self, "Warning", "폴더가 선택되지 않았습니다.")
            return

        subdirs = [d for d in os.listdir(folder_path)
           if os.path.isdir(os.path.join(folder_path, d)) and not d.endswith('.tpl')]
        sorted_subdirs = sorted(subdirs)

        # Create a QDialog to list and select subdirectories
        dialog = QDialog(self)
        dialog.setWindowTitle("Select Folders to Import")
        layout = QVBoxLayout(dialog)
        list_widget = QListWidget()
        list_widget.addItems(sorted_subdirs)
        list_widget.setSelectionMode(QListWidget.ExtendedSelection)
        layout.addWidget(list_widget)

        # Add Remove button to remove selected items from the list
        remove_button = QPushButton("목록에서 삭제")
        remove_button.clicked.connect(lambda: [list_widget.takeItem(list_widget.row(item)) for item in list_widget.selectedItems()])
        layout.addWidget(remove_button)

        ok_button = QPushButton("Import 시작")
        ok_button.clicked.connect(dialog.accept)
        layout.addWidget(ok_button)
        dialog.setLayout(layout)

        # Show the dialog and wait for user selection
        if dialog.exec_() != QDialog.Accepted:
            # If the dialog is closed or canceled, do not proceed
            return

        selected_folders = [list_widget.item(i).text() for i in range(list_widget.count())]

        if not selected_folders:
            QMessageBox.warning(self, "Warning", "선택된 폴더가 없습니다.")
            return

        file_systems_mapping = {
            'Bigmouth': ['/usadata2/Titmouse/Big_Mouth/BM_Season8'],
            'Bobs_Burgers': ['/usadata3/Bento_Project/BB_Season13', '/usadata3/Bento_Project/BB_Season14'],
            'Bobs_Burgers_old': ['/usadata3/Bento_Project/BB_Season'],
            'King_Of_The_Hill': ['/usadata2/Disney/KOTH/KOTH_Season14', '/usadata2/Disney/KOTH/KOTH_Season15'],
            'The_Great_North': ['/usadata3/Bento_Project2/Great_North/GN_Season4', '/usadata3/Bento_Project2/Great_North/GN_Season5'],
            'The_Great_North_old': ['/usadata3/Bento_Project2/Great_North'],
            'Test': ['/usadata3/Test'],
            'Yeson_Test': ['/usadata3/Yeson_Test'],
            'Yeson_Test_4K': ['/usadata3/Yeson_Test_4K']
        }

        file_systems = file_systems_mapping.get(selected_env.text(), [])

        if not file_systems:
            QMessageBox.warning(self, "Warning", "선택한 환경에 대한 파일 시스템이 없습니다.")
            return


        file_system, ok = QInputDialog.getItem(self, 'Import Scene', 'Select file system:', file_systems, 0, False)
        if not ok or not file_system:
            return
        version_pattern = re.compile(r"\.v(\d+)\.(xstage|digital)$")

        existing_scenes = [
            re.sub(r'scene-|\s+\(Frames:.*|\s+/Path:.*', '', self.sceneListBox.item(i).text()).strip()
            for i in range(self.sceneListBox.count())
        ]

        # Initialize the progress dialog
        progress_dialog = QProgressDialog("Importing scenes...", "Cancel", 0, len(selected_folders), self)
        progress_dialog.setWindowTitle("Import Progress")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)
        progress_dialog.show()
        log_entries = []

        successful_imports = 0  # Counter for successful imports
        imported_scenes = []  # List to keep track of imported scenes

        log_dialog = QDialog(self)
        log_dialog.setWindowTitle("Import Log")
        log_dialog.setStyleSheet("background-color: white;")

        layout = QVBoxLayout(log_dialog)

        log_text_edit = QTextEdit()
        log_text_edit.setReadOnly(True)
        layout.addWidget(log_text_edit)
        ok_button = QPushButton("OK")
        ok_button.setEnabled(False)
        ok_button.setStyleSheet("background-color: black; color: white;")
        ok_button.clicked.connect(log_dialog.accept)
        layout.addWidget(ok_button)
        log_dialog.setLayout(layout)
        log_dialog.setMinimumSize(600, 300)
        log_dialog.setWindowFlags(log_dialog.windowFlags() | Qt.WindowStaysOnTopHint)
        log_dialog.show()

        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_text_edit.setTextColor(QColor('green'))
        log_text_edit.append(f"Control Center (Controlcenter) {current_time}")


        for index, folder_name in enumerate(selected_folders):
            folder_full_path = os.path.join(folder_path, folder_name)
            highest_version_file = None
            highest_version_number = -1
            fnl_files_to_import = []
            digital_files_to_import = []

            for root, dirs, files in os.walk(folder_full_path):
                for file in files:
                    if file.endswith('.xstage') or file.endswith('.digital'):
                        # 버전 패턴 체크
                        match = version_pattern.search(file)
                        if match:
                            version_number = int(match.group(1))
                            if version_number > highest_version_number:
                                highest_version_number = version_number
                                highest_version_file = os.path.join(root, file)
                        # FNL 또는 FINAL 파일 체크
                        elif '_FNL' in file or '_FINAL' in file:
                            fnl_files_to_import.append(os.path.join(root, file))
                        # .digital 파일 체크
                        elif file.endswith('.digital'):
                            digital_files_to_import.append(os.path.join(root, file  ))
                        # _S 숫자 파일 체크
                        elif '_S' in file and file.endswith('.xstage'):
                            s_pattern_match = re.search(r'_S([0-9a-zA-Z]+)\.xstage$', file)
                            if s_pattern_match:
                                highest_version_file = os.path.join(root, file)
                                break

            # 우선순위에 따라 파일 선택
            if not highest_version_file and fnl_files_to_import:
                highest_version_file = fnl_files_to_import[0]
            if not highest_version_file and digital_files_to_import:
                highest_version_file = digital_files_to_import[0]

            if highest_version_file:
                base_file_name = os.path.basename(highest_version_file).split('.v')[0]

                if base_file_name in existing_scenes:
                    reply = QMessageBox.question(
                        self,
                        '중복 확인',
                        f"Scene '{base_file_name}'은 이미 존재합니다. 덮어쓰시겠습니까?",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )
                    if reply == QMessageBox.No:
                        continue

                command = f"/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/Controlcenter -batch -import -target_env {selected_env.text()} -target_job {selected_job.text()} -filesys {file_system} -import_scene {base_file_name} -path {highest_version_file}"
                result = subprocess.run(command, shell=True, capture_output=True, text=True)
                print("Executing command:", command)

                if result.returncode == 0:
                    successful_imports += 1  # Increment successful imports counter
                    imported_scenes.append(base_file_name)  # Track imported scenes

                for line in result.stdout.splitlines() + result.stderr.splitlines():
                    if result.returncode != 0:
                        log_text_edit.setTextColor(QColor('red'))
                    else:
                        log_text_edit.setTextColor(QColor('black'))

                    log_text_edit.append(line)

                log_text_edit.append("")  # Add an empty line for separation
            progress_dialog.setValue(index + 1)

        ok_button.setEnabled(True)

        log_dialog.exec_()

        completion_message = f"<font color = 'red'>{successful_imports}</font>개의 Scene이 성공적으로 Import되었습니다."

        job_and_scenes_message = f"Job: {selected_job.text()}, Scenes: {', '.join(imported_scenes)}"

        QMessageBox.information(self, "Import Completed", f"{completion_message}\n{job_and_scenes_message}")

        self.writeLog(completion_message)
        self.writeLog(job_and_scenes_message)

        self.updateScenes()
        progress_dialog.close()

        for index in range(self.sceneListBox.count()):
            list_item = self.sceneListBox.item(index)

            item_text = re.sub(r'scene-|\s+\(Frames:.*|\s+/Path:.*', '', list_item.text()).strip()

            if item_text in imported_scenes and item_text not in self.existing_items:
                list_item.setBackground(QColor('#E7E7E7'))  # Highlight new item
                self.existing_items.add(item_text)  # Add to existing items

    def importScene_S(self):
        selected_env = self.envListBox.currentItem()
        selected_job = self.jobsListBox.currentItem()

        if not selected_env or not selected_job:
            QMessageBox.warning(self, "Warning", "환경 또는 작업이 선택되지 않았습니다.")
            return

        # Open file dialog to select an .xstage file
        archive_path, _ = QFileDialog.getOpenFileName(self, 'Select Archive File', '', 'XStage Files (*.xstage)')

        if not archive_path:
            QMessageBox.warning(self, "Warning", "아카이브 파일이 선택되지 않았습니다.")
            return

        # Extract base filename before '.v' and '.xstage'
        base_file_name = os.path.basename(archive_path).split('.v')[0]

        # Check for duplicate scene name in the Scene list
        existing_scenes = [
            re.sub(r'scene-|\s+\(Frames:.*|\s+/Path:.*', '', self.sceneListBox.item(i).text()).strip()
            for i in range(self.sceneListBox.count())
        ]

        if base_file_name in existing_scenes:
            reply = QMessageBox.question(
                self,
                '중복 확인',
                f"Scene '{base_file_name}'은 이미 존재합니다. 덮어쓰시겠습니까?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

        file_systems_mapping = {
            'Bigmouth': ['/usadata2/Titmouse/Big_Mouth/BM_Season8'],
            'Bobs_Burgers': ['/usadata3/Bento_Project/BB_Season13', '/usadata3/Bento_Project/BB_Season14'],
            'Bobs_Burgers_old': ['/usadata3/Bento_Project/BB_Season'],
            'King_Of_The_Hill': ['/usadata2/Disney/KOTH/KOTH_Season14', '/usadata2/Disney/KOTH/KOTH_Season15'],
            'The_Great_North': ['/usadata3/Bento_Project2/Great_North/GN_Season4', '/usadata3/Bento_Project2/Great_North/GN_Season5'],
            'The_Great_North_old': ['/usadata3/Bento_Project2/Great_North'],
            'Test': ['/usadata3/Test'],
            'Yeson_Test': ['/usadata3/Yeson_Test'],
            'Yeson_Test_4K': ['/usadata3/Yeson_Test_4K']
        }

        file_systems = file_systems_mapping.get(selected_env.text(), [])

        if not file_systems:
            QMessageBox.warning(self, "Warning", "선택한 환경에 대한 파일 시스템이 없습니다.")
            return
        file_system, ok = QInputDialog.getItem(self, 'Import Scene', 'Select file system:', file_systems, 0, False)
        if not ok or not file_system:
            return

        # Create a QDialog for log output
        log_dialog = QDialog(self)
        log_dialog.setWindowTitle("Import Log")
        log_dialog.setStyleSheet("background-color: white;")
        layout = QVBoxLayout(log_dialog)
        log_text_edit = QTextEdit()
        log_text_edit.setReadOnly(True)
        layout.addWidget(log_text_edit)
        ok_button = QPushButton("OK")
        ok_button.setEnabled(False)
        ok_button.setStyleSheet("background-color: black; color: white;")
        ok_button.clicked.connect(log_dialog.accept)
        layout.addWidget(ok_button)
        log_dialog.setLayout(layout)
        log_dialog.setMinimumSize(600, 300)
        log_dialog.setWindowFlags(log_dialog.windowFlags() | Qt.WindowStaysOnTopHint)
        log_dialog.show()


        command = f"/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/Controlcenter -batch -import -target_env {selected_env.text()} -target_job {selected_job.text()} -filesys {file_system} -import_scene {base_file_name} -path {archive_path} -sorted"
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        print("Executing command:", command)
        # Capture logs and display them in the text edit widget
        for line in result.stdout.splitlines() + result.stderr.splitlines():
            if result.returncode != 0:
                log_text_edit.setTextColor(QColor('red'))
            else:
                log_text_edit.setTextColor(QColor('black'))

            log_text_edit.append(line)

        # Log completion of import operation
        if result.returncode != 0:
            completion_message = f"'{selected_job.text()}'에서 '{base_file_name}' Scene Import 중 오류 발생: {result.stderr}"
            QMessageBox.critical(self, "Error", completion_message)
            log_text_edit.setTextColor(QColor('red'))

            # Log error message
            self.writeLog(completion_message)

            # Display error message in the log dialog
            log_text_edit.append(completion_message)

        else:
            completion_message = f"'{selected_job.text()}'에 '{base_file_name}' Scene Import 완료"
            QMessageBox.information(self, "Success", f"<font size='5'>{completion_message}</font>")

            # Log success message
            self.writeLog(completion_message)

            # Display success message in the log dialog
            log_text_edit.setTextColor(QColor('black'))
            log_text_edit.append(completion_message)
            self.updateScenes()

            # Highlight newly added scene item
            for index in range(self.sceneListBox.count()):
                list_item = self.sceneListBox.item(index)
                item_text = re.sub(r'scene-|\s+\(Frames:.*|\s+/Path:.*', '', list_item.text()).strip()

                if item_text == base_file_name and item_text not in self.existing_items:
                    list_item.setBackground(QColor('#E7E7E7'))  # Highlight new item
                    self.existing_items.add(item_text)  # Add to existing items


        # Enable OK button when all commands are complete
        ok_button.setEnabled(True)

        # Execute log dialog and refresh scene list
        log_dialog.exec_()

    def importOutsourcing(self):
        # Select the job folder
        selected_job_folder = QFileDialog.getExistingDirectory(self, 'Select Job Folder')
        if not selected_job_folder:
            QMessageBox.warning(self, "Warning", "작업 폴더가 선택되지 않았습니다.")
            return

        # Select the base folder containing the download structure
        base_download_folder = QFileDialog.getExistingDirectory(self, 'Select Base Download Folder')
        if not base_download_folder:
            QMessageBox.warning(self, "Warning", "다운로드 기본 폴더가 선택되지 않았습니다.")
            return

        # Define folders to exclude from search
        exclude_folders = {'complete', 'annotation', 'audio', 'elements', 'environments', 'frames', 'jobs'}

        # Counter for successful operations
        successful_operations = 0

        # Iterate over each scene folder in the selected job folder
        for scene_folder_name in os.listdir(selected_job_folder):
            scene_folder_path = os.path.join(selected_job_folder, scene_folder_name)
            if not os.path.isdir(scene_folder_path):
                continue

            # Search for the most recent PALETTE_LIST file in the download directory for this scene
            latest_palette_list_file = None
            latest_mod_time = None

            for root, dirs, files in os.walk(base_download_folder):
                # Exclude specified folders from search
                dirs[:] = [d for d in dirs if d not in exclude_folders]

                # Get the base folder name from the current root path
                current_folder_name = os.path.basename(root)

                # Only process if the folder name exactly matches
                if current_folder_name == scene_folder_name and 'PALETTE_LIST' in files:
                    palette_list_file = os.path.join(root, 'PALETTE_LIST')
                    mod_time = os.path.getmtime(palette_list_file)

                    if latest_mod_time is None or mod_time > latest_mod_time:
                        latest_palette_list_file = palette_list_file
                        latest_mod_time = mod_time

            if not latest_palette_list_file:
                QMessageBox.critical(self, "Error", f"{scene_folder_name}에 대한 적절한 PALETTE_LIST 파일을 찾을 수 없습니다.")
                continue

            destination_file = os.path.join(scene_folder_path, 'PALETTE_LIST')

            try:
                # Copy file and overwrite if exists
                shutil.copy(latest_palette_list_file, destination_file)
                successful_operations += 1  # Increment successful operations counter

            except Exception as e:
                QMessageBox.critical(self, "Error", f"{scene_folder_name}의 파일 복사 중 오류 발생: {e}")

        # 현재 날짜와 시간 가져오기
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Log completion message only to log_text_edit
        completion_message = f"-[외주] {successful_operations}개의 Scene 파일이 성공적으로 덮어쓰기되었습니다."
        completion_message_1 = f"{successful_operations}개의 Scene 파일이 성공적으로 덮어쓰기되었습니다."

        # Assuming log_text_edit is a class member or accessible here
        self.logTextEdit.setTextColor(QColor('black'))
        self.writeLog(completion_message)

        # Optionally show a message box as well
        QMessageBox.information(self, "Success", completion_message_1)

    def createJob(self):
        selected_env = self.envListBox.currentItem()

        if not selected_env:
            QMessageBox.warning(self, "Warning", "환경이 선택되지 않았습니다.")
            return

        env_name = selected_env.text()

        # Prompt for job name only
        job_name, ok = QInputDialog.getText(self, 'Create Job', 'Enter new job name:')

        if not ok or not job_name:
            return

        # Set TD and ATD names to 'usabatch'
        td_name = "usabatch"
        atd_name = "usabatch"

        # Predefined paths for job creation
        predefined_paths = {
            'Bigmouth': '/usadata2/Titmouse/Big_Mouth/BM_Season8',
            'Bigmouth2': '/usadata2/Titmouse/Big_Mouth',
            'Bobs_Burgers': '/usadata3/Bento_Project/BB_Season14',
            'Bobs_Burgers2': '/usadata3/Bento_Project',
            'King_Of_The_Hill': '/usadata2/Disney/KOTH/KOTH_Season15',
            'King_Of_The_Hill2': '/usadata2/Disney/KOTH',
            'The_Great_North': '/usadata3/Bento_Project2/Great_North/GN_Season5',
            'The_Great_North2': '/usadata3/Bento_Project2/Great_North',
            'Test': '/usadata3/Test',
            'Yeson_Test': '/usadata3/Yeson_Test',
            'Yeson_Test_4K': '/usadata3/Yeson_Test_4K'
        }



        # Create a dialog to select a base path using a combo box
        dialog = QDialog(self)
        dialog.setWindowTitle("Select Base Path for Job Creation")

        layout = QVBoxLayout(dialog)

        combo_box = QComboBox()
        combo_box.addItems(predefined_paths.values())

        layout.addWidget(combo_box)

        ok_button = QPushButton("OK")
        ok_button.clicked.connect(dialog.accept)

        layout.addWidget(ok_button)

        dialog.setLayout(layout)

        if dialog.exec_() == QDialog.Accepted:
            selected_path = combo_box.currentText()

            # Construct the command with fixed TD and ATD names
            command = f"/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/Controlcenter -create -env {env_name} -job {job_name} -TD {td_name} -ATD {atd_name}"

            result = subprocess.run(command, shell=True, capture_output=True, text=True)

            if result.returncode != 0:
                error_message = f"작업 생성 명령어 실행 중 오류 발생: {result.stderr}"
                QMessageBox.critical(self, "Error", error_message)
                self.writeLog(error_message)
            else:
                # Create a new folder for the job in the selected path
                new_job_path = os.path.join(selected_path, job_name)
                os.makedirs(new_job_path, exist_ok=True)

                success_message = f"[Job] '{job_name}'이(가) 생성되었습니다."
                QMessageBox.information(self, "Success", success_message)
                self.writeLog(success_message)
                self.updateJobs()  # Refresh the jobs list to show the newly created job


    def checkout_scene(self):
        selected_items = self.sceneListBox.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, 'Warning', '선택된 장면이 없습니다.')
            return

        for selected_item in selected_items:
            item_path = selected_item.data(Qt.UserRole)

            if '(Checkout)' not in selected_item.text():
                command_checkout = f'chmod -R 555 "{item_path}"'
                result = subprocess.run(command_checkout, shell=True, capture_output=True, text=True)

                if result.returncode == 0:
                    item_text = f"{selected_item.text()} (Checkout)"
                    selected_item.setText(item_text)
                    selected_item.setForeground(QColor('red'))
                    self.set_checkout_status(item_path, True)
                    print(f'Checkout 명령어 성공: {command_checkout}')  # For debugging
                else:
                    QMessageBox.warning(self, 'Error', f'체크아웃 실패: {result.stderr}')

        QMessageBox.information(self, 'Success', '선택된 장면들이 체크아웃되었습니다.')
    def checkin_scene(self):
        selected_items = self.sceneListBox.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, 'Warning', '선택된 장면이 없습니다.')
            return

        for selected_item in selected_items:
            item_path = selected_item.data(Qt.UserRole)

            command_checkin = f'chmod -R 777 "{item_path}"'
            result = subprocess.run(command_checkin, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                original_text = selected_item.text().replace(' (Checkout)', '')
                selected_item.setText(original_text)
                selected_item.setForeground(QColor('black'))
                self.set_checkout_status(item_path, False)
                print(f'Checkin 명령어 성공: {command_checkin}')  # For debugging
            else:
                QMessageBox.warning(self, 'Error', f'체크인 실패: {result.stderr}')

        QMessageBox.information(self, 'Success', '선택된 장면들이 체크인되었습니다.')

    def reorder_scenes(self):
        selected_env = self.envListBox.currentItem()
        selected_job = self.jobsListBox.currentItem()

        if not selected_env or not selected_job:
            QMessageBox.warning(self, "Warning", "환경 또는 작업이 선택되지 않았습니다.")
            return

        # Create a temporary scene named 'ztest' with sorting option
        temp_scene_name = 'ztest'
        command_create_temp_scene_sorted = (
            f"/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/Controlcenter "
            f"-batch -create -env {selected_env.text()} -job {selected_job.text()} -scene {temp_scene_name} "
            f"-filesys /usadata3/test_jn -sorted"
        )

        try:
            subprocess.run(command_create_temp_scene_sorted, shell=True, check=True)
            print(f"Temporary scene '{temp_scene_name}' created and sorted.")
        except subprocess.CalledProcessError as e:
            QMessageBox.warning(self, 'Error', f"Scene creation failed: {e}")
            return

        # Delete the temporary scene named 'ztest'
        command_delete_temp_scene = (
            f"/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/Controlcenter "
            f"-batch -delete -env {selected_env.text()} -job {selected_job.text()} -scene {temp_scene_name}"
        )

        try:
            subprocess.run(command_delete_temp_scene, shell=True, check=True)
            print(f"Temporary scene '{temp_scene_name}' deleted.")
        except subprocess.CalledProcessError as e:
            QMessageBox.warning(self, 'Error', f"Scene deletion failed: {e}")


    def openExportDialog(self):
        # 현재 UI에서 사용 가능한 환경, 작업, 장면 목록을 동적으로 가져옵니다.
        available_envs = [self.envListBox.item(i).text() for i in range(self.envListBox.count())]
        available_jobs = [self.jobsListBox.item(i).text() for i in range(self.jobsListBox.count())]
        available_scenes = [self.sceneListBox.item(i).text() for i in range(self.sceneListBox.count())]

        # ExportDialog를 생성하고 필요한 모든 인자를 전달합니다.
        dialog = ExportDialog(self, available_envs, available_jobs, available_scenes)
        dialog.exec_()

class ExportDialog(QDialog):
    def __init__(self, parent, available_envs, available_jobs, available_scenes):
        super().__init__(parent)
        self.setWindowTitle("Select Environments and Jobs for Export")
        self.setMinimumSize(600, 400)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        list_layout = QHBoxLayout()
        
        # Environment section
        env_container = QWidget()
        env_layout = QVBoxLayout(env_container)
        env_layout.addWidget(QLabel('Environments'))
        self.envListBox = QListWidget()
        self.envListBox.addItems(available_envs)
        self.envListBox.itemSelectionChanged.connect(self.updateJobs)
        env_layout.addWidget(self.envListBox)
        list_layout.addWidget(env_container)
        
        # Jobs section
        jobs_container = QWidget()
        jobs_layout = QVBoxLayout(jobs_container)
        jobs_layout.addWidget(QLabel('Jobs'))
        self.jobsListBox = QListWidget()
        self.jobsListBox.itemSelectionChanged.connect(self.updateScenes)
        jobs_layout.addWidget(self.jobsListBox)
        list_layout.addWidget(jobs_container)
        
        # Scenes section
        scenes_container = QWidget()
        scenes_layout = QVBoxLayout(scenes_container)
        scenes_layout.addWidget(QLabel('Scenes'))
        self.sceneListBox = QListWidget()
        self.sceneListBox.setSelectionMode(QListWidget.ExtendedSelection)
        scenes_layout.addWidget(self.sceneListBox)
        list_layout.addWidget(scenes_container)
        
        # Add list layout to main layout
        main_layout.addLayout(list_layout)
        
        # Selected items section
        self.selectedItemsDisplay = QListWidget()
        self.selectedItemsDisplay.setSelectionMode(QListWidget.ExtendedSelection)
        main_layout.addWidget(QLabel("Selected Items for Export"))
        main_layout.addWidget(self.selectedItemsDisplay)
        
        # Buttons
        button_layout = QVBoxLayout()
        for text, callback in [
            ("목록에 추가", self.addToExportList),
            ("목록에서 Scene 삭제", self.removeSelectedItems),
            ("Export", self.exportSelectedItems)
        ]:
            btn = QPushButton(text)
            btn.clicked.connect(callback)
            button_layout.addWidget(btn)
        
        main_layout.addLayout(button_layout)

    def updateJobs(self):
        self.jobsListBox.clear()
        selected_env = self.envListBox.currentItem()
        if not selected_env:
            return

        env_text = selected_env.text()
        filter_old = False

        env_settings = {
            'Bigmouth': ['/usadata2/Titmouse/Big_Mouth/BM_Season8', '/usadata2/Titmouse/Big_Mouth'],
            'Bobs_Burgers': ['/usadata3/Bento_Project/BB_Season13', '/usadata3/Bento_Project/BB_Season14', '/usadata3/Bento_Project'],
            'Bobs_Burgers_old': ['/usadata3/Bento_Project/BB_Season'],
            'King_Of_The_Hill': ['/usadata2/Disney/KOTH/KOTH_Season14', '/usadata2/Disney/KOTH/KOTH_Season15', '/usadata2/Disney/KOTH'],
            'The_Great_North': ['/usadata3/Bento_Project2/Great_North/GN_Season4', '/usadata3/Bento_Project2/Great_North/GN_Season5', '/usadata3/Bento_Project2/Great_North'],
            'Test': ['/usadata3/Test'],
            'Yeson_Test': ['/usadata3/Yeson_Test'],
            'Yeson_Test_4K': ['/usadata3/Yeson_Test_4K']
        }

        if env_text not in env_settings:
            return

        # Get the list of paths for the selected environment
        base_paths = env_settings[env_text]

        # Iterate over each base path (e.g., BM_Season8 and BM_Library)
        for base_path in base_paths:
            if os.path.exists(base_path):
                for job in sorted(os.listdir(base_path)):
                    job_path = os.path.join(base_path, job)
                    # Exclude "BM_Season8" and "EXPORT" from being displayed
                    if os.path.isdir(job_path) and "_Season" not in job and job != "EXPORT":
                        self.jobsListBox.addItem(job)

        self.jobsListBox.sortItems()
    def updateScenes(self):
        self.sceneListBox.clear()
        selected_job = self.jobsListBox.currentItem()
        selected_env = self.envListBox.currentItem()
        if not selected_job or not selected_env:
            return

        env_text = selected_env.text()
        base_path = ''

        env_settings = {
            'Bigmouth': ['/usadata2/Titmouse/Big_Mouth/BM_Season8', '/usadata2/Titmouse/Big_Mouth'],
            'Bobs_Burgers': ['/usadata3/Bento_Project/BB_Season13', '/usadata3/Bento_Project/BB_Season14', '/usadata3/Bento_Project'],
            'Bobs_Burgers_old': ['/usadata3/Bento_Project/BB_Season'],
            'King_Of_The_Hill': ['/usadata2/Disney/KOTH/KOTH_Season14', '/usadata2/Disney/KOTH/KOTH_Season15', '/usadata2/Disney/KOTH'],
            'The_Great_North': ['/usadata3/Bento_Project2/Great_North/GN_Season4', '/usadata3/Bento_Project2/Great_North/GN_Season5', '/usadata3/Bento_Project2/Great_North'],
            'Test': ['/usadata3/Test'],
            'Yeson_Test': ['/usadata3/Yeson_Test'],
            'Yeson_Test_4K': ['/usadata3/Yeson_Test_4K']
        }

        if env_text in env_settings:
            base_paths = env_settings[env_text]

        job_paths = []

        for base_path in base_paths:
            job_path = os.path.join(base_path, selected_job.text())

            if os.path.exists(job_path):
                job_paths.append(job_path)


        if not job_paths and env_text in ['Test', 'Yeson_Test', 'Yeson_Test_4K']:
            job_path = os.path.join(base_path, selected_job.text())
            if os.path.exists(job_path):
                job_paths.append(job_path)

        scene_paths = []  # Initialize scene_paths list

        for job_path in job_paths:
            for item in sorted(os.listdir(job_path)):
                item_path = os.path.join(job_path, item)
                # Only add items that start with "scene"
                if os.path.isdir(item_path) and item.startswith('scene'):
                    scene_paths.append(item_path)
        for scene_path in scene_paths:
            list_item_text = os.path.basename(scene_path)

            list_item = QListWidgetItem(list_item_text)

            list_item.setData(Qt.UserRole, scene_path)
            self.sceneListBox.addItem(list_item)
        self.sceneListBox.sortItems()


    def addToExportList(self):
        # Add selected environments, jobs, and scenes to the display list
        selected_envs = [item.text() for item in self.envListBox.selectedItems()]
        selected_jobs = [item.text() for item in self.jobsListBox.selectedItems()]
        selected_scenes = [item.text() for item in self.sceneListBox.selectedItems()]

        if not selected_envs or not selected_jobs or not selected_scenes:
            QMessageBox.warning(self.parent(), "Warning", "환경과 작업 및 장면을 모두 선택해야 합니다.")
            return

        # Add new selections to the display list
        for env in selected_envs:
            for job in selected_jobs:
                for scene in selected_scenes:
                    display_text = f"Env: {env}, Job: {job}, Scene: {scene}"
                    self.selectedItemsDisplay.addItem(display_text)

    def removeSelectedItems(self):
        # Remove selected items from the display list
        for item in self.selectedItemsDisplay.selectedItems():
            row = self.selectedItemsDisplay.row(item)
            self.selectedItemsDisplay.takeItem(row)


    def exportSelectedItems(self):
        # Extract selections from the display list for export
        export_data = [self.selectedItemsDisplay.item(i).text() for i in range(self.selectedItemsDisplay.count())]

        if not export_data:
            QMessageBox.warning(self.parent(), "Warning", "No items selected for export.")
            return

        dest_dir = QFileDialog.getExistingDirectory(self.parent(), "Select Export Directory")

        if not dest_dir:
            print("No directory selected.")
            return

        self.parent().writeLog(f"Export 폴더: {dest_dir}")

        error_occurred = False

        log_dialog = QDialog(self)
        log_dialog.setWindowTitle("Export Log")
        log_dialog.setStyleSheet("background-color: white;")

        layout = QVBoxLayout(log_dialog)

        log_text_edit = QTextEdit()
        log_text_edit.setReadOnly(True)
        layout.addWidget(log_text_edit)
        ok_button = QPushButton("OK")
        ok_button.setEnabled(False)
        ok_button.setStyleSheet("background-color: black; color: white;")
        ok_button.clicked.connect(log_dialog.accept)
        layout.addWidget(ok_button)
        log_dialog.setLayout(layout)
        log_dialog.setMinimumSize(600, 300)
        log_dialog.setWindowFlags(log_dialog.windowFlags() | Qt.WindowStaysOnTopHint)
        log_dialog.show()

        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        log_text_edit.setTextColor(QColor('green'))

        log_text_edit.append(f"Control Center (Controlcenter) {current_time}")

        for data in export_data:
            # Split the display text to get environment, job, and scene
            env_part, job_part, scene_part = [part.split(": ")[1] for part in data.split(", ")]

            # Remove "scene-" prefix from scene_part
            scene_name = scene_part.replace("scene-", "", 1)

            job_folder = os.path.join(dest_dir.strip(), job_part.strip())
            os.makedirs(job_folder.strip(), exist_ok=True)

            # Create a folder for each scene and execute the export command
            scene_folder = os.path.join(job_folder, scene_name.strip())
            os.makedirs(scene_folder.strip(), exist_ok=True)

            command = f"/Applications/Toon\ Boom\ Harmony\ 21.1\ Premium/Harmony\ 21.1\ Premium.app/Contents/tba/macosx/bin/Controlcenter -batch -export -final -offline -export_env {env_part} -export_job {job_part} -export_scene {scene_name} -env_palette -path {scene_folder}"

            try:
                result = subprocess.run(command, shell=True, capture_output=True, text=True)

                for line in result.stdout.splitlines() + result.stderr.splitlines():
                    if "Export: Errors(s) occured, check the log file:" in line:
                        log_text_edit.setTextColor(QColor('red'))
                        error_occurred = True
                    else:
                        log_text_edit.setTextColor(QColor('black'))
                    log_text_edit.append(line)

                if result.returncode == 0:
                    simple_message = f"{job_part}에서 {scene_name}을 export했습니다."
                    self.parent().writeLog(simple_message)  # Assuming this function exists
                else:
                    error_message = f"Error exporting {scene_name}: {result.stderr}"
                    self.parent().writeLog(error_message)  # Assuming this function exists

                nested_scene_folder = os.path.join(scene_folder.strip(), job_part.strip(), scene_name.strip())
                if os.path.exists(nested_scene_folder.strip()):
                    for item in os.listdir(nested_scene_folder.strip()):
                        shutil.move(os.path.join(nested_scene_folder.strip(), item), scene_folder.strip())
                    shutil.rmtree(nested_scene_folder.strip())

                job_nested_folder = os.path.join(scene_folder.strip(), job_part.strip())
                if os.path.exists(job_nested_folder.strip()) and not os.listdir(job_nested_folder.strip()):
                    os.rmdir(job_nested_folder.strip())

            except subprocess.CalledProcessError as e:
                error_message = f"[{current_time}] Error exporting scene {scene_name}: {e}"
                log_text_edit.setTextColor(QColor('red'))
                log_text_edit.append(error_message)
                self.parent().writeLog(error_message)  # Assuming this function exists
                error_occurred = True

            log_text_edit.setTextColor(QColor('black'))

        ok_button.setEnabled(True)

        log_dialog.exec_()

        self.parent().updateScenes()  # Assuming this function exists

        msg_box=QMessageBox(self)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowTitle("작업 완료")

        if error_occurred:
            msg_box.setText("<font color='red'>Export 작업 중 오류가 발생했습니다.</font>")
            self.parent().writeLog("Export 작업 중 오류가 발생했습니다.")  # Assuming this function exists

        else:
            msg_box.setText("Export 작업이 완료되었습니다.")


        msg_box.setStandardButtons(QMessageBox.Ok)

        msg_box.exec_()



if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = LoginWindow()
    window.show()
    sys.exit(app.exec_())
